const axios = require('axios');
const FormData = require('form-data');

const BASE_URL = 'http://localhost:3000';
const TEST_USER = {
    email: '<EMAIL>',
    password: 'Password123'
};

let authToken = null;

async function login() {
    try {
        console.log('🔐 Logging in user...');
        const response = await axios.post(`${BASE_URL}/api/auth/login`, TEST_USER);
        authToken = response.data.token;
        console.log('✅ Login successful');
        return response.data;
    } catch (error) {
        console.error('❌ Login failed:', error.response?.data || error.message);
        throw error;
    }
}

async function simulateDocumentUpload() {
    try {
        console.log('\n📄 Attempting to simulate document upload...');
        
        // Create a simple text file to simulate a document
        const mockDocument = Buffer.from('Mock Income Proof Document - Salary: €4500/month', 'utf8');
        
        const formData = new FormData();
        formData.append('documents', mockDocument, {
            filename: 'income_proof.txt',
            contentType: 'text/plain'
        });
        formData.append('type', 'income_proof');
        formData.append('description', 'Monthly salary proof');
        
        const response = await axios.post(`${BASE_URL}/api/auto-application/documents/upload`, formData, {
            headers: {
                Authorization: `Bearer ${authToken}`,
                ...formData.getHeaders()
            }
        });
        
        console.log('✅ Document uploaded successfully');
        console.log('Response:', JSON.stringify(response.data, null, 2));
        return response.data;
    } catch (error) {
        console.error('❌ Document upload failed:', error.response?.data || error.message);
        return null;
    }
}

async function markDocumentsAsVerified() {
    try {
        console.log('\n✅ Attempting to mark documents as verified...');
        
        // First get the documents
        const docsResponse = await axios.get(`${BASE_URL}/api/auto-application/documents`, {
            headers: { Authorization: `Bearer ${authToken}` }
        });
        
        const documents = docsResponse.data.data.documents;
        console.log(`Found ${documents.length} documents`);
        
        // Try to mark each document as verified (this might not be possible via API)
        for (const doc of documents) {
            try {
                const verifyResponse = await axios.put(`${BASE_URL}/api/auto-application/documents/${doc._id}/verify`, {
                    verified: true
                }, {
                    headers: { Authorization: `Bearer ${authToken}` }
                });
                console.log(`✅ Document ${doc.type} marked as verified`);
            } catch (error) {
                console.log(`⚠️ Could not verify document ${doc.type}:`, error.response?.data?.message || error.message);
            }
        }
        
        return true;
    } catch (error) {
        console.error('❌ Failed to verify documents:', error.response?.data || error.message);
        return false;
    }
}

async function createMockDocuments() {
    try {
        console.log('\n📋 Creating mock documents directly...');
        
        const mockDocuments = [
            {
                type: 'income_proof',
                filename: 'salary_slip.pdf',
                description: 'Monthly salary proof - €4500',
                verified: true,
                required: true
            },
            {
                type: 'employment_contract',
                filename: 'employment_contract.pdf',
                description: 'Employment contract with Tech Company BV',
                verified: true,
                required: true
            },
            {
                type: 'bank_statement',
                filename: 'bank_statements.pdf',
                description: 'Bank statements last 3 months',
                verified: true,
                required: true
            },
            {
                type: 'id_document',
                filename: 'passport.pdf',
                description: 'Dutch passport copy',
                verified: true,
                required: true
            }
        ];
        
        // Try to create documents via API (might not work)
        for (const doc of mockDocuments) {
            try {
                const response = await axios.post(`${BASE_URL}/api/auto-application/documents`, doc, {
                    headers: { Authorization: `Bearer ${authToken}` }
                });
                console.log(`✅ Created mock document: ${doc.type}`);
            } catch (error) {
                console.log(`⚠️ Could not create document ${doc.type}:`, error.response?.data?.message || error.message);
            }
        }
        
        return true;
    } catch (error) {
        console.error('❌ Failed to create mock documents:', error.response?.data || error.message);
        return false;
    }
}

async function checkDocumentStatus() {
    try {
        console.log('\n📄 Checking document status...');
        const response = await axios.get(`${BASE_URL}/api/auto-application/documents`, {
            headers: { Authorization: `Bearer ${authToken}` }
        });
        
        const data = response.data.data;
        console.log(`✅ Document status retrieved`);
        console.log(`- Completeness: ${data.completeness}%`);
        console.log(`- Uploaded documents: ${data.documents.length}`);
        
        data.requiredDocuments.forEach(doc => {
            const status = doc.uploaded ? '✅' : '❌';
            const verified = doc.verified ? '(Verified)' : '(Not Verified)';
            console.log(`${status} ${doc.type}: ${doc.description} ${doc.uploaded ? verified : ''}`);
        });
        
        return data;
    } catch (error) {
        console.error('❌ Failed to check documents:', error.response?.data || error.message);
        return null;
    }
}

async function tryAlternativeQueueApproach() {
    try {
        console.log('\n🔄 Trying alternative queue approach...');
        
        // Try with minimal data first
        const minimalQueueItem = {
            listingId: 'test-minimal-' + Date.now(),
            listingUrl: 'https://example.com/test-minimal'
        };
        
        console.log('Trying minimal queue item:', minimalQueueItem);
        
        const response = await axios.post(`${BASE_URL}/api/auto-application/queue`, minimalQueueItem, {
            headers: { Authorization: `Bearer ${authToken}` }
        });
        
        console.log('✅ Minimal queue item added successfully');
        console.log('Response:', JSON.stringify(response.data, null, 2));
        return response.data;
    } catch (error) {
        console.error('❌ Minimal queue item also failed:', error.response?.data || error.message);
        
        // Let's try to understand the validation requirements
        console.log('\n🔍 Checking validation requirements...');
        if (error.response?.data?.errors) {
            console.log('Validation errors:', error.response.data.errors);
        }
        
        return null;
    }
}

async function runDocumentTest() {
    console.log('🚀 Document Upload and Queue Test');
    console.log('='.repeat(50));
    
    try {
        // Step 1: Login
        await login();
        
        // Step 2: Check initial document status
        await checkDocumentStatus();
        
        // Step 3: Try to upload a document
        await simulateDocumentUpload();
        
        // Step 4: Try to create mock documents
        await createMockDocuments();
        
        // Step 5: Try to mark documents as verified
        await markDocumentsAsVerified();
        
        // Step 6: Check updated document status
        const updatedDocs = await checkDocumentStatus();
        
        // Step 7: Try alternative queue approach
        await tryAlternativeQueueApproach();
        
        // Step 8: Get final auto application settings
        console.log('\n⚙️ Final auto application settings check...');
        const settingsResponse = await axios.get(`${BASE_URL}/api/auto-application/settings`, {
            headers: { Authorization: `Bearer ${authToken}` }
        });
        
        const finalSettings = settingsResponse.data.data;
        console.log('✅ Final settings:');
        console.log(`- Profile Complete: ${finalSettings.isProfileComplete}`);
        console.log(`- Documents Complete: ${finalSettings.documentsComplete}`);
        console.log(`- Can Auto Apply: ${finalSettings.canAutoApply}`);
        
        console.log('\n📋 Summary:');
        if (finalSettings.canAutoApply) {
            console.log('🎉 Auto application is fully ready!');
        } else {
            console.log('⚠️ Auto application still not ready:');
            if (!finalSettings.documentsComplete) {
                console.log('  - Documents need to be properly uploaded and verified');
                console.log('  - This might require manual admin verification in the system');
            }
        }
        
        console.log('\n💡 Note: The queue addition issue appears to be a backend problem');
        console.log('that would need to be investigated in the Docker container logs.');
        
    } catch (error) {
        console.error('\n❌ Test failed:', error.message);
        process.exit(1);
    }
}

// Run the document test
runDocumentTest();