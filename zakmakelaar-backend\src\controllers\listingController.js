const Listing = require("../models/Listing");
const { catchAsync, AppError } = require("../middleware/errorHandler");
const searchService = require("../services/searchService");
const { convertToFrontendFormat } = require("../services/frontendCompatibilityLayer");

exports.getListings = catchAsync(async (req, res, next) => {
  // Use advanced search service
  const searchResult = await searchService.search(req.query);

  // Convert listings to frontend-compatible format if they have unified schema data
  const frontendListings = searchResult.listings.map(listing => {
    // If the listing has unified data, convert it to frontend format
    if (listing.unifiedData) {
      return convertToFrontendFormat(listing.unifiedData);
    }
    // Otherwise return the original listing
    return listing;
  });

  res.status(200).json({
    status: "success",
    results: frontendListings.length,
    pagination: searchResult.pagination,
    searchParams: searchResult.searchParams,
    performance: searchResult.performance,
    data: {
      listings: frontendListings,
    },
  });
});

exports.getListingById = catchAsync(async (req, res, next) => {
  const listing = await Listing.findById(req.params.id);

  if (!listing) {
    return next(new AppError("No listing found with that ID", 404));
  }

  // Convert to frontend-compatible format if it has unified data
  const frontendListing = listing.unifiedData ? 
    convertToFrontendFormat(listing.unifiedData) : 
    listing;

  res.status(200).json({
    status: "success",
    data: {
      listing: frontendListing,
    },
  });
});

// Get search suggestions
exports.getSearchSuggestions = catchAsync(async (req, res, next) => {
  const { q, type = "location" } = req.query;

  if (!q) {
    return next(new AppError("Query parameter 'q' is required", 400));
  }

  const suggestions = await searchService.getSearchSuggestions(q, type);

  res.status(200).json({
    status: "success",
    results: suggestions.length,
    data: {
      suggestions,
    },
  });
});

// Get available cities from listings
exports.getAvailableCities = catchAsync(async (req, res, next) => {
  try {
    // Get distinct locations from all listings
    const locations = await Listing.distinct('location');
    
    // Filter out null/empty values and sort
    const validCities = locations
      .filter(location => location && typeof location === 'string' && location.trim() !== '')
      .sort();

    res.status(200).json({
      status: "success",
      results: validCities.length,
      data: validCities,
    });
  } catch (error) {
    return next(new AppError("Failed to fetch available cities", 500));
  }
});

// Get search statistics
exports.getSearchStats = catchAsync(async (req, res, next) => {
  const stats = await searchService.getSearchStats();

  res.status(200).json({
    status: "success",
    data: {
      stats,
    },
  });
});

// Get quick stats for dashboard
exports.getQuickStats = catchAsync(async (req, res, next) => {
  const startTime = Date.now();
  const { logHelpers } = require("../services/logger");

  try {
    // Log the incoming request
    logHelpers.logPerformance("quick-stats-request", 0, {
      ip: req.ip,
      userAgent: req.get("User-Agent"),
      timestamp: new Date().toISOString()
    });

    const stats = await searchService.getQuickStats();
    const duration = Date.now() - startTime;

    // Determine response status based on data quality
    let responseStatus = "success";
    let httpStatus = 200;

    // Check for degraded service
    if (stats.degraded || stats.error || stats.warnings) {
      responseStatus = "partial";
      // Still return 200 for partial data, but indicate degradation
    }

    // Log successful response
    logHelpers.logPerformance("quick-stats-response", duration, {
      status: responseStatus,
      cached: stats.cached || false,
      degraded: stats.degraded || false,
      hasErrors: !!stats.error,
      hasWarnings: !!(stats.warnings && stats.warnings.length > 0),
      totalListings: stats.totalListings,
      averagePrice: stats.averagePrice,
      newToday: stats.newToday
    });

    const response = {
      status: responseStatus,
      data: {
        stats: {
          totalListings: stats.totalListings || 0,
          averagePrice: stats.averagePrice || 0,
          newToday: stats.newToday || 0
        }
      },
      cached: stats.cached || false,
      timestamp: new Date().toISOString(),
      performance: stats.performance || {
        duration: `${duration}ms`,
        timestamp: new Date().toISOString()
      }
    };

    // Add degradation information if present
    if (stats.degraded) {
      response.degraded = true;
    }

    if (stats.error) {
      response.error = stats.error;
    }

    if (stats.warnings && stats.warnings.length > 0) {
      response.warnings = stats.warnings;
    }

    if (stats.fallback) {
      response.fallback = true;
      response.message = "Data served from cache due to calculation failure";
    }

    res.status(httpStatus).json(response);

  } catch (error) {
    const duration = Date.now() - startTime;
    
    // Log the error with full context
    logHelpers.logPerformance("quick-stats-controller-error", duration, {
      error: error.message,
      stack: error.stack,
      ip: req.ip,
      userAgent: req.get("User-Agent")
    });

    // Try to get cached data as last resort
    try {
      const cacheService = require("../services/cacheService");
      const cachedStats = await cacheService.get('quick-stats');
      
      if (cachedStats) {
        logHelpers.logCache("get", "quick-stats", true, null, "emergency-fallback");
        
        return res.status(200).json({
          status: "fallback",
          data: {
            stats: {
              totalListings: cachedStats.totalListings || 0,
              averagePrice: cachedStats.averagePrice || 0,
              newToday: cachedStats.newToday || 0
            }
          },
          cached: true,
          fallback: true,
          timestamp: new Date().toISOString(),
          error: "Primary calculation failed, serving cached data",
          performance: {
            duration: `${duration}ms`,
            timestamp: new Date().toISOString()
          }
        });
      }
    } catch (cacheError) {
      logHelpers.logCache("get", "quick-stats", false, cacheError, "emergency-fallback");
    }

    // Final fallback to default values
    logHelpers.logPerformance("quick-stats-final-fallback", duration, {
      message: "All fallback mechanisms failed, returning defaults"
    });

    res.status(200).json({
      status: "error",
      data: {
        stats: {
          totalListings: 0,
          averagePrice: 0,
          newToday: 0
        }
      },
      cached: false,
      timestamp: new Date().toISOString(),
      error: "Stats calculation failed, showing default values",
      performance: {
        duration: `${duration}ms`,
        timestamp: new Date().toISOString()
      }
    });
  }
});
