const User = require('../models/User');
const Property = require('../models/Property');
const Listing = require('../models/Listing');
const aiService = require('./aiService');
const tenantScoringService = require('./tenantScoringService');
const cacheService = require('./cacheService');
const { loggers } = require('./logger');

/**
 * PropertyOwnerService - Manages property owner registration, verification, and tenant screening
 * 
 * This service handles:
 * - Property owner registration and verification (Requirements 10.1, 10.2)
 * - Business registration validation and tax number verification (Requirements 10.2, 10.3)
 * - Property owner dashboard with tenant screening tools (Requirements 10.4, 10.5)
 * - Property management functionality and applicant ranking (Requirements 10.5, 10.6)
 * - Integration with AI service for tenant evaluation (Requirements 10.4, 10.5)
 */
class PropertyOwnerService {
  constructor() {
    this.CACHE_TTL = 3600; // 1 hour cache for property owner data
    this.VERIFICATION_CACHE_TTL = 86400; // 24 hours for verification results
    
    // Dutch business registration patterns
    this.KVK_PATTERN = /^\d{8}$/; // KvK number: 8 digits
    this.BTW_PATTERN = /^NL\d{9}B\d{2}$/; // Dutch VAT number format
    this.IBAN_PATTERN = /^NL\d{2}[A-Z]{4}\d{10}$/; // Dutch IBAN format
  }

  /**
   * Register a user as a property owner
   * Requirements: 10.1, 10.2
   */
  async registerAsPropertyOwner(userId, registrationData) {
    try {
      loggers.app.info(`Registering user ${userId} as property owner`);

      const user = await User.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Validate registration data
      this._validateRegistrationData(registrationData);

      // Check if user is already a property owner
      if (user.propertyOwner.isPropertyOwner) {
        throw new Error('User is already registered as a property owner');
      }

      // Update user profile to include property owner type
      if (!user.profile.userType.includes('property_owner')) {
        user.profile.userType.push('property_owner');
      }

      // Set property owner data
      user.propertyOwner = {
        isPropertyOwner: true,
        verificationStatus: 'pending',
        businessRegistration: registrationData.businessRegistration,
        taxNumber: registrationData.taxNumber,
        bankAccount: registrationData.bankAccount,
        properties: []
      };

      // Add business contact information to profile if provided
      if (registrationData.businessName) {
        user.profile.businessName = registrationData.businessName;
      }
      if (registrationData.businessAddress) {
        user.profile.businessAddress = registrationData.businessAddress;
      }
      if (registrationData.businessPhone) {
        user.profile.businessPhone = registrationData.businessPhone;
      }

      await user.save();

      // Start verification process
      const verificationResult = await this._initiateVerification(userId, registrationData);

      loggers.app.info(`Property owner registration completed for user ${userId}`);
      
      return {
        success: true,
        message: 'Property owner registration completed',
        verificationStatus: user.propertyOwner.verificationStatus,
        verificationResult,
        userId
      };

    } catch (error) {
      loggers.app.error(`Error registering property owner for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Verify property owner business registration and documents
   * Requirements: 10.2, 10.3
   */
  async verifyPropertyOwner(userId, verificationDocuments = []) {
    try {
      loggers.app.info(`Verifying property owner ${userId}`);

      const user = await User.findById(userId);
      if (!user || !user.propertyOwner.isPropertyOwner) {
        throw new Error('User is not registered as a property owner');
      }

      // Check cache for recent verification
      const cachedResult = await this._getCachedVerification(userId);
      if (cachedResult) {
        loggers.app.info(`Returning cached verification for user ${userId}`);
        return cachedResult;
      }

      const verificationResults = {
        businessRegistration: await this._verifyBusinessRegistration(user.propertyOwner.businessRegistration),
        taxNumber: await this._verifyTaxNumber(user.propertyOwner.taxNumber),
        bankAccount: await this._verifyBankAccount(user.propertyOwner.bankAccount),
        documents: await this._verifyDocuments(userId, verificationDocuments),
        overallStatus: 'pending'
      };

      // Calculate overall verification status
      const verificationScore = this._calculateVerificationScore(verificationResults);
      
      if (verificationScore >= 80) {
        verificationResults.overallStatus = 'verified';
        user.propertyOwner.verificationStatus = 'verified';
      } else if (verificationScore >= 50) {
        verificationResults.overallStatus = 'partial';
        user.propertyOwner.verificationStatus = 'pending';
      } else {
        verificationResults.overallStatus = 'rejected';
        user.propertyOwner.verificationStatus = 'rejected';
      }

      await user.save();

      // Cache verification results
      await this._cacheVerification(userId, verificationResults);

      loggers.app.info(`Property owner verification completed for user ${userId}: ${verificationResults.overallStatus}`);
      
      return {
        ...verificationResults,
        verificationScore,
        userId,
        verifiedAt: new Date()
      };

    } catch (error) {
      loggers.app.error(`Error verifying property owner ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Get property owner dashboard data
   * Requirements: 10.4, 10.5
   */
  async getPropertyOwnerDashboard(userId) {
    try {
      loggers.app.info(`Getting dashboard data for property owner ${userId}`);

      const user = await User.findById(userId);
      if (!user || !user.propertyOwner.isPropertyOwner) {
        throw new Error('User is not a property owner');
      }

      // Get property owner's properties
      const properties = await this._getOwnerProperties(userId);
      
      // Get recent applications for all properties
      const applications = await this._getRecentApplications(userId);
      
      // Get tenant screening statistics
      const screeningStats = await this._getScreeningStatistics(userId);
      
      // Get performance metrics
      const performanceMetrics = await this._getPerformanceMetrics(userId);

      const dashboardData = {
        owner: {
          id: userId,
          name: user.fullName,
          email: user.email,
          verificationStatus: user.propertyOwner.verificationStatus,
          businessRegistration: user.propertyOwner.businessRegistration,
          memberSince: user.createdAt
        },
        properties: {
          total: properties.length,
          active: properties.filter(p => p.status === 'active').length,
          rented: properties.filter(p => p.status === 'rented').length,
          data: properties
        },
        applications: {
          total: applications.length,
          pending: applications.filter(a => a.status === 'pending').length,
          reviewed: applications.filter(a => a.status === 'reviewed').length,
          recent: applications.slice(0, 10)
        },
        screening: screeningStats,
        performance: performanceMetrics,
        lastUpdated: new Date()
      };

      loggers.app.info(`Dashboard data retrieved for property owner ${userId}`);
      return dashboardData;

    } catch (error) {
      loggers.app.error(`Error getting dashboard for property owner ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Screen tenants for a property
   * Requirements: 10.4, 10.5, 10.6
   */
  async screenTenants(propertyId, applicationIds = []) {
    try {
      loggers.app.info(`Screening tenants for property ${propertyId}`);

      // Validate property ownership
      const property = await this._validatePropertyOwnership(propertyId);
      
      // Get applications to screen
      const applications = await this._getApplicationsForScreening(propertyId, applicationIds);
      
      if (applications.length === 0) {
        return {
          propertyId,
          screenedApplications: [],
          message: 'No applications found for screening'
        };
      }

      // Screen each application
      const screenedApplications = await Promise.all(
        applications.map(async (application) => {
          const screeningResult = await this._screenSingleApplication(application, property);
          return {
            applicationId: application._id,
            applicantId: application.applicantId,
            screeningResult,
            recommendationScore: screeningResult.overallScore,
            riskLevel: this._determineRiskLevel(screeningResult.overallScore),
            aiRecommendation: await this._getAIRecommendation(application, property, screeningResult)
          };
        })
      );

      // Rank applications by screening score
      const rankedApplications = screenedApplications.sort((a, b) => 
        b.recommendationScore - a.recommendationScore
      );

      // Generate screening report
      const screeningReport = await this._generateScreeningReport(propertyId, rankedApplications);

      loggers.app.info(`Tenant screening completed for property ${propertyId}: ${screenedApplications.length} applications screened`);
      
      return {
        propertyId,
        property: {
          title: property.title,
          location: property.location,
          price: property.price
        },
        screenedApplications: rankedApplications,
        screeningReport,
        screenedAt: new Date()
      };

    } catch (error) {
      loggers.app.error(`Error screening tenants for property ${propertyId}:`, error);
      throw error;
    }
  }

  /**
   * Rank applicants using AI-powered evaluation
   * Requirements: 10.5, 10.6
   */
  async rankApplicants(propertyId, criteria = {}) {
    try {
      loggers.app.info(`Ranking applicants for property ${propertyId}`);

      const property = await this._validatePropertyOwnership(propertyId);
      const applications = await this._getApplicationsForScreening(propertyId);

      if (applications.length === 0) {
        return {
          propertyId,
          rankedApplicants: [],
          message: 'No applications found for ranking'
        };
      }

      // Get detailed applicant data including tenant scores
      const applicantsWithScores = await Promise.all(
        applications.map(async (application) => {
          const applicant = await User.findById(application.applicantId);
          const tenantScore = await tenantScoringService.calculateTenantScore(application.applicantId);
          
          return {
            application,
            applicant,
            tenantScore,
            compatibility: await this._calculatePropertyCompatibility(applicant, property)
          };
        })
      );

      // Use AI to rank applicants
      const aiRanking = await this._getAIRanking(applicantsWithScores, property, criteria);

      // Combine AI ranking with tenant scores
      const finalRanking = this._combineRankingFactors(applicantsWithScores, aiRanking, criteria);

      loggers.app.info(`Applicant ranking completed for property ${propertyId}: ${finalRanking.length} applicants ranked`);
      
      return {
        propertyId,
        property: {
          title: property.title,
          location: property.location,
          price: property.price
        },
        rankedApplicants: finalRanking,
        rankingCriteria: criteria,
        rankedAt: new Date()
      };

    } catch (error) {
      loggers.app.error(`Error ranking applicants for property ${propertyId}:`, error);
      throw error;
    }
  }

  /**
   * Manage properties for a property owner
   * Requirements: 10.3, 10.4
   */
  async manageProperties(userId, action, propertyData = null) {
    try {
      loggers.app.info(`Managing properties for user ${userId}, action: ${action}`);

      const user = await User.findById(userId);
      if (!user || !user.propertyOwner.isPropertyOwner) {
        throw new Error('User is not a property owner');
      }

      switch (action) {
        case 'list':
          return await this._getOwnerProperties(userId);
          
        case 'add':
          return await this._addProperty(userId, propertyData);
          
        case 'update':
          return await this._updateProperty(userId, propertyData);
          
        case 'remove':
          return await this._removeProperty(userId, propertyData.propertyId);
          
        case 'activate':
          return await this._activateProperty(userId, propertyData.propertyId);
          
        case 'deactivate':
          return await this._deactivateProperty(userId, propertyData.propertyId);
          
        default:
          throw new Error(`Invalid property management action: ${action}`);
      }

    } catch (error) {
      loggers.app.error(`Error managing properties for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Generate comprehensive property owner report
   * Requirements: 10.6
   */
  async generatePropertyReport(propertyId, reportType = 'comprehensive') {
    try {
      loggers.app.info(`Generating ${reportType} report for property ${propertyId}`);

      const property = await this._validatePropertyOwnership(propertyId);
      const applications = await this._getApplicationsForScreening(propertyId);
      
      let report = {
        propertyId,
        property: {
          title: property.title,
          location: property.location,
          price: property.price,
          size: property.size,
          rooms: property.rooms
        },
        reportType,
        generatedAt: new Date()
      };

      switch (reportType) {
        case 'comprehensive':
          report = {
            ...report,
            ...(await this._generateComprehensiveReport(property, applications))
          };
          break;
          
        case 'screening':
          report = {
            ...report,
            ...(await this._generateScreeningReport(propertyId, applications))
          };
          break;
          
        case 'performance':
          report = {
            ...report,
            ...(await this._generatePerformanceReport(property))
          };
          break;
          
        default:
          throw new Error(`Invalid report type: ${reportType}`);
      }

      loggers.app.info(`Report generated for property ${propertyId}`);
      return report;

    } catch (error) {
      loggers.app.error(`Error generating report for property ${propertyId}:`, error);
      throw error;
    }
  }

  // Private helper methods

  /**
   * Validate property owner registration data
   * @private
   */
  _validateRegistrationData(data) {
    if (!data.businessRegistration) {
      throw new Error('Business registration number is required');
    }

    if (!this.KVK_PATTERN.test(data.businessRegistration)) {
      throw new Error('Invalid KvK number format. Must be 8 digits.');
    }

    if (data.taxNumber && !this.BTW_PATTERN.test(data.taxNumber)) {
      throw new Error('Invalid Dutch VAT number format');
    }

    if (data.bankAccount && !this.IBAN_PATTERN.test(data.bankAccount)) {
      throw new Error('Invalid Dutch IBAN format');
    }
  }

  /**
   * Initiate verification process
   * @private
   */
  async _initiateVerification(userId, registrationData) {
    try {
      // In a real implementation, this would integrate with Dutch business registries
      // For now, we'll simulate the verification process
      
      const verificationChecks = {
        kvkLookup: await this._simulateKvKLookup(registrationData.businessRegistration),
        btwValidation: registrationData.taxNumber ? await this._simulateBTWValidation(registrationData.taxNumber) : null,
        ibanValidation: registrationData.bankAccount ? await this._simulateIBANValidation(registrationData.bankAccount) : null
      };

      return {
        initiated: true,
        checks: verificationChecks,
        estimatedCompletionTime: '2-5 business days'
      };

    } catch (error) {
      loggers.app.error(`Error initiating verification for user ${userId}:`, error);
      return {
        initiated: false,
        error: error.message
      };
    }
  }

  /**
   * Verify business registration with KvK
   * @private
   */
  async _verifyBusinessRegistration(kvkNumber) {
    try {
      // Simulate KvK API call
      // In production, this would call the actual KvK API
      const isValid = this.KVK_PATTERN.test(kvkNumber);
      
      return {
        valid: isValid,
        kvkNumber,
        businessName: isValid ? 'Sample Business B.V.' : null,
        status: isValid ? 'active' : 'invalid',
        verifiedAt: new Date()
      };

    } catch (error) {
      return {
        valid: false,
        error: error.message,
        verifiedAt: new Date()
      };
    }
  }

  /**
   * Verify tax number with Dutch tax authority
   * @private
   */
  async _verifyTaxNumber(taxNumber) {
    try {
      if (!taxNumber) return { valid: true, message: 'Tax number not provided' };
      
      const isValid = this.BTW_PATTERN.test(taxNumber);
      
      return {
        valid: isValid,
        taxNumber,
        status: isValid ? 'valid' : 'invalid',
        verifiedAt: new Date()
      };

    } catch (error) {
      return {
        valid: false,
        error: error.message,
        verifiedAt: new Date()
      };
    }
  }

  /**
   * Verify bank account IBAN
   * @private
   */
  async _verifyBankAccount(iban) {
    try {
      if (!iban) return { valid: true, message: 'Bank account not provided' };
      
      const isValid = this.IBAN_PATTERN.test(iban);
      
      return {
        valid: isValid,
        iban,
        bankName: isValid ? 'ING Bank' : null,
        status: isValid ? 'valid' : 'invalid',
        verifiedAt: new Date()
      };

    } catch (error) {
      return {
        valid: false,
        error: error.message,
        verifiedAt: new Date()
      };
    }
  }

  /**
   * Verify uploaded documents
   * @private
   */
  async _verifyDocuments(userId, documents) {
    try {
      const user = await User.findById(userId);
      const verificationResults = [];

      for (const doc of documents) {
        const userDoc = user.documents.find(d => d.id === doc.documentId);
        if (userDoc) {
          verificationResults.push({
            documentId: doc.documentId,
            type: userDoc.type,
            verified: true, // In production, this would involve actual document verification
            verifiedAt: new Date()
          });
        }
      }

      return verificationResults;

    } catch (error) {
      return [];
    }
  }

  /**
   * Calculate overall verification score
   * @private
   */
  _calculateVerificationScore(results) {
    let score = 0;
    let maxScore = 0;

    // Business registration (40 points)
    maxScore += 40;
    if (results.businessRegistration.valid) score += 40;

    // Tax number (20 points)
    maxScore += 20;
    if (results.taxNumber && results.taxNumber.valid) score += 20;

    // Bank account (20 points)
    maxScore += 20;
    if (results.bankAccount && results.bankAccount.valid) score += 20;

    // Documents (20 points)
    maxScore += 20;
    if (results.documents.length > 0) {
      const verifiedDocs = results.documents.filter(d => d.verified).length;
      score += (verifiedDocs / results.documents.length) * 20;
    }

    return Math.round((score / maxScore) * 100);
  }

  /**
   * Get properties owned by a user
   * @private
   */
  async _getOwnerProperties(userId) {
    try {
      const properties = await Property.findByOwner(userId);
      return properties;
    } catch (error) {
      loggers.app.error(`Error getting owner properties for user ${userId}:`, error);
      return [];
    }
  }

  /**
   * Add new property
   * @private
   */
  async _addProperty(userId, propertyData) {
    try {
      loggers.app.info(`Adding new property for user ${userId}`);

      // Validate user is property owner
      const user = await User.findById(userId);
      if (!user || !user.propertyOwner.isPropertyOwner) {
        throw new Error('User is not a property owner');
      }

      // Create new property
      const property = new Property({
        ...propertyData,
        owner: {
          userId: userId,
          contactPreference: 'email',
          responseTime: 'within 24 hours'
        },
        status: 'draft',
        createdAt: new Date(),
        updatedAt: new Date()
      });

      await property.save();

      // Update user's property list
      if (!user.propertyOwner.properties) {
        user.propertyOwner.properties = [];
      }
      user.propertyOwner.properties.push(property._id);
      await user.save();

      loggers.app.info(`Property ${property._id} added successfully for user ${userId}`);

      return {
        success: true,
        propertyId: property._id,
        status: property.status,
        message: 'Property added successfully'
      };

    } catch (error) {
      loggers.app.error(`Error adding property for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Update existing property
   * @private
   */
  async _updateProperty(userId, propertyData) {
    try {
      const { propertyId, ...updateData } = propertyData;
      loggers.app.info(`Updating property ${propertyId} for user ${userId}`);

      // Find and validate property
      const property = await Property.findById(propertyId);
      if (!property) {
        throw new Error('Property not found');
      }

      if (property.owner.userId.toString() !== userId.toString()) {
        throw new Error('Unauthorized: Property does not belong to this user');
      }

      // Update property fields
      Object.keys(updateData).forEach(key => {
        if (updateData[key] !== undefined) {
          property[key] = updateData[key];
        }
      });

      property.updatedAt = new Date();
      await property.save();

      // If property is active, update the public listing
      if (property.status === 'active') {
        await this._createPublicListing(property);
      }

      loggers.app.info(`Property ${propertyId} updated successfully`);

      return {
        success: true,
        propertyId,
        status: property.status,
        message: 'Property updated successfully'
      };

    } catch (error) {
      loggers.app.error(`Error updating property ${propertyData.propertyId}:`, error);
      throw error;
    }
  }

  /**
   * Remove property
   * @private
   */
  async _removeProperty(userId, propertyId) {
    try {
      loggers.app.info(`Removing property ${propertyId} for user ${userId}`);

      // Find and validate property
      const property = await Property.findById(propertyId);
      if (!property) {
        throw new Error('Property not found');
      }

      if (property.owner.userId.toString() !== userId.toString()) {
        throw new Error('Unauthorized: Property does not belong to this user');
      }

      // Check if property can be deleted
      if (property.status === 'rented') {
        throw new Error('Cannot delete rented property. Please mark as inactive instead.');
      }

      // Remove from public listings
      await this._removePublicListing(propertyId);

      // Remove property
      await Property.findByIdAndDelete(propertyId);

      // Update user's property list
      const user = await User.findById(userId);
      if (user && user.propertyOwner.properties) {
        user.propertyOwner.properties = user.propertyOwner.properties.filter(
          id => id.toString() !== propertyId.toString()
        );
        await user.save();
      }

      loggers.app.info(`Property ${propertyId} removed successfully`);

      return {
        success: true,
        propertyId,
        message: 'Property removed successfully'
      };

    } catch (error) {
      loggers.app.error(`Error removing property ${propertyId}:`, error);
      throw error;
    }
  }

  /**
   * Get properties for property owner (public method)
   * Requirements: 10.3, 10.4
   */
  async getProperties(userId) {
    try {
      loggers.app.info(`Getting properties for property owner ${userId}`);

      const user = await User.findById(userId);
      if (!user || !user.propertyOwner.isPropertyOwner) {
        throw new Error('User is not a property owner');
      }

      const properties = await Property.findByOwner(userId);
      
      loggers.app.info(`Retrieved ${properties.length} properties for owner ${userId}`);
      
      return {
        success: true,
        data: properties,
        total: properties.length
      };

    } catch (error) {
      loggers.app.error(`Error getting properties for owner ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Get single property details for property owner
   * Requirements: 10.3, 10.4
   */
  async getPropertyDetails(userId, propertyId) {
    try {
      loggers.app.info(`Getting property details for ${propertyId} from owner ${userId}`);

      const user = await User.findById(userId);
      if (!user || !user.propertyOwner.isPropertyOwner) {
        throw new Error('User is not a property owner');
      }

      const property = await Property.findById(propertyId);
      
      if (!property) {
        throw new Error('Property not found');
      }

      // Verify that the property belongs to this owner
      if (property.owner.userId.toString() !== userId.toString()) {
        throw new Error('Property does not belong to this owner');
      }
      
      loggers.app.info(`Retrieved property details for ${propertyId}`);
      
      return {
        success: true,
        data: property
      };

    } catch (error) {
      loggers.app.error(`Error getting property details for ${propertyId} from owner ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Get recent applications for property owner
   * @private
   */
  async _getRecentApplications(userId) {
    try {
      // Mock application data
      // In production, this would query an Application model
      return [
        {
          _id: 'app1',
          applicantId: 'user1',
          propertyId: 'prop1',
          status: 'pending',
          appliedAt: new Date(),
          tenantScore: 85
        },
        {
          _id: 'app2',
          applicantId: 'user2',
          propertyId: 'prop1',
          status: 'reviewed',
          appliedAt: new Date(Date.now() - 86400000),
          tenantScore: 72
        }
      ];

    } catch (error) {
      return [];
    }
  }

  /**
   * Get screening statistics
   * @private
   */
  async _getScreeningStatistics(userId) {
    return {
      totalScreened: 45,
      averageScore: 73,
      acceptanceRate: 0.67,
      topScoreRange: '80-100',
      commonRejectionReasons: [
        'Insufficient income',
        'Poor rental history',
        'Incomplete documentation'
      ]
    };
  }

  /**
   * Get performance metrics
   * @private
   */
  async _getPerformanceMetrics(userId) {
    return {
      averageTimeToRent: 14, // days
      occupancyRate: 0.92,
      renewalRate: 0.78,
      averageRentPrice: 2350,
      totalRevenue: 28200,
      monthlyGrowth: 0.05
    };
  }

  // Simulation methods for Dutch business verification
  async _simulateKvKLookup(kvkNumber) {
    return {
      found: this.KVK_PATTERN.test(kvkNumber),
      businessName: 'Sample Business B.V.',
      status: 'active'
    };
  }

  async _simulateBTWValidation(btwNumber) {
    return {
      valid: this.BTW_PATTERN.test(btwNumber),
      status: 'active'
    };
  }

  async _simulateIBANValidation(iban) {
    return {
      valid: this.IBAN_PATTERN.test(iban),
      bankName: 'ING Bank'
    };
  }

  // Cache management methods
  async _getCachedVerification(userId) {
    try {
      return await cacheService.get(`property_owner_verification:${userId}`);
    } catch (error) {
      return null;
    }
  }

  async _cacheVerification(userId, verificationData) {
    try {
      await cacheService.set(
        `property_owner_verification:${userId}`, 
        verificationData, 
        this.VERIFICATION_CACHE_TTL
      );
    } catch (error) {
      loggers.app.warn('Failed to cache verification data:', error);
    }
  }

  // Additional helper methods would be implemented here for:
  // - _validatePropertyOwnership
  // - _getApplicationsForScreening
  // - _screenSingleApplication
  // - _getAIRecommendation
  // - _generateScreeningReport
  // - _getAIRanking
  // - _combineRankingFactors
  // - _calculatePropertyCompatibility
  // - _addProperty, _updateProperty, _removeProperty, etc.
  // - _generateComprehensiveReport, _generatePerformanceReport
  // - _determineRiskLevel

  /**
   * Validate property ownership
   * @private
   */
  async _validatePropertyOwnership(propertyId) {
    // Mock property validation
    return {
      _id: propertyId,
      title: `Property ${propertyId}`,
      location: 'Amsterdam',
      price: '€2,500',
      size: '75m²',
      rooms: '3'
    };
  }

  /**
   * Get applications for screening
   * @private
   */
  async _getApplicationsForScreening(propertyId, applicationIds = []) {
    // Mock applications
    return [
      {
        _id: 'app1',
        applicantId: 'user1',
        propertyId,
        status: 'pending',
        appliedAt: new Date()
      }
    ];
  }

  /**
   * Screen single application
   * @private
   */
  async _screenSingleApplication(application, property) {
    const tenantScore = await tenantScoringService.calculateTenantScore(application.applicantId);
    
    return {
      overallScore: tenantScore.overallScore,
      components: tenantScore.components,
      riskFactors: [],
      recommendations: ['Strong candidate based on tenant score']
    };
  }

  /**
   * Get AI recommendation for application
   * @private
   */
  async _getAIRecommendation(application, property, screeningResult) {
    try {
      const prompt = `
        Analyze this rental application and provide a recommendation.
        
        Property: ${property.title} in ${property.location}, ${property.price}
        Applicant Score: ${screeningResult.overallScore}/100
        
        Provide a brief recommendation (approve/conditional/reject) with reasoning.
      `;

      const aiResponse = await aiService.openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are a property management expert. Provide concise rental application recommendations.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 200,
        temperature: 0.3
      });

      return aiResponse.choices[0].message.content;

    } catch (error) {
      loggers.app.error('Error getting AI recommendation:', error);
      return 'Unable to generate AI recommendation at this time.';
    }
  }

  /**
   * Generate screening report
   * @private
   */
  async _generateScreeningReport(propertyId, applications) {
    return {
      totalApplications: applications.length,
      averageScore: applications.reduce((sum, app) => sum + (app.recommendationScore || 0), 0) / applications.length,
      recommendedApplicants: applications.filter(app => (app.recommendationScore || 0) >= 70).length,
      highRiskApplicants: applications.filter(app => app.riskLevel === 'high').length,
      generatedAt: new Date()
    };
  }

  /**
   * Get AI ranking for applicants
   * @private
   */
  async _getAIRanking(applicantsWithScores, property, criteria) {
    // Mock AI ranking - in production would use actual AI service
    return applicantsWithScores.map((applicant, index) => ({
      ...applicant,
      aiRank: index + 1,
      aiScore: Math.max(0, applicant.tenantScore.overallScore + Math.random() * 10 - 5)
    }));
  }

  /**
   * Combine ranking factors
   * @private
   */
  _combineRankingFactors(applicantsWithScores, aiRanking, criteria) {
    return aiRanking
      .sort((a, b) => b.aiScore - a.aiScore)
      .map((applicant, index) => ({
        rank: index + 1,
        applicantId: applicant.applicant._id,
        applicantName: applicant.applicant.fullName,
        tenantScore: applicant.tenantScore.overallScore,
        aiScore: applicant.aiScore,
        finalScore: Math.round((applicant.tenantScore.overallScore + applicant.aiScore) / 2),
        recommendation: applicant.aiScore >= 75 ? 'approve' : applicant.aiScore >= 60 ? 'conditional' : 'reject'
      }));
  }

  /**
   * Calculate property compatibility
   * @private
   */
  async _calculatePropertyCompatibility(applicant, property) {
    // Mock compatibility calculation
    return {
      score: Math.floor(Math.random() * 40) + 60, // 60-100
      factors: ['Location preference match', 'Budget alignment', 'Property type preference']
    };
  }

  /**
   * Determine risk level based on score
   * @private
   */
  _determineRiskLevel(score) {
    if (score >= 80) return 'low';
    if (score >= 60) return 'medium';
    return 'high';
  }

  /**
   * Add property to owner's portfolio
   * @private
   */
  async _addProperty(userId, propertyData) {
    try {
      loggers.app.info(`Adding property for user ${userId}`);

      const user = await User.findById(userId);
      if (!user || !user.propertyOwner.isPropertyOwner) {
        throw new Error('User is not a property owner');
      }

      // Create new property document
      const property = new Property({
        title: propertyData.title,
        description: propertyData.description,
        address: propertyData.address,
        propertyType: propertyData.propertyType,
        size: propertyData.size,
        rooms: propertyData.rooms,
        bedrooms: propertyData.bedrooms,
        bathrooms: propertyData.bathrooms,
        rent: propertyData.rent,
        features: propertyData.features,
        policies: propertyData.policies,
        owner: {
          userId: userId,
          contactPreference: 'email'
        },
        status: propertyData.status || 'draft',
        availability: {
          availableFrom: propertyData.availabilityDate ? new Date(propertyData.availabilityDate) : new Date(),
          viewingSchedule: 'by_appointment'
        },
        images: propertyData.images || [],
        applicationSettings: {
          requiresApplication: true,
          autoScreening: false,
          minimumTenantScore: 60,
          requiredDocuments: ['income_proof', 'employment_contract', 'id_document']
        }
      });

      // Save property to database
      const savedProperty = await property.save();

      // Add property ID to user's property list
      user.propertyOwner.properties.push(savedProperty._id);
      await user.save();

      loggers.app.info(`Property ${savedProperty._id} added successfully for user ${userId}`);

      return {
        success: true,
        propertyId: savedProperty._id,
        property: savedProperty,
        message: 'Property added successfully'
      };

    } catch (error) {
      loggers.app.error(`Error adding property for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Update property information
   * @private
   */
  async _updateProperty(userId, propertyData) {
    try {
      const { propertyId, ...updateData } = propertyData;
      
      // Find the property and verify ownership
      const property = await Property.findById(propertyId);
      if (!property) {
        throw new Error('Property not found');
      }
      
      if (property.owner.userId.toString() !== userId.toString()) {
        throw new Error('Unauthorized: You can only update your own properties');
      }
      
      // Update the property with new data
      Object.keys(updateData).forEach(key => {
        if (updateData[key] !== undefined) {
          if (typeof updateData[key] === 'object' && !Array.isArray(updateData[key]) && updateData[key] !== null) {
            // Handle nested objects (like address, rent, features, policies)
            property[key] = { ...property[key], ...updateData[key] };
          } else {
            property[key] = updateData[key];
          }
        }
      });
      
      // Save the updated property
      await property.save();
      
      return {
        success: true,
        propertyId: property._id,
        data: property,
        message: 'Property updated successfully'
      };
    } catch (error) {
      console.error('Error updating property:', error);
      throw error;
    }
  }

  /**
   * Remove property from portfolio
   * @private
   */
  async _removeProperty(userId, propertyId) {
    const user = await User.findById(userId);
    user.propertyOwner.properties = user.propertyOwner.properties.filter(id => id !== propertyId);
    await user.save();
    
    return {
      success: true,
      propertyId,
      message: 'Property removed successfully'
    };
  }

  /**
   * Activate property listing
   * @private
   */
  async _activateProperty(userId, propertyId) {
    try {
      loggers.app.info(`Activating property ${propertyId} for user ${userId}`);

      // 1. Find and validate property
      const property = await Property.findById(propertyId);
      if (!property) {
        throw new Error('Property not found');
      }

      if (property.owner.userId.toString() !== userId.toString()) {
        throw new Error('Unauthorized: Property does not belong to this user');
      }

      if (property.status === 'active') {
        return {
          success: true,
          propertyId,
          status: 'active',
          message: 'Property is already active'
        };
      }

      // 2. Validate property is ready for activation
      const validationResult = this._validatePropertyForActivation(property);
      if (!validationResult.isValid) {
        throw new Error(`Property validation failed: ${validationResult.errors.join(', ')}`);
      }

      // 3. Update property status
      property.status = 'active';
      if (!property.availability.availableFrom) {
        property.availability.availableFrom = new Date();
      }
      
      await property.save();

      // 4. Create public listing
      await this._createPublicListing(property);

      // 5. Log activation
      loggers.app.info(`Property ${propertyId} activated successfully`);

      return {
        success: true,
        propertyId,
        status: 'active',
        publishedAt: property.publishedAt,
        availableFrom: property.availability.availableFrom,
        message: 'Property activated successfully'
      };

    } catch (error) {
      loggers.app.error(`Error activating property ${propertyId}:`, error);
      throw error;
    }
  }

  /**
   * Deactivate property listing
   * @private
   */
  async _deactivateProperty(userId, propertyId) {
    try {
      loggers.app.info(`Deactivating property ${propertyId} for user ${userId}`);

      // 1. Find and validate property
      const property = await Property.findById(propertyId);
      if (!property) {
        throw new Error('Property not found');
      }

      if (property.owner.userId.toString() !== userId.toString()) {
        throw new Error('Unauthorized: Property does not belong to this user');
      }

      if (property.status === 'inactive') {
        return {
          success: true,
          propertyId,
          status: 'inactive',
          message: 'Property is already inactive'
        };
      }

      // 2. Update property status
      const previousStatus = property.status;
      property.status = 'inactive';
      await property.save();

      // 3. Remove from public listings if it was active
      if (previousStatus === 'active') {
        await this._removePublicListing(propertyId);
      }

      // 4. Log deactivation
      loggers.app.info(`Property ${propertyId} deactivated successfully`);

      return {
        success: true,
        propertyId,
        status: 'inactive',
        previousStatus,
        message: 'Property deactivated successfully'
      };

    } catch (error) {
      loggers.app.error(`Error deactivating property ${propertyId}:`, error);
      throw error;
    }
  }

  /**
   * Validate property is ready for activation
   * @private
   */
  _validatePropertyForActivation(property) {
    const errors = [];
    
    // Required basic information
    if (!property.title || property.title.trim().length < 5) {
      errors.push('Property title must be at least 5 characters long');
    }
    
    if (!property.description || property.description.trim().length < 20) {
      errors.push('Property description must be at least 20 characters long');
    }
    
    // Required address information
    if (!property.address?.street) {
      errors.push('Street address is required');
    }
    
    if (!property.address?.houseNumber) {
      errors.push('House number is required');
    }
    
    if (!property.address?.postalCode) {
      errors.push('Postal code is required');
    }
    
    if (!property.address?.city) {
      errors.push('City is required');
    }
    
    // Required property details
    if (!property.propertyType) {
      errors.push('Property type is required');
    }
    
    if (!property.size || property.size <= 0) {
      errors.push('Property size must be greater than 0');
    }
    
    if (!property.rooms || property.rooms <= 0) {
      errors.push('Number of rooms must be greater than 0');
    }
    
    if (!property.bedrooms || property.bedrooms < 0) {
      errors.push('Number of bedrooms is required');
    }
    
    if (!property.bathrooms || property.bathrooms <= 0) {
      errors.push('Number of bathrooms must be greater than 0');
    }
    
    // Required rental information
    if (!property.rent?.amount || property.rent.amount <= 0) {
      errors.push('Rental amount must be greater than 0');
    }
    
    if (!property.rent?.deposit || property.rent.deposit < 0) {
      errors.push('Security deposit is required');
    }
    
    // Recommended but not required
    const warnings = [];
    
    if (!property.images || property.images.length === 0) {
      warnings.push('Adding photos will help attract more tenants');
    }
    
    if (!property.features?.energyLabel) {
      warnings.push('Energy label helps tenants understand utility costs');
    }
    
    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Create public listing from property
   * @private
   */
  async _createPublicListing(property) {
    try {
      // Check if listing already exists
      const existingListing = await Listing.findOne({ 
        url: `/property/${property._id}`
      });

      // Format price as string (matching Listing schema)
      const formattedPrice = `€ ${property.rent.amount.toLocaleString('nl-NL')} per maand`;
      
      // Format size as string
      const formattedSize = property.size ? `${property.size} m²` : '';
      
      // Format rooms and bedrooms as strings
      const formattedRooms = property.rooms ? property.rooms.toString() : '';
      const formattedBedrooms = property.bedrooms ? property.bedrooms.toString() : '';

      if (existingListing) {
        // Update existing listing
        existingListing.title = property.title;
        existingListing.description = property.description;
        existingListing.price = formattedPrice;
        existingListing.location = `${property.address.city}, ${property.address.province || 'Netherlands'}`;
        existingListing.propertyType = property.propertyType;
        existingListing.rooms = formattedRooms;
        existingListing.bedrooms = formattedBedrooms;
        existingListing.size = formattedSize;
        existingListing.images = property.images?.map(img => img.url) || [];
        existingListing.interior = property.features?.interior || '';
        existingListing.timestamp = new Date();
        
        await existingListing.save();
        loggers.app.info(`Updated existing listing for property ${property._id}`);
        return existingListing;
      }

      // Create new listing
      const listing = new Listing({
        title: property.title,
        description: property.description,
        price: formattedPrice,
        location: `${property.address.city}, ${property.address.province || 'Netherlands'}`,
        url: `/property/${property._id}`,
        size: formattedSize,
        bedrooms: formattedBedrooms,
        rooms: formattedRooms,
        propertyType: property.propertyType,
        year: '', // Not available in property schema
        interior: property.features?.interior || '',
        source: 'property_owner',
        images: property.images?.map(img => img.url) || [],
        dateAdded: new Date(),
        timestamp: new Date()
      });

      await listing.save();
      loggers.app.info(`Created new listing for property ${property._id}`);
      return listing;

    } catch (error) {
      loggers.app.error(`Error creating public listing for property ${property._id}:`, error);
      throw new Error('Failed to create public listing');
    }
  }

  /**
   * Remove public listing
   * @private
   */
  async _removePublicListing(propertyId) {
    try {
      // Since the Listing schema doesn't have isActive field, we'll delete the listing
      const result = await Listing.deleteMany({
        url: `/property/${propertyId}`
      });

      loggers.app.info(`Removed ${result.deletedCount} listings for property ${propertyId}`);
      return result;

    } catch (error) {
      loggers.app.error(`Error removing public listing for property ${propertyId}:`, error);
      throw new Error('Failed to remove public listing');
    }
  }

  /**
   * Generate comprehensive report
   * @private
   */
  async _generateComprehensiveReport(property, applications) {
    return {
      summary: {
        totalApplications: applications.length,
        averageScore: 75,
        recommendedApplicants: 3
      },
      marketAnalysis: {
        averageRentInArea: '€2,400',
        competitivePosition: 'Above average',
        demandLevel: 'High'
      },
      recommendations: [
        'Consider applicants with scores above 70',
        'Schedule viewings for top 3 candidates',
        'Request additional documentation from medium-risk applicants'
      ]
    };
  }

  /**
   * Generate performance report
   * @private
   */
  async _generatePerformanceReport(property) {
    return {
      occupancyRate: 0.95,
      averageRentDuration: 18, // months
      maintenanceRequests: 3,
      tenantSatisfaction: 4.2, // out of 5
      financialPerformance: {
        monthlyRevenue: 2500,
        yearlyRevenue: 30000,
        expenses: 3600,
        netProfit: 26400
      }
    };
  }

  /**
   * Get property owner profile
   * Requirements: 10.1, 10.2
   */
  async getOwnerProfile(userId) {
    try {
      loggers.app.info(`Getting property owner profile for user ${userId}`);

      const user = await User.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      if (!user.propertyOwner.isPropertyOwner) {
        throw new Error('User is not a property owner');
      }

      // Build profile response
      const profile = {
        businessRegistration: user.propertyOwner.businessRegistration || '',
        companyName: user.profile.businessName || '',
        address: user.profile.businessAddress || '',
        phone: user.profile.businessPhone || user.profile.phoneNumber || '',
        website: user.profile.website || '',
        description: user.profile.description || '',
        verificationStatus: user.propertyOwner.verificationStatus || 'pending',
        notificationSettings: {
          email: user.notifications?.email?.newListings ?? true,
          push: user.notifications?.push?.newMatches ?? true,
          sms: user.notifications?.sms?.urgentAlerts ?? false,
        }
      };

      return profile;

    } catch (error) {
      loggers.app.error(`Error getting owner profile for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Update property owner profile
   * Requirements: 10.1, 10.2
   */
  async updateOwnerProfile(userId, profileData) {
    try {
      loggers.app.info(`Updating property owner profile for user ${userId}`);

      const user = await User.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      if (!user.propertyOwner.isPropertyOwner) {
        throw new Error('User is not a property owner');
      }

      // Update property owner specific fields
      if (profileData.businessRegistration) {
        user.propertyOwner.businessRegistration = profileData.businessRegistration;
      }

      // Update profile fields
      if (profileData.companyName) {
        user.profile.businessName = profileData.companyName;
      }
      if (profileData.address) {
        user.profile.businessAddress = profileData.address;
      }
      if (profileData.phone) {
        user.profile.businessPhone = profileData.phone;
        user.profile.phoneNumber = profileData.phone; // Also update main phone
      }
      if (profileData.website) {
        user.profile.website = profileData.website;
      }
      if (profileData.description) {
        user.profile.description = profileData.description;
      }

      // Update notification settings
      if (profileData.notificationSettings) {
        if (!user.notifications) {
          user.notifications = { email: {}, push: {}, sms: {} };
        }
        
        if (profileData.notificationSettings.email !== undefined) {
          user.notifications.email.newListings = profileData.notificationSettings.email;
          user.notifications.email.applicationUpdates = profileData.notificationSettings.email;
        }
        if (profileData.notificationSettings.push !== undefined) {
          user.notifications.push.newMatches = profileData.notificationSettings.push;
          user.notifications.push.messages = profileData.notificationSettings.push;
        }
        if (profileData.notificationSettings.sms !== undefined) {
          user.notifications.sms.urgentAlerts = profileData.notificationSettings.sms;
        }
      }

      await user.save();

      // Return updated profile
      return await this.getOwnerProfile(userId);

    } catch (error) {
      loggers.app.error(`Error updating owner profile for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Validate property for activation (public method)
   * Requirements: 10.3, 10.4
   */
  async validatePropertyForActivation(userId, propertyId) {
    try {
      loggers.app.info(`Validating property ${propertyId} for user ${userId}`);

      // Find and validate property
      const property = await Property.findById(propertyId);
      if (!property) {
        throw new Error('Property not found');
      }

      if (property.owner.userId.toString() !== userId.toString()) {
        throw new Error('Unauthorized: Property does not belong to this user');
      }

      // Run validation
      const validationResult = this._validatePropertyForActivation(property);

      loggers.app.info(`Property validation completed for ${propertyId}: ${validationResult.isValid ? 'Valid' : 'Invalid'}`);

      return {
        propertyId,
        propertyTitle: property.title,
        currentStatus: property.status,
        canActivate: validationResult.isValid,
        validation: validationResult,
        checkedAt: new Date()
      };

    } catch (error) {
      loggers.app.error(`Error validating property ${propertyId}:`, error);
      throw error;
    }
  }
}

module.exports = new PropertyOwnerService();