const request = require('supertest');
const express = require('express');
const mongoose = require('mongoose');
const jwt = require('jsonwebtoken');
const User = require('../../models/User');
const AutoApplicationSettings = require('../../models/AutoApplicationSettings');
const Document = require('../../models/Document');
const config = require('../../config/config');
const encryptionService = require('../../services/encryptionService');
const auditLogService = require('../../services/auditLogService');
const gdprComplianceService = require('../../services/gdprComplianceService');

// Create test app with all security middleware
const app = express();
app.use(express.json());

// Import all routes
app.use('/api/gdpr', require('../../routes/gdprCompliance'));

describe('Security and Compliance Integration Tests', () => {
  let testUser;
  let authToken;
  let testAutoAppSettings;

  beforeAll(async () => {
    await mongoose.connect(process.env.MONGO_URI || 'mongodb://localhost:27017/zakmakelaar-test');
  });

  afterAll(async () => {
    await mongoose.connection.dropDatabase();
    await mongoose.connection.close();
  });

  beforeEach(async () => {
    // Clear all collections
    await User.deleteMany({});
    await AutoApplicationSettings.deleteMany({});
    await Document.deleteMany({});

    // Create test user with GDPR consent
    testUser = new User({
      email: '<EMAIL>',
      password: 'hashedPassword',
      role: 'user',
      profile: {
        firstName: 'Security',
        lastName: 'Test',
        phoneNumber: '+31612345678'
      },
      gdprConsent: {
        consents: new Map([
          ['data_processing', { 
            granted: true, 
            timestamp: new Date(), 
            version: '1.0',
            ipAddress: '***********',
            userAgent: 'Test Browser'
          }]
        ]),
        consentHistory: [{
          type: 'data_processing',
          granted: true,
          timestamp: new Date(),
          ipAddress: '***********',
          userAgent: 'Test Browser',
          version: '1.0',
          method: 'web_form'
        }],
        lastUpdated: new Date(),
        privacyPolicyVersion: '1.0'
      },
      security: {
        loginAttempts: { count: 0 },
        suspiciousActivity: { flagged: false, resolved: false },
        sessionTokens: [],
        passwordHistory: []
      }
    });
    await testUser.save();

    // Generate auth token
    authToken = jwt.sign(
      { _id: testUser._id, email: testUser.email, role: testUser.role },
      config.jwtSecret,
      { expiresIn: '1h' }
    );

    // Create auto-application settings with encrypted data
    const personalInfo = {
      fullName: 'Security Test User',
      email: '<EMAIL>',
      phone: '+31612345678',
      monthlyIncome: 5000,
      guarantorInfo: {
        name: 'Test Guarantor',
        email: '<EMAIL>',
        phone: '+31687654321'
      }
    };

    testAutoAppSettings = new AutoApplicationSettings({
      userId: testUser._id,
      enabled: true,
      personalInfo: personalInfo, // Will be encrypted by pre-save middleware
      criteria: {
        maxPrice: 2000,
        minRooms: 2,
        maxRooms: 4
      }
    });
    await testAutoAppSettings.save();
  });

  describe('End-to-End Security Flow', () => {
    test('should handle complete security workflow', async () => {
      // 1. Record additional consent
      const consentResponse = await request(app)
        .post('/api/gdpr/consent')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          consentType: 'auto_application',
          granted: true,
          privacyPolicyVersion: '2.0'
        })
        .expect(200);

      expect(consentResponse.body.status).toBe('success');
      expect(consentResponse.body.data.consent.type).toBe('auto_application');

      // 2. Verify consent was recorded
      const consentStatus = await request(app)
        .get('/api/gdpr/consent')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(consentStatus.body.data.consentStatus.hasConsent).toBe(true);
      expect(consentStatus.body.data.consentStatus.consents).toHaveProperty('auto_application');

      // 3. Export user data
      const exportResponse = await request(app)
        .post('/api/gdpr/export')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ format: 'json', includeDocuments: false })
        .expect(200);

      expect(exportResponse.body.exportInfo.userId).toBe(testUser._id.toString());
      expect(exportResponse.body.exportInfo.dataCategories).toContain('personal_info');
      expect(exportResponse.body.exportInfo.dataCategories).toContain('auto_application_data');

      // 4. Get privacy transparency report
      const privacyResponse = await request(app)
        .get('/api/gdpr/privacy-report')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(privacyResponse.body.data.report.userId).toBe(testUser._id.toString());
      expect(privacyResponse.body.data.report.dataProcessing.purposes).toHaveLength(3);
      expect(privacyResponse.body.data.report.userRights.access.available).toBe(true);

      // 5. Get audit logs
      const auditResponse = await request(app)
        .get('/api/gdpr/audit-logs')
        .set('Authorization', `Bearer ${authToken}`)
        .query({ limit: 10 })
        .expect(200);

      expect(auditResponse.body.status).toBe('success');
      expect(Array.isArray(auditResponse.body.data.auditLogs)).toBe(true);
    });

    test('should handle data deletion workflow', async () => {
      // Update user's last login to recent time for deletion validation
      testUser.activity = { lastLogin: new Date() };
      await testUser.save();

      // Request data deletion
      const deletionResponse = await request(app)
        .post('/api/gdpr/delete')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          confirmDeletion: true,
          reason: 'user_request',
          keepAuditLogs: true,
          anonymizeInsteadOfDelete: false
        })
        .expect(200);

      expect(deletionResponse.body.status).toBe('success');
      expect(deletionResponse.body.data.deletionSummary.userId).toBe(testUser._id.toString());
      expect(deletionResponse.body.data.deletionSummary.deletedCategories).toContain('personal_info');

      // Verify user was actually deleted
      const deletedUser = await User.findById(testUser._id);
      expect(deletedUser).toBeNull();

      // Verify auto-application settings were deleted
      const deletedSettings = await AutoApplicationSettings.findOne({ userId: testUser._id });
      expect(deletedSettings).toBeNull();
    });

    test('should handle anonymization instead of deletion', async () => {
      // Update user's last login to recent time
      testUser.activity = { lastLogin: new Date() };
      await testUser.save();

      // Request data anonymization
      const anonymizationResponse = await request(app)
        .post('/api/gdpr/delete')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          confirmDeletion: true,
          reason: 'user_request',
          keepAuditLogs: true,
          anonymizeInsteadOfDelete: true
        })
        .expect(200);

      expect(anonymizationResponse.body.status).toBe('success');
      expect(anonymizationResponse.body.data.deletionSummary.deletedCategories).toContain('personal_info');

      // Verify user still exists but is anonymized
      const anonymizedUser = await User.findById(testUser._id);
      expect(anonymizedUser).toBeTruthy();
      expect(anonymizedUser.email).toBe(`deleted-user-${testUser._id}@anonymized.local`);
      expect(anonymizedUser.profile.firstName).toBe('ANONYMIZED');

      // Verify auto-application settings are anonymized
      const anonymizedSettings = await AutoApplicationSettings.findOne({ userId: testUser._id });
      expect(anonymizedSettings).toBeTruthy();
      expect(anonymizedSettings.enabled).toBe(false);
      
      // Personal info should be anonymized (will be decrypted by post-find middleware)
      expect(anonymizedSettings.personalInfo.fullName).toBe('ANONYMIZED');
    });
  });

  describe('Data Encryption Verification', () => {
    test('should encrypt and decrypt personal information correctly', async () => {
      // Retrieve the auto-application settings
      const settings = await AutoApplicationSettings.findOne({ userId: testUser._id });
      
      // The personal info should be decrypted by the post-find middleware
      expect(settings.personalInfo.fullName).toBe('Security Test User');
      expect(settings.personalInfo.email).toBe('<EMAIL>');
      expect(settings.personalInfo.guarantorInfo.name).toBe('Test Guarantor');

      // Test manual encryption/decryption
      const testData = 'sensitive information';
      const encrypted = encryptionService.encrypt(testData, testUser._id.toString());
      expect(encrypted).not.toBe(testData);
      
      const decrypted = encryptionService.decrypt(encrypted, testUser._id.toString());
      expect(decrypted).toBe(testData);
    });

    test('should fail decryption with wrong user ID', () => {
      const testData = 'sensitive information';
      const encrypted = encryptionService.encrypt(testData, testUser._id.toString());
      
      const wrongUserId = new mongoose.Types.ObjectId().toString();
      expect(() => {
        encryptionService.decrypt(encrypted, wrongUserId);
      }).toThrow('Failed to decrypt data');
    });

    test('should encrypt nested object fields correctly', () => {
      const personalInfo = {
        fullName: 'John Doe',
        email: '<EMAIL>',
        guarantorInfo: {
          name: 'Jane Doe',
          email: '<EMAIL>'
        }
      };

      const encrypted = encryptionService.encryptPersonalInfo(personalInfo, testUser._id.toString());
      expect(encrypted.fullName).not.toBe(personalInfo.fullName);
      expect(encrypted.guarantorInfo.name).not.toBe(personalInfo.guarantorInfo.name);

      const decrypted = encryptionService.decryptPersonalInfo(encrypted, testUser._id.toString());
      expect(decrypted.fullName).toBe(personalInfo.fullName);
      expect(decrypted.guarantorInfo.name).toBe(personalInfo.guarantorInfo.name);
    });
  });

  describe('Audit Logging Verification', () => {
    test('should log all security-relevant actions', async () => {
      // Perform various actions that should be logged
      await auditLogService.logSecurity(
        testUser._id.toString(),
        'login_success',
        { endpoint: '/api/test' },
        { ipAddress: '***********', userAgent: 'Test Browser' }
      );

      await auditLogService.logAutoApplication(
        testUser._id.toString(),
        'auto_application_enabled',
        { maxApplicationsPerDay: 5 },
        { ipAddress: '***********' }
      );

      await auditLogService.logPrivacy(
        testUser._id.toString(),
        'consent_given',
        { consentType: 'auto_application' },
        { ipAddress: '***********' }
      );

      // Retrieve audit logs
      const logs = await auditLogService.getUserAuditLogs(testUser._id.toString(), { limit: 10 });
      
      expect(logs.length).toBeGreaterThan(0);
      
      const securityLog = logs.find(log => log.action === 'login_success');
      expect(securityLog).toBeTruthy();
      expect(securityLog.category).toBe('security');

      const autoAppLog = logs.find(log => log.action === 'auto_application_enabled');
      expect(autoAppLog).toBeTruthy();
      expect(autoAppLog.category).toBe('auto_application');

      const privacyLog = logs.find(log => log.action === 'consent_given');
      expect(privacyLog).toBeTruthy();
      expect(privacyLog.category).toBe('privacy');
    });

    test('should generate compliance reports', async () => {
      // Create some test audit data
      await auditLogService.logAutoApplication(
        testUser._id.toString(),
        'application_submitted',
        { success: true },
        {}
      );

      await auditLogService.logSecurity(
        testUser._id.toString(),
        'login_failed',
        { reason: 'invalid_password' },
        {}
      );

      const report = await auditLogService.generateComplianceReport({
        userId: testUser._id.toString()
      });

      expect(report.userId).toEqual(testUser._id);
      expect(report.summary.totalEvents).toBeGreaterThan(0);
      expect(report.categories).toHaveProperty('auto_application');
      expect(report.categories).toHaveProperty('security');
    });
  });

  describe('User Security Features', () => {
    test('should handle GDPR consent in user model', async () => {
      // Test consent recording
      await testUser.recordGdprConsent('marketing', true, {
        ipAddress: '***********',
        userAgent: 'Test Browser',
        privacyPolicyVersion: '2.0'
      });

      expect(testUser.hasConsent('marketing')).toBe(true);
      expect(testUser.hasConsent('analytics')).toBe(false);
      expect(testUser.hasGdprConsent).toBe(true);
      expect(testUser.hasAutoApplicationConsent).toBe(false); // Not granted yet

      // Grant auto-application consent
      await testUser.recordGdprConsent('auto_application', true);
      expect(testUser.hasAutoApplicationConsent).toBe(true);
    });

    test('should handle security features in user model', async () => {
      // Test failed login recording
      await testUser.recordFailedLogin('***********');
      expect(testUser.security.loginAttempts.count).toBe(1);

      // Test multiple failed logins leading to account lock
      for (let i = 0; i < 4; i++) {
        await testUser.recordFailedLogin('***********');
      }
      
      expect(testUser.isAccountLocked).toBe(true);

      // Test successful login resets attempts
      await testUser.resetLoginAttempts();
      expect(testUser.security.loginAttempts.count).toBe(0);
      expect(testUser.isAccountLocked).toBe(false);
    });

    test('should handle suspicious activity flagging', async () => {
      expect(testUser.isSuspiciousActivityFlagged).toBe(false);

      await testUser.flagSuspiciousActivity('Multiple rapid requests');
      expect(testUser.isSuspiciousActivityFlagged).toBe(true);
      expect(testUser.security.suspiciousActivity.reason).toBe('Multiple rapid requests');

      await testUser.resolveSuspiciousActivity();
      expect(testUser.isSuspiciousActivityFlagged).toBe(false);
    });

    test('should handle session token management', async () => {
      const token = 'test-session-token';
      const expiresAt = new Date(Date.now() + 60 * 60 * 1000); // 1 hour

      await testUser.addSessionToken(token, expiresAt, {
        ipAddress: '***********',
        userAgent: 'Test Browser'
      });

      expect(testUser.activeSessions).toHaveLength(1);
      expect(testUser.activeSessions[0].token).toBe(token);

      await testUser.revokeSessionToken(token);
      expect(testUser.activeSessions).toHaveLength(0);
    });

    test('should handle password history', async () => {
      const passwordHash1 = 'hash1';
      const passwordHash2 = 'hash2';

      await testUser.addPasswordToHistory(passwordHash1);
      expect(testUser.wasPasswordRecentlyUsed(passwordHash1)).toBe(true);
      expect(testUser.wasPasswordRecentlyUsed(passwordHash2)).toBe(false);

      await testUser.addPasswordToHistory(passwordHash2);
      expect(testUser.wasPasswordRecentlyUsed(passwordHash2)).toBe(true);
    });
  });

  describe('Auto-Application Settings Security', () => {
    test('should provide safe object representation', async () => {
      const settings = await AutoApplicationSettings.findOne({ userId: testUser._id });
      const safeObject = settings.toSafeObject();

      // Should mask sensitive information
      expect(safeObject.personalInfo.fullName).toBe('***');
      expect(safeObject.personalInfo.email).toMatch(/se\*\*\*@example\.com/);
      expect(safeObject.personalInfo.phone).toMatch(/\+31\*\*\*78/);
      
      // Should include non-sensitive information
      expect(safeObject.personalInfo.hasGuarantor).toBeDefined();
      expect(safeObject.personalInfo.numberOfOccupants).toBeDefined();
    });

    test('should handle secure personal info updates', async () => {
      const settings = await AutoApplicationSettings.findOne({ userId: testUser._id });
      
      const newPersonalInfo = {
        fullName: 'Updated Name',
        email: '<EMAIL>',
        phone: '+31600000000'
      };

      await settings.updatePersonalInfo(newPersonalInfo);
      
      // Retrieve updated settings
      const updatedSettings = await AutoApplicationSettings.findOne({ userId: testUser._id });
      expect(updatedSettings.personalInfo.fullName).toBe('Updated Name');
      expect(updatedSettings.personalInfo.email).toBe('<EMAIL>');
    });
  });

  describe('Error Handling and Edge Cases', () => {
    test('should handle invalid consent types gracefully', async () => {
      const response = await request(app)
        .post('/api/gdpr/consent')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          consentType: 'invalid_consent_type',
          granted: true
        })
        .expect(400);

      expect(response.body.status).toBe('error');
      expect(response.body.message).toBe('Invalid consent type');
    });

    test('should handle missing authentication', async () => {
      await request(app)
        .get('/api/gdpr/consent')
        .expect(401);
    });

    test('should handle data deletion without recent authentication', async () => {
      // Set last login to more than 5 minutes ago
      testUser.activity = { lastLogin: new Date(Date.now() - 10 * 60 * 1000) };
      await testUser.save();

      const response = await request(app)
        .post('/api/gdpr/delete')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          confirmDeletion: true,
          reason: 'user_request'
        })
        .expect(403);

      expect(response.body.code).toBe('RECENT_AUTH_REQUIRED');
    });

    test('should handle encryption service errors', () => {
      expect(() => {
        encryptionService.encrypt('test', null);
      }).toThrow();

      expect(() => {
        encryptionService.decrypt('invalid-data', testUser._id.toString());
      }).toThrow('Failed to decrypt data');
    });
  });

  describe('Performance and Scalability', () => {
    test('should handle multiple concurrent operations', async () => {
      const promises = [];
      
      // Create multiple concurrent consent recordings
      for (let i = 0; i < 10; i++) {
        promises.push(
          request(app)
            .post('/api/gdpr/consent')
            .set('Authorization', `Bearer ${authToken}`)
            .send({
              consentType: 'analytics',
              granted: i % 2 === 0 // Alternate between true/false
            })
        );
      }

      const responses = await Promise.all(promises);
      
      // All requests should complete successfully
      responses.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.body.status).toBe('success');
      });
    });

    test('should handle large data exports efficiently', async () => {
      // Create additional test data
      for (let i = 0; i < 5; i++) {
        await new Document({
          userId: testUser._id,
          filename: `test-doc-${i}.pdf`,
          originalName: `test-document-${i}.pdf`,
          type: 'income_proof',
          size: 1024 * (i + 1),
          mimeType: 'application/pdf',
          encryptedPath: `/path/to/encrypted/file-${i}`,
          encryptionKey: `test-key-${i}`
        }).save();
      }

      const startTime = Date.now();
      
      const response = await request(app)
        .post('/api/gdpr/export')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ format: 'json', includeDocuments: true })
        .expect(200);

      const endTime = Date.now();
      const processingTime = endTime - startTime;

      expect(response.body.exportInfo.dataCategories).toContain('documents');
      expect(response.body.documents.documentList).toHaveLength(5);
      
      // Should complete within reasonable time (less than 5 seconds)
      expect(processingTime).toBeLessThan(5000);
    });
  });
});