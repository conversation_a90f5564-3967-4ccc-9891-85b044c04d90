import React, { useState, useEffect, useCallback } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  RefreshControl,
  ActivityIndicator,
  Alert,
  Dimensions,
} from 'react-native';
import { useRouter } from 'expo-router';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import Animated, { FadeInUp, FadeInDown } from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';

import { useAuthStore } from '../store/authStore';
import {
  autoApplicationService,
  AutoApplicationStats,
  ApplicationQueue,
  ApplicationResult,
  ScraperIntegrationStats,
} from '../services/autoApplicationService';

const { width } = Dimensions.get('window');

// Theme colors
const THEME = {
  primary: '#4361ee',
  secondary: '#7209b7',
  accent: '#f72585',
  dark: '#0a0a18',
  light: '#ffffff',
  gray: '#6b7280',
  lightGray: '#f3f4f6',
  success: '#10b981',
  warning: '#f59e0b',
  danger: '#ef4444',
};

// Header Component
const Header = () => {
  const router = useRouter();
  const insets = useSafeAreaInsets();

  return (
    <LinearGradient
      colors={[THEME.primary, THEME.secondary]}
      style={[styles.header, { paddingTop: Math.max(insets.top, 16) }]}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
    >
      <Animated.View
        style={styles.headerContent}
        entering={FadeInUp.duration(600)}
      >
        <TouchableOpacity
          onPress={() => {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            router.back();
          }}
          style={styles.backButton}
          activeOpacity={0.8}
        >
          <View style={styles.backButtonInner}>
            <Ionicons name="chevron-back" size={24} color={THEME.primary} />
          </View>
        </TouchableOpacity>

        <View style={styles.headerCenter}>
          <View style={styles.logoContainer}>
            <Text style={styles.logoText}>ZM</Text>
          </View>
          <Text style={styles.headerTitle}>Auto-Application Dashboard</Text>
        </View>

        <TouchableOpacity
          onPress={() => router.push('/auto-application-settings')}
          style={styles.settingsButton}
          activeOpacity={0.8}
        >
          <Ionicons name="settings-outline" size={24} color="#ffffff" />
        </TouchableOpacity>
      </Animated.View>
    </LinearGradient>
  );
};

// Stats Card Component
const StatsCard = ({
  title,
  value,
  subtitle,
  icon,
  color,
  trend,
}: {
  title: string;
  value: string | number;
  subtitle?: string;
  icon: string;
  color: string;
  trend?: { value: number; isPositive: boolean };
}) => (
  <Animated.View
    style={[styles.statsCard, { borderLeftColor: color }]}
    entering={FadeInUp.duration(600)}
  >
    <View style={styles.statsCardHeader}>
      <View style={[styles.statsIcon, { backgroundColor: color }]}>
        <Ionicons name={icon as any} size={20} color="#ffffff" />
      </View>
      <View style={styles.statsContent}>
        <Text style={styles.statsValue}>{value}</Text>
        <Text style={styles.statsTitle}>{title}</Text>
        {subtitle && <Text style={styles.statsSubtitle}>{subtitle}</Text>}
      </View>
      {trend && (
        <View style={styles.trendContainer}>
          <Ionicons
            name={trend.isPositive ? "trending-up" : "trending-down"}
            size={16}
            color={trend.isPositive ? THEME.success : THEME.danger}
          />
          <Text style={[
            styles.trendText,
            { color: trend.isPositive ? THEME.success : THEME.danger }
          ]}>
            {Math.abs(trend.value)}%
          </Text>
        </View>
      )}
    </View>
  </Animated.View>
);

// Queue Item Component
const QueueItem = ({
  item,
  onRemove,
  onUpdatePriority,
}: {
  item: ApplicationQueue;
  onRemove: (id: string) => void;
  onUpdatePriority: (id: string, priority: number) => void;
}) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return THEME.warning;
      case 'processing': return THEME.primary;
      case 'completed': return THEME.success;
      case 'failed': return THEME.danger;
      case 'cancelled': return THEME.gray;
      default: return THEME.gray;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return 'time-outline';
      case 'processing': return 'sync-outline';
      case 'completed': return 'checkmark-circle-outline';
      case 'failed': return 'close-circle-outline';
      case 'cancelled': return 'ban-outline';
      default: return 'help-circle-outline';
    }
  };

  return (
    <Animated.View
      style={styles.queueItem}
      entering={FadeInDown.duration(400)}
    >
      <View style={styles.queueItemHeader}>
        <View style={styles.queueItemTitle}>
          <Text style={styles.queueItemName} numberOfLines={1}>
            {item.listingTitle}
          </Text>
          <View style={styles.queueItemMeta}>
            <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
              <Ionicons name={getStatusIcon(item.status) as any} size={12} color="#ffffff" />
              <Text style={styles.statusText}>{item.status}</Text>
            </View>
            <Text style={styles.priorityText}>Priority: {item.priority}</Text>
          </View>
        </View>

        <View style={styles.queueItemActions}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => onUpdatePriority(item._id, Math.min(10, item.priority + 1))}
          >
            <Ionicons name="arrow-up" size={16} color={THEME.primary} />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => onUpdatePriority(item._id, Math.max(1, item.priority - 1))}
          >
            <Ionicons name="arrow-down" size={16} color={THEME.primary} />
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.actionButton, styles.removeButton]}
            onPress={() => onRemove(item._id)}
          >
            <Ionicons name="trash-outline" size={16} color={THEME.danger} />
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.queueItemDetails}>
        <Text style={styles.queueItemDetail}>
          Scheduled: {new Date(item.scheduledFor).toLocaleString()}
        </Text>
        <Text style={styles.queueItemDetail}>
          Attempts: {item.attempts}/{item.maxAttempts}
        </Text>

      </View>

      {item.errorMessage && (
        <View style={styles.errorContainer}>
          <Ionicons name="warning" size={14} color={THEME.danger} />
          <Text style={styles.errorText}>{item.errorMessage}</Text>
        </View>
      )}
    </Animated.View>
  );
};

// Recent Result Component
const RecentResult = ({ result }: { result: ApplicationResult }) => {
  const statusColor = result.status === 'success' ? THEME.success :
    result.status === 'failed' ? THEME.danger : THEME.gray;

  return (
    <Animated.View
      style={styles.resultItem}
      entering={FadeInDown.duration(400)}
    >
      <View style={styles.resultHeader}>
        <View style={[styles.resultStatus, { backgroundColor: statusColor }]} />
        <View style={styles.resultContent}>
          <Text style={styles.resultTitle} numberOfLines={1}>
            {result.listingTitle}
          </Text>
          <Text style={styles.resultTime}>
            {autoApplicationService.formatApplicationResult(result).timeAgo}
          </Text>
        </View>
        <View style={styles.resultMeta}>
          <Text style={styles.resultScore}>
            {result.metrics?.successProbability ? Math.round(result.metrics.successProbability) : 0}%
          </Text>
          <Text style={styles.resultResponseTime}>
            {result.response?.responseTime || 0}ms
          </Text>
        </View>
      </View>

      {result.landlordResponse && (
        <View style={styles.landlordResponse}>
          <Text style={styles.landlordResponseText}>
            Landlord: {result.landlordResponse.status}
          </Text>
          {result.landlordResponse.message && (
            <Text style={styles.landlordResponseMessage}>
              "{result.landlordResponse.message}"
            </Text>
          )}
        </View>
      )}
    </Animated.View>
  );
};

export default function AutoApplicationDashboardScreen() {
  const router = useRouter();
  const { user } = useAuthStore();

  // State
  const [stats, setStats] = useState<AutoApplicationStats | null>(null);
  const [queue, setQueue] = useState<ApplicationQueue[]>([]);
  const [recentResults, setRecentResults] = useState<ApplicationResult[]>([]);
  const [scraperStats, setScraperStats] = useState<ScraperIntegrationStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState<'overview' | 'queue' | 'results'>('overview');
  const [pausingQueue, setPausingQueue] = useState(false);
  const [resumingQueue, setResumingQueue] = useState(false);

  // Load data
  const loadData = useCallback(async (isRefresh = false) => {
    if (!user?.id) return;

    try {
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }

      const [statsResponse, queueResponse, resultsResponse, scraperResponse] = await Promise.allSettled([
        autoApplicationService.getStats(user.id),
        autoApplicationService.getQueue(user.id),
        autoApplicationService.getResults(user.id, 1, 10),
        autoApplicationService.getScraperIntegrationStats(),
      ]);

      if (statsResponse.status === 'fulfilled' && statsResponse.value.success) {
        setStats(statsResponse.value.data || null);
      }

      if (queueResponse.status === 'fulfilled' && queueResponse.value.success) {
        setQueue(queueResponse.value.data || []);
      }

      if (resultsResponse.status === 'fulfilled' && resultsResponse.value.success) {
        setRecentResults(resultsResponse.value.data?.results || []);
      }

      if (scraperResponse.status === 'fulfilled' && scraperResponse.value.success) {
        setScraperStats(scraperResponse.value.data || null);
      }
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      Alert.alert('Error', 'Failed to load dashboard data');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [user]);

  useEffect(() => {
    loadData();
  }, [loadData]);

  // Queue management
  const handleRemoveFromQueue = async (queueId: string) => {
    try {
      const response = await autoApplicationService.removeFromQueue(queueId);
      if (response.success) {
        setQueue(queue.filter(item => item._id !== queueId));
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      }
    } catch (error) {
      console.error('Error removing from queue:', error);
      Alert.alert('Error', 'Failed to remove item from queue');
    }
  };

  const handleUpdatePriority = async (queueId: string, priority: number) => {
    try {
      const response = await autoApplicationService.updateQueuePriority(queueId, priority);
      if (response.success && response.data) {
        setQueue(queue.map(item =>
          item._id === queueId ? { ...item, priority } : item
        ));
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      }
    } catch (error) {
      console.error('Error updating priority:', error);
      Alert.alert('Error', 'Failed to update priority');
    }
  };

  const handlePauseQueue = async () => {
    if (!user?.id || pausingQueue) return;

    try {
      setPausingQueue(true);
      const response = await autoApplicationService.pauseQueue(user.id);
      if (response.success) {
        const successMessage = response.message || 'Auto-application queue paused';
        Alert.alert('Success', successMessage);
        loadData();
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      } else {
        throw new Error(response.message || 'Failed to pause queue');
      }
    } catch (error: any) {
      console.error('Error pausing queue:', error);
      const errorMessage = error.response?.data?.message || error.message || 'Failed to pause queue';
      Alert.alert('Error', errorMessage);
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    } finally {
      setPausingQueue(false);
    }
  };

  const handleResumeQueue = async () => {
    if (!user?.id || resumingQueue) return;

    try {
      setResumingQueue(true);
      const response = await autoApplicationService.resumeQueue(user.id);
      if (response.success) {
        const successMessage = response.message || 'Auto-application queue resumed';
        Alert.alert('Success', successMessage);
        loadData();
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      } else {
        throw new Error(response.message || 'Failed to resume queue');
      }
    } catch (error: any) {
      console.error('Error resuming queue:', error);
      const errorMessage = error.response?.data?.message || error.message || 'Failed to resume queue';
      Alert.alert('Error', errorMessage);
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    } finally {
      setResumingQueue(false);
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <Header />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={THEME.primary} />
          <Text style={styles.loadingText}>Loading dashboard...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <Header />

      {/* Tab Navigation */}
      <Animated.View
        style={styles.tabContainer}
        entering={FadeInUp.duration(600).delay(200)}
      >
        {[
          { key: 'overview', label: 'Overview', icon: 'analytics-outline' },
          { key: 'queue', label: 'Queue', icon: 'list-outline' },
          { key: 'results', label: 'Results', icon: 'checkmark-done-outline' },
        ].map((tab) => (
          <TouchableOpacity
            key={tab.key}
            style={[styles.tab, activeTab === tab.key && styles.activeTab]}
            onPress={() => {
              setActiveTab(tab.key as any);
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            }}
          >
            <Ionicons
              name={tab.icon as any}
              size={20}
              color={activeTab === tab.key ? THEME.primary : THEME.gray}
            />
            <Text style={[
              styles.tabText,
              activeTab === tab.key && styles.activeTabText
            ]}>
              {tab.label}
            </Text>
          </TouchableOpacity>
        ))}
      </Animated.View>

      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={() => loadData(true)}
            colors={[THEME.primary]}
          />
        }
      >
        {activeTab === 'overview' && (
          <>
            {/* Stats Overview */}
            <Animated.View
              style={styles.statsContainer}
              entering={FadeInUp.duration(600).delay(400)}
            >
              <Text style={styles.sectionTitle}>Statistics</Text>
              <View style={styles.statsGrid}>
                <StatsCard
                  title="Total Applications"
                  value={stats?.totalApplications || 0}
                  icon="paper-plane-outline"
                  color={THEME.primary}
                />
                <StatsCard
                  title="Success Rate"
                  value={`${stats?.successRate || 0}%`}
                  icon="checkmark-circle-outline"
                  color={THEME.success}
                />
                <StatsCard
                  title="This Week"
                  value={stats?.applicationsThisWeek || 0}
                  subtitle="applications"
                  icon="calendar-outline"
                  color={THEME.accent}
                />
                <StatsCard
                  title="Avg Response"
                  value={`${stats?.averageResponseTime || 0}ms`}
                  icon="time-outline"
                  color={THEME.warning}
                />
              </View>
            </Animated.View>

            {/* Scraper Integration Stats */}
            {scraperStats && (
              <Animated.View
                style={styles.scraperStatsContainer}
                entering={FadeInUp.duration(600).delay(600)}
              >
                <Text style={styles.sectionTitle}>Scraper Integration</Text>
                <View style={styles.scraperStatsContent}>
                  <View style={styles.scraperStatItem}>
                    <Text style={styles.scraperStatValue}>{scraperStats.autoApplicationsTriggered}</Text>
                    <Text style={styles.scraperStatLabel}>Auto-Apps Triggered</Text>
                  </View>
                  <View style={styles.scraperStatItem}>
                    <Text style={styles.scraperStatValue}>{scraperStats.processedListingsCount}</Text>
                    <Text style={styles.scraperStatLabel}>Listings Processed</Text>
                  </View>
                  <View style={styles.scraperStatItem}>
                    <Text style={styles.scraperStatValue}>{scraperStats.duplicatesSkipped}</Text>
                    <Text style={styles.scraperStatLabel}>Duplicates Skipped</Text>
                  </View>
                </View>
              </Animated.View>
            )}

            {/* Quick Actions */}
            <Animated.View
              style={styles.quickActionsContainer}
              entering={FadeInUp.duration(600).delay(800)}
            >
              <Text style={styles.sectionTitle}>Quick Actions</Text>
              <View style={styles.quickActionsGrid}>
                <TouchableOpacity
                  style={[styles.quickAction, pausingQueue && styles.quickActionDisabled]}
                  onPress={handlePauseQueue}
                  disabled={pausingQueue}
                >
                  {pausingQueue ? (
                    <ActivityIndicator size="small" color={THEME.warning} />
                  ) : (
                    <Ionicons name="pause-circle-outline" size={24} color={THEME.warning} />
                  )}
                  <Text style={styles.quickActionText}>
                    {pausingQueue ? 'Pausing...' : 'Pause Queue'}
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.quickAction, resumingQueue && styles.quickActionDisabled]}
                  onPress={handleResumeQueue}
                  disabled={resumingQueue}
                >
                  {resumingQueue ? (
                    <ActivityIndicator size="small" color={THEME.success} />
                  ) : (
                    <Ionicons name="play-circle-outline" size={24} color={THEME.success} />
                  )}
                  <Text style={styles.quickActionText}>
                    {resumingQueue ? 'Resuming...' : 'Resume Queue'}
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.quickAction}
                  onPress={() => router.push('/auto-application-settings')}
                >
                  <Ionicons name="settings-outline" size={24} color={THEME.primary} />
                  <Text style={styles.quickActionText}>Settings</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.quickAction}
                  onPress={() => loadData()}
                >
                  <Ionicons name="refresh-outline" size={24} color={THEME.gray} />
                  <Text style={styles.quickActionText}>Refresh</Text>
                </TouchableOpacity>
              </View>
            </Animated.View>
          </>
        )}

        {activeTab === 'queue' && (
          <Animated.View
            style={styles.queueContainer}
            entering={FadeInUp.duration(600).delay(400)}
          >
            <View style={styles.queueHeader}>
              <Text style={styles.sectionTitle}>Application Queue ({queue.length})</Text>
              <TouchableOpacity
                style={styles.queueAction}
                onPress={() => loadData()}
              >
                <Ionicons name="refresh-outline" size={20} color={THEME.primary} />
              </TouchableOpacity>
            </View>

            {queue.length === 0 ? (
              <View style={styles.emptyState}>
                <Ionicons name="list-outline" size={48} color={THEME.gray} />
                <Text style={styles.emptyStateText}>No applications in queue</Text>
                <Text style={styles.emptyStateSubtext}>
                  Applications will appear here when they match your criteria
                </Text>
              </View>
            ) : (
              <View style={styles.queueList}>
                {queue.map((item) => (
                  <QueueItem
                    key={item._id}
                    item={item}
                    onRemove={handleRemoveFromQueue}
                    onUpdatePriority={handleUpdatePriority}
                  />
                ))}
              </View>
            )}
          </Animated.View>
        )}

        {activeTab === 'results' && (
          <Animated.View
            style={styles.resultsContainer}
            entering={FadeInUp.duration(600).delay(400)}
          >
            <Text style={styles.sectionTitle}>Recent Results</Text>

            {recentResults.length === 0 ? (
              <View style={styles.emptyState}>
                <Ionicons name="checkmark-done-outline" size={48} color={THEME.gray} />
                <Text style={styles.emptyStateText}>No results yet</Text>
                <Text style={styles.emptyStateSubtext}>
                  Application results will appear here
                </Text>
              </View>
            ) : (
              <View style={styles.resultsList}>
                {recentResults.map((result) => (
                  <RecentResult key={result._id} result={result} />
                ))}
              </View>
            )}
          </Animated.View>
        )}

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: THEME.lightGray,
  },
  header: {
    paddingBottom: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  backButton: {
    marginRight: 16,
  },
  backButtonInner: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerCenter: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  logoContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  logoText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#ffffff',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#ffffff',
  },
  settingsButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#ffffff',
    marginHorizontal: 20,
    marginTop: -10,
    borderRadius: 12,
    padding: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    gap: 6,
  },
  activeTab: {
    backgroundColor: THEME.lightGray,
  },
  tabText: {
    fontSize: 14,
    fontWeight: '500',
    color: THEME.gray,
  },
  activeTabText: {
    color: THEME.primary,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: THEME.gray,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: THEME.dark,
    marginBottom: 16,
  },
  statsContainer: {
    marginBottom: 24,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  statsCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    width: (width - 52) / 2,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statsCardHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  statsIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  statsContent: {
    flex: 1,
  },
  statsValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: THEME.dark,
    marginBottom: 4,
  },
  statsTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: THEME.gray,
  },
  statsSubtitle: {
    fontSize: 12,
    color: THEME.gray,
    marginTop: 2,
  },
  trendContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 2,
  },
  trendText: {
    fontSize: 12,
    fontWeight: '600',
  },
  scraperStatsContainer: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 20,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  scraperStatsContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  scraperStatItem: {
    alignItems: 'center',
  },
  scraperStatValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: THEME.primary,
    marginBottom: 4,
  },
  scraperStatLabel: {
    fontSize: 12,
    color: THEME.gray,
    textAlign: 'center',
  },
  quickActionsContainer: {
    marginBottom: 24,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  quickAction: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    width: (width - 52) / 2,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  quickActionText: {
    fontSize: 14,
    fontWeight: '500',
    color: THEME.dark,
    marginTop: 8,
  },
  quickActionDisabled: {
    opacity: 0.6,
  },
  queueContainer: {
    marginBottom: 24,
  },
  queueHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  queueAction: {
    padding: 8,
  },
  queueList: {
    gap: 12,
  },
  queueItem: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  queueItemHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  queueItemTitle: {
    flex: 1,
    marginRight: 12,
  },
  queueItemName: {
    fontSize: 16,
    fontWeight: '600',
    color: THEME.dark,
    marginBottom: 4,
  },
  queueItemMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#ffffff',
  },
  priorityText: {
    fontSize: 12,
    color: THEME.gray,
  },
  queueItemActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: THEME.lightGray,
    alignItems: 'center',
    justifyContent: 'center',
  },
  removeButton: {
    backgroundColor: '#fee2e2',
  },
  queueItemDetails: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
    marginBottom: 8,
  },
  queueItemDetail: {
    fontSize: 12,
    color: THEME.gray,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fee2e2',
    padding: 8,
    borderRadius: 6,
    gap: 6,
  },
  errorText: {
    fontSize: 12,
    color: THEME.danger,
    flex: 1,
  },
  resultsContainer: {
    marginBottom: 24,
  },
  resultsList: {
    gap: 12,
  },
  resultItem: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  resultHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  resultStatus: {
    width: 4,
    height: 40,
    borderRadius: 2,
    marginRight: 12,
  },
  resultContent: {
    flex: 1,
  },
  resultTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: THEME.dark,
    marginBottom: 4,
  },
  resultTime: {
    fontSize: 12,
    color: THEME.gray,
  },
  resultMeta: {
    alignItems: 'flex-end',
  },
  resultScore: {
    fontSize: 14,
    fontWeight: '600',
    color: THEME.primary,
    marginBottom: 2,
  },
  resultResponseTime: {
    fontSize: 12,
    color: THEME.gray,
  },
  landlordResponse: {
    marginTop: 12,
    padding: 12,
    backgroundColor: THEME.lightGray,
    borderRadius: 8,
  },
  landlordResponseText: {
    fontSize: 14,
    fontWeight: '500',
    color: THEME.dark,
    marginBottom: 4,
  },
  landlordResponseMessage: {
    fontSize: 12,
    color: THEME.gray,
    fontStyle: 'italic',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: '600',
    color: THEME.gray,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: THEME.gray,
    textAlign: 'center',
    paddingHorizontal: 40,
  },
  bottomSpacing: {
    height: 40,
  },
});