/**
 * Transformation Worker Thread
 * 
 * This worker thread handles transformation processing in a separate thread
 * to enable concurrent processing of multiple properties.
 */

const { isMainThread, parentPort, workerData } = require('worker_threads');
const { SchemaTransformer } = require('./schemaTransformer');
const { FieldMappingRegistry } = require('./fieldMappingRegistry');
const { MappingConfigLoader } = require('./mappingConfigLoader');
const { SchemaError, ErrorTypes } = require('../utils/schemaErrors');
const path = require('path');

// This script should only run in a worker thread
if (isMainThread) {
  throw new Error('This script is designed to run as a worker thread');
}

/**
 * Initialize the transformation pipeline
 * @returns {Promise<SchemaTransformer>} Initialized schema transformer
 */
async function initializeTransformer() {
  // Create field mapping registry
  const fieldMappingRegistry = new FieldMappingRegistry();
  
  // Create mapping config loader
  const mappingLoader = new MappingConfigLoader(fieldMappingRegistry);
  
  // Load mappings from configuration files
  const mappingsDir = path.join(__dirname, '../config/mappings');
  await mappingLoader.loadFromDirectory(mappingsDir);
  
  // Create schema transformer
  return new SchemaTransformer(fieldMappingRegistry);
}

/**
 * Process items in the worker thread
 * @param {Array<Object>} items - Items to process
 * @param {string} source - Source identifier
 * @param {Object} options - Processing options
 * @returns {Promise<Array<Object>>} Processed items
 */
async function processItems(items, source, options) {
  const transformer = await initializeTransformer();
  const results = [];
  
  for (const item of items) {
    try {
      const result = await transformer.transform(item, source, options);
      results.push(result);
    } catch (error) {
      if (options.continueOnError !== false) {
        results.push(transformer._createErrorResult(
          error instanceof SchemaError ? error : new SchemaError(
            ErrorTypes.TRANSFORMATION_ERROR,
            error.message,
            { source, originalError: error }
          ),
          item
        ));
      } else {
        throw error;
      }
    }
  }
  
  return results;
}

// Extract data from workerData
const { items, source, options, workerId } = workerData;

// Process the items and send the results back to the main thread
processItems(items, source, options)
  .then(results => {
    parentPort.postMessage(results);
  })
  .catch(error => {
    // Convert error to a serializable format
    const serializedError = {
      message: error.message,
      stack: error.stack,
      type: error instanceof SchemaError ? error.type : 'Error',
      context: error instanceof SchemaError ? error.context : {}
    };
    
    parentPort.postMessage({
      error: serializedError,
      workerId
    });
  });