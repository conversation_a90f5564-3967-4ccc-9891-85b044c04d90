/**
 * Test Component for Improved Auto-Application Settings
 * This component tests the enhanced settings functionality
 */

import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import AutoApplicationSettingsScreen from '../zakmakelaar-frontend/app/auto-application-settings';
import { autoApplicationService } from '../zakmakelaar-frontend/services/autoApplicationService';

// Mock dependencies
jest.mock('../zakmakelaar-frontend/services/autoApplicationService');
jest.mock('../zakmakelaar-frontend/store/authStore', () => ({
  useAuthStore: () => ({
    user: { 
      id: 'test-user-123', 
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Doe'
    }
  })
}));

jest.mock('expo-router', () => ({
  useRouter: () => ({
    back: jest.fn(),
    push: jest.fn()
  })
}));

jest.mock('react-native-safe-area-context', () => ({
  useSafeAreaInsets: () => ({ top: 44, bottom: 34, left: 0, right: 0 })
}));

// Mock settings data
const mockSettings = {
  userId: 'test-user-123',
  enabled: true,
  settings: {
    maxApplicationsPerDay: 5,
    applicationTemplate: 'professional',
    autoSubmit: false,
    requireManualReview: true,
    notificationPreferences: {
      immediate: true,
      daily: true,
      weekly: false,
    },
    language: 'english',
  },
  criteria: {
    maxPrice: 2000,
    minRooms: 2,
    maxRooms: 4,
    propertyTypes: ['apartment', 'house'],
    locations: ['Amsterdam', 'Utrecht'],
    excludeKeywords: ['student', 'shared'],
    includeKeywords: ['balcony', 'parking'],
    minSize: 50,
    maxSize: 150,
    furnished: true,
    petsAllowed: false,
  },
  personalInfo: {
    fullName: 'John Doe',
    email: '<EMAIL>',
    phone: '+31612345678',
  },
  documents: [],
  statistics: {
    totalApplications: 10,
    successfulApplications: 7,
    pendingApplications: 2,
    rejectedApplications: 1,
    averageResponseTime: 1200,
  },
  status: {
    isActive: true,
    currentQueue: 3,
    dailyApplicationsUsed: 2,
    weeklyApplicationsUsed: 8,
    monthlyApplicationsUsed: 25,
    lastResetDate: new Date(),
  },
  createdAt: new Date(),
  updatedAt: new Date(),
};

describe('Improved AutoApplicationSettingsScreen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    (autoApplicationService.getSettings as jest.Mock).mockResolvedValue({
      success: true,
      data: mockSettings
    });
    
    (autoApplicationService.updateSettings as jest.Mock).mockResolvedValue({
      success: true,
      data: mockSettings
    });
  });

  it('renders settings with improved input fields', async () => {
    const { getByDisplayValue, getByText } = render(<AutoApplicationSettingsScreen />);
    
    await waitFor(() => {
      // Check if room range inputs are rendered
      expect(getByDisplayValue('2')).toBeTruthy(); // Min rooms
      expect(getByDisplayValue('4')).toBeTruthy(); // Max rooms
      
      // Check if price input is rendered with currency symbol
      expect(getByDisplayValue('2000')).toBeTruthy(); // Max price
      expect(getByText('€')).toBeTruthy(); // Currency symbol
      
      // Check if locations input is rendered
      expect(getByDisplayValue('Amsterdam, Utrecht')).toBeTruthy();
    });
  });

  it('allows editing room range with number controls', async () => {
    const { getByDisplayValue, getAllByTestId } = render(<AutoApplicationSettingsScreen />);
    
    await waitFor(() => {
      const minRoomsInput = getByDisplayValue('2');
      
      // Test direct text input
      fireEvent.changeText(minRoomsInput, '3');
      expect(minRoomsInput.props.value).toBe('3');
    });
  });

  it('validates room range constraints', async () => {
    const { getByDisplayValue } = render(<AutoApplicationSettingsScreen />);
    
    await waitFor(() => {
      const minRoomsInput = getByDisplayValue('2');
      const maxRoomsInput = getByDisplayValue('4');
      
      // Try to set min rooms higher than max rooms
      fireEvent.changeText(minRoomsInput, '5');
      
      // The component should prevent this or adjust accordingly
      // This tests the validation logic
    });
  });

  it('allows editing price with currency formatting', async () => {
    const { getByDisplayValue } = render(<AutoApplicationSettingsScreen />);
    
    await waitFor(() => {
      const priceInput = getByDisplayValue('2000');
      
      // Test price input
      fireEvent.changeText(priceInput, '2500');
      expect(priceInput.props.value).toBe('2500');
    });
  });

  it('allows editing locations as comma-separated values', async () => {
    const { getByDisplayValue } = render(<AutoApplicationSettingsScreen />);
    
    await waitFor(() => {
      const locationsInput = getByDisplayValue('Amsterdam, Utrecht');
      
      // Test adding a new location
      fireEvent.changeText(locationsInput, 'Amsterdam, Utrecht, Rotterdam');
      expect(locationsInput.props.value).toBe('Amsterdam, Utrecht, Rotterdam');
    });
  });

  it('allows editing size range with unit labels', async () => {
    const { getByDisplayValue, getByText } = render(<AutoApplicationSettingsScreen />);
    
    await waitFor(() => {
      // Check if size inputs are rendered with m² labels
      expect(getByDisplayValue('50')).toBeTruthy(); // Min size
      expect(getByDisplayValue('150')).toBeTruthy(); // Max size
      expect(getByText('m²')).toBeTruthy(); // Unit label
    });
  });

  it('shows validation errors for invalid settings', async () => {
    const { getByText, getByDisplayValue } = render(<AutoApplicationSettingsScreen />);
    
    await waitFor(() => {
      const minRoomsInput = getByDisplayValue('2');
      const maxRoomsInput = getByDisplayValue('4');
      
      // Set invalid range (min > max)
      fireEvent.changeText(minRoomsInput, '5');
      fireEvent.changeText(maxRoomsInput, '3');
      
      // Try to save
      const saveButton = getByText('Save Settings');
      fireEvent.press(saveButton);
      
      // Should show validation error
      // This would be tested with Alert.alert mock
    });
  });

  it('allows toggling property preferences', async () => {
    const { getByText } = render(<AutoApplicationSettingsScreen />);
    
    await waitFor(() => {
      // Test furnished toggle
      const furnishedToggle = getByText('Furnished Properties').parent;
      // This would test the Switch component interaction
    });
  });

  it('supports keyword editing with proper formatting', async () => {
    const { getByDisplayValue } = render(<AutoApplicationSettingsScreen />);
    
    await waitFor(() => {
      const excludeKeywordsInput = getByDisplayValue('student, shared');
      const includeKeywordsInput = getByDisplayValue('balcony, parking');
      
      // Test keyword editing
      fireEvent.changeText(excludeKeywordsInput, 'student, shared, temporary');
      fireEvent.changeText(includeKeywordsInput, 'balcony, parking, elevator');
      
      expect(excludeKeywordsInput.props.value).toBe('student, shared, temporary');
      expect(includeKeywordsInput.props.value).toBe('balcony, parking, elevator');
    });
  });

  it('shows helpful section notes and information', async () => {
    const { getByText } = render(<AutoApplicationSettingsScreen />);
    
    await waitFor(() => {
      expect(getByText('Configure your preferences to automatically apply to matching properties')).toBeTruthy();
    });
  });

  it('allows resetting settings to defaults', async () => {
    const { getByText } = render(<AutoApplicationSettingsScreen />);
    
    await waitFor(() => {
      const resetButton = getByText('Reset to Defaults');
      fireEvent.press(resetButton);
      
      // Should show confirmation dialog
      // This would be tested with Alert.alert mock
    });
  });
});

// Integration test for the complete settings flow
describe('Settings Integration Flow', () => {
  it('completes full settings configuration flow', async () => {
    const { getByDisplayValue, getByText } = render(<AutoApplicationSettingsScreen />);
    
    await waitFor(() => {
      // 1. Configure price range
      const priceInput = getByDisplayValue('2000');
      fireEvent.changeText(priceInput, '2500');
      
      // 2. Configure room range
      const minRoomsInput = getByDisplayValue('2');
      const maxRoomsInput = getByDisplayValue('4');
      fireEvent.changeText(minRoomsInput, '1');
      fireEvent.changeText(maxRoomsInput, '5');
      
      // 3. Configure locations
      const locationsInput = getByDisplayValue('Amsterdam, Utrecht');
      fireEvent.changeText(locationsInput, 'Amsterdam, Utrecht, Rotterdam, The Hague');
      
      // 4. Configure size range
      const minSizeInput = getByDisplayValue('50');
      const maxSizeInput = getByDisplayValue('150');
      fireEvent.changeText(minSizeInput, '40');
      fireEvent.changeText(maxSizeInput, '200');
      
      // 5. Save settings
      const saveButton = getByText('Save Settings');
      fireEvent.press(saveButton);
      
      // Should call the update service
      expect(autoApplicationService.updateSettings).toHaveBeenCalledWith(
        'test-user-123',
        expect.objectContaining({
          criteria: expect.objectContaining({
            maxPrice: 2500,
            minRooms: 1,
            maxRooms: 5,
            locations: ['Amsterdam', 'Utrecht', 'Rotterdam', 'The Hague'],
            minSize: 40,
            maxSize: 200,
          })
        })
      );
    });
  });
});

export default AutoApplicationSettingsScreen;