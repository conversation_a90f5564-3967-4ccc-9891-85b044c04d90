const ApplicationMonitor = require('../../services/applicationMonitor');
const ApplicationResult = require('../../models/ApplicationResult');
const ApplicationQueue = require('../../models/ApplicationQueue');
const AutoApplicationSettings = require('../../models/AutoApplicationSettings');
const User = require('../../models/User');
const logger = require('../../services/logger');

// Mock dependencies
jest.mock('../../models/ApplicationResult');
jest.mock('../../models/ApplicationQueue');
jest.mock('../../models/AutoApplicationSettings');
jest.mock('../../models/User');
jest.mock('../../services/logger', () => ({
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn()
}));
jest.mock('../../services/alertService', () => ({
  sendAlerts: jest.fn()
}));

describe('ApplicationMonitor', () => {
  let monitor;
  let mockApplicationResult;
  let mockUser;
  let mockSettings;

  beforeEach(() => {
    monitor = new ApplicationMonitor();
    
    // Reset all mocks
    jest.clearAllMocks();
    
    // Mock data
    mockApplicationResult = {
      _id: 'app123',
      userId: 'user123',
      listingId: 'listing123',
      status: 'pending',
      submittedAt: new Date(),
      response: { success: false },
      metrics: { processingTime: 5000 },
      listingSnapshot: { title: 'Test Property' },
      save: jest.fn().mockResolvedValue(true)
    };

    mockUser = {
      _id: 'user123',
      email: '<EMAIL>',
      profile: { phone: '+1234567890' }
    };

    mockSettings = {
      userId: 'user123',
      settings: {
        notificationPreferences: {
          immediate: true,
          daily: false,
          weekly: true,
          email: true,
          sms: false,
          push: true
        }
      }
    };
  });

  describe('trackApplication', () => {
    beforeEach(() => {
      ApplicationResult.findById.mockResolvedValue(mockApplicationResult);
    });

    it('should track application status change successfully', async () => {
      const result = await monitor.trackApplication('app123', 'submitted', {
        response: { success: true },
        metrics: { processingTime: 3000 }
      });

      expect(ApplicationResult.findById).toHaveBeenCalledWith('app123');
      expect(mockApplicationResult.save).toHaveBeenCalled();
      expect(mockApplicationResult.status).toBe('submitted');
      expect(result).toBe(mockApplicationResult);
    });

    it('should throw error if application not found', async () => {
      ApplicationResult.findById.mockResolvedValue(null);

      await expect(monitor.trackApplication('nonexistent', 'submitted'))
        .rejects.toThrow('Application nonexistent not found');
    });

    it('should emit tracking event', async () => {
      const emitSpy = jest.spyOn(monitor, 'emit');
      
      await monitor.trackApplication('app123', 'submitted');

      expect(emitSpy).toHaveBeenCalledWith('applicationStatusChanged', 
        expect.objectContaining({
          applicationId: 'app123',
          userId: 'user123',
          newStatus: 'submitted'
        })
      );
    });

    it('should queue notification', async () => {
      const queueSpy = jest.spyOn(monitor, 'queueNotification');
      
      await monitor.trackApplication('app123', 'submitted');

      expect(queueSpy).toHaveBeenCalledWith('user123', 'status_change', 
        expect.objectContaining({
          applicationId: 'app123',
          status: 'submitted'
        })
      );
    });
  });

  describe('getSuccessRates', () => {
    const mockAggregateResult = [{
      total: 100,
      submitted: 80,
      successful: 60,
      failed: 20,
      blocked: 5,
      captchaRequired: 3,
      landlordResponses: 40,
      acceptances: 15,
      viewingInvites: 25,
      avgProcessingTime: 4500,
      avgSuccessProbability: 75
    }];

    beforeEach(() => {
      ApplicationResult.aggregate.mockResolvedValue(mockAggregateResult);
    });

    it('should calculate success rates correctly', async () => {
      const result = await monitor.getSuccessRates('user123', 30);

      expect(result).toEqual(expect.objectContaining({
        timeframe: 30,
        total: 100,
        submitted: 80,
        successful: 60,
        submissionRate: 80,
        successRate: 75,
        responseRate: expect.closeTo(66.67, 1),
        acceptanceRate: 37.5,
        viewingRate: expect.closeTo(41.67, 1),
        avgProcessingTime: 4500,
        avgSuccessProbability: 75,
        errorRate: 20,
        blockingRate: 5,
        captchaRate: 3
      }));
    });

    it('should handle empty results gracefully', async () => {
      ApplicationResult.aggregate.mockResolvedValue([]);

      const result = await monitor.getSuccessRates('user123', 30);

      expect(result.total).toBe(0);
      expect(result.successRate).toBe(0);
      expect(result.submissionRate).toBe(0);
    });

    it('should use cache for repeated requests', async () => {
      // First call
      await monitor.getSuccessRates('user123', 30);
      // Second call
      await monitor.getSuccessRates('user123', 30);

      expect(ApplicationResult.aggregate).toHaveBeenCalledTimes(1);
    });

    it('should build correct aggregation pipeline for user-specific query', async () => {
      await monitor.getSuccessRates('user123', 30);

      const aggregateCall = ApplicationResult.aggregate.mock.calls[0][0];
      const matchStage = aggregateCall[0].$match;
      
      expect(matchStage.userId).toBe('user123');
      expect(matchStage.submittedAt).toEqual(expect.objectContaining({
        $gte: expect.any(Date)
      }));
    });

    it('should build correct aggregation pipeline for system-wide query', async () => {
      await monitor.getSuccessRates(null, 30);

      const aggregateCall = ApplicationResult.aggregate.mock.calls[0][0];
      const matchStage = aggregateCall[0].$match;
      
      expect(matchStage.userId).toBeUndefined();
    });
  });

  describe('detectPatterns', () => {
    beforeEach(() => {
      // Mock pattern analysis methods
      monitor.analyzeTimePatterns = jest.fn().mockResolvedValue({
        bestHours: [{ _id: { hour: 14 }, successRate: 85 }],
        bestDays: [{ _id: { dayOfWeek: 2 }, successRate: 80 }]
      });
      
      monitor.analyzeLocationPatterns = jest.fn().mockResolvedValue([
        { location: 'Amsterdam', applications: 50, successRate: 75 }
      ]);
      
      monitor.analyzePropertyTypePatterns = jest.fn().mockResolvedValue([
        { propertyType: 'apartment', applications: 30, successRate: 70 }
      ]);
      
      monitor.analyzePricePatterns = jest.fn().mockResolvedValue([
        { priceRange: '1000-1500', applications: 25, successRate: 80 }
      ]);
      
      monitor.analyzeTemplatePatterns = jest.fn().mockResolvedValue([
        { template: 'professional', applications: 40, successRate: 85 }
      ]);
      
      monitor.analyzeMarketConditionPatterns = jest.fn().mockResolvedValue([
        { condition: 'moderate_demand', applications: 60, successRate: 75 }
      ]);
    });

    it('should detect patterns successfully', async () => {
      const result = await monitor.detectPatterns('user123', 30);

      expect(result).toEqual(expect.objectContaining({
        timeframe: 30,
        timePatterns: expect.any(Object),
        locationPatterns: expect.any(Array),
        propertyTypePatterns: expect.any(Array),
        pricePatterns: expect.any(Array),
        templatePatterns: expect.any(Array),
        marketConditionPatterns: expect.any(Array),
        recommendations: expect.any(Array),
        confidence: expect.any(String)
      }));
    });

    it('should call all pattern analysis methods', async () => {
      await monitor.detectPatterns('user123', 30);

      expect(monitor.analyzeTimePatterns).toHaveBeenCalled();
      expect(monitor.analyzeLocationPatterns).toHaveBeenCalled();
      expect(monitor.analyzePropertyTypePatterns).toHaveBeenCalled();
      expect(monitor.analyzePricePatterns).toHaveBeenCalled();
      expect(monitor.analyzeTemplatePatterns).toHaveBeenCalled();
      expect(monitor.analyzeMarketConditionPatterns).toHaveBeenCalled();
    });

    it('should generate recommendations based on patterns', async () => {
      const result = await monitor.detectPatterns('user123', 30);

      expect(result.recommendations).toEqual(expect.arrayContaining([
        expect.objectContaining({
          type: expect.any(String),
          priority: expect.any(String),
          message: expect.any(String)
        })
      ]));
    });
  });

  describe('generateReports', () => {
    beforeEach(() => {
      monitor.getSuccessRates = jest.fn().mockResolvedValue({
        total: 100,
        successRate: 75,
        acceptanceRate: 30,
        avgProcessingTime: 4500
      });
      
      monitor.detectPatterns = jest.fn().mockResolvedValue({
        locationPatterns: [{ location: 'Amsterdam', successRate: 80 }]
      });
      
      monitor.getPerformanceMetrics = jest.fn().mockResolvedValue({
        avgProcessingTime: 4500,
        blockingRate: 5
      });
      
      monitor.getErrorAnalysis = jest.fn().mockResolvedValue({
        commonErrors: [{ category: 'network', percentage: 40 }]
      });
    });

    it('should generate comprehensive report', async () => {
      const result = await monitor.generateReports('user123', 'weekly');

      expect(result).toEqual(expect.objectContaining({
        reportType: 'weekly',
        timeframe: 7,
        generatedAt: expect.any(Date),
        userId: 'user123',
        summary: expect.objectContaining({
          totalApplications: 100,
          successRate: 75,
          acceptanceRate: 30,
          avgProcessingTime: 4500,
          topPerformingLocation: 'Amsterdam',
          mostCommonError: 'network'
        }),
        successRates: expect.any(Object),
        patterns: expect.any(Object),
        performance: expect.any(Object),
        errors: expect.any(Object),
        insights: expect.any(Array),
        actionItems: expect.any(Array)
      }));
    });

    it('should call all required methods', async () => {
      await monitor.generateReports('user123', 'weekly');

      expect(monitor.getSuccessRates).toHaveBeenCalledWith('user123', 7);
      expect(monitor.detectPatterns).toHaveBeenCalledWith('user123', 7);
      expect(monitor.getPerformanceMetrics).toHaveBeenCalledWith('user123', 7);
      expect(monitor.getErrorAnalysis).toHaveBeenCalledWith('user123', 7);
    });

    it('should handle different report types', async () => {
      await monitor.generateReports('user123', 'daily');
      expect(monitor.getSuccessRates).toHaveBeenCalledWith('user123', 1);

      await monitor.generateReports('user123', 'monthly');
      expect(monitor.getSuccessRates).toHaveBeenCalledWith('user123', 30);
    });
  });

  describe('sendNotifications', () => {
    beforeEach(() => {
      User.findById.mockResolvedValue(mockUser);
      AutoApplicationSettings.findOne.mockResolvedValue(mockSettings);
      monitor.deliverNotification = jest.fn().mockResolvedValue();
    });

    it('should send notification when user preferences allow', async () => {
      await monitor.sendNotifications('user123', 'status_change', {
        applicationId: 'app123',
        status: 'submitted'
      });

      expect(monitor.deliverNotification).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: 'user123',
          eventType: 'status_change',
          channels: ['email', 'push']
        })
      );
    });

    it('should not send notification when user preferences disallow', async () => {
      const settingsWithoutImmediate = {
        ...mockSettings,
        settings: {
          notificationPreferences: {
            immediate: false,
            email: true
          }
        }
      };
      AutoApplicationSettings.findOne.mockResolvedValue(settingsWithoutImmediate);

      await monitor.sendNotifications('user123', 'status_change', {});

      expect(monitor.deliverNotification).not.toHaveBeenCalled();
    });

    it('should handle missing user gracefully', async () => {
      User.findById.mockResolvedValue(null);

      await expect(monitor.sendNotifications('nonexistent', 'status_change', {}))
        .resolves.not.toThrow();
      
      expect(monitor.deliverNotification).not.toHaveBeenCalled();
    });

    it('should handle missing settings gracefully', async () => {
      AutoApplicationSettings.findOne.mockResolvedValue(null);

      await expect(monitor.sendNotifications('user123', 'status_change', {}))
        .resolves.not.toThrow();
      
      expect(monitor.deliverNotification).not.toHaveBeenCalled();
    });
  });

  describe('getPerformanceMetrics', () => {
    const mockPerformanceData = [{
      avgProcessingTime: 4500,
      minProcessingTime: 2000,
      maxProcessingTime: 8000,
      avgFormDetectionTime: 1000,
      avgFormFillingTime: 2000,
      avgSubmissionTime: 1500,
      avgNetworkLatency: 200,
      avgPageLoadTime: 3000,
      totalApplications: 100,
      complexForms: 20,
      captchaEncounters: 5,
      blockingIncidents: 3
    }];

    beforeEach(() => {
      ApplicationResult.aggregate.mockResolvedValue(mockPerformanceData);
    });

    it('should calculate performance metrics correctly', async () => {
      const result = await monitor.getPerformanceMetrics('user123', 30);

      expect(result).toEqual(expect.objectContaining({
        timeframe: 30,
        totalApplications: 100,
        avgProcessingTime: 4500,
        minProcessingTime: 2000,
        maxProcessingTime: 8000,
        avgFormDetectionTime: 1000,
        avgFormFillingTime: 2000,
        avgSubmissionTime: 1500,
        avgNetworkLatency: 200,
        avgPageLoadTime: 3000,
        complexFormRate: 20,
        captchaRate: 5,
        blockingRate: 3,
        processingGrade: expect.any(String),
        reliabilityGrade: expect.any(String),
        efficiencyGrade: expect.any(String)
      }));
    });

    it('should handle empty results gracefully', async () => {
      ApplicationResult.aggregate.mockResolvedValue([]);

      const result = await monitor.getPerformanceMetrics('user123', 30);

      expect(result.totalApplications).toBe(0);
      expect(result.avgProcessingTime).toBe(0);
    });
  });

  describe('getErrorAnalysis', () => {
    const mockErrorData = [
      {
        _id: 'network',
        count: 15,
        messages: ['Connection timeout', 'DNS error'],
        retryableCount: 12
      },
      {
        _id: 'form',
        count: 8,
        messages: ['Field not found', 'Validation error'],
        retryableCount: 5
      }
    ];

    beforeEach(() => {
      ApplicationResult.aggregate.mockResolvedValue(mockErrorData);
    });

    it('should analyze errors correctly', async () => {
      const result = await monitor.getErrorAnalysis('user123', 7);

      expect(result).toEqual(expect.objectContaining({
        timeframe: 7,
        totalErrors: 23,
        commonErrors: expect.arrayContaining([
          expect.objectContaining({
            category: 'network',
            count: 15,
            percentage: expect.closeTo(65.22, 1),
            retryablePercentage: 80,
            commonMessages: expect.arrayContaining(['Connection timeout', 'DNS error'])
          }),
          expect.objectContaining({
            category: 'form',
            count: 8,
            percentage: expect.closeTo(34.78, 1),
            retryablePercentage: 62.5
          })
        ]),
        recommendations: expect.any(Array)
      }));
    });

    it('should generate error recommendations', async () => {
      const result = await monitor.getErrorAnalysis('user123', 7);

      expect(result.recommendations).toEqual(expect.arrayContaining([
        expect.objectContaining({
          category: 'network',
          message: expect.stringContaining('retry logic')
        }),
        expect.objectContaining({
          category: 'form',
          message: expect.stringContaining('form detection')
        })
      ]));
    });
  });

  describe('Event System', () => {
    it('should register and emit events correctly', () => {
      const listener = jest.fn();
      monitor.on('test_event', listener);

      monitor.emit('test_event', { data: 'test' });

      expect(listener).toHaveBeenCalledWith({ data: 'test' });
    });

    it('should handle multiple listeners', () => {
      const listener1 = jest.fn();
      const listener2 = jest.fn();
      
      monitor.on('test_event', listener1);
      monitor.on('test_event', listener2);

      monitor.emit('test_event', { data: 'test' });

      expect(listener1).toHaveBeenCalled();
      expect(listener2).toHaveBeenCalled();
    });

    it('should remove listeners correctly', () => {
      const listener = jest.fn();
      monitor.on('test_event', listener);
      monitor.off('test_event', listener);

      monitor.emit('test_event', { data: 'test' });

      expect(listener).not.toHaveBeenCalled();
    });

    it('should handle listener errors gracefully', () => {
      const errorListener = jest.fn().mockImplementation(() => {
        throw new Error('Listener error');
      });
      const goodListener = jest.fn();

      monitor.on('test_event', errorListener);
      monitor.on('test_event', goodListener);

      expect(() => monitor.emit('test_event', {})).not.toThrow();
      expect(goodListener).toHaveBeenCalled();
    });
  });

  describe('Cache Management', () => {
    it('should cache and retrieve data correctly', () => {
      const testData = { test: 'data' };
      monitor.setCache('test_key', testData);

      const retrieved = monitor.getFromCache('test_key');
      expect(retrieved).toEqual(testData);
    });

    it('should return null for expired cache', async () => {
      const testData = { test: 'data' };
      monitor.cacheTimeout = 100; // 100ms timeout
      monitor.setCache('test_key', testData);

      // Wait for cache to expire
      await new Promise(resolve => setTimeout(resolve, 150));

      const retrieved = monitor.getFromCache('test_key');
      expect(retrieved).toBeNull();
    });

    it('should clear user-specific cache', () => {
      monitor.setCache('user123_test', { data: 'test' });
      monitor.setCache('user456_test', { data: 'test' });
      monitor.setCache('system_test', { data: 'test' });

      monitor.clearUserCache('user123');

      expect(monitor.getFromCache('user123_test')).toBeNull();
      expect(monitor.getFromCache('user456_test')).not.toBeNull();
      expect(monitor.getFromCache('system_test')).not.toBeNull();
    });

    it('should clear all cache', () => {
      monitor.setCache('key1', { data: 'test1' });
      monitor.setCache('key2', { data: 'test2' });

      monitor.clearCache();

      expect(monitor.getFromCache('key1')).toBeNull();
      expect(monitor.getFromCache('key2')).toBeNull();
    });
  });

  describe('Notification Queue', () => {
    beforeEach(() => {
      monitor.sendNotifications = jest.fn().mockResolvedValue();
    });

    it('should queue and process notifications', async () => {
      await monitor.queueNotification('user123', 'status_change', { test: 'data' });

      // Wait for queue processing
      await new Promise(resolve => setTimeout(resolve, 50));

      expect(monitor.sendNotifications).toHaveBeenCalledWith(
        'user123',
        'status_change',
        { test: 'data' }
      );
    });

    it('should process multiple notifications in queue', async () => {
      await monitor.queueNotification('user123', 'event1', {});
      await monitor.queueNotification('user456', 'event2', {});

      // Wait for queue processing
      await new Promise(resolve => setTimeout(resolve, 200));

      expect(monitor.sendNotifications).toHaveBeenCalledTimes(2);
    });

    it('should handle queue processing errors gracefully', async () => {
      monitor.sendNotifications.mockRejectedValue(new Error('Notification error'));

      await expect(monitor.queueNotification('user123', 'status_change', {}))
        .resolves.not.toThrow();
    });
  });

  describe('Performance Grading', () => {
    it('should calculate processing grade correctly', () => {
      expect(monitor.calculatePerformanceGrade(2 * 60 * 1000)).toBe('A'); // 2 minutes
      expect(monitor.calculatePerformanceGrade(4 * 60 * 1000)).toBe('B'); // 4 minutes
      expect(monitor.calculatePerformanceGrade(8 * 60 * 1000)).toBe('C'); // 8 minutes
      expect(monitor.calculatePerformanceGrade(15 * 60 * 1000)).toBe('D'); // 15 minutes
    });

    it('should calculate reliability grade correctly', () => {
      expect(monitor.calculateReliabilityGrade(2, 100)).toBe('A'); // 2% blocking
      expect(monitor.calculateReliabilityGrade(8, 100)).toBe('B'); // 8% blocking
      expect(monitor.calculateReliabilityGrade(15, 100)).toBe('C'); // 15% blocking
      expect(monitor.calculateReliabilityGrade(25, 100)).toBe('D'); // 25% blocking
    });

    it('should calculate efficiency grade correctly', () => {
      expect(monitor.calculateEfficiencyGrade(15000, 10000)).toBe('A'); // 25 seconds total
      expect(monitor.calculateEfficiencyGrade(30000, 20000)).toBe('B'); // 50 seconds total
      expect(monitor.calculateEfficiencyGrade(60000, 40000)).toBe('C'); // 100 seconds total
      expect(monitor.calculateEfficiencyGrade(120000, 80000)).toBe('D'); // 200 seconds total
    });
  });

  describe('Notification Formatting', () => {
    it('should format email notifications correctly', () => {
      const result = monitor.formatEmailNotification('status_change', {
        listingTitle: 'Test Property',
        status: 'submitted'
      });

      expect(result).toEqual({
        subject: 'Application Status Update: Test Property',
        body: 'Your application status has changed to: submitted'
      });
    });

    it('should format SMS notifications correctly', () => {
      const result = monitor.formatSMSNotification('status_change', {
        listingTitle: 'Test Property',
        status: 'submitted'
      });

      expect(result).toBe('Application update: submitted for Test Property');
    });

    it('should format push notifications correctly', () => {
      const result = monitor.formatPushNotification('status_change', {
        listingTitle: 'Test Property',
        status: 'submitted',
        applicationId: 'app123'
      });

      expect(result).toEqual({
        title: 'Application Status Update',
        body: 'Test Property: submitted',
        data: { applicationId: 'app123' }
      });
    });
  });

  describe('Cleanup', () => {
    it('should cleanup expired cache entries', async () => {
      monitor.cacheTimeout = 100; // 100ms timeout
      monitor.setCache('test_key', { data: 'test' });

      // Wait for cache to expire
      await new Promise(resolve => setTimeout(resolve, 150));

      await monitor.cleanup();

      expect(monitor.metricsCache.size).toBe(0);
    });

    it('should not remove non-expired cache entries', async () => {
      monitor.setCache('test_key', { data: 'test' });

      await monitor.cleanup();

      expect(monitor.getFromCache('test_key')).not.toBeNull();
    });
  });
});