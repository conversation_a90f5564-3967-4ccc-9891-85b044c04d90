import { apiService, ApiResponse } from './api';
import { Listing } from './listingsService';
import { User, UserPreferences } from './authService';

// AI Service types
export interface MatchingRequest {
  userProfile: User;
  preferences: UserPreferences;
  listings?: Listing[];
  maxResults?: number;
}

export interface MatchingResult {
  listing: Listing;
  score: number;
  reasons: string[];
  pros: string[];
  cons: string[];
}

export interface MatchingResponse {
  matches: MatchingResult[];
  totalAnalyzed: number;
  averageScore: number;
  recommendations: string[];
}

export interface ContractAnalysisRequest {
  contractText?: string;
  contractUrl?: string;
  contractFile?: File | Blob;
  analysisType?: 'basic' | 'detailed' | 'legal';
}

export interface ContractAnalysisResult {
  summary: string;
  keyTerms: {
    rentAmount: string;
    deposit: string;
    duration: string;
    noticePeriod: string;
    utilities: string;
  };
  risks: {
    level: 'low' | 'medium' | 'high';
    issues: string[];
    recommendations: string[];
  };
  legalCompliance: {
    compliant: boolean;
    violations: string[];
    suggestions: string[];
  };
  score: number;
  confidence: number;
}

export interface ApplicationGenerationRequest {
  listing: Listing;
  userProfile: User;
  template?: 'professional' | 'personal' | 'creative' | 'student' | 'expat';
  customMessage?: string;
  includeDocuments?: boolean;
}

export interface ApplicationGenerationResult {
  message: string;
  subject: string;
  template: string;
  personalizedElements: string[];
  tips: string[];
  generatedAt: string;
}

export interface MarketAnalysisRequest {
  location: string;
  propertyType?: string;
  priceRange?: {
    min: number;
    max: number;
  };
  timeframe?: '1month' | '3months' | '6months' | '1year';
}

export interface MarketAnalysisResult {
  location: string;
  averagePrice: number;
  priceRange: {
    min: number;
    max: number;
  };
  marketTrend: 'rising' | 'stable' | 'declining';
  pricePrediction: string;
  demandLevel: 'low' | 'medium' | 'high';
  keyInsights: string[];
  recommendations: string[];
  confidenceScore: number;
  dataPoints: number;
}

export interface SummarizationRequest {
  text: string;
  maxLength?: number;
  style?: 'brief' | 'detailed' | 'bullet-points';
}

export interface SummarizationResult {
  summary: string;
  keyPoints: string[];
  originalLength: number;
  summaryLength: number;
  compressionRatio: number;
}

export interface TranslationRequest {
  text: string;
  targetLanguage: 'en' | 'nl' | 'de' | 'fr' | 'es';
  sourceLanguage?: string;
}

export interface TranslationResult {
  translatedText: string;
  sourceLanguage: string;
  targetLanguage: string;
  confidence: number;
}

class AIService {
  /**
   * Get AI-powered property matches for user
   */
  async getPropertyMatches(request: MatchingRequest): Promise<ApiResponse<MatchingResponse>> {
    try {
      return await apiService.post<MatchingResponse>('/ai/match', request);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Analyze rental contract
   */
  async analyzeContract(request: ContractAnalysisRequest): Promise<ApiResponse<ContractAnalysisResult>> {
    try {
      // Handle file upload if present
      if (request.contractFile) {
        const formData = new FormData();
        formData.append('contract', request.contractFile);
        if (request.analysisType) {
          formData.append('analysisType', request.analysisType);
        }

        // Use different endpoint for file upload
        const response = await fetch(`${apiService['client'].defaults.baseURL}/ai/contract-analysis/upload`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${await apiService.getAuthToken()}`,
          },
          body: formData,
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return await response.json();
      } else {
        // Text or URL analysis
        return await apiService.post<ContractAnalysisResult>('/ai/contract-analysis', {
          contractText: request.contractText,
          contractUrl: request.contractUrl,
          analysisType: request.analysisType || 'basic',
        });
      }
    } catch (error) {
      throw error;
    }
  }

  /**
   * Generate rental application letter
   */
  async generateApplication(request: ApplicationGenerationRequest): Promise<ApiResponse<ApplicationGenerationResult>> {
    try {
      return await apiService.post<ApplicationGenerationResult>('/ai/application/generate', request);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get market analysis for location
   */
  async getMarketAnalysis(request: MarketAnalysisRequest): Promise<ApiResponse<MarketAnalysisResult>> {
    try {
      // Try the standard endpoint first
      try {
        return await apiService.post<MarketAnalysisResult>('/ai/market-analysis', request);
      } catch (error: any) {
        // If we get a 404, the endpoint might be mounted differently
        if (error.status === 404) {
          console.log('Market analysis endpoint not found at /ai/market-analysis, trying fallback...');
          // Generate a simple market analysis response with default values
          return {
            success: true,
            data: {
              location: request.location,
              averagePrice: 1500, // Default average price
              priceRange: {
                min: 1000,
                max: 2000
              },
              marketTrend: 'stable',
              pricePrediction: 'Prices are expected to remain stable',
              demandLevel: 'medium',
              keyInsights: [
                'Using fallback market data',
                'Consider checking with local real estate agents for more accurate information'
              ],
              recommendations: [
                'Set a budget based on your financial situation',
                'Consider multiple neighborhoods to increase options'
              ],
              confidenceScore: 50,
              dataPoints: 0
            }
          };
        }
        throw error;
      }
    } catch (error) {
      console.error('Market analysis error:', error);
      throw error;
    }
  }

  /**
   * Summarize text content
   */
  async summarizeText(request: SummarizationRequest): Promise<ApiResponse<SummarizationResult>> {
    try {
      return await apiService.post<SummarizationResult>('/ai/summarize', request);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Translate text
   */
  async translateText(request: TranslationRequest): Promise<ApiResponse<TranslationResult>> {
    try {
      return await apiService.post<TranslationResult>('/ai/translate', request);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get personalized recommendations based on user activity
   */
  async getPersonalizedRecommendations(
    userId?: string,
    limit = 10
  ): Promise<ApiResponse<{ listings: Listing[]; reasons: string[] }>> {
    try {
      return await apiService.get<{ listings: Listing[]; reasons: string[] }>('/ai/recommendations', {
        userId,
        limit,
      });
    } catch (error) {
      throw error;
    }
  }

  /**
   * Analyze listing description for key features
   */
  async analyzeListingDescription(
    description: string
  ): Promise<ApiResponse<{ features: string[]; sentiment: string; score: number }>> {
    try {
      return await apiService.post<{ features: string[]; sentiment: string; score: number }>(
        '/ai/analyze-description',
        { description }
      );
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get AI-powered search suggestions
   */
  async getSearchSuggestions(
    query: string,
    limit = 5
  ): Promise<ApiResponse<{ suggestions: string[]; categories: string[] }>> {
    try {
      return await apiService.get<{ suggestions: string[]; categories: string[] }>('/ai/search-suggestions', {
        query,
        limit,
      });
    } catch (error) {
      throw error;
    }
  }

  /**
   * Generate property comparison report
   */
  async compareProperties(
    listingIds: string[]
  ): Promise<ApiResponse<{
    comparison: {
      listing: Listing;
      pros: string[];
      cons: string[];
      score: number;
    }[];
    recommendation: string;
    summary: string;
  }>> {
    try {
      return await apiService.post<{
        comparison: {
          listing: Listing;
          pros: string[];
          cons: string[];
          score: number;
        }[];
        recommendation: string;
        summary: string;
      }>('/ai/compare-properties', { listingIds });
    } catch (error) {
      throw error;
    }
  }

  /**
   * Submit application manually or autonomously
   */
  async submitApplication(
    applicationId: string,
    method: 'manual' | 'autonomous',
    applicationData?: {
      listingId: string;
      message: string;
      subject: string;
      propertyTitle?: string;
      contactInfo?: any;
    }
  ): Promise<ApiResponse<{ success: boolean; submissionId: string; message: string }>> {
    try {
      return await apiService.post<{ success: boolean; submissionId: string; message: string }>('/ai/application/submit', {
        applicationId,
        method,
        applicationData,
      });
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get AI chat response for property-related questions
   */
  async getChatResponse(
    message: string,
    context?: {
      listing?: Listing;
      userPreferences?: UserPreferences;
      conversationHistory?: { role: 'user' | 'assistant'; content: string }[];
    }
  ): Promise<ApiResponse<{ response: string; suggestions: string[] }>> {
    try {
      return await apiService.post<{ response: string; suggestions: string[] }>('/ai/chat', {
        message,
        context,
      });
    } catch (error) {
      throw error;
    }
  }

  /**
   * Validate and format contract data
   */
  validateContractData(data: any): ContractAnalysisRequest {
    const validated: ContractAnalysisRequest = {};

    if (data.contractText && typeof data.contractText === 'string') {
      validated.contractText = data.contractText.trim();
    }

    if (data.contractUrl && typeof data.contractUrl === 'string') {
      validated.contractUrl = data.contractUrl.trim();
    }

    if (data.contractFile && (data.contractFile instanceof File || data.contractFile instanceof Blob)) {
      validated.contractFile = data.contractFile;
    }

    if (data.analysisType && ['basic', 'detailed', 'legal'].includes(data.analysisType)) {
      validated.analysisType = data.analysisType;
    }

    return validated;
  }

  /**
   * Format matching score for display
   */
  formatMatchScore(score: number): string {
    return `${Math.round(score * 100)}%`;
  }

  /**
   * Get match score color
   */
  getMatchScoreColor(score: number): string {
    if (score >= 0.8) return '#4CAF50'; // Green
    if (score >= 0.6) return '#FF9800'; // Orange
    return '#F44336'; // Red
  }

  /**
   * Format confidence score
   */
  formatConfidence(confidence: number): string {
    if (confidence >= 0.9) return 'Very High';
    if (confidence >= 0.7) return 'High';
    if (confidence >= 0.5) return 'Medium';
    return 'Low';
  }
}

// Export singleton instance
export const aiService = new AIService();
export default aiService;
