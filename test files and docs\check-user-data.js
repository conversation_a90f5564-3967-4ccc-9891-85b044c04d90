// Check user data structure
async function checkUserData() {
  try {
    console.log('🔍 Checking user data structure...');
    
    const loginResponse = await fetch('http://localhost:3000/api/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123'
      })
    });
    
    if (loginResponse.ok) {
      const loginData = await loginResponse.json();
      console.log('✅ Login successful');
      console.log('\n📋 Full user data structure:');
      console.log(JSON.stringify(loginData, null, 2));
      
      // Check specific fields
      console.log('\n🔍 Key fields:');
      console.log('- User ID:', loginData.user?._id);
      console.log('- Email:', loginData.user?.email);
      console.log('- Role:', loginData.user?.role);
      console.log('- Is Property Owner:', loginData.user?.propertyOwner?.isPropertyOwner);
      console.log('- Verification Status:', loginData.user?.propertyOwner?.verificationStatus);
      console.log('- Properties Count:', loginData.user?.propertyOwner?.properties?.length);
      
    } else {
      console.log('❌ Login failed:', loginResponse.status);
    }
  } catch (error) {
    console.log('❌ Error:', error.message);
  }
}

checkUserData();