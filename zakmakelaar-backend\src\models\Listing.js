const mongoose = require("mongoose");

const listingSchema = new mongoose.Schema({
  title: { type: String, required: true },
  price: { 
    type: String, 
    required: true,
    // Price format: '€ X.XXX per maand' with proper handling of European number formats
    // Supports various input formats like €3,950 (thousands separator) and properly normalizes them
  },
  location: { type: String, required: true },
  url: { type: String, required: true, unique: true },
  size: { type: String },
  bedrooms: { type: String },
  rooms: { type: String }, // Total number of rooms
  propertyType: { type: String },
  description: { type: String },
  year: { type: String }, // Build year
  interior: { type: String }, // <PERSON><PERSON>, Gestoffeerd, Gemeubileerd
  source: { type: String }, // Which site the listing came from
  images: { type: [String], default: [] }, // Array of image URLs
  dateAdded: { type: Date, default: Date.now },
  timestamp: { type: Date, default: Date.now },
});

module.exports = mongoose.model("Listing", listingSchema);
