import { useInfiniteQuery, useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { listingsService, Listing, ListingFilters, ListingQuery } from '../services/listingsService';
import { queryKeys, cacheUtils } from '../services/queryClient';

// Listings queries
export const useListings = (query?: ListingQuery, enabled = true) => {
  return useQuery({
    queryKey: queryKeys.listings.list(query || {}),
    queryFn: async () => {
      const response = await listingsService.getListings(query);
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch listings');
      }
      return response.data;
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    enabled,
    keepPreviousData: true, // Keep previous data while fetching new data
  });
};

export const useInfiniteListings = (query?: ListingQuery, enabled = true) => {
  return useInfiniteQuery({
    queryKey: queryKeys.listings.list(query || {}),
    queryFn: async ({ pageParam = 1 }) => {
      const response = await listingsService.getListings({
        ...query,
        page: pageParam,
        limit: query?.limit || 20,
      });
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch listings');
      }
      return {
        listings: response.data?.listings || [],
        pagination: response.pagination,
        nextPage: pageParam < (response.pagination?.totalPages || 1) ? pageParam + 1 : undefined,
      };
    },
    getNextPageParam: (lastPage) => lastPage.nextPage,
    staleTime: 2 * 60 * 1000, // 2 minutes
    enabled,
    initialPageParam: 1,
  });
};

export const useListing = (id: string, enabled = true) => {
  return useQuery({
    queryKey: queryKeys.listings.detail(id),
    queryFn: async () => {
      const response = await listingsService.getListing(id);
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch listing');
      }
      return response.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: enabled && !!id,
    retry: (failureCount, error: any) => {
      // Don't retry on 404 errors
      if (error?.status === 404) {
        return false;
      }
      return failureCount < 2;
    },
  });
};

export const useSavedListings = (enabled = true) => {
  return useQuery({
    queryKey: queryKeys.listings.saved(),
    queryFn: async () => {
      const response = await listingsService.getSavedListings();
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch saved listings');
      }
      return response.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled,
  });
};

export const useRecentListings = (limit = 10, enabled = true) => {
  return useQuery({
    queryKey: queryKeys.listings.recent(),
    queryFn: async () => {
      const response = await listingsService.getRecentListings(limit);
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch recent listings');
      }
      return response.data;
    },
    staleTime: 3 * 60 * 1000, // 3 minutes
    enabled,
  });
};

export const useSearchListings = (searchTerm: string, filters?: ListingFilters, enabled = true) => {
  return useQuery({
    queryKey: queryKeys.listings.search(searchTerm, filters),
    queryFn: async () => {
      const response = await listingsService.searchListings(searchTerm, filters);
      if (!response.success) {
        throw new Error(response.message || 'Search failed');
      }
      return response.data;
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    enabled: enabled && !!searchTerm.trim(),
    keepPreviousData: true,
  });
};

// Listings mutations
export const useSaveListing = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (listingId: string) => {
      const response = await listingsService.saveListing(listingId);
      if (!response.success) {
        throw new Error(response.message || 'Failed to save listing');
      }
      return response.data;
    },
    onMutate: async (listingId) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: queryKeys.listings.all });
      
      // Optimistically update listing in all relevant queries
      queryClient.setQueriesData(
        { queryKey: queryKeys.listings.all },
        (oldData: any) => {
          if (!oldData) return oldData;
          
          // Handle different data structures
          if (oldData.listings) {
            // For paginated results
            return {
              ...oldData,
              listings: oldData.listings.map((listing: Listing) =>
                listing._id === listingId ? { ...listing, isSaved: true } : listing
              ),
            };
          } else if (Array.isArray(oldData)) {
            // For direct arrays
            return oldData.map((listing: Listing) =>
              listing._id === listingId ? { ...listing, isSaved: true } : listing
            );
          } else if (oldData._id === listingId) {
            // For single listing
            return { ...oldData, isSaved: true };
          }
          
          return oldData;
        }
      );
      
      return { listingId };
    },
    onError: (error, listingId, context) => {
      // Rollback optimistic update
      queryClient.setQueriesData(
        { queryKey: queryKeys.listings.all },
        (oldData: any) => {
          if (!oldData) return oldData;
          
          if (oldData.listings) {
            return {
              ...oldData,
              listings: oldData.listings.map((listing: Listing) =>
                listing._id === listingId ? { ...listing, isSaved: false } : listing
              ),
            };
          } else if (Array.isArray(oldData)) {
            return oldData.map((listing: Listing) =>
              listing._id === listingId ? { ...listing, isSaved: false } : listing
            );
          } else if (oldData._id === listingId) {
            return { ...oldData, isSaved: false };
          }
          
          return oldData;
        }
      );
      
      console.error('Save listing error:', error);
    },
    onSuccess: () => {
      // Invalidate saved listings to refetch
      queryClient.invalidateQueries({ queryKey: queryKeys.listings.saved() });
    },
  });
};

export const useUnsaveListing = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (listingId: string) => {
      const response = await listingsService.unsaveListing(listingId);
      if (!response.success) {
        throw new Error(response.message || 'Failed to unsave listing');
      }
      return response.data;
    },
    onMutate: async (listingId) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: queryKeys.listings.all });
      
      // Optimistically update listing in all relevant queries
      queryClient.setQueriesData(
        { queryKey: queryKeys.listings.all },
        (oldData: any) => {
          if (!oldData) return oldData;
          
          if (oldData.listings) {
            return {
              ...oldData,
              listings: oldData.listings.map((listing: Listing) =>
                listing._id === listingId ? { ...listing, isSaved: false } : listing
              ),
            };
          } else if (Array.isArray(oldData)) {
            return oldData.map((listing: Listing) =>
              listing._id === listingId ? { ...listing, isSaved: false } : listing
            );
          } else if (oldData._id === listingId) {
            return { ...oldData, isSaved: false };
          }
          
          return oldData;
        }
      );
      
      // Remove from saved listings
      queryClient.setQueryData(queryKeys.listings.saved(), (oldData: any) => {
        if (!oldData?.listings) return oldData;
        return {
          ...oldData,
          listings: oldData.listings.filter((listing: Listing) => listing._id !== listingId),
        };
      });
      
      return { listingId };
    },
    onError: (error, listingId, context) => {
      // Rollback optimistic updates
      queryClient.setQueriesData(
        { queryKey: queryKeys.listings.all },
        (oldData: any) => {
          if (!oldData) return oldData;
          
          if (oldData.listings) {
            return {
              ...oldData,
              listings: oldData.listings.map((listing: Listing) =>
                listing._id === listingId ? { ...listing, isSaved: true } : listing
              ),
            };
          } else if (Array.isArray(oldData)) {
            return oldData.map((listing: Listing) =>
              listing._id === listingId ? { ...listing, isSaved: true } : listing
            );
          } else if (oldData._id === listingId) {
            return { ...oldData, isSaved: true };
          }
          
          return oldData;
        }
      );
      
      console.error('Unsave listing error:', error);
    },
    onSuccess: () => {
      // Invalidate saved listings to refetch
      queryClient.invalidateQueries({ queryKey: queryKeys.listings.saved() });
    },
  });
};

// Utility hooks
export const useListingActions = (listingId: string) => {
  const saveMutation = useSaveListing();
  const unsaveMutation = useUnsaveListing();
  
  const toggleSave = (isSaved: boolean) => {
    if (isSaved) {
      unsaveMutation.mutate(listingId);
    } else {
      saveMutation.mutate(listingId);
    }
  };
  
  return {
    toggleSave,
    isSaving: saveMutation.isPending,
    isUnsaving: unsaveMutation.isPending,
    isLoading: saveMutation.isPending || unsaveMutation.isPending,
    error: saveMutation.error || unsaveMutation.error,
  };
};

export const useListingsWithFilters = (filters: ListingFilters) => {
  const query: ListingQuery = {
    ...filters,
    limit: 20,
  };
  
  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    error,
    refetch,
  } = useInfiniteListings(query);
  
  // Flatten all pages into a single array
  const listings = data?.pages.flatMap(page => page.listings) || [];
  
  return {
    listings,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    error,
    refetch,
    totalCount: data?.pages[0]?.pagination?.total || 0,
  };
};

// Prefetch utilities
export const usePrefetchListing = () => {
  const queryClient = useQueryClient();
  
  return (listingId: string) => {
    queryClient.prefetchQuery({
      queryKey: queryKeys.listings.detail(listingId),
      queryFn: async () => {
        const response = await listingsService.getListing(listingId);
        if (!response.success) {
          throw new Error(response.message || 'Failed to fetch listing');
        }
        return response.data;
      },
      staleTime: 5 * 60 * 1000, // 5 minutes
    });
  };
};