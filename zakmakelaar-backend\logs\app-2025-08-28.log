{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:00:00'
}
{
  component: 'scraper',
  message: 'Running enabled scrapers: funda',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:00:00'
}
{
  message: 'Error handling service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:01:09'
}
{
  message: 'Error handling service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:01:09'
}
{
  message: 'Error handling service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:01:09'
}
{
  message: 'Initializing ApplicationQueueManager',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:01:09'
}
{
  message: 'Notification queue processing started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:01:10'
}
{
  service: 'zakmakelaar-api',
  port: '3000',
  environment: 'production',
  endpoints: {
    api: 'http://localhost:3000',
    docs: 'http://localhost:3000/api-docs',
    health: 'http://localhost:3000/health'
  },
  level: 'info',
  message: '🚀 ZakMakelaar API Server Started',
  timestamp: '2025-08-28 00:01:10'
}
{
  message: 'WebSocket service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:01:10'
}
{
  message: 'NotificationScheduler initialized with cron jobs',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:01:10'
}
{
  message: 'Started scheduled job: dailySummary',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:01:10'
}
{
  message: 'Started scheduled job: weeklySummary',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:01:10'
}
{
  message: 'Started scheduled job: cleanup',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:01:10'
}
{
  message: 'NotificationScheduler started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:01:10'
}
{
  message: 'Auto-application processing started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:01:10'
}
{
  message: 'Auto-application service initialized and started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:01:10'
}
{
  message: 'Document vault directories initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:01:10'
}
{
  message: 'Starting queue processing',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:01:10'
}
{
  message: 'ApplicationQueueManager initialized successfully',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:01:10'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:05:00'
}
{
  component: 'scraper',
  message: 'Running enabled scrapers: funda',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:05:00'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:06:09',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:06:09',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:06:09',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:10:00'
}
{
  component: 'scraper',
  message: 'Running enabled scrapers: funda',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:10:00'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:11:09',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:11:09',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:11:09',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  message: 'Error handling service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:13:54'
}
{
  message: 'Error handling service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:13:54'
}
{
  message: 'Error handling service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:13:54'
}
{
  message: 'Initializing ApplicationQueueManager',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:13:54'
}
{
  message: 'Notification queue processing started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:13:54'
}
{
  service: 'zakmakelaar-api',
  port: '3000',
  environment: 'production',
  endpoints: {
    api: 'http://localhost:3000',
    docs: 'http://localhost:3000/api-docs',
    health: 'http://localhost:3000/health'
  },
  level: 'info',
  message: '🚀 ZakMakelaar API Server Started',
  timestamp: '2025-08-28 00:13:54'
}
{
  message: 'WebSocket service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:13:54'
}
{
  message: 'NotificationScheduler initialized with cron jobs',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:13:55'
}
{
  message: 'Started scheduled job: dailySummary',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:13:55'
}
{
  message: 'Started scheduled job: weeklySummary',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:13:55'
}
{
  message: 'Started scheduled job: cleanup',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:13:55'
}
{
  message: 'NotificationScheduler started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:13:55'
}
{
  message: 'Auto-application processing started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:13:55'
}
{
  message: 'Auto-application service initialized and started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:13:55'
}
{
  message: 'Document vault directories initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:13:55'
}
{
  message: 'Starting queue processing',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:13:55'
}
{
  message: 'ApplicationQueueManager initialized successfully',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:13:55'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:15:00'
}
{
  component: 'scraper',
  message: 'Running enabled scrapers: funda',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:15:00'
}
{
  message: 'Error handling service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:15:24'
}
{
  message: 'Error handling service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:15:24'
}
{
  message: 'Error handling service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:15:24'
}
{
  message: 'Initializing ApplicationQueueManager',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:15:24'
}
{
  message: 'Notification queue processing started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:15:24'
}
{
  service: 'zakmakelaar-api',
  port: '3000',
  environment: 'production',
  endpoints: {
    api: 'http://localhost:3000',
    docs: 'http://localhost:3000/api-docs',
    health: 'http://localhost:3000/health'
  },
  level: 'info',
  message: '🚀 ZakMakelaar API Server Started',
  timestamp: '2025-08-28 00:15:24'
}
{
  message: 'WebSocket service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:15:24'
}
{
  message: 'NotificationScheduler initialized with cron jobs',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:15:24'
}
{
  message: 'Started scheduled job: dailySummary',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:15:24'
}
{
  message: 'Started scheduled job: weeklySummary',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:15:24'
}
{
  message: 'Started scheduled job: cleanup',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:15:24'
}
{
  message: 'NotificationScheduler started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:15:24'
}
{
  message: 'Auto-application processing started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:15:24'
}
{
  message: 'Auto-application service initialized and started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:15:24'
}
{
  message: 'Document vault directories initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:15:24'
}
{
  message: 'Starting queue processing',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:15:24'
}
{
  message: 'ApplicationQueueManager initialized successfully',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:15:24'
}
{
  message: 'Error handling service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:15:32'
}
{
  message: 'Error handling service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:15:32'
}
{
  message: 'Error handling service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:15:32'
}
{
  message: 'Initializing ApplicationQueueManager',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:15:32'
}
{
  message: 'Notification queue processing started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:15:33'
}
{
  service: 'zakmakelaar-api',
  port: '3000',
  environment: 'production',
  endpoints: {
    api: 'http://localhost:3000',
    docs: 'http://localhost:3000/api-docs',
    health: 'http://localhost:3000/health'
  },
  level: 'info',
  message: '🚀 ZakMakelaar API Server Started',
  timestamp: '2025-08-28 00:15:33'
}
{
  message: 'WebSocket service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:15:33'
}
{
  message: 'NotificationScheduler initialized with cron jobs',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:15:33'
}
{
  message: 'Started scheduled job: dailySummary',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:15:33'
}
{
  message: 'Started scheduled job: weeklySummary',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:15:33'
}
{
  message: 'Started scheduled job: cleanup',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:15:33'
}
{
  message: 'NotificationScheduler started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:15:33'
}
{
  message: 'Auto-application processing started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:15:33'
}
{
  message: 'Auto-application service initialized and started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:15:33'
}
{
  message: 'Document vault directories initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:15:33'
}
{
  message: 'Starting queue processing',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:15:33'
}
{
  message: 'ApplicationQueueManager initialized successfully',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:15:33'
}
{
  message: 'Error handling service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:18:38'
}
{
  message: 'Error handling service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:18:38'
}
{
  message: 'Error handling service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:18:38'
}
{
  message: 'Initializing ApplicationQueueManager',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:18:38'
}
{
  message: 'Notification queue processing started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:18:38'
}
{
  service: 'zakmakelaar-api',
  port: '3000',
  environment: 'production',
  endpoints: {
    api: 'http://localhost:3000',
    docs: 'http://localhost:3000/api-docs',
    health: 'http://localhost:3000/health'
  },
  level: 'info',
  message: '🚀 ZakMakelaar API Server Started',
  timestamp: '2025-08-28 00:18:38'
}
{
  message: 'WebSocket service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:18:38'
}
{
  message: 'NotificationScheduler initialized with cron jobs',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:18:38'
}
{
  message: 'Started scheduled job: dailySummary',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:18:38'
}
{
  message: 'Started scheduled job: weeklySummary',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:18:38'
}
{
  message: 'Started scheduled job: cleanup',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:18:38'
}
{
  message: 'NotificationScheduler started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:18:38'
}
{
  message: 'Auto-application processing started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:18:38'
}
{
  message: 'Auto-application service initialized and started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:18:38'
}
{
  message: 'Document vault directories initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:18:38'
}
{
  message: 'Starting queue processing',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:18:38'
}
{
  message: 'ApplicationQueueManager initialized successfully',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:18:38'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:20:00'
}
{
  component: 'scraper',
  message: 'Running enabled scrapers: funda',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:20:00'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:23:38',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:23:38',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:23:38',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:25:00'
}
{
  component: 'scraper',
  message: 'Running enabled scrapers: funda',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:25:00'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:28:38',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:28:38',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:28:38',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:30:00'
}
{
  component: 'scraper',
  message: 'Running enabled scrapers: funda',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:30:00'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:33:38',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:33:38',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:33:38',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:35:00'
}
{
  component: 'scraper',
  message: 'Running enabled scrapers: funda',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:35:00'
}
{
  message: 'Optimized transformation pipeline initialized successfully',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:35:33'
}
{
  message: 'Transformation monitor initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.084Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.115Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.122Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.131Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.139Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.148Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.155Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.165Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1756337733160_kilw0',
  source: 'funda',
  duration: '4ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:818:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'Altijd al in een prachtig statig herenhuis willen wonen daterend uit 1910? Deze totaal gerenoveerde en VERDUURZAAMDE bovenwoning met energie label A, dakterras en charmant balkon. Met een totaal woonplezier van 180m2, hoge platfonds, oase van rust in een van de prachtige authentieke Utrechtse wijk. Met een maatvoering van bijna 8.00m bij 11.50m is dit echt een uniek woonhuis. Bij de zeer recente renovatie in 2021 is het huis volledig gerenoveerd, voorzien van een nieuwe indeling, prachtige woon-eetkeuken en nog 5 extra kamers, zoals een mooie eetkamer naast de keuken, waar je direct de mooie breedte van het huis ervaart.\n' +
              '\n' +
              "De woning is gelegen in een rustige wijk met mooie rijen herenhuizen. De Zeeheldenbuurt is ruim opgezet met veel groen voorzieningen en ruime tuinen. Dit woonhuis kijkt uit op de markante vrijstaande villa's die de buurt typeren. Restaurant C’est Ca en Goesting liggen om de hoek en voor de boodschappen ligt het vernieuwde winkelcentrum De Gaard op 3 minuten fietsen. Of als u zin hebt om op zaterdag ochtend naar de markt te willen fietsen, dan bent u binnen 10min in het prachtige Utrechtse centrum. \n" +
              '\n' +
              'Indeling\n' +
              '\n' +
              'Begane grond:\n' +
              '\n' +
              'Ruime entree waar direct de mooie sfeer voelbaar is, hoge plafond, mooie vloer en ruime trap naar boven, omdat de entree zo ruim is, is er voldoende plek om uw goede fietsen binnen te plaatsen. \n' +
              'Eerste verdieping:\n' +
              "Met zoals gezegd een vloeroppervlak van ca. 8.00m bij 11.50m is dit een zeer ruim opgezette verdieping, de vertrekken zijn ruim met het fraaie trappenhuis en de ruime overloop met hier een ruim toilet voorzien van prachtige marmer uit Italië. De woonkamer is van het type kamer-en-suite en ademt ook veel sfeer uit, de keuken ligt aan de achterzijde van de woning en is een indrukwekkend groot kookeiland, mooi midden in de ruimte geplaatst, voorzien van een ruime kastenwand wat het geheel mooi, gebalanceerd en zeer functioneel maakt. Naar achter toe is er een nieuwe glazen pui met openslaande deuren, veel glas wat de woning nog ruimtelijker maakt. Naast de keuken ligt een heerlijke knusse eetkamer waar voldoende ruimte is om heerlijk met uw familie te dineren. Aan de voorzijde naast de woonkamer ligt een extra kamer, deze is nu in gebruik als werkkamer, met aansluitend van een inpandig terras. Dit terras heeft een prachtige vloer, ronde boog en uitzicht op de witte villa's, het is hier heerlijk vertoeven, de zon ligging is zuid, een heerlijke plek van het huis. Aan de achterzijde kijk u juist weer uit op de ruime tuinen van de omgeving met het watertje wat achter het huis loopt, een mooi groen geheel met veel vogels. De muur naar het mooie en statige trappenhuis is open gemaakt, dit is ruimtelijk en laat de mooie breedte zien.\n" +
              '\n' +
              'Tweede verdieping:\n' +
              '\n' +
              'Ook hier tref u weer het mooie formaat van het huis aan, er is weer een ruime overloop met een spectaculair glazen dak. Hierdoor ervaart u een zeer ruimtelijk gevoel en wordt de gehele woning voorzien van natuurlijk licht en warmte van de zon, hier tref je een vaste trap naar het dakterras. Er zijn 3 slaapkamers en een nieuwe ruime badkamer. De badkamer is ook weer zeer modern en gebalanceerd met een ruime inloopdouche met een regen douche, vrijstaand ovaal ligbad en een tweede toilet. Er is een fraai wastafel meubel met twee waskommen, met een druk op de knop worden de ramen gebandeerd en heb je geen gordijnen nodig, een van de mooie gadgets van dit huis. \n' +
              'De slaapkamers zijn allemaal ruim van opzet. De vaste trap brengt je naar een fraai dakterras, een extra plek om buiten te zitten met mooi zicht op de omgeving en zicht op de Dom toren. Een groendak, voorzien van sedum en de plantenbakken zorgen voor de groene beleving. Een ruim terras om heerlijk op een zomerse dag buiten te eten, weer een prachtig geheel! \n' +
              '\n' +
              'Huurprijs:\n' +
              '\n' +
              '3895,00 per maand exclusief nuts voorzieningen.\n' +
              'Huurder is zelf contractant voor alle nutsvoorzieningen. \n' +
              '\n' +
              'Borg:\n' +
              'Staat gelijk aan 2 maanden huur.\n' +
              '\n' +
              'Ingangsdatum:\n' +
              'In overleg\n' +
              '\n' +
              'WILT U DE WONING BEZICHTIGINGEN?\n' +
              'Graag ontvangen wij per e-mail of contactformulier op onze website de volgende informatie:\n' +
              '\n' +
              '1. adres van de woning waarin u bent geïnteresseerd\n' +
              '2. telefoonnummer\n' +
              '3. bent u student, in loondienst of zelfstandig ondernemer?\n' +
              '4. werkende; voor welk bedrijf of organisatie? Studerend; welke studie/welke instelling\n' +
              '5. de gewenste huurperiode (voorkeur startdatum en verwachte huurperiode)\n' +
              '6. aantal bewoners en de relatie onderling (gezin/partners/vrienden)\n' +
              '7. bruto maand of jaarinkomen (eventueel ook dat van de partner)\n' +
              '\n' +
              'Nadat wij bovenstaande gegevens hebben ontvangen zullen wij contact opnemen voor het maken van een afspraak. Wij zullen de kandidaten die als eerst reageren als eerst benaderen.\n' +
              '\n' +
              'Alleen volledige aanvragen zullen in behandeling worden genomen.\n' +
              '\n' +
              'Heeft u vragen of het project of wilt u een bezichtiging plannen. Neem gerust contact op met ons kantoor.\n' +
              '\n' +
              'Deze informatie is door ons met de nodige zorgvuldigheid samengesteld. Geen enkele aansprakelijkheid wordt aanvaard voor enige onvolledigheid, onjuistheid of anderszins, of de consequenties daarvan. Alle maten en afmetingen zijn indicatief. De woning is niet NEN ingemeten en hierdoor zijn kleine afwijking mogelijk.',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-08-27T23:35:33.164Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.175Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.184Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.191Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.199Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1756337733196_ux9ih',
  source: 'funda',
  duration: '3ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:818:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'Let op: alleen bezichtigingsaanvragen via funda worden in behandeling genomen!\n' +
              '\n' +
              'Je eigen 2-kamerappartement op een aantrekkelijke locatie op fietsafstand van het centrum! Dit appartementencomplex is gelegen op een ruim perceel aan de Hooft Graaflandstraat.\n' +
              '\n' +
              'Het betreffen 15 zelfstandige appartementen gelegen in de populaire woonwijk Hoograven op de begane grond, eerste en tweede etage. Deze appartementen zijn goed geïsoleerd en strak afgewerkt met hoogwaardige materialen. Ze zijn voorzien van een PVC-vloer, gestukte en gesausde wanden, eigen aansluitingen voor nutsvoorzieningen, internet en tv. \n' +
              'Ieder appartement beschikt over een luxe keuken voorzien van o.a. afwasmachine, combi-oven en afzuigkap. De badkamer is keurig in neutrale kleurstelling betegeld en is voorzien van een douche, wastafel en toilet. \n' +
              'Daarnaast is het pand volledig gasloos en beschikt het over een warmtepomp en HR++ glas, wat resulteert in een lage energierekening. \n' +
              '\n' +
              'Op het terrein is een overdekte fietsenstalling en 6 parkeerplaatsen welke vrij gebruikt mogen worden als bewoner. \n' +
              '\n' +
              'Locatie:\n' +
              'Op loopafstand van diverse winkelvoorzieningen waaronder het winkelcentrum “het hart van Hoograven” en nabij dé culinaire hotspot van Utrecht (Rotsoord) met haar verschillende cafés en restaurants zoals WT Urban Kitchen en Klein Berlijn. Tevens bevindt je je op fietsafstand van het oude centrum en is het openbaar vervoer om de hoek waarbij de parkeermogelijkheden (thans vrij parkeren) als extra pre aanwezig zijn. \n' +
              '\n' +
              'Appartement 2C-5\n' +
              'Indeling:\n' +
              'Eerste verdieping:\n' +
              'Appartementsvoordeur: hal, slaapkamer, badkamer met douche, toilet en wastafel, ruimte met wasmachine-aansluiting. Lichte woonkamer met keuken welke is voorzien van diverse apparatuur waaronder een koelkast, vriezer, vaatwasser, inductiekookplaat, afzuigkap & magnetron combi-oven. Vanuit de woonkamer bereik je het balkon door de grote raampartij met haar openslaande deuren.\n' +
              '\n' +
              'Ben je benieuwd of dit appartement bij je past? Wij maken graag een afspraak voor een rondleiding!\n' +
              '\n' +
              'Bijzonderheden:\n' +
              '- Prijs EUR 1.775,- exclusief;\n' +
              '- Huurder is zelfstandig contractant van de NUTS voorzieningen;\n' +
              '- Servicekosten EUR 86,24 per mnd;\n' +
              '- Oppervlakte ca. 45 m2;\n' +
              '- Balkon;\n' +
              '- Gelegen op de eerste etage;\n' +
              '- Twee maanden borg;\n' +
              '- Inkomenseis: 3 x maandhuur bruto;\n' +
              '- Energielabel: A++;\n' +
              '- Hoogwaardig afwerkingsniveau;\n' +
              '- Volledig gestoffeerd: PVC vloer, gordijnen/rolgordijnen/jaloezieën, lampen, handdoek haakjes, kapstok;\n' +
              '- Internet/tv aansluiting aanwezig, abonnement naar keuze;\n' +
              '- Huisdieren zijn niet toegestaan;\n' +
              '- Vrij parkeren op terrein of op openbare weg;\n' +
              '- Oplevering: 12 september 2025.\n' +
              '\n' +
              'Deze aanbieding is met zorg samengesteld echter geheel vrijblijvend en onder voorbehoud van goedkeuring verhuurder. Derhalve kunnen hier op geen enkele wijze geen rechten aan ontleend worden. Alle maten en afmetingen zijn indicatief en niet exact volgens de NEN-2580 norm.\n' +
              '\n' +
              'Note: Only viewing requests made via Funda will be processed!\n' +
              '\n' +
              'Your own 2-room apartment in an attractive location, just a short cycle from the city center! This apartment complex is situated on a spacious plot on Hooft Graaflandstraat.\n' +
              '\n' +
              'The complex consists of 15 self-contained apartments located in the popular residential area of Hoograven, spread across the ground, first, and second floors. These apartments are well insulated and finished to a high standard with quality materials. Each unit is equipped with PVC flooring, plastered and painted walls, individual utility connections, internet, and TV.\n' +
              '\n' +
              'Every apartment has a modern kitchen equipped with a dishwasher, combi oven, and extractor hood. The bathroom is neatly tiled in a neutral color scheme and features a shower, washbasin, and toilet.\n' +
              '\n' +
              'The building is completely gas-free and fitted with a heat pump and HR++ glass, resulting in a low energy bill.\n' +
              '\n' +
              'On the premises, there is a covered bicycle storage area and 6 parking spaces available for residents’ use.\n' +
              '\n' +
              'Location:\n' +
              'Within walking distance of several shopping facilities, including the shopping center “Het Hart van Hoograven,” and close to Utrecht’s culinary hotspot Rotsoord, home to cafés and restaurants such as WT Urban Kitchen and Klein Berlijn. The historic city center is only a short cycle away, public transport is just around the corner, and the availability of (currently free) parking adds to the convenience.\n' +
              '\n' +
              'Apartment 2C-5\n' +
              '\n' +
              'Layout – First floor:\n' +
              'Private entrance: hallway, bedroom, bathroom with shower, toilet, and washbasin, and a separate laundry area with washing machine connection. Bright living room with kitchen, fitted with various appliances including fridge, freezer, dishwasher, induction hob, extractor hood & microwave-combi oven. From the living room, large French doors provide access to the balcony.\n' +
              '\n' +
              'Curious whether this apartment is right for you? We would be happy to arrange a viewing!\n' +
              '\n' +
              'Details:\n' +
              '\n' +
              '- Rent: €1,775 excl.;\n' +
              '- Tenant contracts utilities directly;\n' +
              '- Service charges: €86.24 per month;\n' +
              '- Approx. 45 m²;\n' +
              '- Balcony;\n' +
              '- Located on the first floor;\n' +
              '- Two months’ deposit;\n' +
              '- Income requirement: 3x monthly rent (gross);\n' +
              '- Energy label: A++;\n' +
              '- High-quality finish;\n' +
              '- Fully furnished: PVC flooring, curtains/blinds, lighting, towel hooks, coat rack;\n' +
              '- Internet/TV connection available, subscription at tenant’s choice;\n' +
              '- Pets are not allowed;\n' +
              '- Free parking on-site or in public areas;\n' +
              '- Available: September 12, 2025.\n' +
              '- Note: Only viewing requests made via Funda will be processed!\n' +
              '\n' +
              'This listing has been compiled with care, but is entirely without obligation and subject to landlord approval. No rights can be derived from this advertisement in any way. All sizes and measurements are indicative and not exactly in accordance with the NEN-2580 standard.',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-08-27T23:35:33.199Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.207Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.215Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.221Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1756337733219_ighe9',
  source: 'funda',
  duration: '1ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: Build year must be between 1800 and 2030
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:818:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: 'Build year must be between 1800 and 2030',
          path: [ 'year' ],
          type: 'custom',
          context: { label: 'year', value: '1450', key: 'year' }
        }
      ]
    },
    timestamp: 2025-08-27T23:35:33.220Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.227Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.234Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.239Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.245Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.251Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.257Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.264Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1756337733262_yukpj',
  source: 'funda',
  duration: '2ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:818:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'Luxe en Duurzaam Wonen in Wonderwoods – Hoola van Nootenstraat 355, Utrecht\n' +
              '\n' +
              'Wonen in het hart van Utrecht, omringd door groen en moderne luxe? Dit volledig gemeubileerde appartement in het iconische Wonderwoods-gebouw biedt alles wat je zoekt! Met een woonoppervlakte van ca. 67 m², een prachtig balkon met adembenemend uitzicht over Utrecht, en een toplocatie vlak bij het Centraal Station, is dit een unieke kans.\n' +
              '\n' +
              'Over Wonderwoods\n' +
              'Wonderwoods is een baanbrekend, duurzaam woonconcept dat natuur en stad samenbrengt. Dit architectonische meesterwerk, geïnspireerd door het Bosco Verticale in Milaan, biedt een groene en gezonde leefomgeving met weelderige beplanting op de gevels en daktuinen. Het gebouw beschikt over luxe faciliteiten en innovatieve technologieën die bijdragen aan een duurzame en comfortabele levensstijl.\n' +
              '\n' +
              'Perfecte Locatie\n' +
              'Wonderwoods ligt in het nieuwe Beurskwartier, dé opkomende wijk van Utrecht. Hier woon je op steenworp afstand van Utrecht Centraal, het bruisende stadscentrum, en tal van restaurants, cafés en winkels. Ook het Jaarbeursplein en het Beatrix Theater bevinden zich op loopafstand. Daarnaast is het gebied goed bereikbaar met zowel het openbaar vervoer als de auto, met directe toegang tot belangrijke uitvalswegen.\n' +
              '\n' +
              'Indeling van het appartement\n' +
              'Ruime en lichte woonkamer met moderne inrichting en toegang tot het balkon met uitzicht op de Dom. De open keuken is voorzien van luxe inbouwapparatuur.\n' +
              'Via de hal bereik je de comfortabele slaapkamer, welke stijlvol is ingericht. De moderne badkamer met inloopdouche en wastafel is tevens bereikbaar via de hal, daarnaast is de separate toilet gesitueerd alsmede het washok met warmtepomp. \n' +
              '\n' +
              'Bijzonderheden\n' +
              '- Huurprijs: €2.600 per maand (exclusief servicekosten en nutsvoorzieningen).\n' +
              '- Servicekosten bedragen € 100,- per maand.\n' +
              '- Volledig gemeubileerd – direct intrekbaar!\n' +
              '- Duurzaam wonen in een van de meest innovatieve gebouwen van Utrecht.\n' +
              '- Toplocatie: vlak bij Utrecht Centraal, winkels en horeca.\n' +
              '- Woonoppervlakte: ca. 67 m².\n' +
              '- Groene leefomgeving met beplanting op het gebouw en gedeelde buitenruimtes.\n' +
              '- Fietsenstalling in de onderbouw.\n' +
              '\n' +
              'Wil jij wonen op deze unieke plek in Utrecht? Neem snel contact op voor een bezichtiging! \n' +
              '\n' +
              'Deze aanbieding is met zorg samengesteld echter geheel vrijblijvend en onder voorbehoud van goedkeuring verhuurder. Derhalve kunnen hier op geen enkele wijze geen rechten aan ontleend worden. Alle maten en afmetingen zijn indicatief en niet exact volgens de NEN-2580 norm.\n' +
              '\n' +
              'Luxury and Sustainable Living in Wonderwoods – Hoola van Nootenstraat 355, Utrecht\n' +
              '\n' +
              'Would you like to live in the heart of Utrecht, surrounded by greenery and modern luxury? This fully furnished apartment in the iconic Wonderwoods building offers everything you need! With approximately 67 m² of living space, a beautiful balcony with breathtaking views of Utrecht, and a prime location near Central Station, this is a unique opportunity.\n' +
              '\n' +
              'About Wonderwoods\n' +
              'Wonderwoods is a groundbreaking, sustainable living concept that seamlessly integrates nature and urban life. Inspired by Bosco Verticale in Milan, this architectural masterpiece provides a green and healthy living environment with lush vegetation on the façades and rooftop gardens. The building offers luxurious amenities and innovative technologies, ensuring a comfortable and eco-friendly lifestyle.\n' +
              '\n' +
              'Perfect Location\n' +
              'Wonderwoods is situated in Beurskwartier, Utrecht’s vibrant new district. Living here means being just a stone’s throw from Utrecht Central Station, the lively city center, and numerous restaurants, cafés, and shops. Jaarbeursplein and Beatrix Theater are also within walking distance. The area is easily accessible by public transport and car, with direct access to major highways.\n' +
              '\n' +
              'Apartment Layout\n' +
              'Spacious and bright living room with modern furnishings and access to the balcony, offering stunning views of the Dom Tower.\n' +
              'Open kitchen, fully equipped with high-end built-in appliances.\n' +
              'Comfortable and stylishly furnished bedroom, accessible via the hallway.\n' +
              'Modern bathroom with a walk-in shower and sink.\n' +
              'Separate toilet and utility room with a heat pump, also accessible via the hallway.\n' +
              'Key Features\n' +
              '? Rent: €2,600 per month (excluding service charges and utilities).\n' +
              '? Servicecosts: € 100 per month.\n' +
              '? Fully furnished – move-in ready!\n' +
              '? Sustainable living in one of Utrecht’s most innovative buildings.\n' +
              '? Prime location near Utrecht Central Station, shops, and restaurants.\n' +
              '? Living area: approx. 67 m².\n' +
              '? Green environment with vegetation on the building and shared outdoor spaces.\n' +
              '? Bicycle storage in the basement.\n' +
              '\n' +
              'Would you like to live in this one-of-a-kind location in Utrecht? Contact us today to schedule a viewing! \n' +
              '\n' +
              'Disclaimer: This offer has been carefully compiled; however, it is entirely without obligation and subject to the landlord’s approval. Therefore, no rights can be derived from it in any way. All measurements and dimensions are indicative and not exact according to the NEN-2580 standard.',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-08-27T23:35:33.264Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.271Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.277Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.282Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.287Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.293Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.301Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.307Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.313Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.318Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.323Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.329Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.334Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.339Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1756337733337_6c6j1',
  source: 'funda',
  duration: '2ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:818:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'MODERN EN NIEUW 3-KAMERAPPARTEMENT MET BALKON, BERGING EN PRIVÉPARKEERPLAATS – BESCHIKBAAR PER 1 SEPTEMBER 2025\n' +
              '\n' +
              'Ben jij op zoek naar een licht, ruim en instap klaar appartement in een rustige en goed bereikbare wijk van Utrecht? Dan is dit prachtige 3-kamerappartement aan de Madridstraat 228 in Leidsche Rijn precies wat je zoekt!\n' +
              '\n' +
              'Met twee slaapkamers, een groot balkon, een privéberging én een eigen parkeerplaats, biedt deze woning alles wat je nodig hebt voor comfortabel wonen. De woning is volledig gestoffeerd en tot in de puntjes afgewerkt met een moderne pvc-vloer in houtlook, stijlvolle raambekleding en verlichting in elke kamer.\n' +
              '\n' +
              'Dit appartement bevindt zich op een ideale locatie in Utrecht: rustig gelegen, maar toch vlak bij alle voorzieningen. Je woont op korte afstand van Leidsche Rijn Centrum met gezellige horeca, winkels, sportfaciliteiten, scholen en het Máximapark. De bereikbaarheid is uitstekend: station Leidsche Rijn en station Terwijde liggen om de hoek en via de A2 of A12 ben je binnen no-time in Amsterdam of Den Haag.\n' +
              '\n' +
              'Indeling:\n' +
              '\n' +
              'Via de centrale entree met bellentableau, brievenbussen, lift en trappenhuis bereik je de derde verdieping.\n' +
              'Het appartement beschikt over een ruime hal die toegang geeft tot alle vertrekken. De woonkamer is licht en ruim en biedt directe toegang tot het zonnige balkon. De open keuken biedt ruimte voor inbouwapparatuur en sluit naadloos aan op de woonruimte.\n' +
              '\n' +
              'Er zijn twee comfortabele slaapkamers, een nette badkamer met inloopdouche en wastafel, een apart toilet, en een handige inpandige berging met wasmachine-aansluiting.\n' +
              '\n' +
              'Voordelen:\n' +
              '\n' +
              'Instapklaar, modern en sfeervol ingericht\n' +
              'Gestoffeerd met pvc-vloer, verlichting en gordijnen\n' +
              'Ruim balkon op het zuidwesten\n' +
              'Privéparkeerplaats in de ondergelegen garage\n' +
              'Eigen berging voor extra opslagruimte\n' +
              'Energiezuinig (energielabel A+++)\n' +
              'Uitstekende bereikbaarheid met OV en auto\n' +
              'Geschikt voor lange termijn verhuur\n' +
              '\n' +
              'Huurcondities:\n' +
              '\n' +
              'Huurprijs; € 2.600,- per maand, is inclusief de servicekosten en exclusief gas, water, elektra, internet, tv en gemeentelijke belastingen;\n' +
              '\n' +
              'Er wordt een inkomenscheck gedaan;\n' +
              '\n' +
              'Waarborgsom; € 5.200,- eenmalig;\n' +
              '\n' +
              'Huisdieren zijn niet toegestaan;\n' +
              '\n' +
              'Roken niet toegestaan;\n' +
              '\n' +
              'Niet geschikt voor studenten of woningdelers;\n' +
              '\n' +
              'Vloerverwarming via stadsverwarming;\n' +
              '\n' +
              'Huurperiode: langdurig;\n' +
              '\n' +
              'Professionele eindschoonmaak verplicht op kosten huurder;\n' +
              '\n' +
              'Appartement kan per 1 september 2025 worden betrokken\n' +
              '\n' +
              'De informatie is met de nodige zorgvuldigheid samengesteld. Geen aansprakelijkheid wordt aanvaard voor enige onvolledigheid, onjuistheid of anderszins, of consequenties daarvan. Alle maten en afmetingen zijn indicatief.\n' +
              '\n' +
              'MODERN AND NEW 3-ROOM APARTMENT WITH BALCONY, STORAGE, AND PRIVATE PARKING – AVAILABLE FROM SEPTEMBER 1, 2025\n' +
              '\n' +
              'Are you looking for a bright, spacious, and move-in ready apartment in a quiet yet easily accessible neighbourhood of Utrecht? Then this beautiful 3-room apartment on Madridstraat 228 in Leidsche Rijn is exactly what you’re looking for!\n' +
              '\n' +
              'With two bedrooms, a large balcony, a private storage unit, and your own parking space, this home offers everything you need for comfortable living. The property comes fully fitted and is finished to perfection with a modern wood-look PVC floor, stylish window coverings, and lighting in every room.\n' +
              '\n' +
              'This apartment is ideally located in Utrecht: peacefully situated, yet close to all amenities. You are within short distance of Leidsche Rijn Center with cozy restaurants, shops, sports facilities, schools, and the Máximapark. The location offers excellent accessibility: Leidsche Rijn and Terwijde train stations are nearby, and the A2 and A12 motorways will get you to Amsterdam or The Hague in no time.\n' +
              '\n' +
              'Layout:\n' +
              'Through the central entrance with intercom panel, mailboxes, elevator, and stairwell, you reach the third floor.\n' +
              '\n' +
              'The apartment features a spacious hallway providing access to all rooms. The living room is bright and spacious, with direct access to the sunny balcony. The open kitchen has space for built-in appliances and connects seamlessly to the living area.\n' +
              '\n' +
              'There are two comfortable bedrooms, a neat bathroom with walk-in shower and sink, a separate toilet, and a convenient internal storage room with washing machine connection.\n' +
              '\n' +
              'Features:\n' +
              '\n' +
              '- Move-in ready, modern, and tastefully decorated\n' +
              '- Fitted with PVC flooring, lighting, and curtains\n' +
              '- Spacious southwest-facing balcony\n' +
              '- Private parking space in the underground garage\n' +
              '- Private storage unit for extra storage space\n' +
              '- Energy-efficient (Energy Label A+++)\n' +
              '- Excellent accessibility by public transport and car\n' +
              '- Suitable for long-term rental\n' +
              '\n' +
              'Rental conditions:\n' +
              '\n' +
              '- Rent: €2,600 per month, including service charges and excluding gas, water, electricity, internet, TV, and municipal taxes\n' +
              '- Income check required\n' +
              '- Security deposit: €5,200 (one-time)\n' +
              '- Pets not allowed\n' +
              '- Smoking not allowed\n' +
              '- Not suitable for students or house sharing\n' +
              '- Underfloor heating via district heating\n' +
              '- Long-term rental period\n' +
              '- Professional end-of-tenancy cleaning required at tenant’s expense\n' +
              '- Available from September 1, 2025\n' +
              '\n' +
              'The information has been compiled with due care. No liability is accepted for any incompleteness, inaccuracies, or otherwise, or the consequences thereof. All measurements and dimensions are indicative.',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-08-27T23:35:33.339Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.347Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1756337733343_f1n8a',
  source: 'funda',
  duration: '4ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:818:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'Een prachtig gestoffeerd appartement met groot balkon op het zuidwesten in Leidsche Rijn Centrum, grenzend aan het gezellige Brusselplein.\n' +
              '\n' +
              'De woning ligt centraal in Leidsche Rijn Centrum dat is ontworpen om te wonen, te werken en te leven. In een mediterrane sfeer met boulevards en promenades is dit centrum het kloppend hart van de wijk Leidsche Rijn met leuke winkels, gezellige terrasjes en pleinen.\n' +
              '\n' +
              'Hier kunt u ruiken, voelen, proeven en kopen. Beleving staat centraal. Daarnaast bevat het centrum een eigen NS-station en busstation. Er zijn niet alleen culturele voorzieningen in de buurt zoals de bioscoop en het Berlijnplein, maar ook tal van 1e lijns zorgvoorzieningen. Al deze voorzieningen zijn op loopafstand van het appartementencomplex.\n' +
              '\n' +
              'Begane grond:\n' +
              '\n' +
              'Centrale entree met brievenbussen en een bellentableau, met toegang tot de lift en het trappenhuis evenals de berging en de parkeergarage inclusief de overdekte fietsenstalling en eigen parkeerplek.\n' +
              '\n' +
              'Indeling:\n' +
              '\n' +
              'Het appartement is gesitueerd op de 2e verdieping. Entree in de hal die toegang geeft tot de andere ruimtes; een apart toilet met fonteintje, de badkamer met douche en wastafel met opberglades, een berging, de 2 slaapkamers met kiep/kantelraam en de woonkamer met semi-open keuken.\n' +
              '\n' +
              'De woonkamer biedt uitzicht op de Grauwaartsingel en geeft toegang tot het balkon. De semi-open keuken is van alle gemakken voorzien met inbouwapparatuur zoals een vaatwasser, koel-vries combinatie, combi-oven, kookplaat en afzuigkap. Daarnaast is er een berging met opstelplaats voor de wasmachine en de droger, de boiler en de vloerverwarmingsunit is eveneens daar.\n' +
              '\n' +
              'Het appartement beschikt over een moderne lichte laminaatvloer, offwhite gordijnen, de badkamer en het toilet hebben moderne accenten. Het appartement is gebouwd met de nieuwste, meest duurzame materialen en de afwerking is hoogwaardig en luxe. Tevens is het appartement zeer energiezuinig en heeft energielabel A; zeer goede isolatie, vloerverwarming- en koeling en een warmte-terugwininstallatie.\n' +
              '\n' +
              'Bijzonderheden:\n' +
              '\n' +
              'Huurprijs is inclusief de servicekosten en exclusief gas, water, elektra, internet, tv en gemeentelijke belastingen;\n' +
              '\n' +
              'Er wordt een inkomenscheck gedaan;\n' +
              '\n' +
              'Waarborg staat gelijk aan twee keer de maand huur;\n' +
              '\n' +
              'Huisdieren zijn niet toegestaan;\n' +
              '\n' +
              'Roken niet toegestaan;\n' +
              '\n' +
              'Niet geschikt voor studenten;\n' +
              '\n' +
              'Vloerverwarming via stadsverwarming;\n' +
              '\n' +
              'Woning kan per 1 september 2025 worden betrokken.\n' +
              '\n' +
              'Deze informatie is met de nodige zorgvuldigheid samengesteld. Geen aansprakelijkheid wordt aanvaard voor enige onvolledigheid, onjuistheid of anderszins, of de consequenties daarvan. Alle maten en afmetingen zijn indicatief.\n' +
              '\n' +
              '---------------------------------------------\n' +
              '\n' +
              'A beautifully furnished apartment with a large southwest-facing balcony in Leidsche Rijn Centrum, adjacent to the lively Brusselplein.\n' +
              '\n' +
              'The property is centrally located in Leidsche Rijn Centrum, designed for living, working, and enjoying life. With Mediterranean-inspired boulevards and promenades, this center is the heart of the Leidsche Rijn district, offering charming shops, cozy terraces, and squares. You can experience and enjoy everything here: the sights, smells, tastes, and experiences. Additionally, the center has its own NS train station and bus station. Nearby are not only cultural amenities like the cinema and Berlijnplein, but also many primary healthcare facilities. All these amenities are within walking distance of the apartment complex.\n' +
              '\n' +
              'Ground floor: Central entrance with mailboxes and an intercom panel, with access to the elevator and stairwell, as well as the storage area and parking garage, which includes a covered bike shed and your own parking space.\n' +
              '\n' +
              'Layout: The apartment is located on the 2nd floor. Entry through the hall, which provides access to the other rooms: a separate toilet with a small sink, the bathroom with a shower and sink with storage drawers, a storage room, two bedrooms with tilt/turn windows, and the living room with a semi-open kitchen. The living room overlooks the Grauwaartsingel and gives access to the balcony. The semi-open kitchen is fully equipped with built-in appliances, including a dishwasher, fridge-freezer combination, combi oven, hob, and extractor hood. There is also a storage room with space for a washing machine and dryer, as well as the boiler and floor heating unit. The apartment features a modern light laminate floor, off-white curtains, and the bathroom and toilet have modern accents. The apartment is built with the latest, most sustainable materials, and the finish is high-quality and luxurious. Furthermore, the apartment is very energy-efficient with energy label A; it has excellent insulation, floor heating and cooling, and a heat recovery system.\n' +
              '\n' +
              'Special features:\n' +
              '\n' +
              'Rent includes service costs and excludes gas, water, electricity, internet, TV, and municipal taxes;\n' +
              '\n' +
              'Income check will be performed;\n' +
              '\n' +
              "Deposit is equivalent to two months' rent;\n" +
              '\n' +
              'Pets are not allowed;\n' +
              '\n' +
              'Smoking is not allowed;\n' +
              '\n' +
              'Not suitable for students;\n' +
              '\n' +
              'Floor heating via district heating;\n' +
              '\n' +
              'Available from September 1, 2025.\n' +
              '\n' +
              'This information has been compiled with due care. No liability is accepted for any incompleteness, inaccuracies, or otherwise, or for the consequences thereof. All dimensions and measurements are indicative.',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-08-27T23:35:33.347Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.352Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.357Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.363Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.369Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.375Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1756337733373_mvbco',
  source: 'funda',
  duration: '2ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:818:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: '123WONEN DÉ VERHUURMAKELAAR VAN UTRECHT BIEDT AAN VOOR TIJDELIJKE VERHUUR VAN 9 MAANDEN:\n' +
              '\n' +
              'Charmant herenhuis aan de Badstraat – Utrecht (Buiten Wittevrouwen).\n' +
              '\n' +
              'Ben jij op zoek naar een ruim, sfeervol en karakteristiek woonhuis op een toplocatie in Utrecht? Dit prachtige herenhuis aan de Badstraat biedt comfortabel en stijlvol wonen in de populaire wijk Buiten Wittevrouwen. Met vier ruime slaapkamers, moderne badkamer, luxe keuken met groot eiland en een zonnige onderhoudsarme buitenruimte, plus een balkon op de eerste verdieping, is dit de ideale woning voor een stel, expat-gezin of werkende professional.\n' +
              '\n' +
              'Op loopafstand van het Griftpark en het Wilhelminapark, diverse winkels, supermarkten en horeca, slechts enkele minuten fietsen naar het centrum, CS Utrecht en Universiteit. Uitstekend bereikbaar met OV en nabij uitvalswegen (A27/A28)\n' +
              '\n' +
              'INDELING:\n' +
              '\n' +
              'BEGANE GROND:\n' +
              '\n' +
              'Bij binnenkomst in de hal heb je de trapopgang naar de 1e verdieping, toilet en de toegang naar de ruime woonkamer en open keuken. De vloer op de benedenverdieping is voorzien van een houten vloer. De woonkamer is ruim en licht en de open keuken is voorzien van een groot eiland met de gootsteen, inbouwkoelkast/vriezer, inbouw magnetron en oven en een 6-pits gaskookplaat en meer dan voldoende opbergruimte.\n' +
              '\n' +
              'EERSTE VERDIEPING:\n' +
              '\n' +
              'Op deze verdieping vindt je twee in grote variërende slaapkamers, de ruime badkamer met een bad en aparte douche en de trapopgang naar de tweede verdieping. De vloer op deze verdieping is net als op de begane grond een houten vloer. Op deze verdieping is ook een balkon.\n' +
              '\n' +
              'TWEEDE VERDIEPING:\n' +
              '\n' +
              'Op deze verdieping vindt je nog 2 in grote varierende slaapkamers, waar genoeg ruimte is om te slapen en/of te werken.\n' +
              '\n' +
              'BIJZONDERHEDEN:\n' +
              '\n' +
              '- De woning is beschikbaar per 6 oktober 2025;\n' +
              '- De huurperiode is een vaste periode van 9 maanden;\n' +
              '- Huurprijs is € 2400,- per maand;\n' +
              '- Waarborgsom 2 maanden huur;\n' +
              '- De huurprijs is exclusief G/W/E, tv/internet en Gemeentelijke heffingen deel huurder(s);\n' +
              '- De woning is gestoffeerd;\n' +
              '- Energielabel E;\n' +
              '- Parkeergelegenheid voor de deur (betaald);\n' +
              '- Niet roken;\n' +
              '- Geen huisdieren;\n' +
              '- Geen studenten / woningdelers;\n' +
              '\n' +
              'GEEN COURTAGE! 123WONEN WERKT ALS VERHUURMAKELAAR VOOR DE EIGENAAR;\n' +
              'BEZICHTIGINGEN KUNNEN ALLEEN ONLINE WORDEN AANGEVRAAGD.\n' +
              '\n' +
              'Vind je deze woning op een website waarop wij doorplaatsen?\n' +
              'Kijk dan op onze eigen website:  voor ons actuele aanbod!\n' +
              '\n' +
              'Vind je deze woning op een website waar wij doorverkopen?\n' +
              'Kijk dan op onze eigen website:  voor ons actuele aanbod!\n' +
              '\n' +
              'Neem contact op\n' +
              '\n' +
              '123WONEN UTRECHT\n' +
              'Ramstraat 31\n' +
              '3581 HD Utrecht\n' +
              '\n' +
              'T 030 - 760 33 94\n' +
              'E \n' +
              '\n' +
              '----\n' +
              '\n' +
              '123WONEN DÉ VERHUURMAKELAAR VAN UTRECHT OFFERS FOR TEMPORARY RENTAL OF 9 MONTHS:\n' +
              '\n' +
              'Charming townhouse on the Badstraat - Utrecht (Buiten Wittevrouwen).\n' +
              '\n' +
              'Are you looking for a spacious, attractive and characteristic house in a prime location in Utrecht? This beautiful townhouse on Badstraat offers comfortable and stylish living in the popular neighborhood of Buiten Wittevrouwen. With four spacious bedrooms, modern bathroom, luxury kitchen with large island and a sunny low maintenance outdoor area, plus a balcony on the second floor, this is the ideal home for a couple, expat family or working professional.\n' +
              '\n' +
              'Within walking distance of the Griftpark and Wilhelminapark, several stores, supermarkets and restaurants, just minutes by bike to the city center, CS Utrecht and University. Easily accessible by public transport and near roads (A27/A28)\n' +
              '\n' +
              'LAYOUT:\n' +
              '\n' +
              'GROUND FLOOR:\n' +
              '\n' +
              'Upon entering the hall you have the staircase to the 1st floor, toilet and access to the spacious living room and open kitchen. The floor on the ground floor has a wooden floor. The living room is spacious and bright and the open kitchen has a large island with the sink, built-in fridge/freezer, built-in microwave and oven and a 6-burner gas hob and more than enough storage space.\n' +
              '\n' +
              'FIRST FLOOR:\n' +
              '\n' +
              'On this floor you will find two bedrooms varying in size, the spacious bathroom with a tub and separate shower and the staircase to the second floor. The floor is also a wooden floor. There is also a balcony on this floor.\n' +
              '\n' +
              'SECOND FLOOR:\n' +
              '\n' +
              'On this floor you will find the 2 bedrooms varying in size, where there is enough space to sleep and/or work.\n' +
              '\n' +
              'PARTICULARS:\n' +
              '\n' +
              '- The property is available from October 6, 2025;\n' +
              '- The rental period is a fixed period of 9 months;\n' +
              '- Rent is € 2400,- per month;\n' +
              '- Security deposit 2 months rent;\n' +
              '- The rent is excluding G/W/E, TV/internet and Municipal levies share tenant(s);\n' +
              '- The property is unfurnished;\n' +
              '- Energy label E;\n' +
              '- Parking in front of the door (paid);\n' +
              '- No smoking;\n' +
              '- No pets;\n' +
              '- No students / house sharers\n' +
              '\n' +
              'NO COMMISSION! 123WONEN WORKS AS A RENTAL AGENT FOR THE OWNER;\n' +
              'VIEWINGS CAN ONLY BE REQUESTED ONLINE.\n' +
              '\n' +
              'Do you find this property on a website on which we post?\n' +
              'Please check our own website:  for our current offer!\n' +
              '\n' +
              'Do you find this property on a website where we resell?\n' +
              'Please check our own website:  for our current offer!\n' +
              '\n' +
              'Contact us\n' +
              '\n' +
              'EXPATRENTALS HOLLAND UTRECHT\n' +
              'Ramstraat 31\n' +
              '3581 HD Utrecht\n' +
              '\n' +
              'T 030 - 760 33 94\n' +
              'E',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-08-27T23:35:33.375Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.382Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1756337733380_gifj2',
  source: 'funda',
  duration: '2ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:818:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'For English below\n' +
              '\n' +
              'Let op! Alleen voor (deel- of voltijd) studenten beschikbaar!\n' +
              '\n' +
              'Sfeervolle 3-kamer (waarvan 1 slaapkamer) woning (ca. 70 m2) met grote achtertuin met berging en achterom in de Rivierenwijk in Utrecht!\n' +
              '\n' +
              'In de Rivierenwijk woon je op een ideale locatie, met alle voorzieningen binnen handbereik en het centrum van Utrecht op korte afstand. Hier vind je echt iets bijzonders – een plek zoals geen ander.\n' +
              '\n' +
              'Deze charmante tussenwoning uit de jaren 30 ligt in een rustige straat en valt op door de mooie lichtinval, de nette afwerking en de combinatie van authentieke elementen met modern comfort. De woning is voorzien van een royale woonkeuken, klassieke en suite deuren en een heerlijke, diepe achtertuin op het oosten.\n' +
              '\n' +
              'De Rivierenwijk is een gewilde en levendige buurt met een dorps karakter, op slechts enkele minuten fietsen van de binnenstad. Je woont hier tussen de gezellige straatjes, met volop voorzieningen zoals winkels, cafés, scholen en sportfaciliteiten in de buurt. NS-station Vaartsche Rijn en het populaire Ledig Erf liggen op loopafstand, en ook Utrecht Centraal is snel te bereiken. Parkeren kan met parkeervergunning te verkrijgen bij de gemeente Utrecht of betaald parkeren.\n' +
              '\n' +
              'Indeling\n' +
              '\n' +
              'Begane grond:\n' +
              'Entree, hal, meterkast en modern toilet. Vanuit de hal kom je in de ruime woonkeuken aan de voorzijde van de woning, voorzien van een royaal keukenblok met o.a. vaatwasser, combi-oven/magnetron en een koelkast met vriesvak. De lichte lamelparketvloer en de originele ensuite deuren geven de ruimte karakter. Aan de achterzijde bevindt zich de uitgebouwde woonkamer, met een lichtkoepel in het plafond en openslaande deuren naar de tuin – een heerlijke, lichte leefruimte!\n' +
              '\n' +
              'Eerste verdieping:\n' +
              'Overloop met toegang tot een aparte kamer met wasmachineaansluiting, doorlopend naar een royale slaapkamer met hoog plafond en praktische kastruimte. Indien gewenst is deze kamer eenvoudig ook te gebruiken als werkkamer.\n' +
              '\n' +
              'De verzorgde badkamer is uitgerust met een ligbad/douche, tweede toilet en een wastafelmeubel.\n' +
              '\n' +
              'Tweede verdieping:\n' +
              'Vliering met bergruimte en opstelplaats voor de Cv-combiketel.\n' +
              '\n' +
              'Tuin:\n' +
              'De onderhoudsvriendelijke achtertuin van circa 64 m² ligt op het oosten en is bereikbaar via een achterom. Dankzij de diepte van de tuin is er op elk moment van de dag wel een zonnig plekje te vinden. Er staat een stenen berging – ideaal voor fietsen of tuingereedschap.\n' +
              '\n' +
              'Bijzonderheden:\n' +
              '- Huurprijs: € 2.125,- excl. g/w/e en gemeentelijke (huurders-)lasten per maand;\n' +
              '- Alleen voor (deel- of voltijd) studenten beschikbaar/alleenstaand of stel;\n' +
              '- Woonoppervlakte: circa 70 m²;\n' +
              '- Tuin op het oosten, ca. 60 m²\n' +
              '- Energielabel B;\n' +
              '- Huurperiode voor 24 maanden;\n' +
              '- Verwarming en warm water via C.V.-ketel;\n' +
              '- Waarborgsom 2 maanden huur;\n' +
              '- Beschikbaar: 1 oktober 2025.\n' +
              '\n' +
              'Protocol toewijzing kandidaat-huurders: \n' +
              '------------\n' +
              '\n' +
              'Attention! Only available for (part-time or full-time) students!\n' +
              '\n' +
              'Charming 3-room house (approx. 70 m²) (1 large bedroom) with a large back garden with a storage shed and rear access in the Rivierenwijk district of Utrecht!\n' +
              '\n' +
              "In the Rivierenwijk district, you'll live in an ideal location, with all amenities within easy reach and the city center of Utrecht just a short distance away. Here you'll find something truly special – a place like no other.\n" +
              '\n' +
              'This charming 1930s terraced house is located on a quiet street and stands out for its beautiful natural light, neat finish, and combination of authentic features with modern comforts. The house features a spacious kitchen/diner, classic en suite doors, and a lovely, deep east-facing back garden.\n' +
              '\n' +
              "The Rivierenwijk district is a desirable and vibrant neighborhood with a village character, just a few minutes' bike ride from the city center. You'll live here among charming streets, with plenty of amenities such as shops, cafes, schools, and sports facilities nearby. Vaartsche Rijn train station and the popular Ledig Erf shopping center are within walking distance, and Utrecht Central Station is also easily accessible. Parking is available with a permit available from the municipality of Utrecht, or there's paid parking.\n" +
              '\n' +
              'Layout\n' +
              '\n' +
              'Ground floor:\n' +
              'Entrance hall, meter cupboard, and modern toilet. From the hall, you enter the spacious kitchen/diner at the front of the house, equipped with a generously sized kitchen unit including a dishwasher, combination oven/microwave, and a refrigerator with freezer compartment. The light laminated parquet flooring and the original ensuite doors give the space character. At the rear is the extended living room, with a skylight in the ceiling and French doors leading to the garden – a wonderful, bright living space!\n' +
              '\n' +
              'First floor:\n' +
              'Landing with access to a separate room with a washing machine connection, leading to a spacious bedroom with a high ceiling and practical closet space. This room could easily be used as a study if desired.\n' +
              '\n' +
              'The well-maintained bathroom is equipped with a bath/shower, a second toilet, and a vanity unit.\n' +
              '\n' +
              'Second floor:\n' +
              'Attic with storage space and the central heating boiler. Garden:\n' +
              "The low-maintenance backyard of approximately 64 m² faces east and is accessible via a rear entrance. Thanks to its depth, there's always a sunny spot to be found. There's a brick shed – ideal for bicycles or garden tools.\n" +
              '\n' +
              'Garden:\n' +
              "The low-maintenance backyard of approximately 64 m² faces east and is accessible via a rear entrance. Thanks to its depth, there's always a sunny spot to be found. There's a brick shed – ideal for bicycles or garden tools.\n" +
              '\n' +
              'Details:\n' +
              '- Rent: € 2.125,- per month, excluding utilities and municipal (tenant) charges;\n' +
              '- Available only for (part-time or full-time) students/singles or couples;\n' +
              '- Living area: approximately 70 m²;\n' +
              '- East-facing garden, approximately 60 m²;\n' +
              '- Energy label B;\n' +
              '- 24-month lease;\n' +
              '- Heating and hot water via central heating boiler;\n' +
              '- Deposit 2 months rent;\n' +
              '- Available: October 1, 2025.',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-08-27T23:35:33.382Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.388Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.393Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.399Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.404Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.408Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.415Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.420Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.424Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.430Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.437Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.444Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.450Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.455Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1756337733453_4ajww',
  source: 'funda',
  duration: '2ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:818:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'Omschrijving: \n' +
              '\n' +
              'Instapklaar, charmant en centraal in de binnenstad van Utrecht gelegen dubbel bovenhuis met zowel nieuwe àls oude stijlkenmerken. Aan deze erg gewilde en tevens rustige woonstraat in de wijk Hoogh Boulandt bevindt zich dit complete en zeer leefbare appartement. De woning biedt sfeer, comfort en veiligheid. Het Centraal Station, Station Vaartsche Rijn, het Ledig Erf, ’t Wed en de voorzieningen in het centrum, aan de Oudegracht en in de Twijnstraat zijn allemaal binnen 10 minuten lopend (of ca. 2-3 minuten per fiets) te bereiken. Ga je niet voor winkelen en horeca dan ligt het singelpark om de hoek om te recreëren. De klassieke details, modern gemak en de geweldige ligging maken dat je hier nooit meer weg wilt!\n' +
              '\n' +
              'Indeling:\n' +
              'Op de beletage binnengekomen loop je direct de trap op naar de woonverdieping. Aan de hal grenzen een kleine kamer aan de voorzijde, een WC en de woonkamer met open keuken. Deze verdieping biedt hoge plafonds en een mooie houten vloerafwerking. De woonkamer is opgedeeld in drie delen: een zitkamer, een eetkamer en een leeskamer/serre. De zitkamer en de eetkamer zijn vanuit de hal apart toegankelijk en af te sluiten middels stijlvolle en suite deuren. De serre is ook af te sluiten door glazen schuifdeuren. De moderne open keuken is door het spoeleiland praktisch van opzet en is van diverse (inbouw)apparatuur voorzien. De keuken biedt toegang tot het ruime overkapte balkon/terras. Hier is het door het hoge plafond fijn borrelen en tafelen in de middag- en avondzon (ZW ligging).\n' +
              'Via de trap in de hal kom je op de slaap-/werkverdieping (bovenste woonlaag). In het trapgat bevindt zich de overgedimensioneerde airconditioning die naar gelang je kamerdeuren open/dicht doet de achterliggende vertrekken al dan niet koelt of verwarmt (in aanvulling op de CV installatie). Naast de trap ligt het ketelhok met wasmachine. Aan de overloop grenst ook badkamer voorzien van regendouche, WC en dubbele wastafel. Er zijn drie slaapvertrekken waarvan er nu één dienst doet als werkkamer. In de inbouwkasten is een condensdroger opgenomen. Achter deze werkkamer bevindt zicht het royale dakterras dat aan de groene binnentuin grenst en al tegen het eind van de ochtend tot ca. 20.30 uur in de zomerzon ligt. Het is hier heerlijk loungen!\n' +
              '\n' +
              'Omgeving en parkeren:\n' +
              'De locatie en de omgeving waar het appartement is gelegen is ideaal voor mensen zonder auto, maar ook door bewoners met een auto kan er moeiteloos geparkeerd worden in de straat (eenrichtings-/bestemmingsverkeer, parkeervergunning aanvragen). \n' +
              'De woning is zeer goed gelegen ten opzichte van het Centrum, de dichtbij gelegen NS stations en ook ten opzichte van de uitvalswegen, denk aan Catharijnesingel richting o.a. Rubenslaan/Waterlinieweg (richting A27-A28), Jutfaseweg/Europalaan (richting A27-A12/A2) en overige. \n' +
              '\n' +
              'Bijzonderheden:\n' +
              'Ingangsdatum: 01-09-2025\n' +
              'Duur huurperiode: onbepaalde tijd met een minimumduur van 12 maanden\n' +
              'Opleveringsniveau: de woning wordt gemeubileerd opgeleverd op een basisniveau. In overleg is een luxer niveau mogelijk\n' +
              'Aanwezig: airconditioning (warm/koud)\n' +
              'Veiligheid: een kluis en een NVR videosysteem (voorzien van diverse hoogwaardige camera’s) zijn ingebouwd\n' +
              'Parkeren: op straat (geen wachtlijst)\n' +
              'Borg: twee maanden huur\n' +
              'Huisdieren: niet toegestaan\n' +
              'Woningdelers/meerpersoonshuishouden niet toegestaan\n' +
              'De beelden zijn indicatief. Tijdens een bezichtiging kunt u alles zien, vragen en een uitgebreide toelichting krijgen wat deze woning biedt en hoe u deze nog deels naar uw hand kunt zetten!\n' +
              '\n' +
              'Kenmerken: \n' +
              '* Kale huurprijs € 3.100 per maand \n' +
              '* Kosten voor meubilering € 150,- per maand (basisniveau)\n' +
              '* Geen servicekosten. Wel onbereikbare ramen wassen 4x p/j (nu € 40 per keer)\n' +
              '* Excl. gas/water/elektra/internet/gemeentelijke gebruikersheffingen\n' +
              '* Glasvezel internet is beschikbaar op dit adres\n' +
              '* Dubbel glas, eigen entree. Bouwjaar 1905\n' +
              '\n' +
              '* ENGLISH VERSION * \n' +
              '\n' +
              'Discribtion\n' +
              "Turnkey, charming and centrally located in Utrecht's city center, this duplex apartment features both new and period features. This fully equipped and very livable apartment is located on this highly desirable and quiet residential street in the Hoogh Boulandt neighborhood. It offers ambiance, comfort and security. Central Station, Vaartsche Rijn Station, Ledig Erf, 't Wed and the amenities in the city center, along Oudegracht and in Twijnstraat are all within a 10-minute walk (or approximately 2-3 minutes by bike). If shopping and dining aren't your thing, the Singelpark is just around the corner for recreation. The classic details, modern conveniences, and fantastic location will make you never want to leave!\n" +
              '\n' +
              'Floorplan\n' +
              'Entering the raised ground floor, you immediately ascend the stairs to the living area. Adjacent to the hall are a small room at the front, a toilet, and the living room with an open-plan kitchen. This floor boasts high ceilings and beautiful wooden flooring. The living room is divided into three areas: a sitting room, a dining room, and a reading room/conservatory. The sitting room and dining room are accessible separately from the hall and can be separated by stylish en-suite doors. The conservatory can also be closed off with sliding glass doors. The modern open-plan kitchen features a practical layout thanks to its island sink and is equipped with various (built-in) appliances. The kitchen opens onto the spacious covered balcony/terrace. The high ceilings make it ideal for enjoying drinks and meals in the afternoon and evening sun (southwest orientation).\n' +
              '\n' +
              "The stairs in the hall lead to the bedrooms/study (top floor). The oversized air conditioning unit is located in the stairwell and cools or heats the rooms behind it depending on whether you open or close the doors (in addition to the central heating system). Next to the stairs is the boiler room with a washing machine. The bathroom has a rain shower, a toilet and a double sink. There are three bedrooms, one of which currently serves as a study. A condenser dryer is integrated into the built-in wardrobes. Behind this study is the spacious roof terrace, which borders the green courtyard and enjoys the summer sun from late morning until around 8:30 PM. It's a wonderful place to lounge!\n" +
              '\n' +
              'ENVIRONMENT AND PARKING\n' +
              'The location and surrounding area of the apartment are ideal for those without a car, but residents with a car can also easily park on the street (one-way/destination traffic; parking permit required). The apartment is very well-located near the city center, the nearby train stations and arterial roads, such as Catharijnesingel towards Rubenslaan/Waterlinieweg (towards the A27-A28), Jutfaseweg/Europalaan (towards the A27-A12/A2) and others.\n' +
              '\n' +
              'DETAILS\n' +
              'Start date: September 1, 2025\n' +
              'Lease term: indefinite with a minimum of 12 months\n' +
              'Finishing level: the property is furnished and furnished to a basic standard. A more luxurious level is possible upon consultation\n' +
              'Included: air conditioning (hot/cold)\n' +
              'Security: a safe and an NVR video system (equipped with high-quality cameras) are built-in\n' +
              'Parking: on the street (no waiting list)\n' +
              "Deposit: two months' rent\n" +
              'Pets: not allowed\n' +
              'Students/room sharers: not allowed\n' +
              'The images are for illustrative purposes only. During a viewing, you can see everything, ask questions, and receive a detailed explanation of what this property has to offer and how you can further customize it to your liking!\n' +
              '\n' +
              'FEATURES\n' +
              '* Rent €3,100 per month \n' +
              '* Contribution for fully furnishment € 150 per month (basic level furnishings)\n' +
              '* No service charges. Inaccessible windows are cleaned 4 times a year (currently €40 per wash)\n' +
              '* Excluding gas, water, electricity, internet and municipal user charges\n' +
              '* Fiber optic internet is available at this address\n' +
              '* Double glazing, private entrance. Built in 1905',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-08-27T23:35:33.455Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.461Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.466Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.471Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.475Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.482Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.487Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.492Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1756337733491_ixh7d',
  source: 'funda',
  duration: '1ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:818:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'Heerlijke en ruime 5 kamer gezinswoning met tuin op het Zuiden en  en energielabel A.\n' +
              '\n' +
              'Gelegen in in een rustige en tevens kindvriendelijke woonwijk in Leidsche Rijn, vlakbij Het Lint, de Haarrijnseplas en winkelcentrum Terwijde. \n' +
              'Tevens is de bushalte met een directe verbinding met de stad op een paar minuten lopen. Ook de snelwegen en treinstation Terwijde zijn binnen een zeer afzienbare tijd te bereiken. \n' +
              '\n' +
              'De woning is gelegen in een rustig woningblok met een eigen parkeerplaats (overige plekken vrij parkeren) in een soort van hofje.\n' +
              '\n' +
              'De woning is ideaal voor een een stel of een gezin met (meer) kinderen gezien het aantal kamers.\n' +
              'Deze goed afgewerkte twee-laags woning kenmerkt zich door de grote raampartijen aan de achterzijde, heerlijke tuin op het Zuiden met achterom en 4 prima slaapkamers op de 1e verdieping.\n' +
              '\n' +
              'Indeling\n' +
              'Een opvallend ruime entree met toegang tot een netjes afgewerkte toilet met hangend closet en een doorgang naar de speelse leefruimte met half deels gesloten keuken. \n' +
              '\n' +
              'De woonkamer is samen met de keuken voorzien van mooie wit gesausde muren en een sfeervolle laminaatvloer welke netjes is doorgelegd. De ruimte is bijna 6 meter breed en handig ingedeeld in een zit en eetruimte. Aansluitend bevindt zich de keuken welke afgesloten is middels een muur met deur waardoor deze al dan wel of niet bij de woonkamer betrokken kan worden. Doordat er een deur met glas is geplaatst valt de doorzon verlichting lekker door de keuken. \n' +
              'De hoek vormige keuken is aan de voorzijde gelegen en beschikt over de meest noodzakelijke apparaten (o.a. vaatwasser) en heeft zowel veel aanrecht- dan wel kastruimte. De keuken is voorzien van neutrale witte kastjes met een donker blad. De tuin is handig te bereiken vanuit de woonkamer. \n' +
              '\n' +
              'Eerste verdieping\n' +
              'Op de 1e verdieping zijn de 4 slaapkamer en nieuw aangelegde badkamer met ruime inloopdouche, dubbele wastafel en zwevend toilet gelegen. De overloop is groot waardoor hier nog een extra werkplek is gecreëerd. \n' +
              '\n' +
              'Tuin\n' +
              'Heerlijke en netjes onderhouden achtertuin op het Zuiden van ca. 50m2 welke recentelijk nog voorzien is van nieuwe betegeling. Vrijstaande houten schuur aanwezig met betonnen ondervloer en licht en elektra. De tuin kan ook via een achterom bereikt worden. \n' +
              'Ook de voortuin is voorzien dezelfde tegels. Voor en achter bevinden zich een buitenkraan.  \n' +
              '\n' +
              'Overig:\n' +
              'In 2024 zijn de volgende zaken vernieuwd: Keukenkastjes voorzien van nieuwe witte folie, achter en voortuin voorzien van nette tegels, alle ramen op de eerste verdieping zijn voorzien van vliegenhorren, koel-vriescombinatie in de keuken, schilderwerk muren binnen, eetkamer stoelen, plafondlamp boven eetkamer, stofzuiger, buitenkranen, etc. \n' +
              '\n' +
              'Bijzonderheden\n' +
              '- Beschikbaar: 01 september 2025;\n' +
              '- Bij voorkeur een periode van 24 -36 maanden;\n' +
              '- De woning wordt gestoffeerd opgeleverd met een aantal meubels; eventueel kunnen er enkele eigen spullen worden vervangen naar wens van de nieuwe huurder of overgenomen worden van de huidige bewoner;\n' +
              "- De foto's zijn van de woning in verhuurde staat, dus de meeste meubels niet van toepassing, dan wel vernieuwd;\n" +
              '- Huurprijs is 2850 euro exclusief gebruikerslasten huurder (gas, water en elektra, gebruikersdeel gemeentelijke heffingen en TV/Internet );\n' +
              '- Niet te huur voor studenten of woningdelers; \n' +
              '- nabij alle faciliteiten, uitvalswegen en natuur;\n' +
              '- woning word verwarmd middels stadsverwarming;\n' +
              '- Eigen parkeerplaats voor de deur.\n' +
              '\n' +
              '----------------------------------------------------------------------------------------------------------------------\n' +
              'ENGLISH:\n' +
              '\n' +
              'Spacious and Delightful 5-Room Family Home with South-Facing Garden and Energy Label A**\n' +
              '\n' +
              "Located in a quiet and child-friendly residential area in Leidsche Rijn, close to *Het Lint*, *Haarrijnse Plas*, and *Terwijde* shopping center. A bus stop with a direct connection to the city center is just a few minutes' walk away. Highways and *Terwijde* train station are also easily and quickly accessible.\n" +
              '\n' +
              'The property is situated in a peaceful housing block with its own private parking space (additional free parking available) in a courtyard-like setting.\n' +
              '\n' +
              'The home is ideal for a couple or a family with (more) children, given the number of rooms.\n' +
              'This well-finished two-story house features large rear-facing windows, a lovely south-facing garden with back entrance, and four good-sized bedrooms on the first floor.\n' +
              '\n' +
              'Layout\n' +
              'A remarkably spacious entrance hall gives access to a neatly finished toilet with wall-mounted closet, and a passage to the playfully designed living area with a partially enclosed kitchen.\n' +
              '\n' +
              'The living room and kitchen feature clean white painted walls and an attractive laminate floor that continues seamlessly throughout the space. The room is nearly 6 meters wide and conveniently laid out with a sitting and dining area.\n' +
              'The kitchen is partially enclosed by a wall with a door, allowing it to be integrated into or separated from the living room. The door includes a glass panel, allowing natural light to flow through to the kitchen.\n' +
              '\n' +
              'The L-shaped kitchen is situated at the front of the house and comes equipped with essential appliances (including a dishwasher) and offers ample countertop and cabinet space. The kitchen has neutral white cabinetry with a dark countertop. The garden is easily accessible from the living room.\n' +
              '\n' +
              'First Floor\n' +
              'The first floor comprises four bedrooms and a newly renovated bathroom featuring a spacious walk-in shower, double sink, and wall-mounted toilet.\n' +
              'The landing is large enough to accommodate an additional workspace.\n' +
              '\n' +
              'Garden\n' +
              'A well-maintained, south-facing backyard of approx. 50m², recently updated with new paving. There is a detached wooden shed with a concrete base, lighting, and electricity. The garden can also be accessed via a back entrance.\n' +
              'The front garden is paved with the same tiles. Outdoor taps are available at both the front and rear.\n' +
              '\n' +
              'Additional Information\n' +
              'The following items were renewed in 2024: kitchen cabinet fronts with new white film, new paving in front and back gardens, insect screens on all first-floor windows, fridge-freezer in kitchen, interior wall paint, dining chairs, ceiling light above dining area, vacuum cleaner, outdoor taps, etc.\n' +
              '\n' +
              'Details\n' +
              '* Not available for students or home sharers;\n' +
              '* Available: September 1, 2025;\n' +
              '* Preferred rental period: 24–36 months;\n' +
              '* The property is rented semi-furnished with some furniture included; some items can be replaced by the new tenant or taken over from the current resident;\n' +
              '* Photos shown are from the rented state; most furniture shown is not included or has since been updated;\n' +
              '* Rent: €2,850 excluding user costs (gas, water, electricity, municipal user taxes, TV/internet);\n' +
              '* Close to all amenities, highways, and nature areas;\n' +
              '* Heating via district heating system;\n' +
              '* Private parking space in front of the house;',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-08-27T23:35:33.492Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.499Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.504Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1756337733502_dgo0f',
  source: 'funda',
  duration: '2ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:818:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'In het bruisend hart van Leidsche Rijn ligt op een schitterende locatie appartementencomplex “De Rossfelt”, de Vinex wijk van Utrecht! Leidsche Rijn is voorzien van een goede infrastructuur waardoor het complex, zowel met openbaar vervoer als de auto, goed te bereiken is en het van hieruit goed reizen is naar alle kanten van het land. De Rossfelt ligt midden in het centrum van Leidsche Rijn met een goed en compleet winkelaanbod. In de Rossfelt bevindt zich dit fraaie, luxe en aardgas loze vrije 3-kamer appartement.\n' +
              '\n' +
              'De overzichtelijke hal geeft gelijk een gevoel van thuiskomen en herbergt de meter- en stadsverwarmingskast in zich, de binnen berging met wasmachine aansluiting naast dat het ook de toegang geeft tot de 2 slaapkamers, het luxe afgewerkte toilet en badkamer welke beide luxe afgewerkt zijn met Villeroy & Boch en Grohekranen. Eenmaal in de lichte woonkamer valt gelijk het oog op de moderne, complete en luxe open Bruynzeel keuken welke is voorzien van Siemens inbouwapparatuur waar de koel- vriescombinatie, vaatwasser, combi-oven, keramische kookplaat en recirculatie afzuigkap dan ook niet in ontbreken. De lichte en ruime woonkamer geeft, net als een van de slaapkamers, toegang tot het fijne balkon, wat een goede toevoeging is voor het woongenot. Dit appartement is goed geïsoleerd, voorzien van HR++ glas en geheel voorzien van vloerverwarming en mechanische ventilatie met het fijne gevolg dat als er juist mee om wordt gegaan de energiekosten beperkt blijven. De appartementen zijn hoogwaardig afgewerkt en voorzien van behang. Kortom een zeer compleet en energiezuinig appartement op een goede en bereikbare locatie in Utrecht waar het fijn wonen en werken is. De eigen berging en parkeerplaats (verplichte afname) maken het plaatje compleet, kortom Leidsche Rijn zorgt voor fijn woongenot!\n' +
              '\n' +
              "Let op: Foto's dienen als impressie. De uitvoering van de badkamer, toilet en keuken kan per appartement verschillen.\n" +
              '\n' +
              'Kan ik deze woning huren?\n' +
              '    €   7.000,- Bruto per maand (één inkomen)\n' +
              '    €   8.000,- Bruto per maand (tweeverdieners)\n' +
              '\n' +
              'Wonen in De Rossfeld\n' +
              'In het bruisend hart van Leidsche Rijn ligt op een schitterende locatie appartementencomplex “De Rossfelt”, de Vinex wijk van Utrecht! Het openbaar vervoer is in deze wijk ruimschoots aanwezig waardoor er een vlotte verbinding is tussen de oude en nieuwe stadswijken van Utrecht. Diverse bruggen en viaducten in Leidsche Rijn zijn gebouwd met aparte rijbanen voor de bus. Het treinstation van Leidsche Rijn bevindt zich op loopafstand van het gebouw. Daarnaast ligt Leidsche Rijn tussen de snelwegen A2 en A12 waardoor het ook met de auto goed bereikbaar is. \n' +
              'Ook aan groen is gedacht binnen de wijk Leidsche Rijn, zo ligt hier het Willem-Alexanderpark op het dak van de Leidsche Rijntunnel. In het zuiden van de wijk bevindt zich de Recreatieplas Strijkviertel, een vroegere zandwinningsplas die is ontstaan in verband met de aanleg van de nabijgelegen autosnelwegen A2, A12 en het knooppunt Oudenrijn. Aan de rand van de wijk ligt het Máximapark, waarvan de oorspronkelijke naam Leidsche Rijnpark was. Het is een centraal park binnen de Vinex-locatie Leidsche Rijn, omringd door de wijk Leidsche Rijn in het oosten, het dorp De Meern in het zuiden en het dorp Vleuten in het westen. \n' +
              'De kers op de taart in deze wijk is het winkelcentrum wat het kloppend hart is van deze fijne woonwijk. Complex De Rossfelt telt 2-3- en 4 luxe kamerappartementen die elk voorzien zijn van hoogwaardige elementen, daarnaast heeft elk appartement zijn eigen balkon en/of terras. In het midden van het complex kan met z’n allen genoten worden van de fijne binnentuin. De appartementen zijn comfortabel en luxe en voorzien van een luxe Bruynzeelkeuken met een Siemens apparatuur zoals een kookplaat, afzuigkap, vaatwasser, combi-oven en koel-vriescombinatie. De complete badkamer is voorzien van Villeroy & Boch sanitair. \n' +
              'Het appartement is hoogwaardig afgewerkt en geheel voorzien van behang en vloerverwarming. De eigen berging en de mogelijkheid tot het huren van een zwerfparkeerplaats maken het plaatje compleet,\n' +
              'kortom Leidsche Rijn zorgt voor fijn woongenot!\n' +
              '\n' +
              'BEZICHTIGINGEN / INFORMATIE: \n' +
              "- Bezichtigingen kunnen ALLEEN op werkdagen tussen 8.30 en 17.00 uur, dus niet 's avonds of in het weekend. \n" +
              '- Telefonisch een bezichtiging plannen of informatie aanvragen is NIET mogelijk. Verhuurder heeft ervoor gekozen de gegadigden zelf rond te leiden en/of van informatie te voorzien. Een bezichtiging of informatie kan enkel worden aangevraagd via Funda of via de website van de verhurende makelaar. \n' +
              '\n' +
              'ENGLISH - VIEWINGS / INFORMATION: \n' +
              '- Viewings can ONLY be scheduled on weekdays between 8:30am and 5:00pm. Appointments in the evenings or on weekends are not possible. \n' +
              '- Planning a viewing and/or requesting information by phone is NOT possible. The landlord has chosen to arrange all the viewings and provide information themselves. A viewing or information can only be requested through Funda or the website of the rental agent.',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-08-27T23:35:33.504Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.511Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.517Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.522Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.527Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.533Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.539Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.545Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.552Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.560Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.567Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:35:33.573Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:35:33'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  listings: 75,
  level: 'info',
  message: 'funda scraping completed',
  timestamp: '2025-08-28 00:35:33'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '933576ms',
  listingsProcessed: 75,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-08-28 00:35:33'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:38:38',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:38:38',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:38:38',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:40:00'
}
{
  component: 'scraper',
  message: 'Running enabled scrapers: funda',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:40:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  listings: 75,
  level: 'info',
  message: 'funda scraping completed',
  timestamp: '2025-08-28 00:41:10'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '970196ms',
  listingsProcessed: 75,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-08-28 00:41:10'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:43:38',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:43:38',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:43:38',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:45:00'
}
{
  component: 'scraper',
  message: 'Running enabled scrapers: funda',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:45:00'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:46:27.868Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:27'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:46:27.881Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:27'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:46:27.887Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:27'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:46:27.892Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:27'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:46:27.896Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:27'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:46:27.902Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:27'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:46:27.907Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:27'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:46:27.912Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:27'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1756338387910_0yzgp',
  source: 'funda',
  duration: '2ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:818:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'Altijd al in een prachtig statig herenhuis willen wonen daterend uit 1910? Deze totaal gerenoveerde en VERDUURZAAMDE bovenwoning met energie label A, dakterras en charmant balkon. Met een totaal woonplezier van 180m2, hoge platfonds, oase van rust in een van de prachtige authentieke Utrechtse wijk. Met een maatvoering van bijna 8.00m bij 11.50m is dit echt een uniek woonhuis. Bij de zeer recente renovatie in 2021 is het huis volledig gerenoveerd, voorzien van een nieuwe indeling, prachtige woon-eetkeuken en nog 5 extra kamers, zoals een mooie eetkamer naast de keuken, waar je direct de mooie breedte van het huis ervaart.\n' +
              '\n' +
              "De woning is gelegen in een rustige wijk met mooie rijen herenhuizen. De Zeeheldenbuurt is ruim opgezet met veel groen voorzieningen en ruime tuinen. Dit woonhuis kijkt uit op de markante vrijstaande villa's die de buurt typeren. Restaurant C’est Ca en Goesting liggen om de hoek en voor de boodschappen ligt het vernieuwde winkelcentrum De Gaard op 3 minuten fietsen. Of als u zin hebt om op zaterdag ochtend naar de markt te willen fietsen, dan bent u binnen 10min in het prachtige Utrechtse centrum. \n" +
              '\n' +
              'Indeling\n' +
              '\n' +
              'Begane grond:\n' +
              '\n' +
              'Ruime entree waar direct de mooie sfeer voelbaar is, hoge plafond, mooie vloer en ruime trap naar boven, omdat de entree zo ruim is, is er voldoende plek om uw goede fietsen binnen te plaatsen. \n' +
              'Eerste verdieping:\n' +
              "Met zoals gezegd een vloeroppervlak van ca. 8.00m bij 11.50m is dit een zeer ruim opgezette verdieping, de vertrekken zijn ruim met het fraaie trappenhuis en de ruime overloop met hier een ruim toilet voorzien van prachtige marmer uit Italië. De woonkamer is van het type kamer-en-suite en ademt ook veel sfeer uit, de keuken ligt aan de achterzijde van de woning en is een indrukwekkend groot kookeiland, mooi midden in de ruimte geplaatst, voorzien van een ruime kastenwand wat het geheel mooi, gebalanceerd en zeer functioneel maakt. Naar achter toe is er een nieuwe glazen pui met openslaande deuren, veel glas wat de woning nog ruimtelijker maakt. Naast de keuken ligt een heerlijke knusse eetkamer waar voldoende ruimte is om heerlijk met uw familie te dineren. Aan de voorzijde naast de woonkamer ligt een extra kamer, deze is nu in gebruik als werkkamer, met aansluitend van een inpandig terras. Dit terras heeft een prachtige vloer, ronde boog en uitzicht op de witte villa's, het is hier heerlijk vertoeven, de zon ligging is zuid, een heerlijke plek van het huis. Aan de achterzijde kijk u juist weer uit op de ruime tuinen van de omgeving met het watertje wat achter het huis loopt, een mooi groen geheel met veel vogels. De muur naar het mooie en statige trappenhuis is open gemaakt, dit is ruimtelijk en laat de mooie breedte zien.\n" +
              '\n' +
              'Tweede verdieping:\n' +
              '\n' +
              'Ook hier tref u weer het mooie formaat van het huis aan, er is weer een ruime overloop met een spectaculair glazen dak. Hierdoor ervaart u een zeer ruimtelijk gevoel en wordt de gehele woning voorzien van natuurlijk licht en warmte van de zon, hier tref je een vaste trap naar het dakterras. Er zijn 3 slaapkamers en een nieuwe ruime badkamer. De badkamer is ook weer zeer modern en gebalanceerd met een ruime inloopdouche met een regen douche, vrijstaand ovaal ligbad en een tweede toilet. Er is een fraai wastafel meubel met twee waskommen, met een druk op de knop worden de ramen gebandeerd en heb je geen gordijnen nodig, een van de mooie gadgets van dit huis. \n' +
              'De slaapkamers zijn allemaal ruim van opzet. De vaste trap brengt je naar een fraai dakterras, een extra plek om buiten te zitten met mooi zicht op de omgeving en zicht op de Dom toren. Een groendak, voorzien van sedum en de plantenbakken zorgen voor de groene beleving. Een ruim terras om heerlijk op een zomerse dag buiten te eten, weer een prachtig geheel! \n' +
              '\n' +
              'Huurprijs:\n' +
              '\n' +
              '3895,00 per maand exclusief nuts voorzieningen.\n' +
              'Huurder is zelf contractant voor alle nutsvoorzieningen. \n' +
              '\n' +
              'Borg:\n' +
              'Staat gelijk aan 2 maanden huur.\n' +
              '\n' +
              'Ingangsdatum:\n' +
              'In overleg\n' +
              '\n' +
              'WILT U DE WONING BEZICHTIGINGEN?\n' +
              'Graag ontvangen wij per e-mail of contactformulier op onze website de volgende informatie:\n' +
              '\n' +
              '1. adres van de woning waarin u bent geïnteresseerd\n' +
              '2. telefoonnummer\n' +
              '3. bent u student, in loondienst of zelfstandig ondernemer?\n' +
              '4. werkende; voor welk bedrijf of organisatie? Studerend; welke studie/welke instelling\n' +
              '5. de gewenste huurperiode (voorkeur startdatum en verwachte huurperiode)\n' +
              '6. aantal bewoners en de relatie onderling (gezin/partners/vrienden)\n' +
              '7. bruto maand of jaarinkomen (eventueel ook dat van de partner)\n' +
              '\n' +
              'Nadat wij bovenstaande gegevens hebben ontvangen zullen wij contact opnemen voor het maken van een afspraak. Wij zullen de kandidaten die als eerst reageren als eerst benaderen.\n' +
              '\n' +
              'Alleen volledige aanvragen zullen in behandeling worden genomen.\n' +
              '\n' +
              'Heeft u vragen of het project of wilt u een bezichtiging plannen. Neem gerust contact op met ons kantoor.\n' +
              '\n' +
              'Deze informatie is door ons met de nodige zorgvuldigheid samengesteld. Geen enkele aansprakelijkheid wordt aanvaard voor enige onvolledigheid, onjuistheid of anderszins, of de consequenties daarvan. Alle maten en afmetingen zijn indicatief. De woning is niet NEN ingemeten en hierdoor zijn kleine afwijking mogelijk.',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-08-27T23:46:27.912Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-08-28 00:46:27'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:46:27.918Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:27'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:46:27.924Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:27'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:46:27.930Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:27'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:46:27.937Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:27'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1756338387935_0sjiz',
  source: 'funda',
  duration: '2ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:818:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'Let op: alleen bezichtigingsaanvragen via funda worden in behandeling genomen!\n' +
              '\n' +
              'Je eigen 2-kamerappartement op een aantrekkelijke locatie op fietsafstand van het centrum! Dit appartementencomplex is gelegen op een ruim perceel aan de Hooft Graaflandstraat.\n' +
              '\n' +
              'Het betreffen 15 zelfstandige appartementen gelegen in de populaire woonwijk Hoograven op de begane grond, eerste en tweede etage. Deze appartementen zijn goed geïsoleerd en strak afgewerkt met hoogwaardige materialen. Ze zijn voorzien van een PVC-vloer, gestukte en gesausde wanden, eigen aansluitingen voor nutsvoorzieningen, internet en tv. \n' +
              'Ieder appartement beschikt over een luxe keuken voorzien van o.a. afwasmachine, combi-oven en afzuigkap. De badkamer is keurig in neutrale kleurstelling betegeld en is voorzien van een douche, wastafel en toilet. \n' +
              'Daarnaast is het pand volledig gasloos en beschikt het over een warmtepomp en HR++ glas, wat resulteert in een lage energierekening. \n' +
              '\n' +
              'Op het terrein is een overdekte fietsenstalling en 6 parkeerplaatsen welke vrij gebruikt mogen worden als bewoner. \n' +
              '\n' +
              'Locatie:\n' +
              'Op loopafstand van diverse winkelvoorzieningen waaronder het winkelcentrum “het hart van Hoograven” en nabij dé culinaire hotspot van Utrecht (Rotsoord) met haar verschillende cafés en restaurants zoals WT Urban Kitchen en Klein Berlijn. Tevens bevindt je je op fietsafstand van het oude centrum en is het openbaar vervoer om de hoek waarbij de parkeermogelijkheden (thans vrij parkeren) als extra pre aanwezig zijn. \n' +
              '\n' +
              'Appartement 2C-5\n' +
              'Indeling:\n' +
              'Eerste verdieping:\n' +
              'Appartementsvoordeur: hal, slaapkamer, badkamer met douche, toilet en wastafel, ruimte met wasmachine-aansluiting. Lichte woonkamer met keuken welke is voorzien van diverse apparatuur waaronder een koelkast, vriezer, vaatwasser, inductiekookplaat, afzuigkap & magnetron combi-oven. Vanuit de woonkamer bereik je het balkon door de grote raampartij met haar openslaande deuren.\n' +
              '\n' +
              'Ben je benieuwd of dit appartement bij je past? Wij maken graag een afspraak voor een rondleiding!\n' +
              '\n' +
              'Bijzonderheden:\n' +
              '- Prijs EUR 1.775,- exclusief;\n' +
              '- Huurder is zelfstandig contractant van de NUTS voorzieningen;\n' +
              '- Servicekosten EUR 86,24 per mnd;\n' +
              '- Oppervlakte ca. 45 m2;\n' +
              '- Balkon;\n' +
              '- Gelegen op de eerste etage;\n' +
              '- Twee maanden borg;\n' +
              '- Inkomenseis: 3 x maandhuur bruto;\n' +
              '- Energielabel: A++;\n' +
              '- Hoogwaardig afwerkingsniveau;\n' +
              '- Volledig gestoffeerd: PVC vloer, gordijnen/rolgordijnen/jaloezieën, lampen, handdoek haakjes, kapstok;\n' +
              '- Internet/tv aansluiting aanwezig, abonnement naar keuze;\n' +
              '- Huisdieren zijn niet toegestaan;\n' +
              '- Vrij parkeren op terrein of op openbare weg;\n' +
              '- Oplevering: 12 september 2025.\n' +
              '\n' +
              'Deze aanbieding is met zorg samengesteld echter geheel vrijblijvend en onder voorbehoud van goedkeuring verhuurder. Derhalve kunnen hier op geen enkele wijze geen rechten aan ontleend worden. Alle maten en afmetingen zijn indicatief en niet exact volgens de NEN-2580 norm.\n' +
              '\n' +
              'Note: Only viewing requests made via Funda will be processed!\n' +
              '\n' +
              'Your own 2-room apartment in an attractive location, just a short cycle from the city center! This apartment complex is situated on a spacious plot on Hooft Graaflandstraat.\n' +
              '\n' +
              'The complex consists of 15 self-contained apartments located in the popular residential area of Hoograven, spread across the ground, first, and second floors. These apartments are well insulated and finished to a high standard with quality materials. Each unit is equipped with PVC flooring, plastered and painted walls, individual utility connections, internet, and TV.\n' +
              '\n' +
              'Every apartment has a modern kitchen equipped with a dishwasher, combi oven, and extractor hood. The bathroom is neatly tiled in a neutral color scheme and features a shower, washbasin, and toilet.\n' +
              '\n' +
              'The building is completely gas-free and fitted with a heat pump and HR++ glass, resulting in a low energy bill.\n' +
              '\n' +
              'On the premises, there is a covered bicycle storage area and 6 parking spaces available for residents’ use.\n' +
              '\n' +
              'Location:\n' +
              'Within walking distance of several shopping facilities, including the shopping center “Het Hart van Hoograven,” and close to Utrecht’s culinary hotspot Rotsoord, home to cafés and restaurants such as WT Urban Kitchen and Klein Berlijn. The historic city center is only a short cycle away, public transport is just around the corner, and the availability of (currently free) parking adds to the convenience.\n' +
              '\n' +
              'Apartment 2C-5\n' +
              '\n' +
              'Layout – First floor:\n' +
              'Private entrance: hallway, bedroom, bathroom with shower, toilet, and washbasin, and a separate laundry area with washing machine connection. Bright living room with kitchen, fitted with various appliances including fridge, freezer, dishwasher, induction hob, extractor hood & microwave-combi oven. From the living room, large French doors provide access to the balcony.\n' +
              '\n' +
              'Curious whether this apartment is right for you? We would be happy to arrange a viewing!\n' +
              '\n' +
              'Details:\n' +
              '\n' +
              '- Rent: €1,775 excl.;\n' +
              '- Tenant contracts utilities directly;\n' +
              '- Service charges: €86.24 per month;\n' +
              '- Approx. 45 m²;\n' +
              '- Balcony;\n' +
              '- Located on the first floor;\n' +
              '- Two months’ deposit;\n' +
              '- Income requirement: 3x monthly rent (gross);\n' +
              '- Energy label: A++;\n' +
              '- High-quality finish;\n' +
              '- Fully furnished: PVC flooring, curtains/blinds, lighting, towel hooks, coat rack;\n' +
              '- Internet/TV connection available, subscription at tenant’s choice;\n' +
              '- Pets are not allowed;\n' +
              '- Free parking on-site or in public areas;\n' +
              '- Available: September 12, 2025.\n' +
              '- Note: Only viewing requests made via Funda will be processed!\n' +
              '\n' +
              'This listing has been compiled with care, but is entirely without obligation and subject to landlord approval. No rights can be derived from this advertisement in any way. All sizes and measurements are indicative and not exactly in accordance with the NEN-2580 standard.',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-08-27T23:46:27.937Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-08-28 00:46:27'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:46:27.943Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:27'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:46:27.948Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:27'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:46:27.953Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:27'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1756338387952_mss0s',
  source: 'funda',
  duration: '1ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: Build year must be between 1800 and 2030
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:818:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: 'Build year must be between 1800 and 2030',
          path: [ 'year' ],
          type: 'custom',
          context: { label: 'year', value: '1450', key: 'year' }
        }
      ]
    },
    timestamp: 2025-08-27T23:46:27.953Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-08-28 00:46:27'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:46:27.958Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:27'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:46:27.964Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:27'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:46:27.969Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:27'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:46:27.974Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:27'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:46:27.979Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:27'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:46:27.983Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:27'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:46:27.988Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:27'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1756338387987_fz0hl',
  source: 'funda',
  duration: '0ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:818:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'Luxe en Duurzaam Wonen in Wonderwoods – Hoola van Nootenstraat 355, Utrecht\n' +
              '\n' +
              'Wonen in het hart van Utrecht, omringd door groen en moderne luxe? Dit volledig gemeubileerde appartement in het iconische Wonderwoods-gebouw biedt alles wat je zoekt! Met een woonoppervlakte van ca. 67 m², een prachtig balkon met adembenemend uitzicht over Utrecht, en een toplocatie vlak bij het Centraal Station, is dit een unieke kans.\n' +
              '\n' +
              'Over Wonderwoods\n' +
              'Wonderwoods is een baanbrekend, duurzaam woonconcept dat natuur en stad samenbrengt. Dit architectonische meesterwerk, geïnspireerd door het Bosco Verticale in Milaan, biedt een groene en gezonde leefomgeving met weelderige beplanting op de gevels en daktuinen. Het gebouw beschikt over luxe faciliteiten en innovatieve technologieën die bijdragen aan een duurzame en comfortabele levensstijl.\n' +
              '\n' +
              'Perfecte Locatie\n' +
              'Wonderwoods ligt in het nieuwe Beurskwartier, dé opkomende wijk van Utrecht. Hier woon je op steenworp afstand van Utrecht Centraal, het bruisende stadscentrum, en tal van restaurants, cafés en winkels. Ook het Jaarbeursplein en het Beatrix Theater bevinden zich op loopafstand. Daarnaast is het gebied goed bereikbaar met zowel het openbaar vervoer als de auto, met directe toegang tot belangrijke uitvalswegen.\n' +
              '\n' +
              'Indeling van het appartement\n' +
              'Ruime en lichte woonkamer met moderne inrichting en toegang tot het balkon met uitzicht op de Dom. De open keuken is voorzien van luxe inbouwapparatuur.\n' +
              'Via de hal bereik je de comfortabele slaapkamer, welke stijlvol is ingericht. De moderne badkamer met inloopdouche en wastafel is tevens bereikbaar via de hal, daarnaast is de separate toilet gesitueerd alsmede het washok met warmtepomp. \n' +
              '\n' +
              'Bijzonderheden\n' +
              '- Huurprijs: €2.600 per maand (exclusief servicekosten en nutsvoorzieningen).\n' +
              '- Servicekosten bedragen € 100,- per maand.\n' +
              '- Volledig gemeubileerd – direct intrekbaar!\n' +
              '- Duurzaam wonen in een van de meest innovatieve gebouwen van Utrecht.\n' +
              '- Toplocatie: vlak bij Utrecht Centraal, winkels en horeca.\n' +
              '- Woonoppervlakte: ca. 67 m².\n' +
              '- Groene leefomgeving met beplanting op het gebouw en gedeelde buitenruimtes.\n' +
              '- Fietsenstalling in de onderbouw.\n' +
              '\n' +
              'Wil jij wonen op deze unieke plek in Utrecht? Neem snel contact op voor een bezichtiging! \n' +
              '\n' +
              'Deze aanbieding is met zorg samengesteld echter geheel vrijblijvend en onder voorbehoud van goedkeuring verhuurder. Derhalve kunnen hier op geen enkele wijze geen rechten aan ontleend worden. Alle maten en afmetingen zijn indicatief en niet exact volgens de NEN-2580 norm.\n' +
              '\n' +
              'Luxury and Sustainable Living in Wonderwoods – Hoola van Nootenstraat 355, Utrecht\n' +
              '\n' +
              'Would you like to live in the heart of Utrecht, surrounded by greenery and modern luxury? This fully furnished apartment in the iconic Wonderwoods building offers everything you need! With approximately 67 m² of living space, a beautiful balcony with breathtaking views of Utrecht, and a prime location near Central Station, this is a unique opportunity.\n' +
              '\n' +
              'About Wonderwoods\n' +
              'Wonderwoods is a groundbreaking, sustainable living concept that seamlessly integrates nature and urban life. Inspired by Bosco Verticale in Milan, this architectural masterpiece provides a green and healthy living environment with lush vegetation on the façades and rooftop gardens. The building offers luxurious amenities and innovative technologies, ensuring a comfortable and eco-friendly lifestyle.\n' +
              '\n' +
              'Perfect Location\n' +
              'Wonderwoods is situated in Beurskwartier, Utrecht’s vibrant new district. Living here means being just a stone’s throw from Utrecht Central Station, the lively city center, and numerous restaurants, cafés, and shops. Jaarbeursplein and Beatrix Theater are also within walking distance. The area is easily accessible by public transport and car, with direct access to major highways.\n' +
              '\n' +
              'Apartment Layout\n' +
              'Spacious and bright living room with modern furnishings and access to the balcony, offering stunning views of the Dom Tower.\n' +
              'Open kitchen, fully equipped with high-end built-in appliances.\n' +
              'Comfortable and stylishly furnished bedroom, accessible via the hallway.\n' +
              'Modern bathroom with a walk-in shower and sink.\n' +
              'Separate toilet and utility room with a heat pump, also accessible via the hallway.\n' +
              'Key Features\n' +
              '? Rent: €2,600 per month (excluding service charges and utilities).\n' +
              '? Servicecosts: € 100 per month.\n' +
              '? Fully furnished – move-in ready!\n' +
              '? Sustainable living in one of Utrecht’s most innovative buildings.\n' +
              '? Prime location near Utrecht Central Station, shops, and restaurants.\n' +
              '? Living area: approx. 67 m².\n' +
              '? Green environment with vegetation on the building and shared outdoor spaces.\n' +
              '? Bicycle storage in the basement.\n' +
              '\n' +
              'Would you like to live in this one-of-a-kind location in Utrecht? Contact us today to schedule a viewing! \n' +
              '\n' +
              'Disclaimer: This offer has been carefully compiled; however, it is entirely without obligation and subject to the landlord’s approval. Therefore, no rights can be derived from it in any way. All measurements and dimensions are indicative and not exact according to the NEN-2580 standard.',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-08-27T23:46:27.987Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-08-28 00:46:27'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:46:27.994Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:27'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:46:27.999Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:27'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:46:28.004Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:28'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:46:28.009Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:28'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:46:28.013Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:28'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:46:28.018Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:28'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:46:28.022Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:28'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:46:28.028Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:28'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:46:28.032Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:28'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:46:28.037Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:28'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:46:28.042Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:28'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:46:28.047Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:28'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:46:28.051Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:28'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1756338388050_mvm9l',
  source: 'funda',
  duration: '1ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:818:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'MODERN EN NIEUW 3-KAMERAPPARTEMENT MET BALKON, BERGING EN PRIVÉPARKEERPLAATS – BESCHIKBAAR PER 1 SEPTEMBER 2025\n' +
              '\n' +
              'Ben jij op zoek naar een licht, ruim en instap klaar appartement in een rustige en goed bereikbare wijk van Utrecht? Dan is dit prachtige 3-kamerappartement aan de Madridstraat 228 in Leidsche Rijn precies wat je zoekt!\n' +
              '\n' +
              'Met twee slaapkamers, een groot balkon, een privéberging én een eigen parkeerplaats, biedt deze woning alles wat je nodig hebt voor comfortabel wonen. De woning is volledig gestoffeerd en tot in de puntjes afgewerkt met een moderne pvc-vloer in houtlook, stijlvolle raambekleding en verlichting in elke kamer.\n' +
              '\n' +
              'Dit appartement bevindt zich op een ideale locatie in Utrecht: rustig gelegen, maar toch vlak bij alle voorzieningen. Je woont op korte afstand van Leidsche Rijn Centrum met gezellige horeca, winkels, sportfaciliteiten, scholen en het Máximapark. De bereikbaarheid is uitstekend: station Leidsche Rijn en station Terwijde liggen om de hoek en via de A2 of A12 ben je binnen no-time in Amsterdam of Den Haag.\n' +
              '\n' +
              'Indeling:\n' +
              '\n' +
              'Via de centrale entree met bellentableau, brievenbussen, lift en trappenhuis bereik je de derde verdieping.\n' +
              'Het appartement beschikt over een ruime hal die toegang geeft tot alle vertrekken. De woonkamer is licht en ruim en biedt directe toegang tot het zonnige balkon. De open keuken biedt ruimte voor inbouwapparatuur en sluit naadloos aan op de woonruimte.\n' +
              '\n' +
              'Er zijn twee comfortabele slaapkamers, een nette badkamer met inloopdouche en wastafel, een apart toilet, en een handige inpandige berging met wasmachine-aansluiting.\n' +
              '\n' +
              'Voordelen:\n' +
              '\n' +
              'Instapklaar, modern en sfeervol ingericht\n' +
              'Gestoffeerd met pvc-vloer, verlichting en gordijnen\n' +
              'Ruim balkon op het zuidwesten\n' +
              'Privéparkeerplaats in de ondergelegen garage\n' +
              'Eigen berging voor extra opslagruimte\n' +
              'Energiezuinig (energielabel A+++)\n' +
              'Uitstekende bereikbaarheid met OV en auto\n' +
              'Geschikt voor lange termijn verhuur\n' +
              '\n' +
              'Huurcondities:\n' +
              '\n' +
              'Huurprijs; € 2.600,- per maand, is inclusief de servicekosten en exclusief gas, water, elektra, internet, tv en gemeentelijke belastingen;\n' +
              '\n' +
              'Er wordt een inkomenscheck gedaan;\n' +
              '\n' +
              'Waarborgsom; € 5.200,- eenmalig;\n' +
              '\n' +
              'Huisdieren zijn niet toegestaan;\n' +
              '\n' +
              'Roken niet toegestaan;\n' +
              '\n' +
              'Niet geschikt voor studenten of woningdelers;\n' +
              '\n' +
              'Vloerverwarming via stadsverwarming;\n' +
              '\n' +
              'Huurperiode: langdurig;\n' +
              '\n' +
              'Professionele eindschoonmaak verplicht op kosten huurder;\n' +
              '\n' +
              'Appartement kan per 1 september 2025 worden betrokken\n' +
              '\n' +
              'De informatie is met de nodige zorgvuldigheid samengesteld. Geen aansprakelijkheid wordt aanvaard voor enige onvolledigheid, onjuistheid of anderszins, of consequenties daarvan. Alle maten en afmetingen zijn indicatief.\n' +
              '\n' +
              'MODERN AND NEW 3-ROOM APARTMENT WITH BALCONY, STORAGE, AND PRIVATE PARKING – AVAILABLE FROM SEPTEMBER 1, 2025\n' +
              '\n' +
              'Are you looking for a bright, spacious, and move-in ready apartment in a quiet yet easily accessible neighbourhood of Utrecht? Then this beautiful 3-room apartment on Madridstraat 228 in Leidsche Rijn is exactly what you’re looking for!\n' +
              '\n' +
              'With two bedrooms, a large balcony, a private storage unit, and your own parking space, this home offers everything you need for comfortable living. The property comes fully fitted and is finished to perfection with a modern wood-look PVC floor, stylish window coverings, and lighting in every room.\n' +
              '\n' +
              'This apartment is ideally located in Utrecht: peacefully situated, yet close to all amenities. You are within short distance of Leidsche Rijn Center with cozy restaurants, shops, sports facilities, schools, and the Máximapark. The location offers excellent accessibility: Leidsche Rijn and Terwijde train stations are nearby, and the A2 and A12 motorways will get you to Amsterdam or The Hague in no time.\n' +
              '\n' +
              'Layout:\n' +
              'Through the central entrance with intercom panel, mailboxes, elevator, and stairwell, you reach the third floor.\n' +
              '\n' +
              'The apartment features a spacious hallway providing access to all rooms. The living room is bright and spacious, with direct access to the sunny balcony. The open kitchen has space for built-in appliances and connects seamlessly to the living area.\n' +
              '\n' +
              'There are two comfortable bedrooms, a neat bathroom with walk-in shower and sink, a separate toilet, and a convenient internal storage room with washing machine connection.\n' +
              '\n' +
              'Features:\n' +
              '\n' +
              '- Move-in ready, modern, and tastefully decorated\n' +
              '- Fitted with PVC flooring, lighting, and curtains\n' +
              '- Spacious southwest-facing balcony\n' +
              '- Private parking space in the underground garage\n' +
              '- Private storage unit for extra storage space\n' +
              '- Energy-efficient (Energy Label A+++)\n' +
              '- Excellent accessibility by public transport and car\n' +
              '- Suitable for long-term rental\n' +
              '\n' +
              'Rental conditions:\n' +
              '\n' +
              '- Rent: €2,600 per month, including service charges and excluding gas, water, electricity, internet, TV, and municipal taxes\n' +
              '- Income check required\n' +
              '- Security deposit: €5,200 (one-time)\n' +
              '- Pets not allowed\n' +
              '- Smoking not allowed\n' +
              '- Not suitable for students or house sharing\n' +
              '- Underfloor heating via district heating\n' +
              '- Long-term rental period\n' +
              '- Professional end-of-tenancy cleaning required at tenant’s expense\n' +
              '- Available from September 1, 2025\n' +
              '\n' +
              'The information has been compiled with due care. No liability is accepted for any incompleteness, inaccuracies, or otherwise, or the consequences thereof. All measurements and dimensions are indicative.',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-08-27T23:46:28.051Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-08-28 00:46:28'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:46:28.057Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:28'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1756338388055_1j9fr',
  source: 'funda',
  duration: '2ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:818:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'Een prachtig gestoffeerd appartement met groot balkon op het zuidwesten in Leidsche Rijn Centrum, grenzend aan het gezellige Brusselplein.\n' +
              '\n' +
              'De woning ligt centraal in Leidsche Rijn Centrum dat is ontworpen om te wonen, te werken en te leven. In een mediterrane sfeer met boulevards en promenades is dit centrum het kloppend hart van de wijk Leidsche Rijn met leuke winkels, gezellige terrasjes en pleinen.\n' +
              '\n' +
              'Hier kunt u ruiken, voelen, proeven en kopen. Beleving staat centraal. Daarnaast bevat het centrum een eigen NS-station en busstation. Er zijn niet alleen culturele voorzieningen in de buurt zoals de bioscoop en het Berlijnplein, maar ook tal van 1e lijns zorgvoorzieningen. Al deze voorzieningen zijn op loopafstand van het appartementencomplex.\n' +
              '\n' +
              'Begane grond:\n' +
              '\n' +
              'Centrale entree met brievenbussen en een bellentableau, met toegang tot de lift en het trappenhuis evenals de berging en de parkeergarage inclusief de overdekte fietsenstalling en eigen parkeerplek.\n' +
              '\n' +
              'Indeling:\n' +
              '\n' +
              'Het appartement is gesitueerd op de 2e verdieping. Entree in de hal die toegang geeft tot de andere ruimtes; een apart toilet met fonteintje, de badkamer met douche en wastafel met opberglades, een berging, de 2 slaapkamers met kiep/kantelraam en de woonkamer met semi-open keuken.\n' +
              '\n' +
              'De woonkamer biedt uitzicht op de Grauwaartsingel en geeft toegang tot het balkon. De semi-open keuken is van alle gemakken voorzien met inbouwapparatuur zoals een vaatwasser, koel-vries combinatie, combi-oven, kookplaat en afzuigkap. Daarnaast is er een berging met opstelplaats voor de wasmachine en de droger, de boiler en de vloerverwarmingsunit is eveneens daar.\n' +
              '\n' +
              'Het appartement beschikt over een moderne lichte laminaatvloer, offwhite gordijnen, de badkamer en het toilet hebben moderne accenten. Het appartement is gebouwd met de nieuwste, meest duurzame materialen en de afwerking is hoogwaardig en luxe. Tevens is het appartement zeer energiezuinig en heeft energielabel A; zeer goede isolatie, vloerverwarming- en koeling en een warmte-terugwininstallatie.\n' +
              '\n' +
              'Bijzonderheden:\n' +
              '\n' +
              'Huurprijs is inclusief de servicekosten en exclusief gas, water, elektra, internet, tv en gemeentelijke belastingen;\n' +
              '\n' +
              'Er wordt een inkomenscheck gedaan;\n' +
              '\n' +
              'Waarborg staat gelijk aan twee keer de maand huur;\n' +
              '\n' +
              'Huisdieren zijn niet toegestaan;\n' +
              '\n' +
              'Roken niet toegestaan;\n' +
              '\n' +
              'Niet geschikt voor studenten;\n' +
              '\n' +
              'Vloerverwarming via stadsverwarming;\n' +
              '\n' +
              'Woning kan per 1 september 2025 worden betrokken.\n' +
              '\n' +
              'Deze informatie is met de nodige zorgvuldigheid samengesteld. Geen aansprakelijkheid wordt aanvaard voor enige onvolledigheid, onjuistheid of anderszins, of de consequenties daarvan. Alle maten en afmetingen zijn indicatief.\n' +
              '\n' +
              '---------------------------------------------\n' +
              '\n' +
              'A beautifully furnished apartment with a large southwest-facing balcony in Leidsche Rijn Centrum, adjacent to the lively Brusselplein.\n' +
              '\n' +
              'The property is centrally located in Leidsche Rijn Centrum, designed for living, working, and enjoying life. With Mediterranean-inspired boulevards and promenades, this center is the heart of the Leidsche Rijn district, offering charming shops, cozy terraces, and squares. You can experience and enjoy everything here: the sights, smells, tastes, and experiences. Additionally, the center has its own NS train station and bus station. Nearby are not only cultural amenities like the cinema and Berlijnplein, but also many primary healthcare facilities. All these amenities are within walking distance of the apartment complex.\n' +
              '\n' +
              'Ground floor: Central entrance with mailboxes and an intercom panel, with access to the elevator and stairwell, as well as the storage area and parking garage, which includes a covered bike shed and your own parking space.\n' +
              '\n' +
              'Layout: The apartment is located on the 2nd floor. Entry through the hall, which provides access to the other rooms: a separate toilet with a small sink, the bathroom with a shower and sink with storage drawers, a storage room, two bedrooms with tilt/turn windows, and the living room with a semi-open kitchen. The living room overlooks the Grauwaartsingel and gives access to the balcony. The semi-open kitchen is fully equipped with built-in appliances, including a dishwasher, fridge-freezer combination, combi oven, hob, and extractor hood. There is also a storage room with space for a washing machine and dryer, as well as the boiler and floor heating unit. The apartment features a modern light laminate floor, off-white curtains, and the bathroom and toilet have modern accents. The apartment is built with the latest, most sustainable materials, and the finish is high-quality and luxurious. Furthermore, the apartment is very energy-efficient with energy label A; it has excellent insulation, floor heating and cooling, and a heat recovery system.\n' +
              '\n' +
              'Special features:\n' +
              '\n' +
              'Rent includes service costs and excludes gas, water, electricity, internet, TV, and municipal taxes;\n' +
              '\n' +
              'Income check will be performed;\n' +
              '\n' +
              "Deposit is equivalent to two months' rent;\n" +
              '\n' +
              'Pets are not allowed;\n' +
              '\n' +
              'Smoking is not allowed;\n' +
              '\n' +
              'Not suitable for students;\n' +
              '\n' +
              'Floor heating via district heating;\n' +
              '\n' +
              'Available from September 1, 2025.\n' +
              '\n' +
              'This information has been compiled with due care. No liability is accepted for any incompleteness, inaccuracies, or otherwise, or for the consequences thereof. All dimensions and measurements are indicative.',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-08-27T23:46:28.057Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-08-28 00:46:28'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:46:28.061Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:28'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:46:28.067Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:28'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:46:28.072Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:28'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:46:28.077Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:28'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:46:28.082Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:28'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1756338388081_uagny',
  source: 'funda',
  duration: '1ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:818:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: '123WONEN DÉ VERHUURMAKELAAR VAN UTRECHT BIEDT AAN VOOR TIJDELIJKE VERHUUR VAN 9 MAANDEN:\n' +
              '\n' +
              'Charmant herenhuis aan de Badstraat – Utrecht (Buiten Wittevrouwen).\n' +
              '\n' +
              'Ben jij op zoek naar een ruim, sfeervol en karakteristiek woonhuis op een toplocatie in Utrecht? Dit prachtige herenhuis aan de Badstraat biedt comfortabel en stijlvol wonen in de populaire wijk Buiten Wittevrouwen. Met vier ruime slaapkamers, moderne badkamer, luxe keuken met groot eiland en een zonnige onderhoudsarme buitenruimte, plus een balkon op de eerste verdieping, is dit de ideale woning voor een stel, expat-gezin of werkende professional.\n' +
              '\n' +
              'Op loopafstand van het Griftpark en het Wilhelminapark, diverse winkels, supermarkten en horeca, slechts enkele minuten fietsen naar het centrum, CS Utrecht en Universiteit. Uitstekend bereikbaar met OV en nabij uitvalswegen (A27/A28)\n' +
              '\n' +
              'INDELING:\n' +
              '\n' +
              'BEGANE GROND:\n' +
              '\n' +
              'Bij binnenkomst in de hal heb je de trapopgang naar de 1e verdieping, toilet en de toegang naar de ruime woonkamer en open keuken. De vloer op de benedenverdieping is voorzien van een houten vloer. De woonkamer is ruim en licht en de open keuken is voorzien van een groot eiland met de gootsteen, inbouwkoelkast/vriezer, inbouw magnetron en oven en een 6-pits gaskookplaat en meer dan voldoende opbergruimte.\n' +
              '\n' +
              'EERSTE VERDIEPING:\n' +
              '\n' +
              'Op deze verdieping vindt je twee in grote variërende slaapkamers, de ruime badkamer met een bad en aparte douche en de trapopgang naar de tweede verdieping. De vloer op deze verdieping is net als op de begane grond een houten vloer. Op deze verdieping is ook een balkon.\n' +
              '\n' +
              'TWEEDE VERDIEPING:\n' +
              '\n' +
              'Op deze verdieping vindt je nog 2 in grote varierende slaapkamers, waar genoeg ruimte is om te slapen en/of te werken.\n' +
              '\n' +
              'BIJZONDERHEDEN:\n' +
              '\n' +
              '- De woning is beschikbaar per 6 oktober 2025;\n' +
              '- De huurperiode is een vaste periode van 9 maanden;\n' +
              '- Huurprijs is € 2400,- per maand;\n' +
              '- Waarborgsom 2 maanden huur;\n' +
              '- De huurprijs is exclusief G/W/E, tv/internet en Gemeentelijke heffingen deel huurder(s);\n' +
              '- De woning is gestoffeerd;\n' +
              '- Energielabel E;\n' +
              '- Parkeergelegenheid voor de deur (betaald);\n' +
              '- Niet roken;\n' +
              '- Geen huisdieren;\n' +
              '- Geen studenten / woningdelers;\n' +
              '\n' +
              'GEEN COURTAGE! 123WONEN WERKT ALS VERHUURMAKELAAR VOOR DE EIGENAAR;\n' +
              'BEZICHTIGINGEN KUNNEN ALLEEN ONLINE WORDEN AANGEVRAAGD.\n' +
              '\n' +
              'Vind je deze woning op een website waarop wij doorplaatsen?\n' +
              'Kijk dan op onze eigen website:  voor ons actuele aanbod!\n' +
              '\n' +
              'Vind je deze woning op een website waar wij doorverkopen?\n' +
              'Kijk dan op onze eigen website:  voor ons actuele aanbod!\n' +
              '\n' +
              'Neem contact op\n' +
              '\n' +
              '123WONEN UTRECHT\n' +
              'Ramstraat 31\n' +
              '3581 HD Utrecht\n' +
              '\n' +
              'T 030 - 760 33 94\n' +
              'E \n' +
              '\n' +
              '----\n' +
              '\n' +
              '123WONEN DÉ VERHUURMAKELAAR VAN UTRECHT OFFERS FOR TEMPORARY RENTAL OF 9 MONTHS:\n' +
              '\n' +
              'Charming townhouse on the Badstraat - Utrecht (Buiten Wittevrouwen).\n' +
              '\n' +
              'Are you looking for a spacious, attractive and characteristic house in a prime location in Utrecht? This beautiful townhouse on Badstraat offers comfortable and stylish living in the popular neighborhood of Buiten Wittevrouwen. With four spacious bedrooms, modern bathroom, luxury kitchen with large island and a sunny low maintenance outdoor area, plus a balcony on the second floor, this is the ideal home for a couple, expat family or working professional.\n' +
              '\n' +
              'Within walking distance of the Griftpark and Wilhelminapark, several stores, supermarkets and restaurants, just minutes by bike to the city center, CS Utrecht and University. Easily accessible by public transport and near roads (A27/A28)\n' +
              '\n' +
              'LAYOUT:\n' +
              '\n' +
              'GROUND FLOOR:\n' +
              '\n' +
              'Upon entering the hall you have the staircase to the 1st floor, toilet and access to the spacious living room and open kitchen. The floor on the ground floor has a wooden floor. The living room is spacious and bright and the open kitchen has a large island with the sink, built-in fridge/freezer, built-in microwave and oven and a 6-burner gas hob and more than enough storage space.\n' +
              '\n' +
              'FIRST FLOOR:\n' +
              '\n' +
              'On this floor you will find two bedrooms varying in size, the spacious bathroom with a tub and separate shower and the staircase to the second floor. The floor is also a wooden floor. There is also a balcony on this floor.\n' +
              '\n' +
              'SECOND FLOOR:\n' +
              '\n' +
              'On this floor you will find the 2 bedrooms varying in size, where there is enough space to sleep and/or work.\n' +
              '\n' +
              'PARTICULARS:\n' +
              '\n' +
              '- The property is available from October 6, 2025;\n' +
              '- The rental period is a fixed period of 9 months;\n' +
              '- Rent is € 2400,- per month;\n' +
              '- Security deposit 2 months rent;\n' +
              '- The rent is excluding G/W/E, TV/internet and Municipal levies share tenant(s);\n' +
              '- The property is unfurnished;\n' +
              '- Energy label E;\n' +
              '- Parking in front of the door (paid);\n' +
              '- No smoking;\n' +
              '- No pets;\n' +
              '- No students / house sharers\n' +
              '\n' +
              'NO COMMISSION! 123WONEN WORKS AS A RENTAL AGENT FOR THE OWNER;\n' +
              'VIEWINGS CAN ONLY BE REQUESTED ONLINE.\n' +
              '\n' +
              'Do you find this property on a website on which we post?\n' +
              'Please check our own website:  for our current offer!\n' +
              '\n' +
              'Do you find this property on a website where we resell?\n' +
              'Please check our own website:  for our current offer!\n' +
              '\n' +
              'Contact us\n' +
              '\n' +
              'EXPATRENTALS HOLLAND UTRECHT\n' +
              'Ramstraat 31\n' +
              '3581 HD Utrecht\n' +
              '\n' +
              'T 030 - 760 33 94\n' +
              'E',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-08-27T23:46:28.082Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-08-28 00:46:28'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:46:28.087Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:28'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1756338388086_vbgcw',
  source: 'funda',
  duration: '1ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:818:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'For English below\n' +
              '\n' +
              'Let op! Alleen voor (deel- of voltijd) studenten beschikbaar!\n' +
              '\n' +
              'Sfeervolle 3-kamer (waarvan 1 slaapkamer) woning (ca. 70 m2) met grote achtertuin met berging en achterom in de Rivierenwijk in Utrecht!\n' +
              '\n' +
              'In de Rivierenwijk woon je op een ideale locatie, met alle voorzieningen binnen handbereik en het centrum van Utrecht op korte afstand. Hier vind je echt iets bijzonders – een plek zoals geen ander.\n' +
              '\n' +
              'Deze charmante tussenwoning uit de jaren 30 ligt in een rustige straat en valt op door de mooie lichtinval, de nette afwerking en de combinatie van authentieke elementen met modern comfort. De woning is voorzien van een royale woonkeuken, klassieke en suite deuren en een heerlijke, diepe achtertuin op het oosten.\n' +
              '\n' +
              'De Rivierenwijk is een gewilde en levendige buurt met een dorps karakter, op slechts enkele minuten fietsen van de binnenstad. Je woont hier tussen de gezellige straatjes, met volop voorzieningen zoals winkels, cafés, scholen en sportfaciliteiten in de buurt. NS-station Vaartsche Rijn en het populaire Ledig Erf liggen op loopafstand, en ook Utrecht Centraal is snel te bereiken. Parkeren kan met parkeervergunning te verkrijgen bij de gemeente Utrecht of betaald parkeren.\n' +
              '\n' +
              'Indeling\n' +
              '\n' +
              'Begane grond:\n' +
              'Entree, hal, meterkast en modern toilet. Vanuit de hal kom je in de ruime woonkeuken aan de voorzijde van de woning, voorzien van een royaal keukenblok met o.a. vaatwasser, combi-oven/magnetron en een koelkast met vriesvak. De lichte lamelparketvloer en de originele ensuite deuren geven de ruimte karakter. Aan de achterzijde bevindt zich de uitgebouwde woonkamer, met een lichtkoepel in het plafond en openslaande deuren naar de tuin – een heerlijke, lichte leefruimte!\n' +
              '\n' +
              'Eerste verdieping:\n' +
              'Overloop met toegang tot een aparte kamer met wasmachineaansluiting, doorlopend naar een royale slaapkamer met hoog plafond en praktische kastruimte. Indien gewenst is deze kamer eenvoudig ook te gebruiken als werkkamer.\n' +
              '\n' +
              'De verzorgde badkamer is uitgerust met een ligbad/douche, tweede toilet en een wastafelmeubel.\n' +
              '\n' +
              'Tweede verdieping:\n' +
              'Vliering met bergruimte en opstelplaats voor de Cv-combiketel.\n' +
              '\n' +
              'Tuin:\n' +
              'De onderhoudsvriendelijke achtertuin van circa 64 m² ligt op het oosten en is bereikbaar via een achterom. Dankzij de diepte van de tuin is er op elk moment van de dag wel een zonnig plekje te vinden. Er staat een stenen berging – ideaal voor fietsen of tuingereedschap.\n' +
              '\n' +
              'Bijzonderheden:\n' +
              '- Huurprijs: € 2.125,- excl. g/w/e en gemeentelijke (huurders-)lasten per maand;\n' +
              '- Alleen voor (deel- of voltijd) studenten beschikbaar/alleenstaand of stel;\n' +
              '- Woonoppervlakte: circa 70 m²;\n' +
              '- Tuin op het oosten, ca. 60 m²\n' +
              '- Energielabel B;\n' +
              '- Huurperiode voor 24 maanden;\n' +
              '- Verwarming en warm water via C.V.-ketel;\n' +
              '- Waarborgsom 2 maanden huur;\n' +
              '- Beschikbaar: 1 oktober 2025.\n' +
              '\n' +
              'Protocol toewijzing kandidaat-huurders: \n' +
              '------------\n' +
              '\n' +
              'Attention! Only available for (part-time or full-time) students!\n' +
              '\n' +
              'Charming 3-room house (approx. 70 m²) (1 large bedroom) with a large back garden with a storage shed and rear access in the Rivierenwijk district of Utrecht!\n' +
              '\n' +
              "In the Rivierenwijk district, you'll live in an ideal location, with all amenities within easy reach and the city center of Utrecht just a short distance away. Here you'll find something truly special – a place like no other.\n" +
              '\n' +
              'This charming 1930s terraced house is located on a quiet street and stands out for its beautiful natural light, neat finish, and combination of authentic features with modern comforts. The house features a spacious kitchen/diner, classic en suite doors, and a lovely, deep east-facing back garden.\n' +
              '\n' +
              "The Rivierenwijk district is a desirable and vibrant neighborhood with a village character, just a few minutes' bike ride from the city center. You'll live here among charming streets, with plenty of amenities such as shops, cafes, schools, and sports facilities nearby. Vaartsche Rijn train station and the popular Ledig Erf shopping center are within walking distance, and Utrecht Central Station is also easily accessible. Parking is available with a permit available from the municipality of Utrecht, or there's paid parking.\n" +
              '\n' +
              'Layout\n' +
              '\n' +
              'Ground floor:\n' +
              'Entrance hall, meter cupboard, and modern toilet. From the hall, you enter the spacious kitchen/diner at the front of the house, equipped with a generously sized kitchen unit including a dishwasher, combination oven/microwave, and a refrigerator with freezer compartment. The light laminated parquet flooring and the original ensuite doors give the space character. At the rear is the extended living room, with a skylight in the ceiling and French doors leading to the garden – a wonderful, bright living space!\n' +
              '\n' +
              'First floor:\n' +
              'Landing with access to a separate room with a washing machine connection, leading to a spacious bedroom with a high ceiling and practical closet space. This room could easily be used as a study if desired.\n' +
              '\n' +
              'The well-maintained bathroom is equipped with a bath/shower, a second toilet, and a vanity unit.\n' +
              '\n' +
              'Second floor:\n' +
              'Attic with storage space and the central heating boiler. Garden:\n' +
              "The low-maintenance backyard of approximately 64 m² faces east and is accessible via a rear entrance. Thanks to its depth, there's always a sunny spot to be found. There's a brick shed – ideal for bicycles or garden tools.\n" +
              '\n' +
              'Garden:\n' +
              "The low-maintenance backyard of approximately 64 m² faces east and is accessible via a rear entrance. Thanks to its depth, there's always a sunny spot to be found. There's a brick shed – ideal for bicycles or garden tools.\n" +
              '\n' +
              'Details:\n' +
              '- Rent: € 2.125,- per month, excluding utilities and municipal (tenant) charges;\n' +
              '- Available only for (part-time or full-time) students/singles or couples;\n' +
              '- Living area: approximately 70 m²;\n' +
              '- East-facing garden, approximately 60 m²;\n' +
              '- Energy label B;\n' +
              '- 24-month lease;\n' +
              '- Heating and hot water via central heating boiler;\n' +
              '- Deposit 2 months rent;\n' +
              '- Available: October 1, 2025.',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-08-27T23:46:28.087Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-08-28 00:46:28'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:46:28.092Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:28'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:46:28.096Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:28'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (33%) is below threshold (80%)',
    value: 33,
    threshold: 80,
    timestamp: '2025-08-27T23:46:28.101Z'
  },
  level: 'error',
  message: 'Transformation success rate (33%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:28'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-27T23:46:28.105Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:28'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-27T23:46:28.110Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:28'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-27T23:46:28.115Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:28'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-27T23:46:28.120Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:28'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-27T23:46:28.124Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:28'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-27T23:46:28.131Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:28'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-27T23:46:28.136Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:28'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-27T23:46:28.141Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:28'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-27T23:46:28.147Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:28'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-27T23:46:28.152Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:28'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1756338388151_vevrf',
  source: 'funda',
  duration: '1ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:818:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'Omschrijving: \n' +
              '\n' +
              'Instapklaar, charmant en centraal in de binnenstad van Utrecht gelegen dubbel bovenhuis met zowel nieuwe àls oude stijlkenmerken. Aan deze erg gewilde en tevens rustige woonstraat in de wijk Hoogh Boulandt bevindt zich dit complete en zeer leefbare appartement. De woning biedt sfeer, comfort en veiligheid. Het Centraal Station, Station Vaartsche Rijn, het Ledig Erf, ’t Wed en de voorzieningen in het centrum, aan de Oudegracht en in de Twijnstraat zijn allemaal binnen 10 minuten lopend (of ca. 2-3 minuten per fiets) te bereiken. Ga je niet voor winkelen en horeca dan ligt het singelpark om de hoek om te recreëren. De klassieke details, modern gemak en de geweldige ligging maken dat je hier nooit meer weg wilt!\n' +
              '\n' +
              'Indeling:\n' +
              'Op de beletage binnengekomen loop je direct de trap op naar de woonverdieping. Aan de hal grenzen een kleine kamer aan de voorzijde, een WC en de woonkamer met open keuken. Deze verdieping biedt hoge plafonds en een mooie houten vloerafwerking. De woonkamer is opgedeeld in drie delen: een zitkamer, een eetkamer en een leeskamer/serre. De zitkamer en de eetkamer zijn vanuit de hal apart toegankelijk en af te sluiten middels stijlvolle en suite deuren. De serre is ook af te sluiten door glazen schuifdeuren. De moderne open keuken is door het spoeleiland praktisch van opzet en is van diverse (inbouw)apparatuur voorzien. De keuken biedt toegang tot het ruime overkapte balkon/terras. Hier is het door het hoge plafond fijn borrelen en tafelen in de middag- en avondzon (ZW ligging).\n' +
              'Via de trap in de hal kom je op de slaap-/werkverdieping (bovenste woonlaag). In het trapgat bevindt zich de overgedimensioneerde airconditioning die naar gelang je kamerdeuren open/dicht doet de achterliggende vertrekken al dan niet koelt of verwarmt (in aanvulling op de CV installatie). Naast de trap ligt het ketelhok met wasmachine. Aan de overloop grenst ook badkamer voorzien van regendouche, WC en dubbele wastafel. Er zijn drie slaapvertrekken waarvan er nu één dienst doet als werkkamer. In de inbouwkasten is een condensdroger opgenomen. Achter deze werkkamer bevindt zicht het royale dakterras dat aan de groene binnentuin grenst en al tegen het eind van de ochtend tot ca. 20.30 uur in de zomerzon ligt. Het is hier heerlijk loungen!\n' +
              '\n' +
              'Omgeving en parkeren:\n' +
              'De locatie en de omgeving waar het appartement is gelegen is ideaal voor mensen zonder auto, maar ook door bewoners met een auto kan er moeiteloos geparkeerd worden in de straat (eenrichtings-/bestemmingsverkeer, parkeervergunning aanvragen). \n' +
              'De woning is zeer goed gelegen ten opzichte van het Centrum, de dichtbij gelegen NS stations en ook ten opzichte van de uitvalswegen, denk aan Catharijnesingel richting o.a. Rubenslaan/Waterlinieweg (richting A27-A28), Jutfaseweg/Europalaan (richting A27-A12/A2) en overige. \n' +
              '\n' +
              'Bijzonderheden:\n' +
              'Ingangsdatum: 01-09-2025\n' +
              'Duur huurperiode: onbepaalde tijd met een minimumduur van 12 maanden\n' +
              'Opleveringsniveau: de woning wordt gemeubileerd opgeleverd op een basisniveau. In overleg is een luxer niveau mogelijk\n' +
              'Aanwezig: airconditioning (warm/koud)\n' +
              'Veiligheid: een kluis en een NVR videosysteem (voorzien van diverse hoogwaardige camera’s) zijn ingebouwd\n' +
              'Parkeren: op straat (geen wachtlijst)\n' +
              'Borg: twee maanden huur\n' +
              'Huisdieren: niet toegestaan\n' +
              'Woningdelers/meerpersoonshuishouden niet toegestaan\n' +
              'De beelden zijn indicatief. Tijdens een bezichtiging kunt u alles zien, vragen en een uitgebreide toelichting krijgen wat deze woning biedt en hoe u deze nog deels naar uw hand kunt zetten!\n' +
              '\n' +
              'Kenmerken: \n' +
              '* Kale huurprijs € 3.100 per maand \n' +
              '* Kosten voor meubilering € 150,- per maand (basisniveau)\n' +
              '* Geen servicekosten. Wel onbereikbare ramen wassen 4x p/j (nu € 40 per keer)\n' +
              '* Excl. gas/water/elektra/internet/gemeentelijke gebruikersheffingen\n' +
              '* Glasvezel internet is beschikbaar op dit adres\n' +
              '* Dubbel glas, eigen entree. Bouwjaar 1905\n' +
              '\n' +
              '* ENGLISH VERSION * \n' +
              '\n' +
              'Discribtion\n' +
              "Turnkey, charming and centrally located in Utrecht's city center, this duplex apartment features both new and period features. This fully equipped and very livable apartment is located on this highly desirable and quiet residential street in the Hoogh Boulandt neighborhood. It offers ambiance, comfort and security. Central Station, Vaartsche Rijn Station, Ledig Erf, 't Wed and the amenities in the city center, along Oudegracht and in Twijnstraat are all within a 10-minute walk (or approximately 2-3 minutes by bike). If shopping and dining aren't your thing, the Singelpark is just around the corner for recreation. The classic details, modern conveniences, and fantastic location will make you never want to leave!\n" +
              '\n' +
              'Floorplan\n' +
              'Entering the raised ground floor, you immediately ascend the stairs to the living area. Adjacent to the hall are a small room at the front, a toilet, and the living room with an open-plan kitchen. This floor boasts high ceilings and beautiful wooden flooring. The living room is divided into three areas: a sitting room, a dining room, and a reading room/conservatory. The sitting room and dining room are accessible separately from the hall and can be separated by stylish en-suite doors. The conservatory can also be closed off with sliding glass doors. The modern open-plan kitchen features a practical layout thanks to its island sink and is equipped with various (built-in) appliances. The kitchen opens onto the spacious covered balcony/terrace. The high ceilings make it ideal for enjoying drinks and meals in the afternoon and evening sun (southwest orientation).\n' +
              '\n' +
              "The stairs in the hall lead to the bedrooms/study (top floor). The oversized air conditioning unit is located in the stairwell and cools or heats the rooms behind it depending on whether you open or close the doors (in addition to the central heating system). Next to the stairs is the boiler room with a washing machine. The bathroom has a rain shower, a toilet and a double sink. There are three bedrooms, one of which currently serves as a study. A condenser dryer is integrated into the built-in wardrobes. Behind this study is the spacious roof terrace, which borders the green courtyard and enjoys the summer sun from late morning until around 8:30 PM. It's a wonderful place to lounge!\n" +
              '\n' +
              'ENVIRONMENT AND PARKING\n' +
              'The location and surrounding area of the apartment are ideal for those without a car, but residents with a car can also easily park on the street (one-way/destination traffic; parking permit required). The apartment is very well-located near the city center, the nearby train stations and arterial roads, such as Catharijnesingel towards Rubenslaan/Waterlinieweg (towards the A27-A28), Jutfaseweg/Europalaan (towards the A27-A12/A2) and others.\n' +
              '\n' +
              'DETAILS\n' +
              'Start date: September 1, 2025\n' +
              'Lease term: indefinite with a minimum of 12 months\n' +
              'Finishing level: the property is furnished and furnished to a basic standard. A more luxurious level is possible upon consultation\n' +
              'Included: air conditioning (hot/cold)\n' +
              'Security: a safe and an NVR video system (equipped with high-quality cameras) are built-in\n' +
              'Parking: on the street (no waiting list)\n' +
              "Deposit: two months' rent\n" +
              'Pets: not allowed\n' +
              'Students/room sharers: not allowed\n' +
              'The images are for illustrative purposes only. During a viewing, you can see everything, ask questions, and receive a detailed explanation of what this property has to offer and how you can further customize it to your liking!\n' +
              '\n' +
              'FEATURES\n' +
              '* Rent €3,100 per month \n' +
              '* Contribution for fully furnishment € 150 per month (basic level furnishings)\n' +
              '* No service charges. Inaccessible windows are cleaned 4 times a year (currently €40 per wash)\n' +
              '* Excluding gas, water, electricity, internet and municipal user charges\n' +
              '* Fiber optic internet is available at this address\n' +
              '* Double glazing, private entrance. Built in 1905',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-08-27T23:46:28.152Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-08-28 00:46:28'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-27T23:46:28.159Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:28'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-27T23:46:28.164Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:28'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-27T23:46:28.170Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:28'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-27T23:46:28.175Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:28'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-27T23:46:28.179Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:28'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-27T23:46:28.184Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:28'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-27T23:46:28.189Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:28'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1756338388188_0i8xw',
  source: 'funda',
  duration: '1ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:818:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'Heerlijke en ruime 5 kamer gezinswoning met tuin op het Zuiden en  en energielabel A.\n' +
              '\n' +
              'Gelegen in in een rustige en tevens kindvriendelijke woonwijk in Leidsche Rijn, vlakbij Het Lint, de Haarrijnseplas en winkelcentrum Terwijde. \n' +
              'Tevens is de bushalte met een directe verbinding met de stad op een paar minuten lopen. Ook de snelwegen en treinstation Terwijde zijn binnen een zeer afzienbare tijd te bereiken. \n' +
              '\n' +
              'De woning is gelegen in een rustig woningblok met een eigen parkeerplaats (overige plekken vrij parkeren) in een soort van hofje.\n' +
              '\n' +
              'De woning is ideaal voor een een stel of een gezin met (meer) kinderen gezien het aantal kamers.\n' +
              'Deze goed afgewerkte twee-laags woning kenmerkt zich door de grote raampartijen aan de achterzijde, heerlijke tuin op het Zuiden met achterom en 4 prima slaapkamers op de 1e verdieping.\n' +
              '\n' +
              'Indeling\n' +
              'Een opvallend ruime entree met toegang tot een netjes afgewerkte toilet met hangend closet en een doorgang naar de speelse leefruimte met half deels gesloten keuken. \n' +
              '\n' +
              'De woonkamer is samen met de keuken voorzien van mooie wit gesausde muren en een sfeervolle laminaatvloer welke netjes is doorgelegd. De ruimte is bijna 6 meter breed en handig ingedeeld in een zit en eetruimte. Aansluitend bevindt zich de keuken welke afgesloten is middels een muur met deur waardoor deze al dan wel of niet bij de woonkamer betrokken kan worden. Doordat er een deur met glas is geplaatst valt de doorzon verlichting lekker door de keuken. \n' +
              'De hoek vormige keuken is aan de voorzijde gelegen en beschikt over de meest noodzakelijke apparaten (o.a. vaatwasser) en heeft zowel veel aanrecht- dan wel kastruimte. De keuken is voorzien van neutrale witte kastjes met een donker blad. De tuin is handig te bereiken vanuit de woonkamer. \n' +
              '\n' +
              'Eerste verdieping\n' +
              'Op de 1e verdieping zijn de 4 slaapkamer en nieuw aangelegde badkamer met ruime inloopdouche, dubbele wastafel en zwevend toilet gelegen. De overloop is groot waardoor hier nog een extra werkplek is gecreëerd. \n' +
              '\n' +
              'Tuin\n' +
              'Heerlijke en netjes onderhouden achtertuin op het Zuiden van ca. 50m2 welke recentelijk nog voorzien is van nieuwe betegeling. Vrijstaande houten schuur aanwezig met betonnen ondervloer en licht en elektra. De tuin kan ook via een achterom bereikt worden. \n' +
              'Ook de voortuin is voorzien dezelfde tegels. Voor en achter bevinden zich een buitenkraan.  \n' +
              '\n' +
              'Overig:\n' +
              'In 2024 zijn de volgende zaken vernieuwd: Keukenkastjes voorzien van nieuwe witte folie, achter en voortuin voorzien van nette tegels, alle ramen op de eerste verdieping zijn voorzien van vliegenhorren, koel-vriescombinatie in de keuken, schilderwerk muren binnen, eetkamer stoelen, plafondlamp boven eetkamer, stofzuiger, buitenkranen, etc. \n' +
              '\n' +
              'Bijzonderheden\n' +
              '- Beschikbaar: 01 september 2025;\n' +
              '- Bij voorkeur een periode van 24 -36 maanden;\n' +
              '- De woning wordt gestoffeerd opgeleverd met een aantal meubels; eventueel kunnen er enkele eigen spullen worden vervangen naar wens van de nieuwe huurder of overgenomen worden van de huidige bewoner;\n' +
              "- De foto's zijn van de woning in verhuurde staat, dus de meeste meubels niet van toepassing, dan wel vernieuwd;\n" +
              '- Huurprijs is 2850 euro exclusief gebruikerslasten huurder (gas, water en elektra, gebruikersdeel gemeentelijke heffingen en TV/Internet );\n' +
              '- Niet te huur voor studenten of woningdelers; \n' +
              '- nabij alle faciliteiten, uitvalswegen en natuur;\n' +
              '- woning word verwarmd middels stadsverwarming;\n' +
              '- Eigen parkeerplaats voor de deur.\n' +
              '\n' +
              '----------------------------------------------------------------------------------------------------------------------\n' +
              'ENGLISH:\n' +
              '\n' +
              'Spacious and Delightful 5-Room Family Home with South-Facing Garden and Energy Label A**\n' +
              '\n' +
              "Located in a quiet and child-friendly residential area in Leidsche Rijn, close to *Het Lint*, *Haarrijnse Plas*, and *Terwijde* shopping center. A bus stop with a direct connection to the city center is just a few minutes' walk away. Highways and *Terwijde* train station are also easily and quickly accessible.\n" +
              '\n' +
              'The property is situated in a peaceful housing block with its own private parking space (additional free parking available) in a courtyard-like setting.\n' +
              '\n' +
              'The home is ideal for a couple or a family with (more) children, given the number of rooms.\n' +
              'This well-finished two-story house features large rear-facing windows, a lovely south-facing garden with back entrance, and four good-sized bedrooms on the first floor.\n' +
              '\n' +
              'Layout\n' +
              'A remarkably spacious entrance hall gives access to a neatly finished toilet with wall-mounted closet, and a passage to the playfully designed living area with a partially enclosed kitchen.\n' +
              '\n' +
              'The living room and kitchen feature clean white painted walls and an attractive laminate floor that continues seamlessly throughout the space. The room is nearly 6 meters wide and conveniently laid out with a sitting and dining area.\n' +
              'The kitchen is partially enclosed by a wall with a door, allowing it to be integrated into or separated from the living room. The door includes a glass panel, allowing natural light to flow through to the kitchen.\n' +
              '\n' +
              'The L-shaped kitchen is situated at the front of the house and comes equipped with essential appliances (including a dishwasher) and offers ample countertop and cabinet space. The kitchen has neutral white cabinetry with a dark countertop. The garden is easily accessible from the living room.\n' +
              '\n' +
              'First Floor\n' +
              'The first floor comprises four bedrooms and a newly renovated bathroom featuring a spacious walk-in shower, double sink, and wall-mounted toilet.\n' +
              'The landing is large enough to accommodate an additional workspace.\n' +
              '\n' +
              'Garden\n' +
              'A well-maintained, south-facing backyard of approx. 50m², recently updated with new paving. There is a detached wooden shed with a concrete base, lighting, and electricity. The garden can also be accessed via a back entrance.\n' +
              'The front garden is paved with the same tiles. Outdoor taps are available at both the front and rear.\n' +
              '\n' +
              'Additional Information\n' +
              'The following items were renewed in 2024: kitchen cabinet fronts with new white film, new paving in front and back gardens, insect screens on all first-floor windows, fridge-freezer in kitchen, interior wall paint, dining chairs, ceiling light above dining area, vacuum cleaner, outdoor taps, etc.\n' +
              '\n' +
              'Details\n' +
              '* Not available for students or home sharers;\n' +
              '* Available: September 1, 2025;\n' +
              '* Preferred rental period: 24–36 months;\n' +
              '* The property is rented semi-furnished with some furniture included; some items can be replaced by the new tenant or taken over from the current resident;\n' +
              '* Photos shown are from the rented state; most furniture shown is not included or has since been updated;\n' +
              '* Rent: €2,850 excluding user costs (gas, water, electricity, municipal user taxes, TV/internet);\n' +
              '* Close to all amenities, highways, and nature areas;\n' +
              '* Heating via district heating system;\n' +
              '* Private parking space in front of the house;',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-08-27T23:46:28.189Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-08-28 00:46:28'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-27T23:46:28.195Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:28'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-27T23:46:28.199Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:28'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1756338388198_jtwtw',
  source: 'funda',
  duration: '1ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:818:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'In het bruisend hart van Leidsche Rijn ligt op een schitterende locatie appartementencomplex “De Rossfelt”, de Vinex wijk van Utrecht! Leidsche Rijn is voorzien van een goede infrastructuur waardoor het complex, zowel met openbaar vervoer als de auto, goed te bereiken is en het van hieruit goed reizen is naar alle kanten van het land. De Rossfelt ligt midden in het centrum van Leidsche Rijn met een goed en compleet winkelaanbod. In de Rossfelt bevindt zich dit fraaie, luxe en aardgas loze vrije 3-kamer appartement.\n' +
              '\n' +
              'De overzichtelijke hal geeft gelijk een gevoel van thuiskomen en herbergt de meter- en stadsverwarmingskast in zich, de binnen berging met wasmachine aansluiting naast dat het ook de toegang geeft tot de 2 slaapkamers, het luxe afgewerkte toilet en badkamer welke beide luxe afgewerkt zijn met Villeroy & Boch en Grohekranen. Eenmaal in de lichte woonkamer valt gelijk het oog op de moderne, complete en luxe open Bruynzeel keuken welke is voorzien van Siemens inbouwapparatuur waar de koel- vriescombinatie, vaatwasser, combi-oven, keramische kookplaat en recirculatie afzuigkap dan ook niet in ontbreken. De lichte en ruime woonkamer geeft, net als een van de slaapkamers, toegang tot het fijne balkon, wat een goede toevoeging is voor het woongenot. Dit appartement is goed geïsoleerd, voorzien van HR++ glas en geheel voorzien van vloerverwarming en mechanische ventilatie met het fijne gevolg dat als er juist mee om wordt gegaan de energiekosten beperkt blijven. De appartementen zijn hoogwaardig afgewerkt en voorzien van behang. Kortom een zeer compleet en energiezuinig appartement op een goede en bereikbare locatie in Utrecht waar het fijn wonen en werken is. De eigen berging en parkeerplaats (verplichte afname) maken het plaatje compleet, kortom Leidsche Rijn zorgt voor fijn woongenot!\n' +
              '\n' +
              "Let op: Foto's dienen als impressie. De uitvoering van de badkamer, toilet en keuken kan per appartement verschillen.\n" +
              '\n' +
              'Kan ik deze woning huren?\n' +
              '    €   7.000,- Bruto per maand (één inkomen)\n' +
              '    €   8.000,- Bruto per maand (tweeverdieners)\n' +
              '\n' +
              'Wonen in De Rossfeld\n' +
              'In het bruisend hart van Leidsche Rijn ligt op een schitterende locatie appartementencomplex “De Rossfelt”, de Vinex wijk van Utrecht! Het openbaar vervoer is in deze wijk ruimschoots aanwezig waardoor er een vlotte verbinding is tussen de oude en nieuwe stadswijken van Utrecht. Diverse bruggen en viaducten in Leidsche Rijn zijn gebouwd met aparte rijbanen voor de bus. Het treinstation van Leidsche Rijn bevindt zich op loopafstand van het gebouw. Daarnaast ligt Leidsche Rijn tussen de snelwegen A2 en A12 waardoor het ook met de auto goed bereikbaar is. \n' +
              'Ook aan groen is gedacht binnen de wijk Leidsche Rijn, zo ligt hier het Willem-Alexanderpark op het dak van de Leidsche Rijntunnel. In het zuiden van de wijk bevindt zich de Recreatieplas Strijkviertel, een vroegere zandwinningsplas die is ontstaan in verband met de aanleg van de nabijgelegen autosnelwegen A2, A12 en het knooppunt Oudenrijn. Aan de rand van de wijk ligt het Máximapark, waarvan de oorspronkelijke naam Leidsche Rijnpark was. Het is een centraal park binnen de Vinex-locatie Leidsche Rijn, omringd door de wijk Leidsche Rijn in het oosten, het dorp De Meern in het zuiden en het dorp Vleuten in het westen. \n' +
              'De kers op de taart in deze wijk is het winkelcentrum wat het kloppend hart is van deze fijne woonwijk. Complex De Rossfelt telt 2-3- en 4 luxe kamerappartementen die elk voorzien zijn van hoogwaardige elementen, daarnaast heeft elk appartement zijn eigen balkon en/of terras. In het midden van het complex kan met z’n allen genoten worden van de fijne binnentuin. De appartementen zijn comfortabel en luxe en voorzien van een luxe Bruynzeelkeuken met een Siemens apparatuur zoals een kookplaat, afzuigkap, vaatwasser, combi-oven en koel-vriescombinatie. De complete badkamer is voorzien van Villeroy & Boch sanitair. \n' +
              'Het appartement is hoogwaardig afgewerkt en geheel voorzien van behang en vloerverwarming. De eigen berging en de mogelijkheid tot het huren van een zwerfparkeerplaats maken het plaatje compleet,\n' +
              'kortom Leidsche Rijn zorgt voor fijn woongenot!\n' +
              '\n' +
              'BEZICHTIGINGEN / INFORMATIE: \n' +
              "- Bezichtigingen kunnen ALLEEN op werkdagen tussen 8.30 en 17.00 uur, dus niet 's avonds of in het weekend. \n" +
              '- Telefonisch een bezichtiging plannen of informatie aanvragen is NIET mogelijk. Verhuurder heeft ervoor gekozen de gegadigden zelf rond te leiden en/of van informatie te voorzien. Een bezichtiging of informatie kan enkel worden aangevraagd via Funda of via de website van de verhurende makelaar. \n' +
              '\n' +
              'ENGLISH - VIEWINGS / INFORMATION: \n' +
              '- Viewings can ONLY be scheduled on weekdays between 8:30am and 5:00pm. Appointments in the evenings or on weekends are not possible. \n' +
              '- Planning a viewing and/or requesting information by phone is NOT possible. The landlord has chosen to arrange all the viewings and provide information themselves. A viewing or information can only be requested through Funda or the website of the rental agent.',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-08-27T23:46:28.199Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-08-28 00:46:28'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-27T23:46:28.204Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:28'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-27T23:46:28.207Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:28'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-27T23:46:28.213Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:28'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-27T23:46:28.218Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:28'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-27T23:46:28.222Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:28'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-27T23:46:28.226Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:28'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-27T23:46:28.231Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:28'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-27T23:46:28.235Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:28'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-27T23:46:28.239Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:28'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-27T23:46:28.244Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:28'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-27T23:46:28.249Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 00:46:28'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  listings: 75,
  level: 'info',
  message: 'funda scraping completed',
  timestamp: '2025-08-28 00:46:28'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '988177ms',
  listingsProcessed: 75,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-08-28 00:46:28'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:48:38',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:48:38',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:48:38',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:50:00'
}
{
  component: 'scraper',
  message: 'Running enabled scrapers: funda',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:50:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  listings: 75,
  level: 'info',
  message: 'funda scraping completed',
  timestamp: '2025-08-28 00:51:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '960189ms',
  listingsProcessed: 75,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-08-28 00:51:00'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:53:38',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:53:38',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:53:38',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:55:00'
}
{
  component: 'scraper',
  message: 'Running enabled scrapers: funda',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:55:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  listings: 75,
  level: 'info',
  message: 'funda scraping completed',
  timestamp: '2025-08-28 00:56:19'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '979947ms',
  listingsProcessed: 75,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-08-28 00:56:19'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:58:38',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:58:38',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 00:58:38',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:00:00'
}
{
  component: 'scraper',
  message: 'Running enabled scrapers: funda',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:00:00'
}
{
  message: 'Starting notification data cleanup',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:00:00'
}
{
  service: 'zakmakelaar-api',
  deletedResults: 0,
  deletedQueueItems: 0,
  level: 'info',
  message: 'Notification data cleanup completed',
  timestamp: '2025-08-28 01:00:00'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.367Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.378Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.382Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.387Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.391Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.395Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.400Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.407Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1756339279406_2kh0v',
  source: 'funda',
  duration: '1ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:818:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'Altijd al in een prachtig statig herenhuis willen wonen daterend uit 1910? Deze totaal gerenoveerde en VERDUURZAAMDE bovenwoning met energie label A, dakterras en charmant balkon. Met een totaal woonplezier van 180m2, hoge platfonds, oase van rust in een van de prachtige authentieke Utrechtse wijk. Met een maatvoering van bijna 8.00m bij 11.50m is dit echt een uniek woonhuis. Bij de zeer recente renovatie in 2021 is het huis volledig gerenoveerd, voorzien van een nieuwe indeling, prachtige woon-eetkeuken en nog 5 extra kamers, zoals een mooie eetkamer naast de keuken, waar je direct de mooie breedte van het huis ervaart.\n' +
              '\n' +
              "De woning is gelegen in een rustige wijk met mooie rijen herenhuizen. De Zeeheldenbuurt is ruim opgezet met veel groen voorzieningen en ruime tuinen. Dit woonhuis kijkt uit op de markante vrijstaande villa's die de buurt typeren. Restaurant C’est Ca en Goesting liggen om de hoek en voor de boodschappen ligt het vernieuwde winkelcentrum De Gaard op 3 minuten fietsen. Of als u zin hebt om op zaterdag ochtend naar de markt te willen fietsen, dan bent u binnen 10min in het prachtige Utrechtse centrum. \n" +
              '\n' +
              'Indeling\n' +
              '\n' +
              'Begane grond:\n' +
              '\n' +
              'Ruime entree waar direct de mooie sfeer voelbaar is, hoge plafond, mooie vloer en ruime trap naar boven, omdat de entree zo ruim is, is er voldoende plek om uw goede fietsen binnen te plaatsen. \n' +
              'Eerste verdieping:\n' +
              "Met zoals gezegd een vloeroppervlak van ca. 8.00m bij 11.50m is dit een zeer ruim opgezette verdieping, de vertrekken zijn ruim met het fraaie trappenhuis en de ruime overloop met hier een ruim toilet voorzien van prachtige marmer uit Italië. De woonkamer is van het type kamer-en-suite en ademt ook veel sfeer uit, de keuken ligt aan de achterzijde van de woning en is een indrukwekkend groot kookeiland, mooi midden in de ruimte geplaatst, voorzien van een ruime kastenwand wat het geheel mooi, gebalanceerd en zeer functioneel maakt. Naar achter toe is er een nieuwe glazen pui met openslaande deuren, veel glas wat de woning nog ruimtelijker maakt. Naast de keuken ligt een heerlijke knusse eetkamer waar voldoende ruimte is om heerlijk met uw familie te dineren. Aan de voorzijde naast de woonkamer ligt een extra kamer, deze is nu in gebruik als werkkamer, met aansluitend van een inpandig terras. Dit terras heeft een prachtige vloer, ronde boog en uitzicht op de witte villa's, het is hier heerlijk vertoeven, de zon ligging is zuid, een heerlijke plek van het huis. Aan de achterzijde kijk u juist weer uit op de ruime tuinen van de omgeving met het watertje wat achter het huis loopt, een mooi groen geheel met veel vogels. De muur naar het mooie en statige trappenhuis is open gemaakt, dit is ruimtelijk en laat de mooie breedte zien.\n" +
              '\n' +
              'Tweede verdieping:\n' +
              '\n' +
              'Ook hier tref u weer het mooie formaat van het huis aan, er is weer een ruime overloop met een spectaculair glazen dak. Hierdoor ervaart u een zeer ruimtelijk gevoel en wordt de gehele woning voorzien van natuurlijk licht en warmte van de zon, hier tref je een vaste trap naar het dakterras. Er zijn 3 slaapkamers en een nieuwe ruime badkamer. De badkamer is ook weer zeer modern en gebalanceerd met een ruime inloopdouche met een regen douche, vrijstaand ovaal ligbad en een tweede toilet. Er is een fraai wastafel meubel met twee waskommen, met een druk op de knop worden de ramen gebandeerd en heb je geen gordijnen nodig, een van de mooie gadgets van dit huis. \n' +
              'De slaapkamers zijn allemaal ruim van opzet. De vaste trap brengt je naar een fraai dakterras, een extra plek om buiten te zitten met mooi zicht op de omgeving en zicht op de Dom toren. Een groendak, voorzien van sedum en de plantenbakken zorgen voor de groene beleving. Een ruim terras om heerlijk op een zomerse dag buiten te eten, weer een prachtig geheel! \n' +
              '\n' +
              'Huurprijs:\n' +
              '\n' +
              '3895,00 per maand exclusief nuts voorzieningen.\n' +
              'Huurder is zelf contractant voor alle nutsvoorzieningen. \n' +
              '\n' +
              'Borg:\n' +
              'Staat gelijk aan 2 maanden huur.\n' +
              '\n' +
              'Ingangsdatum:\n' +
              'In overleg\n' +
              '\n' +
              'WILT U DE WONING BEZICHTIGINGEN?\n' +
              'Graag ontvangen wij per e-mail of contactformulier op onze website de volgende informatie:\n' +
              '\n' +
              '1. adres van de woning waarin u bent geïnteresseerd\n' +
              '2. telefoonnummer\n' +
              '3. bent u student, in loondienst of zelfstandig ondernemer?\n' +
              '4. werkende; voor welk bedrijf of organisatie? Studerend; welke studie/welke instelling\n' +
              '5. de gewenste huurperiode (voorkeur startdatum en verwachte huurperiode)\n' +
              '6. aantal bewoners en de relatie onderling (gezin/partners/vrienden)\n' +
              '7. bruto maand of jaarinkomen (eventueel ook dat van de partner)\n' +
              '\n' +
              'Nadat wij bovenstaande gegevens hebben ontvangen zullen wij contact opnemen voor het maken van een afspraak. Wij zullen de kandidaten die als eerst reageren als eerst benaderen.\n' +
              '\n' +
              'Alleen volledige aanvragen zullen in behandeling worden genomen.\n' +
              '\n' +
              'Heeft u vragen of het project of wilt u een bezichtiging plannen. Neem gerust contact op met ons kantoor.\n' +
              '\n' +
              'Deze informatie is door ons met de nodige zorgvuldigheid samengesteld. Geen enkele aansprakelijkheid wordt aanvaard voor enige onvolledigheid, onjuistheid of anderszins, of de consequenties daarvan. Alle maten en afmetingen zijn indicatief. De woning is niet NEN ingemeten en hierdoor zijn kleine afwijking mogelijk.',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-08-28T00:01:19.407Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.412Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.416Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.420Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.424Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1756339279423_fcy06',
  source: 'funda',
  duration: '1ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:818:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'Let op: alleen bezichtigingsaanvragen via funda worden in behandeling genomen!\n' +
              '\n' +
              'Je eigen 2-kamerappartement op een aantrekkelijke locatie op fietsafstand van het centrum! Dit appartementencomplex is gelegen op een ruim perceel aan de Hooft Graaflandstraat.\n' +
              '\n' +
              'Het betreffen 15 zelfstandige appartementen gelegen in de populaire woonwijk Hoograven op de begane grond, eerste en tweede etage. Deze appartementen zijn goed geïsoleerd en strak afgewerkt met hoogwaardige materialen. Ze zijn voorzien van een PVC-vloer, gestukte en gesausde wanden, eigen aansluitingen voor nutsvoorzieningen, internet en tv. \n' +
              'Ieder appartement beschikt over een luxe keuken voorzien van o.a. afwasmachine, combi-oven en afzuigkap. De badkamer is keurig in neutrale kleurstelling betegeld en is voorzien van een douche, wastafel en toilet. \n' +
              'Daarnaast is het pand volledig gasloos en beschikt het over een warmtepomp en HR++ glas, wat resulteert in een lage energierekening. \n' +
              '\n' +
              'Op het terrein is een overdekte fietsenstalling en 6 parkeerplaatsen welke vrij gebruikt mogen worden als bewoner. \n' +
              '\n' +
              'Locatie:\n' +
              'Op loopafstand van diverse winkelvoorzieningen waaronder het winkelcentrum “het hart van Hoograven” en nabij dé culinaire hotspot van Utrecht (Rotsoord) met haar verschillende cafés en restaurants zoals WT Urban Kitchen en Klein Berlijn. Tevens bevindt je je op fietsafstand van het oude centrum en is het openbaar vervoer om de hoek waarbij de parkeermogelijkheden (thans vrij parkeren) als extra pre aanwezig zijn. \n' +
              '\n' +
              'Appartement 2C-5\n' +
              'Indeling:\n' +
              'Eerste verdieping:\n' +
              'Appartementsvoordeur: hal, slaapkamer, badkamer met douche, toilet en wastafel, ruimte met wasmachine-aansluiting. Lichte woonkamer met keuken welke is voorzien van diverse apparatuur waaronder een koelkast, vriezer, vaatwasser, inductiekookplaat, afzuigkap & magnetron combi-oven. Vanuit de woonkamer bereik je het balkon door de grote raampartij met haar openslaande deuren.\n' +
              '\n' +
              'Ben je benieuwd of dit appartement bij je past? Wij maken graag een afspraak voor een rondleiding!\n' +
              '\n' +
              'Bijzonderheden:\n' +
              '- Prijs EUR 1.775,- exclusief;\n' +
              '- Huurder is zelfstandig contractant van de NUTS voorzieningen;\n' +
              '- Servicekosten EUR 86,24 per mnd;\n' +
              '- Oppervlakte ca. 45 m2;\n' +
              '- Balkon;\n' +
              '- Gelegen op de eerste etage;\n' +
              '- Twee maanden borg;\n' +
              '- Inkomenseis: 3 x maandhuur bruto;\n' +
              '- Energielabel: A++;\n' +
              '- Hoogwaardig afwerkingsniveau;\n' +
              '- Volledig gestoffeerd: PVC vloer, gordijnen/rolgordijnen/jaloezieën, lampen, handdoek haakjes, kapstok;\n' +
              '- Internet/tv aansluiting aanwezig, abonnement naar keuze;\n' +
              '- Huisdieren zijn niet toegestaan;\n' +
              '- Vrij parkeren op terrein of op openbare weg;\n' +
              '- Oplevering: 12 september 2025.\n' +
              '\n' +
              'Deze aanbieding is met zorg samengesteld echter geheel vrijblijvend en onder voorbehoud van goedkeuring verhuurder. Derhalve kunnen hier op geen enkele wijze geen rechten aan ontleend worden. Alle maten en afmetingen zijn indicatief en niet exact volgens de NEN-2580 norm.\n' +
              '\n' +
              'Note: Only viewing requests made via Funda will be processed!\n' +
              '\n' +
              'Your own 2-room apartment in an attractive location, just a short cycle from the city center! This apartment complex is situated on a spacious plot on Hooft Graaflandstraat.\n' +
              '\n' +
              'The complex consists of 15 self-contained apartments located in the popular residential area of Hoograven, spread across the ground, first, and second floors. These apartments are well insulated and finished to a high standard with quality materials. Each unit is equipped with PVC flooring, plastered and painted walls, individual utility connections, internet, and TV.\n' +
              '\n' +
              'Every apartment has a modern kitchen equipped with a dishwasher, combi oven, and extractor hood. The bathroom is neatly tiled in a neutral color scheme and features a shower, washbasin, and toilet.\n' +
              '\n' +
              'The building is completely gas-free and fitted with a heat pump and HR++ glass, resulting in a low energy bill.\n' +
              '\n' +
              'On the premises, there is a covered bicycle storage area and 6 parking spaces available for residents’ use.\n' +
              '\n' +
              'Location:\n' +
              'Within walking distance of several shopping facilities, including the shopping center “Het Hart van Hoograven,” and close to Utrecht’s culinary hotspot Rotsoord, home to cafés and restaurants such as WT Urban Kitchen and Klein Berlijn. The historic city center is only a short cycle away, public transport is just around the corner, and the availability of (currently free) parking adds to the convenience.\n' +
              '\n' +
              'Apartment 2C-5\n' +
              '\n' +
              'Layout – First floor:\n' +
              'Private entrance: hallway, bedroom, bathroom with shower, toilet, and washbasin, and a separate laundry area with washing machine connection. Bright living room with kitchen, fitted with various appliances including fridge, freezer, dishwasher, induction hob, extractor hood & microwave-combi oven. From the living room, large French doors provide access to the balcony.\n' +
              '\n' +
              'Curious whether this apartment is right for you? We would be happy to arrange a viewing!\n' +
              '\n' +
              'Details:\n' +
              '\n' +
              '- Rent: €1,775 excl.;\n' +
              '- Tenant contracts utilities directly;\n' +
              '- Service charges: €86.24 per month;\n' +
              '- Approx. 45 m²;\n' +
              '- Balcony;\n' +
              '- Located on the first floor;\n' +
              '- Two months’ deposit;\n' +
              '- Income requirement: 3x monthly rent (gross);\n' +
              '- Energy label: A++;\n' +
              '- High-quality finish;\n' +
              '- Fully furnished: PVC flooring, curtains/blinds, lighting, towel hooks, coat rack;\n' +
              '- Internet/TV connection available, subscription at tenant’s choice;\n' +
              '- Pets are not allowed;\n' +
              '- Free parking on-site or in public areas;\n' +
              '- Available: September 12, 2025.\n' +
              '- Note: Only viewing requests made via Funda will be processed!\n' +
              '\n' +
              'This listing has been compiled with care, but is entirely without obligation and subject to landlord approval. No rights can be derived from this advertisement in any way. All sizes and measurements are indicative and not exactly in accordance with the NEN-2580 standard.',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-08-28T00:01:19.424Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.429Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.434Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.438Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1756339279437_hqedp',
  source: 'funda',
  duration: '1ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: Build year must be between 1800 and 2030
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:818:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: 'Build year must be between 1800 and 2030',
          path: [ 'year' ],
          type: 'custom',
          context: { label: 'year', value: '1450', key: 'year' }
        }
      ]
    },
    timestamp: 2025-08-28T00:01:19.438Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.442Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.446Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.451Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.455Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.460Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.465Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.468Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1756339279467_bivw1',
  source: 'funda',
  duration: '1ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:818:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'Luxe en Duurzaam Wonen in Wonderwoods – Hoola van Nootenstraat 355, Utrecht\n' +
              '\n' +
              'Wonen in het hart van Utrecht, omringd door groen en moderne luxe? Dit volledig gemeubileerde appartement in het iconische Wonderwoods-gebouw biedt alles wat je zoekt! Met een woonoppervlakte van ca. 67 m², een prachtig balkon met adembenemend uitzicht over Utrecht, en een toplocatie vlak bij het Centraal Station, is dit een unieke kans.\n' +
              '\n' +
              'Over Wonderwoods\n' +
              'Wonderwoods is een baanbrekend, duurzaam woonconcept dat natuur en stad samenbrengt. Dit architectonische meesterwerk, geïnspireerd door het Bosco Verticale in Milaan, biedt een groene en gezonde leefomgeving met weelderige beplanting op de gevels en daktuinen. Het gebouw beschikt over luxe faciliteiten en innovatieve technologieën die bijdragen aan een duurzame en comfortabele levensstijl.\n' +
              '\n' +
              'Perfecte Locatie\n' +
              'Wonderwoods ligt in het nieuwe Beurskwartier, dé opkomende wijk van Utrecht. Hier woon je op steenworp afstand van Utrecht Centraal, het bruisende stadscentrum, en tal van restaurants, cafés en winkels. Ook het Jaarbeursplein en het Beatrix Theater bevinden zich op loopafstand. Daarnaast is het gebied goed bereikbaar met zowel het openbaar vervoer als de auto, met directe toegang tot belangrijke uitvalswegen.\n' +
              '\n' +
              'Indeling van het appartement\n' +
              'Ruime en lichte woonkamer met moderne inrichting en toegang tot het balkon met uitzicht op de Dom. De open keuken is voorzien van luxe inbouwapparatuur.\n' +
              'Via de hal bereik je de comfortabele slaapkamer, welke stijlvol is ingericht. De moderne badkamer met inloopdouche en wastafel is tevens bereikbaar via de hal, daarnaast is de separate toilet gesitueerd alsmede het washok met warmtepomp. \n' +
              '\n' +
              'Bijzonderheden\n' +
              '- Huurprijs: €2.600 per maand (exclusief servicekosten en nutsvoorzieningen).\n' +
              '- Servicekosten bedragen € 100,- per maand.\n' +
              '- Volledig gemeubileerd – direct intrekbaar!\n' +
              '- Duurzaam wonen in een van de meest innovatieve gebouwen van Utrecht.\n' +
              '- Toplocatie: vlak bij Utrecht Centraal, winkels en horeca.\n' +
              '- Woonoppervlakte: ca. 67 m².\n' +
              '- Groene leefomgeving met beplanting op het gebouw en gedeelde buitenruimtes.\n' +
              '- Fietsenstalling in de onderbouw.\n' +
              '\n' +
              'Wil jij wonen op deze unieke plek in Utrecht? Neem snel contact op voor een bezichtiging! \n' +
              '\n' +
              'Deze aanbieding is met zorg samengesteld echter geheel vrijblijvend en onder voorbehoud van goedkeuring verhuurder. Derhalve kunnen hier op geen enkele wijze geen rechten aan ontleend worden. Alle maten en afmetingen zijn indicatief en niet exact volgens de NEN-2580 norm.\n' +
              '\n' +
              'Luxury and Sustainable Living in Wonderwoods – Hoola van Nootenstraat 355, Utrecht\n' +
              '\n' +
              'Would you like to live in the heart of Utrecht, surrounded by greenery and modern luxury? This fully furnished apartment in the iconic Wonderwoods building offers everything you need! With approximately 67 m² of living space, a beautiful balcony with breathtaking views of Utrecht, and a prime location near Central Station, this is a unique opportunity.\n' +
              '\n' +
              'About Wonderwoods\n' +
              'Wonderwoods is a groundbreaking, sustainable living concept that seamlessly integrates nature and urban life. Inspired by Bosco Verticale in Milan, this architectural masterpiece provides a green and healthy living environment with lush vegetation on the façades and rooftop gardens. The building offers luxurious amenities and innovative technologies, ensuring a comfortable and eco-friendly lifestyle.\n' +
              '\n' +
              'Perfect Location\n' +
              'Wonderwoods is situated in Beurskwartier, Utrecht’s vibrant new district. Living here means being just a stone’s throw from Utrecht Central Station, the lively city center, and numerous restaurants, cafés, and shops. Jaarbeursplein and Beatrix Theater are also within walking distance. The area is easily accessible by public transport and car, with direct access to major highways.\n' +
              '\n' +
              'Apartment Layout\n' +
              'Spacious and bright living room with modern furnishings and access to the balcony, offering stunning views of the Dom Tower.\n' +
              'Open kitchen, fully equipped with high-end built-in appliances.\n' +
              'Comfortable and stylishly furnished bedroom, accessible via the hallway.\n' +
              'Modern bathroom with a walk-in shower and sink.\n' +
              'Separate toilet and utility room with a heat pump, also accessible via the hallway.\n' +
              'Key Features\n' +
              '? Rent: €2,600 per month (excluding service charges and utilities).\n' +
              '? Servicecosts: € 100 per month.\n' +
              '? Fully furnished – move-in ready!\n' +
              '? Sustainable living in one of Utrecht’s most innovative buildings.\n' +
              '? Prime location near Utrecht Central Station, shops, and restaurants.\n' +
              '? Living area: approx. 67 m².\n' +
              '? Green environment with vegetation on the building and shared outdoor spaces.\n' +
              '? Bicycle storage in the basement.\n' +
              '\n' +
              'Would you like to live in this one-of-a-kind location in Utrecht? Contact us today to schedule a viewing! \n' +
              '\n' +
              'Disclaimer: This offer has been carefully compiled; however, it is entirely without obligation and subject to the landlord’s approval. Therefore, no rights can be derived from it in any way. All measurements and dimensions are indicative and not exact according to the NEN-2580 standard.',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-08-28T00:01:19.468Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.472Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.476Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.480Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.485Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.489Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.492Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.495Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.499Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.505Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.510Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.515Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.520Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.524Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1756339279523_mgrn5',
  source: 'funda',
  duration: '1ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:818:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'MODERN EN NIEUW 3-KAMERAPPARTEMENT MET BALKON, BERGING EN PRIVÉPARKEERPLAATS – BESCHIKBAAR PER 1 SEPTEMBER 2025\n' +
              '\n' +
              'Ben jij op zoek naar een licht, ruim en instap klaar appartement in een rustige en goed bereikbare wijk van Utrecht? Dan is dit prachtige 3-kamerappartement aan de Madridstraat 228 in Leidsche Rijn precies wat je zoekt!\n' +
              '\n' +
              'Met twee slaapkamers, een groot balkon, een privéberging én een eigen parkeerplaats, biedt deze woning alles wat je nodig hebt voor comfortabel wonen. De woning is volledig gestoffeerd en tot in de puntjes afgewerkt met een moderne pvc-vloer in houtlook, stijlvolle raambekleding en verlichting in elke kamer.\n' +
              '\n' +
              'Dit appartement bevindt zich op een ideale locatie in Utrecht: rustig gelegen, maar toch vlak bij alle voorzieningen. Je woont op korte afstand van Leidsche Rijn Centrum met gezellige horeca, winkels, sportfaciliteiten, scholen en het Máximapark. De bereikbaarheid is uitstekend: station Leidsche Rijn en station Terwijde liggen om de hoek en via de A2 of A12 ben je binnen no-time in Amsterdam of Den Haag.\n' +
              '\n' +
              'Indeling:\n' +
              '\n' +
              'Via de centrale entree met bellentableau, brievenbussen, lift en trappenhuis bereik je de derde verdieping.\n' +
              'Het appartement beschikt over een ruime hal die toegang geeft tot alle vertrekken. De woonkamer is licht en ruim en biedt directe toegang tot het zonnige balkon. De open keuken biedt ruimte voor inbouwapparatuur en sluit naadloos aan op de woonruimte.\n' +
              '\n' +
              'Er zijn twee comfortabele slaapkamers, een nette badkamer met inloopdouche en wastafel, een apart toilet, en een handige inpandige berging met wasmachine-aansluiting.\n' +
              '\n' +
              'Voordelen:\n' +
              '\n' +
              'Instapklaar, modern en sfeervol ingericht\n' +
              'Gestoffeerd met pvc-vloer, verlichting en gordijnen\n' +
              'Ruim balkon op het zuidwesten\n' +
              'Privéparkeerplaats in de ondergelegen garage\n' +
              'Eigen berging voor extra opslagruimte\n' +
              'Energiezuinig (energielabel A+++)\n' +
              'Uitstekende bereikbaarheid met OV en auto\n' +
              'Geschikt voor lange termijn verhuur\n' +
              '\n' +
              'Huurcondities:\n' +
              '\n' +
              'Huurprijs; € 2.600,- per maand, is inclusief de servicekosten en exclusief gas, water, elektra, internet, tv en gemeentelijke belastingen;\n' +
              '\n' +
              'Er wordt een inkomenscheck gedaan;\n' +
              '\n' +
              'Waarborgsom; € 5.200,- eenmalig;\n' +
              '\n' +
              'Huisdieren zijn niet toegestaan;\n' +
              '\n' +
              'Roken niet toegestaan;\n' +
              '\n' +
              'Niet geschikt voor studenten of woningdelers;\n' +
              '\n' +
              'Vloerverwarming via stadsverwarming;\n' +
              '\n' +
              'Huurperiode: langdurig;\n' +
              '\n' +
              'Professionele eindschoonmaak verplicht op kosten huurder;\n' +
              '\n' +
              'Appartement kan per 1 september 2025 worden betrokken\n' +
              '\n' +
              'De informatie is met de nodige zorgvuldigheid samengesteld. Geen aansprakelijkheid wordt aanvaard voor enige onvolledigheid, onjuistheid of anderszins, of consequenties daarvan. Alle maten en afmetingen zijn indicatief.\n' +
              '\n' +
              'MODERN AND NEW 3-ROOM APARTMENT WITH BALCONY, STORAGE, AND PRIVATE PARKING – AVAILABLE FROM SEPTEMBER 1, 2025\n' +
              '\n' +
              'Are you looking for a bright, spacious, and move-in ready apartment in a quiet yet easily accessible neighbourhood of Utrecht? Then this beautiful 3-room apartment on Madridstraat 228 in Leidsche Rijn is exactly what you’re looking for!\n' +
              '\n' +
              'With two bedrooms, a large balcony, a private storage unit, and your own parking space, this home offers everything you need for comfortable living. The property comes fully fitted and is finished to perfection with a modern wood-look PVC floor, stylish window coverings, and lighting in every room.\n' +
              '\n' +
              'This apartment is ideally located in Utrecht: peacefully situated, yet close to all amenities. You are within short distance of Leidsche Rijn Center with cozy restaurants, shops, sports facilities, schools, and the Máximapark. The location offers excellent accessibility: Leidsche Rijn and Terwijde train stations are nearby, and the A2 and A12 motorways will get you to Amsterdam or The Hague in no time.\n' +
              '\n' +
              'Layout:\n' +
              'Through the central entrance with intercom panel, mailboxes, elevator, and stairwell, you reach the third floor.\n' +
              '\n' +
              'The apartment features a spacious hallway providing access to all rooms. The living room is bright and spacious, with direct access to the sunny balcony. The open kitchen has space for built-in appliances and connects seamlessly to the living area.\n' +
              '\n' +
              'There are two comfortable bedrooms, a neat bathroom with walk-in shower and sink, a separate toilet, and a convenient internal storage room with washing machine connection.\n' +
              '\n' +
              'Features:\n' +
              '\n' +
              '- Move-in ready, modern, and tastefully decorated\n' +
              '- Fitted with PVC flooring, lighting, and curtains\n' +
              '- Spacious southwest-facing balcony\n' +
              '- Private parking space in the underground garage\n' +
              '- Private storage unit for extra storage space\n' +
              '- Energy-efficient (Energy Label A+++)\n' +
              '- Excellent accessibility by public transport and car\n' +
              '- Suitable for long-term rental\n' +
              '\n' +
              'Rental conditions:\n' +
              '\n' +
              '- Rent: €2,600 per month, including service charges and excluding gas, water, electricity, internet, TV, and municipal taxes\n' +
              '- Income check required\n' +
              '- Security deposit: €5,200 (one-time)\n' +
              '- Pets not allowed\n' +
              '- Smoking not allowed\n' +
              '- Not suitable for students or house sharing\n' +
              '- Underfloor heating via district heating\n' +
              '- Long-term rental period\n' +
              '- Professional end-of-tenancy cleaning required at tenant’s expense\n' +
              '- Available from September 1, 2025\n' +
              '\n' +
              'The information has been compiled with due care. No liability is accepted for any incompleteness, inaccuracies, or otherwise, or the consequences thereof. All measurements and dimensions are indicative.',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-08-28T00:01:19.524Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.528Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1756339279527_6xpl8',
  source: 'funda',
  duration: '1ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:818:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'Een prachtig gestoffeerd appartement met groot balkon op het zuidwesten in Leidsche Rijn Centrum, grenzend aan het gezellige Brusselplein.\n' +
              '\n' +
              'De woning ligt centraal in Leidsche Rijn Centrum dat is ontworpen om te wonen, te werken en te leven. In een mediterrane sfeer met boulevards en promenades is dit centrum het kloppend hart van de wijk Leidsche Rijn met leuke winkels, gezellige terrasjes en pleinen.\n' +
              '\n' +
              'Hier kunt u ruiken, voelen, proeven en kopen. Beleving staat centraal. Daarnaast bevat het centrum een eigen NS-station en busstation. Er zijn niet alleen culturele voorzieningen in de buurt zoals de bioscoop en het Berlijnplein, maar ook tal van 1e lijns zorgvoorzieningen. Al deze voorzieningen zijn op loopafstand van het appartementencomplex.\n' +
              '\n' +
              'Begane grond:\n' +
              '\n' +
              'Centrale entree met brievenbussen en een bellentableau, met toegang tot de lift en het trappenhuis evenals de berging en de parkeergarage inclusief de overdekte fietsenstalling en eigen parkeerplek.\n' +
              '\n' +
              'Indeling:\n' +
              '\n' +
              'Het appartement is gesitueerd op de 2e verdieping. Entree in de hal die toegang geeft tot de andere ruimtes; een apart toilet met fonteintje, de badkamer met douche en wastafel met opberglades, een berging, de 2 slaapkamers met kiep/kantelraam en de woonkamer met semi-open keuken.\n' +
              '\n' +
              'De woonkamer biedt uitzicht op de Grauwaartsingel en geeft toegang tot het balkon. De semi-open keuken is van alle gemakken voorzien met inbouwapparatuur zoals een vaatwasser, koel-vries combinatie, combi-oven, kookplaat en afzuigkap. Daarnaast is er een berging met opstelplaats voor de wasmachine en de droger, de boiler en de vloerverwarmingsunit is eveneens daar.\n' +
              '\n' +
              'Het appartement beschikt over een moderne lichte laminaatvloer, offwhite gordijnen, de badkamer en het toilet hebben moderne accenten. Het appartement is gebouwd met de nieuwste, meest duurzame materialen en de afwerking is hoogwaardig en luxe. Tevens is het appartement zeer energiezuinig en heeft energielabel A; zeer goede isolatie, vloerverwarming- en koeling en een warmte-terugwininstallatie.\n' +
              '\n' +
              'Bijzonderheden:\n' +
              '\n' +
              'Huurprijs is inclusief de servicekosten en exclusief gas, water, elektra, internet, tv en gemeentelijke belastingen;\n' +
              '\n' +
              'Er wordt een inkomenscheck gedaan;\n' +
              '\n' +
              'Waarborg staat gelijk aan twee keer de maand huur;\n' +
              '\n' +
              'Huisdieren zijn niet toegestaan;\n' +
              '\n' +
              'Roken niet toegestaan;\n' +
              '\n' +
              'Niet geschikt voor studenten;\n' +
              '\n' +
              'Vloerverwarming via stadsverwarming;\n' +
              '\n' +
              'Woning kan per 1 september 2025 worden betrokken.\n' +
              '\n' +
              'Deze informatie is met de nodige zorgvuldigheid samengesteld. Geen aansprakelijkheid wordt aanvaard voor enige onvolledigheid, onjuistheid of anderszins, of de consequenties daarvan. Alle maten en afmetingen zijn indicatief.\n' +
              '\n' +
              '---------------------------------------------\n' +
              '\n' +
              'A beautifully furnished apartment with a large southwest-facing balcony in Leidsche Rijn Centrum, adjacent to the lively Brusselplein.\n' +
              '\n' +
              'The property is centrally located in Leidsche Rijn Centrum, designed for living, working, and enjoying life. With Mediterranean-inspired boulevards and promenades, this center is the heart of the Leidsche Rijn district, offering charming shops, cozy terraces, and squares. You can experience and enjoy everything here: the sights, smells, tastes, and experiences. Additionally, the center has its own NS train station and bus station. Nearby are not only cultural amenities like the cinema and Berlijnplein, but also many primary healthcare facilities. All these amenities are within walking distance of the apartment complex.\n' +
              '\n' +
              'Ground floor: Central entrance with mailboxes and an intercom panel, with access to the elevator and stairwell, as well as the storage area and parking garage, which includes a covered bike shed and your own parking space.\n' +
              '\n' +
              'Layout: The apartment is located on the 2nd floor. Entry through the hall, which provides access to the other rooms: a separate toilet with a small sink, the bathroom with a shower and sink with storage drawers, a storage room, two bedrooms with tilt/turn windows, and the living room with a semi-open kitchen. The living room overlooks the Grauwaartsingel and gives access to the balcony. The semi-open kitchen is fully equipped with built-in appliances, including a dishwasher, fridge-freezer combination, combi oven, hob, and extractor hood. There is also a storage room with space for a washing machine and dryer, as well as the boiler and floor heating unit. The apartment features a modern light laminate floor, off-white curtains, and the bathroom and toilet have modern accents. The apartment is built with the latest, most sustainable materials, and the finish is high-quality and luxurious. Furthermore, the apartment is very energy-efficient with energy label A; it has excellent insulation, floor heating and cooling, and a heat recovery system.\n' +
              '\n' +
              'Special features:\n' +
              '\n' +
              'Rent includes service costs and excludes gas, water, electricity, internet, TV, and municipal taxes;\n' +
              '\n' +
              'Income check will be performed;\n' +
              '\n' +
              "Deposit is equivalent to two months' rent;\n" +
              '\n' +
              'Pets are not allowed;\n' +
              '\n' +
              'Smoking is not allowed;\n' +
              '\n' +
              'Not suitable for students;\n' +
              '\n' +
              'Floor heating via district heating;\n' +
              '\n' +
              'Available from September 1, 2025.\n' +
              '\n' +
              'This information has been compiled with due care. No liability is accepted for any incompleteness, inaccuracies, or otherwise, or for the consequences thereof. All dimensions and measurements are indicative.',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-08-28T00:01:19.528Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.533Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.537Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.542Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.546Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.551Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1756339279550_xrd69',
  source: 'funda',
  duration: '1ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:818:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: '123WONEN DÉ VERHUURMAKELAAR VAN UTRECHT BIEDT AAN VOOR TIJDELIJKE VERHUUR VAN 9 MAANDEN:\n' +
              '\n' +
              'Charmant herenhuis aan de Badstraat – Utrecht (Buiten Wittevrouwen).\n' +
              '\n' +
              'Ben jij op zoek naar een ruim, sfeervol en karakteristiek woonhuis op een toplocatie in Utrecht? Dit prachtige herenhuis aan de Badstraat biedt comfortabel en stijlvol wonen in de populaire wijk Buiten Wittevrouwen. Met vier ruime slaapkamers, moderne badkamer, luxe keuken met groot eiland en een zonnige onderhoudsarme buitenruimte, plus een balkon op de eerste verdieping, is dit de ideale woning voor een stel, expat-gezin of werkende professional.\n' +
              '\n' +
              'Op loopafstand van het Griftpark en het Wilhelminapark, diverse winkels, supermarkten en horeca, slechts enkele minuten fietsen naar het centrum, CS Utrecht en Universiteit. Uitstekend bereikbaar met OV en nabij uitvalswegen (A27/A28)\n' +
              '\n' +
              'INDELING:\n' +
              '\n' +
              'BEGANE GROND:\n' +
              '\n' +
              'Bij binnenkomst in de hal heb je de trapopgang naar de 1e verdieping, toilet en de toegang naar de ruime woonkamer en open keuken. De vloer op de benedenverdieping is voorzien van een houten vloer. De woonkamer is ruim en licht en de open keuken is voorzien van een groot eiland met de gootsteen, inbouwkoelkast/vriezer, inbouw magnetron en oven en een 6-pits gaskookplaat en meer dan voldoende opbergruimte.\n' +
              '\n' +
              'EERSTE VERDIEPING:\n' +
              '\n' +
              'Op deze verdieping vindt je twee in grote variërende slaapkamers, de ruime badkamer met een bad en aparte douche en de trapopgang naar de tweede verdieping. De vloer op deze verdieping is net als op de begane grond een houten vloer. Op deze verdieping is ook een balkon.\n' +
              '\n' +
              'TWEEDE VERDIEPING:\n' +
              '\n' +
              'Op deze verdieping vindt je nog 2 in grote varierende slaapkamers, waar genoeg ruimte is om te slapen en/of te werken.\n' +
              '\n' +
              'BIJZONDERHEDEN:\n' +
              '\n' +
              '- De woning is beschikbaar per 6 oktober 2025;\n' +
              '- De huurperiode is een vaste periode van 9 maanden;\n' +
              '- Huurprijs is € 2400,- per maand;\n' +
              '- Waarborgsom 2 maanden huur;\n' +
              '- De huurprijs is exclusief G/W/E, tv/internet en Gemeentelijke heffingen deel huurder(s);\n' +
              '- De woning is gestoffeerd;\n' +
              '- Energielabel E;\n' +
              '- Parkeergelegenheid voor de deur (betaald);\n' +
              '- Niet roken;\n' +
              '- Geen huisdieren;\n' +
              '- Geen studenten / woningdelers;\n' +
              '\n' +
              'GEEN COURTAGE! 123WONEN WERKT ALS VERHUURMAKELAAR VOOR DE EIGENAAR;\n' +
              'BEZICHTIGINGEN KUNNEN ALLEEN ONLINE WORDEN AANGEVRAAGD.\n' +
              '\n' +
              'Vind je deze woning op een website waarop wij doorplaatsen?\n' +
              'Kijk dan op onze eigen website:  voor ons actuele aanbod!\n' +
              '\n' +
              'Vind je deze woning op een website waar wij doorverkopen?\n' +
              'Kijk dan op onze eigen website:  voor ons actuele aanbod!\n' +
              '\n' +
              'Neem contact op\n' +
              '\n' +
              '123WONEN UTRECHT\n' +
              'Ramstraat 31\n' +
              '3581 HD Utrecht\n' +
              '\n' +
              'T 030 - 760 33 94\n' +
              'E \n' +
              '\n' +
              '----\n' +
              '\n' +
              '123WONEN DÉ VERHUURMAKELAAR VAN UTRECHT OFFERS FOR TEMPORARY RENTAL OF 9 MONTHS:\n' +
              '\n' +
              'Charming townhouse on the Badstraat - Utrecht (Buiten Wittevrouwen).\n' +
              '\n' +
              'Are you looking for a spacious, attractive and characteristic house in a prime location in Utrecht? This beautiful townhouse on Badstraat offers comfortable and stylish living in the popular neighborhood of Buiten Wittevrouwen. With four spacious bedrooms, modern bathroom, luxury kitchen with large island and a sunny low maintenance outdoor area, plus a balcony on the second floor, this is the ideal home for a couple, expat family or working professional.\n' +
              '\n' +
              'Within walking distance of the Griftpark and Wilhelminapark, several stores, supermarkets and restaurants, just minutes by bike to the city center, CS Utrecht and University. Easily accessible by public transport and near roads (A27/A28)\n' +
              '\n' +
              'LAYOUT:\n' +
              '\n' +
              'GROUND FLOOR:\n' +
              '\n' +
              'Upon entering the hall you have the staircase to the 1st floor, toilet and access to the spacious living room and open kitchen. The floor on the ground floor has a wooden floor. The living room is spacious and bright and the open kitchen has a large island with the sink, built-in fridge/freezer, built-in microwave and oven and a 6-burner gas hob and more than enough storage space.\n' +
              '\n' +
              'FIRST FLOOR:\n' +
              '\n' +
              'On this floor you will find two bedrooms varying in size, the spacious bathroom with a tub and separate shower and the staircase to the second floor. The floor is also a wooden floor. There is also a balcony on this floor.\n' +
              '\n' +
              'SECOND FLOOR:\n' +
              '\n' +
              'On this floor you will find the 2 bedrooms varying in size, where there is enough space to sleep and/or work.\n' +
              '\n' +
              'PARTICULARS:\n' +
              '\n' +
              '- The property is available from October 6, 2025;\n' +
              '- The rental period is a fixed period of 9 months;\n' +
              '- Rent is € 2400,- per month;\n' +
              '- Security deposit 2 months rent;\n' +
              '- The rent is excluding G/W/E, TV/internet and Municipal levies share tenant(s);\n' +
              '- The property is unfurnished;\n' +
              '- Energy label E;\n' +
              '- Parking in front of the door (paid);\n' +
              '- No smoking;\n' +
              '- No pets;\n' +
              '- No students / house sharers\n' +
              '\n' +
              'NO COMMISSION! 123WONEN WORKS AS A RENTAL AGENT FOR THE OWNER;\n' +
              'VIEWINGS CAN ONLY BE REQUESTED ONLINE.\n' +
              '\n' +
              'Do you find this property on a website on which we post?\n' +
              'Please check our own website:  for our current offer!\n' +
              '\n' +
              'Do you find this property on a website where we resell?\n' +
              'Please check our own website:  for our current offer!\n' +
              '\n' +
              'Contact us\n' +
              '\n' +
              'EXPATRENTALS HOLLAND UTRECHT\n' +
              'Ramstraat 31\n' +
              '3581 HD Utrecht\n' +
              '\n' +
              'T 030 - 760 33 94\n' +
              'E',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-08-28T00:01:19.551Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.556Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1756339279555_sa1gv',
  source: 'funda',
  duration: '1ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:818:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'For English below\n' +
              '\n' +
              'Let op! Alleen voor (deel- of voltijd) studenten beschikbaar!\n' +
              '\n' +
              'Sfeervolle 3-kamer (waarvan 1 slaapkamer) woning (ca. 70 m2) met grote achtertuin met berging en achterom in de Rivierenwijk in Utrecht!\n' +
              '\n' +
              'In de Rivierenwijk woon je op een ideale locatie, met alle voorzieningen binnen handbereik en het centrum van Utrecht op korte afstand. Hier vind je echt iets bijzonders – een plek zoals geen ander.\n' +
              '\n' +
              'Deze charmante tussenwoning uit de jaren 30 ligt in een rustige straat en valt op door de mooie lichtinval, de nette afwerking en de combinatie van authentieke elementen met modern comfort. De woning is voorzien van een royale woonkeuken, klassieke en suite deuren en een heerlijke, diepe achtertuin op het oosten.\n' +
              '\n' +
              'De Rivierenwijk is een gewilde en levendige buurt met een dorps karakter, op slechts enkele minuten fietsen van de binnenstad. Je woont hier tussen de gezellige straatjes, met volop voorzieningen zoals winkels, cafés, scholen en sportfaciliteiten in de buurt. NS-station Vaartsche Rijn en het populaire Ledig Erf liggen op loopafstand, en ook Utrecht Centraal is snel te bereiken. Parkeren kan met parkeervergunning te verkrijgen bij de gemeente Utrecht of betaald parkeren.\n' +
              '\n' +
              'Indeling\n' +
              '\n' +
              'Begane grond:\n' +
              'Entree, hal, meterkast en modern toilet. Vanuit de hal kom je in de ruime woonkeuken aan de voorzijde van de woning, voorzien van een royaal keukenblok met o.a. vaatwasser, combi-oven/magnetron en een koelkast met vriesvak. De lichte lamelparketvloer en de originele ensuite deuren geven de ruimte karakter. Aan de achterzijde bevindt zich de uitgebouwde woonkamer, met een lichtkoepel in het plafond en openslaande deuren naar de tuin – een heerlijke, lichte leefruimte!\n' +
              '\n' +
              'Eerste verdieping:\n' +
              'Overloop met toegang tot een aparte kamer met wasmachineaansluiting, doorlopend naar een royale slaapkamer met hoog plafond en praktische kastruimte. Indien gewenst is deze kamer eenvoudig ook te gebruiken als werkkamer.\n' +
              '\n' +
              'De verzorgde badkamer is uitgerust met een ligbad/douche, tweede toilet en een wastafelmeubel.\n' +
              '\n' +
              'Tweede verdieping:\n' +
              'Vliering met bergruimte en opstelplaats voor de Cv-combiketel.\n' +
              '\n' +
              'Tuin:\n' +
              'De onderhoudsvriendelijke achtertuin van circa 64 m² ligt op het oosten en is bereikbaar via een achterom. Dankzij de diepte van de tuin is er op elk moment van de dag wel een zonnig plekje te vinden. Er staat een stenen berging – ideaal voor fietsen of tuingereedschap.\n' +
              '\n' +
              'Bijzonderheden:\n' +
              '- Huurprijs: € 2.125,- excl. g/w/e en gemeentelijke (huurders-)lasten per maand;\n' +
              '- Alleen voor (deel- of voltijd) studenten beschikbaar/alleenstaand of stel;\n' +
              '- Woonoppervlakte: circa 70 m²;\n' +
              '- Tuin op het oosten, ca. 60 m²\n' +
              '- Energielabel B;\n' +
              '- Huurperiode voor 24 maanden;\n' +
              '- Verwarming en warm water via C.V.-ketel;\n' +
              '- Waarborgsom 2 maanden huur;\n' +
              '- Beschikbaar: 1 oktober 2025.\n' +
              '\n' +
              'Protocol toewijzing kandidaat-huurders: \n' +
              '------------\n' +
              '\n' +
              'Attention! Only available for (part-time or full-time) students!\n' +
              '\n' +
              'Charming 3-room house (approx. 70 m²) (1 large bedroom) with a large back garden with a storage shed and rear access in the Rivierenwijk district of Utrecht!\n' +
              '\n' +
              "In the Rivierenwijk district, you'll live in an ideal location, with all amenities within easy reach and the city center of Utrecht just a short distance away. Here you'll find something truly special – a place like no other.\n" +
              '\n' +
              'This charming 1930s terraced house is located on a quiet street and stands out for its beautiful natural light, neat finish, and combination of authentic features with modern comforts. The house features a spacious kitchen/diner, classic en suite doors, and a lovely, deep east-facing back garden.\n' +
              '\n' +
              "The Rivierenwijk district is a desirable and vibrant neighborhood with a village character, just a few minutes' bike ride from the city center. You'll live here among charming streets, with plenty of amenities such as shops, cafes, schools, and sports facilities nearby. Vaartsche Rijn train station and the popular Ledig Erf shopping center are within walking distance, and Utrecht Central Station is also easily accessible. Parking is available with a permit available from the municipality of Utrecht, or there's paid parking.\n" +
              '\n' +
              'Layout\n' +
              '\n' +
              'Ground floor:\n' +
              'Entrance hall, meter cupboard, and modern toilet. From the hall, you enter the spacious kitchen/diner at the front of the house, equipped with a generously sized kitchen unit including a dishwasher, combination oven/microwave, and a refrigerator with freezer compartment. The light laminated parquet flooring and the original ensuite doors give the space character. At the rear is the extended living room, with a skylight in the ceiling and French doors leading to the garden – a wonderful, bright living space!\n' +
              '\n' +
              'First floor:\n' +
              'Landing with access to a separate room with a washing machine connection, leading to a spacious bedroom with a high ceiling and practical closet space. This room could easily be used as a study if desired.\n' +
              '\n' +
              'The well-maintained bathroom is equipped with a bath/shower, a second toilet, and a vanity unit.\n' +
              '\n' +
              'Second floor:\n' +
              'Attic with storage space and the central heating boiler. Garden:\n' +
              "The low-maintenance backyard of approximately 64 m² faces east and is accessible via a rear entrance. Thanks to its depth, there's always a sunny spot to be found. There's a brick shed – ideal for bicycles or garden tools.\n" +
              '\n' +
              'Garden:\n' +
              "The low-maintenance backyard of approximately 64 m² faces east and is accessible via a rear entrance. Thanks to its depth, there's always a sunny spot to be found. There's a brick shed – ideal for bicycles or garden tools.\n" +
              '\n' +
              'Details:\n' +
              '- Rent: € 2.125,- per month, excluding utilities and municipal (tenant) charges;\n' +
              '- Available only for (part-time or full-time) students/singles or couples;\n' +
              '- Living area: approximately 70 m²;\n' +
              '- East-facing garden, approximately 60 m²;\n' +
              '- Energy label B;\n' +
              '- 24-month lease;\n' +
              '- Heating and hot water via central heating boiler;\n' +
              '- Deposit 2 months rent;\n' +
              '- Available: October 1, 2025.',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-08-28T00:01:19.556Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.559Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.562Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.566Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.570Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.574Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.577Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.581Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.585Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.589Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.592Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.596Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.600Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.604Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1756339279603_kcfjk',
  source: 'funda',
  duration: '1ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:818:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'Omschrijving: \n' +
              '\n' +
              'Instapklaar, charmant en centraal in de binnenstad van Utrecht gelegen dubbel bovenhuis met zowel nieuwe àls oude stijlkenmerken. Aan deze erg gewilde en tevens rustige woonstraat in de wijk Hoogh Boulandt bevindt zich dit complete en zeer leefbare appartement. De woning biedt sfeer, comfort en veiligheid. Het Centraal Station, Station Vaartsche Rijn, het Ledig Erf, ’t Wed en de voorzieningen in het centrum, aan de Oudegracht en in de Twijnstraat zijn allemaal binnen 10 minuten lopend (of ca. 2-3 minuten per fiets) te bereiken. Ga je niet voor winkelen en horeca dan ligt het singelpark om de hoek om te recreëren. De klassieke details, modern gemak en de geweldige ligging maken dat je hier nooit meer weg wilt!\n' +
              '\n' +
              'Indeling:\n' +
              'Op de beletage binnengekomen loop je direct de trap op naar de woonverdieping. Aan de hal grenzen een kleine kamer aan de voorzijde, een WC en de woonkamer met open keuken. Deze verdieping biedt hoge plafonds en een mooie houten vloerafwerking. De woonkamer is opgedeeld in drie delen: een zitkamer, een eetkamer en een leeskamer/serre. De zitkamer en de eetkamer zijn vanuit de hal apart toegankelijk en af te sluiten middels stijlvolle en suite deuren. De serre is ook af te sluiten door glazen schuifdeuren. De moderne open keuken is door het spoeleiland praktisch van opzet en is van diverse (inbouw)apparatuur voorzien. De keuken biedt toegang tot het ruime overkapte balkon/terras. Hier is het door het hoge plafond fijn borrelen en tafelen in de middag- en avondzon (ZW ligging).\n' +
              'Via de trap in de hal kom je op de slaap-/werkverdieping (bovenste woonlaag). In het trapgat bevindt zich de overgedimensioneerde airconditioning die naar gelang je kamerdeuren open/dicht doet de achterliggende vertrekken al dan niet koelt of verwarmt (in aanvulling op de CV installatie). Naast de trap ligt het ketelhok met wasmachine. Aan de overloop grenst ook badkamer voorzien van regendouche, WC en dubbele wastafel. Er zijn drie slaapvertrekken waarvan er nu één dienst doet als werkkamer. In de inbouwkasten is een condensdroger opgenomen. Achter deze werkkamer bevindt zicht het royale dakterras dat aan de groene binnentuin grenst en al tegen het eind van de ochtend tot ca. 20.30 uur in de zomerzon ligt. Het is hier heerlijk loungen!\n' +
              '\n' +
              'Omgeving en parkeren:\n' +
              'De locatie en de omgeving waar het appartement is gelegen is ideaal voor mensen zonder auto, maar ook door bewoners met een auto kan er moeiteloos geparkeerd worden in de straat (eenrichtings-/bestemmingsverkeer, parkeervergunning aanvragen). \n' +
              'De woning is zeer goed gelegen ten opzichte van het Centrum, de dichtbij gelegen NS stations en ook ten opzichte van de uitvalswegen, denk aan Catharijnesingel richting o.a. Rubenslaan/Waterlinieweg (richting A27-A28), Jutfaseweg/Europalaan (richting A27-A12/A2) en overige. \n' +
              '\n' +
              'Bijzonderheden:\n' +
              'Ingangsdatum: 01-09-2025\n' +
              'Duur huurperiode: onbepaalde tijd met een minimumduur van 12 maanden\n' +
              'Opleveringsniveau: de woning wordt gemeubileerd opgeleverd op een basisniveau. In overleg is een luxer niveau mogelijk\n' +
              'Aanwezig: airconditioning (warm/koud)\n' +
              'Veiligheid: een kluis en een NVR videosysteem (voorzien van diverse hoogwaardige camera’s) zijn ingebouwd\n' +
              'Parkeren: op straat (geen wachtlijst)\n' +
              'Borg: twee maanden huur\n' +
              'Huisdieren: niet toegestaan\n' +
              'Woningdelers/meerpersoonshuishouden niet toegestaan\n' +
              'De beelden zijn indicatief. Tijdens een bezichtiging kunt u alles zien, vragen en een uitgebreide toelichting krijgen wat deze woning biedt en hoe u deze nog deels naar uw hand kunt zetten!\n' +
              '\n' +
              'Kenmerken: \n' +
              '* Kale huurprijs € 3.100 per maand \n' +
              '* Kosten voor meubilering € 150,- per maand (basisniveau)\n' +
              '* Geen servicekosten. Wel onbereikbare ramen wassen 4x p/j (nu € 40 per keer)\n' +
              '* Excl. gas/water/elektra/internet/gemeentelijke gebruikersheffingen\n' +
              '* Glasvezel internet is beschikbaar op dit adres\n' +
              '* Dubbel glas, eigen entree. Bouwjaar 1905\n' +
              '\n' +
              '* ENGLISH VERSION * \n' +
              '\n' +
              'Discribtion\n' +
              "Turnkey, charming and centrally located in Utrecht's city center, this duplex apartment features both new and period features. This fully equipped and very livable apartment is located on this highly desirable and quiet residential street in the Hoogh Boulandt neighborhood. It offers ambiance, comfort and security. Central Station, Vaartsche Rijn Station, Ledig Erf, 't Wed and the amenities in the city center, along Oudegracht and in Twijnstraat are all within a 10-minute walk (or approximately 2-3 minutes by bike). If shopping and dining aren't your thing, the Singelpark is just around the corner for recreation. The classic details, modern conveniences, and fantastic location will make you never want to leave!\n" +
              '\n' +
              'Floorplan\n' +
              'Entering the raised ground floor, you immediately ascend the stairs to the living area. Adjacent to the hall are a small room at the front, a toilet, and the living room with an open-plan kitchen. This floor boasts high ceilings and beautiful wooden flooring. The living room is divided into three areas: a sitting room, a dining room, and a reading room/conservatory. The sitting room and dining room are accessible separately from the hall and can be separated by stylish en-suite doors. The conservatory can also be closed off with sliding glass doors. The modern open-plan kitchen features a practical layout thanks to its island sink and is equipped with various (built-in) appliances. The kitchen opens onto the spacious covered balcony/terrace. The high ceilings make it ideal for enjoying drinks and meals in the afternoon and evening sun (southwest orientation).\n' +
              '\n' +
              "The stairs in the hall lead to the bedrooms/study (top floor). The oversized air conditioning unit is located in the stairwell and cools or heats the rooms behind it depending on whether you open or close the doors (in addition to the central heating system). Next to the stairs is the boiler room with a washing machine. The bathroom has a rain shower, a toilet and a double sink. There are three bedrooms, one of which currently serves as a study. A condenser dryer is integrated into the built-in wardrobes. Behind this study is the spacious roof terrace, which borders the green courtyard and enjoys the summer sun from late morning until around 8:30 PM. It's a wonderful place to lounge!\n" +
              '\n' +
              'ENVIRONMENT AND PARKING\n' +
              'The location and surrounding area of the apartment are ideal for those without a car, but residents with a car can also easily park on the street (one-way/destination traffic; parking permit required). The apartment is very well-located near the city center, the nearby train stations and arterial roads, such as Catharijnesingel towards Rubenslaan/Waterlinieweg (towards the A27-A28), Jutfaseweg/Europalaan (towards the A27-A12/A2) and others.\n' +
              '\n' +
              'DETAILS\n' +
              'Start date: September 1, 2025\n' +
              'Lease term: indefinite with a minimum of 12 months\n' +
              'Finishing level: the property is furnished and furnished to a basic standard. A more luxurious level is possible upon consultation\n' +
              'Included: air conditioning (hot/cold)\n' +
              'Security: a safe and an NVR video system (equipped with high-quality cameras) are built-in\n' +
              'Parking: on the street (no waiting list)\n' +
              "Deposit: two months' rent\n" +
              'Pets: not allowed\n' +
              'Students/room sharers: not allowed\n' +
              'The images are for illustrative purposes only. During a viewing, you can see everything, ask questions, and receive a detailed explanation of what this property has to offer and how you can further customize it to your liking!\n' +
              '\n' +
              'FEATURES\n' +
              '* Rent €3,100 per month \n' +
              '* Contribution for fully furnishment € 150 per month (basic level furnishings)\n' +
              '* No service charges. Inaccessible windows are cleaned 4 times a year (currently €40 per wash)\n' +
              '* Excluding gas, water, electricity, internet and municipal user charges\n' +
              '* Fiber optic internet is available at this address\n' +
              '* Double glazing, private entrance. Built in 1905',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-08-28T00:01:19.604Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.608Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.611Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.615Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.619Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.623Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.627Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.631Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1756339279630_kuxt8',
  source: 'funda',
  duration: '1ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:818:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'Heerlijke en ruime 5 kamer gezinswoning met tuin op het Zuiden en  en energielabel A.\n' +
              '\n' +
              'Gelegen in in een rustige en tevens kindvriendelijke woonwijk in Leidsche Rijn, vlakbij Het Lint, de Haarrijnseplas en winkelcentrum Terwijde. \n' +
              'Tevens is de bushalte met een directe verbinding met de stad op een paar minuten lopen. Ook de snelwegen en treinstation Terwijde zijn binnen een zeer afzienbare tijd te bereiken. \n' +
              '\n' +
              'De woning is gelegen in een rustig woningblok met een eigen parkeerplaats (overige plekken vrij parkeren) in een soort van hofje.\n' +
              '\n' +
              'De woning is ideaal voor een een stel of een gezin met (meer) kinderen gezien het aantal kamers.\n' +
              'Deze goed afgewerkte twee-laags woning kenmerkt zich door de grote raampartijen aan de achterzijde, heerlijke tuin op het Zuiden met achterom en 4 prima slaapkamers op de 1e verdieping.\n' +
              '\n' +
              'Indeling\n' +
              'Een opvallend ruime entree met toegang tot een netjes afgewerkte toilet met hangend closet en een doorgang naar de speelse leefruimte met half deels gesloten keuken. \n' +
              '\n' +
              'De woonkamer is samen met de keuken voorzien van mooie wit gesausde muren en een sfeervolle laminaatvloer welke netjes is doorgelegd. De ruimte is bijna 6 meter breed en handig ingedeeld in een zit en eetruimte. Aansluitend bevindt zich de keuken welke afgesloten is middels een muur met deur waardoor deze al dan wel of niet bij de woonkamer betrokken kan worden. Doordat er een deur met glas is geplaatst valt de doorzon verlichting lekker door de keuken. \n' +
              'De hoek vormige keuken is aan de voorzijde gelegen en beschikt over de meest noodzakelijke apparaten (o.a. vaatwasser) en heeft zowel veel aanrecht- dan wel kastruimte. De keuken is voorzien van neutrale witte kastjes met een donker blad. De tuin is handig te bereiken vanuit de woonkamer. \n' +
              '\n' +
              'Eerste verdieping\n' +
              'Op de 1e verdieping zijn de 4 slaapkamer en nieuw aangelegde badkamer met ruime inloopdouche, dubbele wastafel en zwevend toilet gelegen. De overloop is groot waardoor hier nog een extra werkplek is gecreëerd. \n' +
              '\n' +
              'Tuin\n' +
              'Heerlijke en netjes onderhouden achtertuin op het Zuiden van ca. 50m2 welke recentelijk nog voorzien is van nieuwe betegeling. Vrijstaande houten schuur aanwezig met betonnen ondervloer en licht en elektra. De tuin kan ook via een achterom bereikt worden. \n' +
              'Ook de voortuin is voorzien dezelfde tegels. Voor en achter bevinden zich een buitenkraan.  \n' +
              '\n' +
              'Overig:\n' +
              'In 2024 zijn de volgende zaken vernieuwd: Keukenkastjes voorzien van nieuwe witte folie, achter en voortuin voorzien van nette tegels, alle ramen op de eerste verdieping zijn voorzien van vliegenhorren, koel-vriescombinatie in de keuken, schilderwerk muren binnen, eetkamer stoelen, plafondlamp boven eetkamer, stofzuiger, buitenkranen, etc. \n' +
              '\n' +
              'Bijzonderheden\n' +
              '- Beschikbaar: 01 september 2025;\n' +
              '- Bij voorkeur een periode van 24 -36 maanden;\n' +
              '- De woning wordt gestoffeerd opgeleverd met een aantal meubels; eventueel kunnen er enkele eigen spullen worden vervangen naar wens van de nieuwe huurder of overgenomen worden van de huidige bewoner;\n' +
              "- De foto's zijn van de woning in verhuurde staat, dus de meeste meubels niet van toepassing, dan wel vernieuwd;\n" +
              '- Huurprijs is 2850 euro exclusief gebruikerslasten huurder (gas, water en elektra, gebruikersdeel gemeentelijke heffingen en TV/Internet );\n' +
              '- Niet te huur voor studenten of woningdelers; \n' +
              '- nabij alle faciliteiten, uitvalswegen en natuur;\n' +
              '- woning word verwarmd middels stadsverwarming;\n' +
              '- Eigen parkeerplaats voor de deur.\n' +
              '\n' +
              '----------------------------------------------------------------------------------------------------------------------\n' +
              'ENGLISH:\n' +
              '\n' +
              'Spacious and Delightful 5-Room Family Home with South-Facing Garden and Energy Label A**\n' +
              '\n' +
              "Located in a quiet and child-friendly residential area in Leidsche Rijn, close to *Het Lint*, *Haarrijnse Plas*, and *Terwijde* shopping center. A bus stop with a direct connection to the city center is just a few minutes' walk away. Highways and *Terwijde* train station are also easily and quickly accessible.\n" +
              '\n' +
              'The property is situated in a peaceful housing block with its own private parking space (additional free parking available) in a courtyard-like setting.\n' +
              '\n' +
              'The home is ideal for a couple or a family with (more) children, given the number of rooms.\n' +
              'This well-finished two-story house features large rear-facing windows, a lovely south-facing garden with back entrance, and four good-sized bedrooms on the first floor.\n' +
              '\n' +
              'Layout\n' +
              'A remarkably spacious entrance hall gives access to a neatly finished toilet with wall-mounted closet, and a passage to the playfully designed living area with a partially enclosed kitchen.\n' +
              '\n' +
              'The living room and kitchen feature clean white painted walls and an attractive laminate floor that continues seamlessly throughout the space. The room is nearly 6 meters wide and conveniently laid out with a sitting and dining area.\n' +
              'The kitchen is partially enclosed by a wall with a door, allowing it to be integrated into or separated from the living room. The door includes a glass panel, allowing natural light to flow through to the kitchen.\n' +
              '\n' +
              'The L-shaped kitchen is situated at the front of the house and comes equipped with essential appliances (including a dishwasher) and offers ample countertop and cabinet space. The kitchen has neutral white cabinetry with a dark countertop. The garden is easily accessible from the living room.\n' +
              '\n' +
              'First Floor\n' +
              'The first floor comprises four bedrooms and a newly renovated bathroom featuring a spacious walk-in shower, double sink, and wall-mounted toilet.\n' +
              'The landing is large enough to accommodate an additional workspace.\n' +
              '\n' +
              'Garden\n' +
              'A well-maintained, south-facing backyard of approx. 50m², recently updated with new paving. There is a detached wooden shed with a concrete base, lighting, and electricity. The garden can also be accessed via a back entrance.\n' +
              'The front garden is paved with the same tiles. Outdoor taps are available at both the front and rear.\n' +
              '\n' +
              'Additional Information\n' +
              'The following items were renewed in 2024: kitchen cabinet fronts with new white film, new paving in front and back gardens, insect screens on all first-floor windows, fridge-freezer in kitchen, interior wall paint, dining chairs, ceiling light above dining area, vacuum cleaner, outdoor taps, etc.\n' +
              '\n' +
              'Details\n' +
              '* Not available for students or home sharers;\n' +
              '* Available: September 1, 2025;\n' +
              '* Preferred rental period: 24–36 months;\n' +
              '* The property is rented semi-furnished with some furniture included; some items can be replaced by the new tenant or taken over from the current resident;\n' +
              '* Photos shown are from the rented state; most furniture shown is not included or has since been updated;\n' +
              '* Rent: €2,850 excluding user costs (gas, water, electricity, municipal user taxes, TV/internet);\n' +
              '* Close to all amenities, highways, and nature areas;\n' +
              '* Heating via district heating system;\n' +
              '* Private parking space in front of the house;',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-08-28T00:01:19.631Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.635Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.638Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1756339279637_p8bj1',
  source: 'funda',
  duration: '1ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:818:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'In het bruisend hart van Leidsche Rijn ligt op een schitterende locatie appartementencomplex “De Rossfelt”, de Vinex wijk van Utrecht! Leidsche Rijn is voorzien van een goede infrastructuur waardoor het complex, zowel met openbaar vervoer als de auto, goed te bereiken is en het van hieruit goed reizen is naar alle kanten van het land. De Rossfelt ligt midden in het centrum van Leidsche Rijn met een goed en compleet winkelaanbod. In de Rossfelt bevindt zich dit fraaie, luxe en aardgas loze vrije 3-kamer appartement.\n' +
              '\n' +
              'De overzichtelijke hal geeft gelijk een gevoel van thuiskomen en herbergt de meter- en stadsverwarmingskast in zich, de binnen berging met wasmachine aansluiting naast dat het ook de toegang geeft tot de 2 slaapkamers, het luxe afgewerkte toilet en badkamer welke beide luxe afgewerkt zijn met Villeroy & Boch en Grohekranen. Eenmaal in de lichte woonkamer valt gelijk het oog op de moderne, complete en luxe open Bruynzeel keuken welke is voorzien van Siemens inbouwapparatuur waar de koel- vriescombinatie, vaatwasser, combi-oven, keramische kookplaat en recirculatie afzuigkap dan ook niet in ontbreken. De lichte en ruime woonkamer geeft, net als een van de slaapkamers, toegang tot het fijne balkon, wat een goede toevoeging is voor het woongenot. Dit appartement is goed geïsoleerd, voorzien van HR++ glas en geheel voorzien van vloerverwarming en mechanische ventilatie met het fijne gevolg dat als er juist mee om wordt gegaan de energiekosten beperkt blijven. De appartementen zijn hoogwaardig afgewerkt en voorzien van behang. Kortom een zeer compleet en energiezuinig appartement op een goede en bereikbare locatie in Utrecht waar het fijn wonen en werken is. De eigen berging en parkeerplaats (verplichte afname) maken het plaatje compleet, kortom Leidsche Rijn zorgt voor fijn woongenot!\n' +
              '\n' +
              "Let op: Foto's dienen als impressie. De uitvoering van de badkamer, toilet en keuken kan per appartement verschillen.\n" +
              '\n' +
              'Kan ik deze woning huren?\n' +
              '    €   7.000,- Bruto per maand (één inkomen)\n' +
              '    €   8.000,- Bruto per maand (tweeverdieners)\n' +
              '\n' +
              'Wonen in De Rossfeld\n' +
              'In het bruisend hart van Leidsche Rijn ligt op een schitterende locatie appartementencomplex “De Rossfelt”, de Vinex wijk van Utrecht! Het openbaar vervoer is in deze wijk ruimschoots aanwezig waardoor er een vlotte verbinding is tussen de oude en nieuwe stadswijken van Utrecht. Diverse bruggen en viaducten in Leidsche Rijn zijn gebouwd met aparte rijbanen voor de bus. Het treinstation van Leidsche Rijn bevindt zich op loopafstand van het gebouw. Daarnaast ligt Leidsche Rijn tussen de snelwegen A2 en A12 waardoor het ook met de auto goed bereikbaar is. \n' +
              'Ook aan groen is gedacht binnen de wijk Leidsche Rijn, zo ligt hier het Willem-Alexanderpark op het dak van de Leidsche Rijntunnel. In het zuiden van de wijk bevindt zich de Recreatieplas Strijkviertel, een vroegere zandwinningsplas die is ontstaan in verband met de aanleg van de nabijgelegen autosnelwegen A2, A12 en het knooppunt Oudenrijn. Aan de rand van de wijk ligt het Máximapark, waarvan de oorspronkelijke naam Leidsche Rijnpark was. Het is een centraal park binnen de Vinex-locatie Leidsche Rijn, omringd door de wijk Leidsche Rijn in het oosten, het dorp De Meern in het zuiden en het dorp Vleuten in het westen. \n' +
              'De kers op de taart in deze wijk is het winkelcentrum wat het kloppend hart is van deze fijne woonwijk. Complex De Rossfelt telt 2-3- en 4 luxe kamerappartementen die elk voorzien zijn van hoogwaardige elementen, daarnaast heeft elk appartement zijn eigen balkon en/of terras. In het midden van het complex kan met z’n allen genoten worden van de fijne binnentuin. De appartementen zijn comfortabel en luxe en voorzien van een luxe Bruynzeelkeuken met een Siemens apparatuur zoals een kookplaat, afzuigkap, vaatwasser, combi-oven en koel-vriescombinatie. De complete badkamer is voorzien van Villeroy & Boch sanitair. \n' +
              'Het appartement is hoogwaardig afgewerkt en geheel voorzien van behang en vloerverwarming. De eigen berging en de mogelijkheid tot het huren van een zwerfparkeerplaats maken het plaatje compleet,\n' +
              'kortom Leidsche Rijn zorgt voor fijn woongenot!\n' +
              '\n' +
              'BEZICHTIGINGEN / INFORMATIE: \n' +
              "- Bezichtigingen kunnen ALLEEN op werkdagen tussen 8.30 en 17.00 uur, dus niet 's avonds of in het weekend. \n" +
              '- Telefonisch een bezichtiging plannen of informatie aanvragen is NIET mogelijk. Verhuurder heeft ervoor gekozen de gegadigden zelf rond te leiden en/of van informatie te voorzien. Een bezichtiging of informatie kan enkel worden aangevraagd via Funda of via de website van de verhurende makelaar. \n' +
              '\n' +
              'ENGLISH - VIEWINGS / INFORMATION: \n' +
              '- Viewings can ONLY be scheduled on weekdays between 8:30am and 5:00pm. Appointments in the evenings or on weekends are not possible. \n' +
              '- Planning a viewing and/or requesting information by phone is NOT possible. The landlord has chosen to arrange all the viewings and provide information themselves. A viewing or information can only be requested through Funda or the website of the rental agent.',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-08-28T00:01:19.638Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.642Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.645Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.649Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.652Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.656Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.661Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.664Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.668Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.672Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:01:19.676Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:01:19'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  listings: 75,
  level: 'info',
  message: 'funda scraping completed',
  timestamp: '2025-08-28 01:01:19'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '979688ms',
  listingsProcessed: 75,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-08-28 01:01:19'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:03:38',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:03:38',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:03:38',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:05:00'
}
{
  component: 'scraper',
  message: 'Running enabled scrapers: funda',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:05:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  listings: 75,
  level: 'info',
  message: 'funda scraping completed',
  timestamp: '2025-08-28 01:06:18'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '978739ms',
  listingsProcessed: 75,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-08-28 01:06:18'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:08:38',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:08:38',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:08:38',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:10:00'
}
{
  component: 'scraper',
  message: 'Running enabled scrapers: funda',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:10:00'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:12:14.170Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:12:14'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:12:14.192Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:12:14'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:12:14.244Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:12:14'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:12:14.248Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:12:14'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:12:14.252Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:12:14'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:12:14.257Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:12:14'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:12:14.261Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:12:14'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:12:14.265Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:12:14'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:12:14.269Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:12:14'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1756339934268_rdawo',
  source: 'funda',
  duration: '1ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:818:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'Altijd al in een prachtig statig herenhuis willen wonen daterend uit 1910? Deze totaal gerenoveerde en VERDUURZAAMDE bovenwoning met energie label A, dakterras en charmant balkon. Met een totaal woonplezier van 180m2, hoge platfonds, oase van rust in een van de prachtige authentieke Utrechtse wijk. Met een maatvoering van bijna 8.00m bij 11.50m is dit echt een uniek woonhuis. Bij de zeer recente renovatie in 2021 is het huis volledig gerenoveerd, voorzien van een nieuwe indeling, prachtige woon-eetkeuken en nog 5 extra kamers, zoals een mooie eetkamer naast de keuken, waar je direct de mooie breedte van het huis ervaart.\n' +
              '\n' +
              "De woning is gelegen in een rustige wijk met mooie rijen herenhuizen. De Zeeheldenbuurt is ruim opgezet met veel groen voorzieningen en ruime tuinen. Dit woonhuis kijkt uit op de markante vrijstaande villa's die de buurt typeren. Restaurant C’est Ca en Goesting liggen om de hoek en voor de boodschappen ligt het vernieuwde winkelcentrum De Gaard op 3 minuten fietsen. Of als u zin hebt om op zaterdag ochtend naar de markt te willen fietsen, dan bent u binnen 10min in het prachtige Utrechtse centrum. \n" +
              '\n' +
              'Indeling\n' +
              '\n' +
              'Begane grond:\n' +
              '\n' +
              'Ruime entree waar direct de mooie sfeer voelbaar is, hoge plafond, mooie vloer en ruime trap naar boven, omdat de entree zo ruim is, is er voldoende plek om uw goede fietsen binnen te plaatsen. \n' +
              'Eerste verdieping:\n' +
              "Met zoals gezegd een vloeroppervlak van ca. 8.00m bij 11.50m is dit een zeer ruim opgezette verdieping, de vertrekken zijn ruim met het fraaie trappenhuis en de ruime overloop met hier een ruim toilet voorzien van prachtige marmer uit Italië. De woonkamer is van het type kamer-en-suite en ademt ook veel sfeer uit, de keuken ligt aan de achterzijde van de woning en is een indrukwekkend groot kookeiland, mooi midden in de ruimte geplaatst, voorzien van een ruime kastenwand wat het geheel mooi, gebalanceerd en zeer functioneel maakt. Naar achter toe is er een nieuwe glazen pui met openslaande deuren, veel glas wat de woning nog ruimtelijker maakt. Naast de keuken ligt een heerlijke knusse eetkamer waar voldoende ruimte is om heerlijk met uw familie te dineren. Aan de voorzijde naast de woonkamer ligt een extra kamer, deze is nu in gebruik als werkkamer, met aansluitend van een inpandig terras. Dit terras heeft een prachtige vloer, ronde boog en uitzicht op de witte villa's, het is hier heerlijk vertoeven, de zon ligging is zuid, een heerlijke plek van het huis. Aan de achterzijde kijk u juist weer uit op de ruime tuinen van de omgeving met het watertje wat achter het huis loopt, een mooi groen geheel met veel vogels. De muur naar het mooie en statige trappenhuis is open gemaakt, dit is ruimtelijk en laat de mooie breedte zien.\n" +
              '\n' +
              'Tweede verdieping:\n' +
              '\n' +
              'Ook hier tref u weer het mooie formaat van het huis aan, er is weer een ruime overloop met een spectaculair glazen dak. Hierdoor ervaart u een zeer ruimtelijk gevoel en wordt de gehele woning voorzien van natuurlijk licht en warmte van de zon, hier tref je een vaste trap naar het dakterras. Er zijn 3 slaapkamers en een nieuwe ruime badkamer. De badkamer is ook weer zeer modern en gebalanceerd met een ruime inloopdouche met een regen douche, vrijstaand ovaal ligbad en een tweede toilet. Er is een fraai wastafel meubel met twee waskommen, met een druk op de knop worden de ramen gebandeerd en heb je geen gordijnen nodig, een van de mooie gadgets van dit huis. \n' +
              'De slaapkamers zijn allemaal ruim van opzet. De vaste trap brengt je naar een fraai dakterras, een extra plek om buiten te zitten met mooi zicht op de omgeving en zicht op de Dom toren. Een groendak, voorzien van sedum en de plantenbakken zorgen voor de groene beleving. Een ruim terras om heerlijk op een zomerse dag buiten te eten, weer een prachtig geheel! \n' +
              '\n' +
              'Huurprijs:\n' +
              '\n' +
              '3895,00 per maand exclusief nuts voorzieningen.\n' +
              'Huurder is zelf contractant voor alle nutsvoorzieningen. \n' +
              '\n' +
              'Borg:\n' +
              'Staat gelijk aan 2 maanden huur.\n' +
              '\n' +
              'Ingangsdatum:\n' +
              'In overleg\n' +
              '\n' +
              'WILT U DE WONING BEZICHTIGINGEN?\n' +
              'Graag ontvangen wij per e-mail of contactformulier op onze website de volgende informatie:\n' +
              '\n' +
              '1. adres van de woning waarin u bent geïnteresseerd\n' +
              '2. telefoonnummer\n' +
              '3. bent u student, in loondienst of zelfstandig ondernemer?\n' +
              '4. werkende; voor welk bedrijf of organisatie? Studerend; welke studie/welke instelling\n' +
              '5. de gewenste huurperiode (voorkeur startdatum en verwachte huurperiode)\n' +
              '6. aantal bewoners en de relatie onderling (gezin/partners/vrienden)\n' +
              '7. bruto maand of jaarinkomen (eventueel ook dat van de partner)\n' +
              '\n' +
              'Nadat wij bovenstaande gegevens hebben ontvangen zullen wij contact opnemen voor het maken van een afspraak. Wij zullen de kandidaten die als eerst reageren als eerst benaderen.\n' +
              '\n' +
              'Alleen volledige aanvragen zullen in behandeling worden genomen.\n' +
              '\n' +
              'Heeft u vragen of het project of wilt u een bezichtiging plannen. Neem gerust contact op met ons kantoor.\n' +
              '\n' +
              'Deze informatie is door ons met de nodige zorgvuldigheid samengesteld. Geen enkele aansprakelijkheid wordt aanvaard voor enige onvolledigheid, onjuistheid of anderszins, of de consequenties daarvan. Alle maten en afmetingen zijn indicatief. De woning is niet NEN ingemeten en hierdoor zijn kleine afwijking mogelijk.',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-08-28T00:12:14.269Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-08-28 01:12:14'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:12:14.275Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:12:14'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:12:14.279Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:12:14'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:12:14.283Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:12:14'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:12:14.288Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:12:14'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1756339934287_8wv3p',
  source: 'funda',
  duration: '1ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:818:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'Let op: alleen bezichtigingsaanvragen via funda worden in behandeling genomen!\n' +
              '\n' +
              'Je eigen 2-kamerappartement op een aantrekkelijke locatie op fietsafstand van het centrum! Dit appartementencomplex is gelegen op een ruim perceel aan de Hooft Graaflandstraat.\n' +
              '\n' +
              'Het betreffen 15 zelfstandige appartementen gelegen in de populaire woonwijk Hoograven op de begane grond, eerste en tweede etage. Deze appartementen zijn goed geïsoleerd en strak afgewerkt met hoogwaardige materialen. Ze zijn voorzien van een PVC-vloer, gestukte en gesausde wanden, eigen aansluitingen voor nutsvoorzieningen, internet en tv. \n' +
              'Ieder appartement beschikt over een luxe keuken voorzien van o.a. afwasmachine, combi-oven en afzuigkap. De badkamer is keurig in neutrale kleurstelling betegeld en is voorzien van een douche, wastafel en toilet. \n' +
              'Daarnaast is het pand volledig gasloos en beschikt het over een warmtepomp en HR++ glas, wat resulteert in een lage energierekening. \n' +
              '\n' +
              'Op het terrein is een overdekte fietsenstalling en 6 parkeerplaatsen welke vrij gebruikt mogen worden als bewoner. \n' +
              '\n' +
              'Locatie:\n' +
              'Op loopafstand van diverse winkelvoorzieningen waaronder het winkelcentrum “het hart van Hoograven” en nabij dé culinaire hotspot van Utrecht (Rotsoord) met haar verschillende cafés en restaurants zoals WT Urban Kitchen en Klein Berlijn. Tevens bevindt je je op fietsafstand van het oude centrum en is het openbaar vervoer om de hoek waarbij de parkeermogelijkheden (thans vrij parkeren) als extra pre aanwezig zijn. \n' +
              '\n' +
              'Appartement 2C-5\n' +
              'Indeling:\n' +
              'Eerste verdieping:\n' +
              'Appartementsvoordeur: hal, slaapkamer, badkamer met douche, toilet en wastafel, ruimte met wasmachine-aansluiting. Lichte woonkamer met keuken welke is voorzien van diverse apparatuur waaronder een koelkast, vriezer, vaatwasser, inductiekookplaat, afzuigkap & magnetron combi-oven. Vanuit de woonkamer bereik je het balkon door de grote raampartij met haar openslaande deuren.\n' +
              '\n' +
              'Ben je benieuwd of dit appartement bij je past? Wij maken graag een afspraak voor een rondleiding!\n' +
              '\n' +
              'Bijzonderheden:\n' +
              '- Prijs EUR 1.775,- exclusief;\n' +
              '- Huurder is zelfstandig contractant van de NUTS voorzieningen;\n' +
              '- Servicekosten EUR 86,24 per mnd;\n' +
              '- Oppervlakte ca. 45 m2;\n' +
              '- Balkon;\n' +
              '- Gelegen op de eerste etage;\n' +
              '- Twee maanden borg;\n' +
              '- Inkomenseis: 3 x maandhuur bruto;\n' +
              '- Energielabel: A++;\n' +
              '- Hoogwaardig afwerkingsniveau;\n' +
              '- Volledig gestoffeerd: PVC vloer, gordijnen/rolgordijnen/jaloezieën, lampen, handdoek haakjes, kapstok;\n' +
              '- Internet/tv aansluiting aanwezig, abonnement naar keuze;\n' +
              '- Huisdieren zijn niet toegestaan;\n' +
              '- Vrij parkeren op terrein of op openbare weg;\n' +
              '- Oplevering: 12 september 2025.\n' +
              '\n' +
              'Deze aanbieding is met zorg samengesteld echter geheel vrijblijvend en onder voorbehoud van goedkeuring verhuurder. Derhalve kunnen hier op geen enkele wijze geen rechten aan ontleend worden. Alle maten en afmetingen zijn indicatief en niet exact volgens de NEN-2580 norm.\n' +
              '\n' +
              'Note: Only viewing requests made via Funda will be processed!\n' +
              '\n' +
              'Your own 2-room apartment in an attractive location, just a short cycle from the city center! This apartment complex is situated on a spacious plot on Hooft Graaflandstraat.\n' +
              '\n' +
              'The complex consists of 15 self-contained apartments located in the popular residential area of Hoograven, spread across the ground, first, and second floors. These apartments are well insulated and finished to a high standard with quality materials. Each unit is equipped with PVC flooring, plastered and painted walls, individual utility connections, internet, and TV.\n' +
              '\n' +
              'Every apartment has a modern kitchen equipped with a dishwasher, combi oven, and extractor hood. The bathroom is neatly tiled in a neutral color scheme and features a shower, washbasin, and toilet.\n' +
              '\n' +
              'The building is completely gas-free and fitted with a heat pump and HR++ glass, resulting in a low energy bill.\n' +
              '\n' +
              'On the premises, there is a covered bicycle storage area and 6 parking spaces available for residents’ use.\n' +
              '\n' +
              'Location:\n' +
              'Within walking distance of several shopping facilities, including the shopping center “Het Hart van Hoograven,” and close to Utrecht’s culinary hotspot Rotsoord, home to cafés and restaurants such as WT Urban Kitchen and Klein Berlijn. The historic city center is only a short cycle away, public transport is just around the corner, and the availability of (currently free) parking adds to the convenience.\n' +
              '\n' +
              'Apartment 2C-5\n' +
              '\n' +
              'Layout – First floor:\n' +
              'Private entrance: hallway, bedroom, bathroom with shower, toilet, and washbasin, and a separate laundry area with washing machine connection. Bright living room with kitchen, fitted with various appliances including fridge, freezer, dishwasher, induction hob, extractor hood & microwave-combi oven. From the living room, large French doors provide access to the balcony.\n' +
              '\n' +
              'Curious whether this apartment is right for you? We would be happy to arrange a viewing!\n' +
              '\n' +
              'Details:\n' +
              '\n' +
              '- Rent: €1,775 excl.;\n' +
              '- Tenant contracts utilities directly;\n' +
              '- Service charges: €86.24 per month;\n' +
              '- Approx. 45 m²;\n' +
              '- Balcony;\n' +
              '- Located on the first floor;\n' +
              '- Two months’ deposit;\n' +
              '- Income requirement: 3x monthly rent (gross);\n' +
              '- Energy label: A++;\n' +
              '- High-quality finish;\n' +
              '- Fully furnished: PVC flooring, curtains/blinds, lighting, towel hooks, coat rack;\n' +
              '- Internet/TV connection available, subscription at tenant’s choice;\n' +
              '- Pets are not allowed;\n' +
              '- Free parking on-site or in public areas;\n' +
              '- Available: September 12, 2025.\n' +
              '- Note: Only viewing requests made via Funda will be processed!\n' +
              '\n' +
              'This listing has been compiled with care, but is entirely without obligation and subject to landlord approval. No rights can be derived from this advertisement in any way. All sizes and measurements are indicative and not exactly in accordance with the NEN-2580 standard.',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-08-28T00:12:14.288Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-08-28 01:12:14'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:12:14.293Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:12:14'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:12:14.297Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:12:14'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:12:14.302Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:12:14'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1756339934300_xz9jt',
  source: 'funda',
  duration: '2ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: Build year must be between 1800 and 2030
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:818:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: 'Build year must be between 1800 and 2030',
          path: [ 'year' ],
          type: 'custom',
          context: { label: 'year', value: '1450', key: 'year' }
        }
      ]
    },
    timestamp: 2025-08-28T00:12:14.301Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-08-28 01:12:14'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:12:14.307Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:12:14'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:12:14.311Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:12:14'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:12:14.315Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:12:14'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:12:14.320Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:12:14'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:12:14.324Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:12:14'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:12:14.328Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:12:14'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:12:14.332Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:12:14'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1756339934331_9a3ld',
  source: 'funda',
  duration: '1ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:818:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'Luxe en Duurzaam Wonen in Wonderwoods – Hoola van Nootenstraat 355, Utrecht\n' +
              '\n' +
              'Wonen in het hart van Utrecht, omringd door groen en moderne luxe? Dit volledig gemeubileerde appartement in het iconische Wonderwoods-gebouw biedt alles wat je zoekt! Met een woonoppervlakte van ca. 67 m², een prachtig balkon met adembenemend uitzicht over Utrecht, en een toplocatie vlak bij het Centraal Station, is dit een unieke kans.\n' +
              '\n' +
              'Over Wonderwoods\n' +
              'Wonderwoods is een baanbrekend, duurzaam woonconcept dat natuur en stad samenbrengt. Dit architectonische meesterwerk, geïnspireerd door het Bosco Verticale in Milaan, biedt een groene en gezonde leefomgeving met weelderige beplanting op de gevels en daktuinen. Het gebouw beschikt over luxe faciliteiten en innovatieve technologieën die bijdragen aan een duurzame en comfortabele levensstijl.\n' +
              '\n' +
              'Perfecte Locatie\n' +
              'Wonderwoods ligt in het nieuwe Beurskwartier, dé opkomende wijk van Utrecht. Hier woon je op steenworp afstand van Utrecht Centraal, het bruisende stadscentrum, en tal van restaurants, cafés en winkels. Ook het Jaarbeursplein en het Beatrix Theater bevinden zich op loopafstand. Daarnaast is het gebied goed bereikbaar met zowel het openbaar vervoer als de auto, met directe toegang tot belangrijke uitvalswegen.\n' +
              '\n' +
              'Indeling van het appartement\n' +
              'Ruime en lichte woonkamer met moderne inrichting en toegang tot het balkon met uitzicht op de Dom. De open keuken is voorzien van luxe inbouwapparatuur.\n' +
              'Via de hal bereik je de comfortabele slaapkamer, welke stijlvol is ingericht. De moderne badkamer met inloopdouche en wastafel is tevens bereikbaar via de hal, daarnaast is de separate toilet gesitueerd alsmede het washok met warmtepomp. \n' +
              '\n' +
              'Bijzonderheden\n' +
              '- Huurprijs: €2.600 per maand (exclusief servicekosten en nutsvoorzieningen).\n' +
              '- Servicekosten bedragen € 100,- per maand.\n' +
              '- Volledig gemeubileerd – direct intrekbaar!\n' +
              '- Duurzaam wonen in een van de meest innovatieve gebouwen van Utrecht.\n' +
              '- Toplocatie: vlak bij Utrecht Centraal, winkels en horeca.\n' +
              '- Woonoppervlakte: ca. 67 m².\n' +
              '- Groene leefomgeving met beplanting op het gebouw en gedeelde buitenruimtes.\n' +
              '- Fietsenstalling in de onderbouw.\n' +
              '\n' +
              'Wil jij wonen op deze unieke plek in Utrecht? Neem snel contact op voor een bezichtiging! \n' +
              '\n' +
              'Deze aanbieding is met zorg samengesteld echter geheel vrijblijvend en onder voorbehoud van goedkeuring verhuurder. Derhalve kunnen hier op geen enkele wijze geen rechten aan ontleend worden. Alle maten en afmetingen zijn indicatief en niet exact volgens de NEN-2580 norm.\n' +
              '\n' +
              'Luxury and Sustainable Living in Wonderwoods – Hoola van Nootenstraat 355, Utrecht\n' +
              '\n' +
              'Would you like to live in the heart of Utrecht, surrounded by greenery and modern luxury? This fully furnished apartment in the iconic Wonderwoods building offers everything you need! With approximately 67 m² of living space, a beautiful balcony with breathtaking views of Utrecht, and a prime location near Central Station, this is a unique opportunity.\n' +
              '\n' +
              'About Wonderwoods\n' +
              'Wonderwoods is a groundbreaking, sustainable living concept that seamlessly integrates nature and urban life. Inspired by Bosco Verticale in Milan, this architectural masterpiece provides a green and healthy living environment with lush vegetation on the façades and rooftop gardens. The building offers luxurious amenities and innovative technologies, ensuring a comfortable and eco-friendly lifestyle.\n' +
              '\n' +
              'Perfect Location\n' +
              'Wonderwoods is situated in Beurskwartier, Utrecht’s vibrant new district. Living here means being just a stone’s throw from Utrecht Central Station, the lively city center, and numerous restaurants, cafés, and shops. Jaarbeursplein and Beatrix Theater are also within walking distance. The area is easily accessible by public transport and car, with direct access to major highways.\n' +
              '\n' +
              'Apartment Layout\n' +
              'Spacious and bright living room with modern furnishings and access to the balcony, offering stunning views of the Dom Tower.\n' +
              'Open kitchen, fully equipped with high-end built-in appliances.\n' +
              'Comfortable and stylishly furnished bedroom, accessible via the hallway.\n' +
              'Modern bathroom with a walk-in shower and sink.\n' +
              'Separate toilet and utility room with a heat pump, also accessible via the hallway.\n' +
              'Key Features\n' +
              '? Rent: €2,600 per month (excluding service charges and utilities).\n' +
              '? Servicecosts: € 100 per month.\n' +
              '? Fully furnished – move-in ready!\n' +
              '? Sustainable living in one of Utrecht’s most innovative buildings.\n' +
              '? Prime location near Utrecht Central Station, shops, and restaurants.\n' +
              '? Living area: approx. 67 m².\n' +
              '? Green environment with vegetation on the building and shared outdoor spaces.\n' +
              '? Bicycle storage in the basement.\n' +
              '\n' +
              'Would you like to live in this one-of-a-kind location in Utrecht? Contact us today to schedule a viewing! \n' +
              '\n' +
              'Disclaimer: This offer has been carefully compiled; however, it is entirely without obligation and subject to the landlord’s approval. Therefore, no rights can be derived from it in any way. All measurements and dimensions are indicative and not exact according to the NEN-2580 standard.',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-08-28T00:12:14.332Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-08-28 01:12:14'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:12:14.336Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:12:14'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:12:14.340Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:12:14'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:12:14.344Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:12:14'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:12:14.348Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:12:14'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:12:14.352Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:12:14'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:12:14.356Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:12:14'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:12:14.359Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:12:14'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:12:14.364Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:12:14'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:12:14.367Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:12:14'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:12:14.371Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:12:14'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:12:14.375Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:12:14'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:12:14.378Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:12:14'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:12:14.382Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:12:14'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1756339934381_hapig',
  source: 'funda',
  duration: '1ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:818:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'MODERN EN NIEUW 3-KAMERAPPARTEMENT MET BALKON, BERGING EN PRIVÉPARKEERPLAATS – BESCHIKBAAR PER 1 SEPTEMBER 2025\n' +
              '\n' +
              'Ben jij op zoek naar een licht, ruim en instap klaar appartement in een rustige en goed bereikbare wijk van Utrecht? Dan is dit prachtige 3-kamerappartement aan de Madridstraat 228 in Leidsche Rijn precies wat je zoekt!\n' +
              '\n' +
              'Met twee slaapkamers, een groot balkon, een privéberging én een eigen parkeerplaats, biedt deze woning alles wat je nodig hebt voor comfortabel wonen. De woning is volledig gestoffeerd en tot in de puntjes afgewerkt met een moderne pvc-vloer in houtlook, stijlvolle raambekleding en verlichting in elke kamer.\n' +
              '\n' +
              'Dit appartement bevindt zich op een ideale locatie in Utrecht: rustig gelegen, maar toch vlak bij alle voorzieningen. Je woont op korte afstand van Leidsche Rijn Centrum met gezellige horeca, winkels, sportfaciliteiten, scholen en het Máximapark. De bereikbaarheid is uitstekend: station Leidsche Rijn en station Terwijde liggen om de hoek en via de A2 of A12 ben je binnen no-time in Amsterdam of Den Haag.\n' +
              '\n' +
              'Indeling:\n' +
              '\n' +
              'Via de centrale entree met bellentableau, brievenbussen, lift en trappenhuis bereik je de derde verdieping.\n' +
              'Het appartement beschikt over een ruime hal die toegang geeft tot alle vertrekken. De woonkamer is licht en ruim en biedt directe toegang tot het zonnige balkon. De open keuken biedt ruimte voor inbouwapparatuur en sluit naadloos aan op de woonruimte.\n' +
              '\n' +
              'Er zijn twee comfortabele slaapkamers, een nette badkamer met inloopdouche en wastafel, een apart toilet, en een handige inpandige berging met wasmachine-aansluiting.\n' +
              '\n' +
              'Voordelen:\n' +
              '\n' +
              'Instapklaar, modern en sfeervol ingericht\n' +
              'Gestoffeerd met pvc-vloer, verlichting en gordijnen\n' +
              'Ruim balkon op het zuidwesten\n' +
              'Privéparkeerplaats in de ondergelegen garage\n' +
              'Eigen berging voor extra opslagruimte\n' +
              'Energiezuinig (energielabel A+++)\n' +
              'Uitstekende bereikbaarheid met OV en auto\n' +
              'Geschikt voor lange termijn verhuur\n' +
              '\n' +
              'Huurcondities:\n' +
              '\n' +
              'Huurprijs; € 2.600,- per maand, is inclusief de servicekosten en exclusief gas, water, elektra, internet, tv en gemeentelijke belastingen;\n' +
              '\n' +
              'Er wordt een inkomenscheck gedaan;\n' +
              '\n' +
              'Waarborgsom; € 5.200,- eenmalig;\n' +
              '\n' +
              'Huisdieren zijn niet toegestaan;\n' +
              '\n' +
              'Roken niet toegestaan;\n' +
              '\n' +
              'Niet geschikt voor studenten of woningdelers;\n' +
              '\n' +
              'Vloerverwarming via stadsverwarming;\n' +
              '\n' +
              'Huurperiode: langdurig;\n' +
              '\n' +
              'Professionele eindschoonmaak verplicht op kosten huurder;\n' +
              '\n' +
              'Appartement kan per 1 september 2025 worden betrokken\n' +
              '\n' +
              'De informatie is met de nodige zorgvuldigheid samengesteld. Geen aansprakelijkheid wordt aanvaard voor enige onvolledigheid, onjuistheid of anderszins, of consequenties daarvan. Alle maten en afmetingen zijn indicatief.\n' +
              '\n' +
              'MODERN AND NEW 3-ROOM APARTMENT WITH BALCONY, STORAGE, AND PRIVATE PARKING – AVAILABLE FROM SEPTEMBER 1, 2025\n' +
              '\n' +
              'Are you looking for a bright, spacious, and move-in ready apartment in a quiet yet easily accessible neighbourhood of Utrecht? Then this beautiful 3-room apartment on Madridstraat 228 in Leidsche Rijn is exactly what you’re looking for!\n' +
              '\n' +
              'With two bedrooms, a large balcony, a private storage unit, and your own parking space, this home offers everything you need for comfortable living. The property comes fully fitted and is finished to perfection with a modern wood-look PVC floor, stylish window coverings, and lighting in every room.\n' +
              '\n' +
              'This apartment is ideally located in Utrecht: peacefully situated, yet close to all amenities. You are within short distance of Leidsche Rijn Center with cozy restaurants, shops, sports facilities, schools, and the Máximapark. The location offers excellent accessibility: Leidsche Rijn and Terwijde train stations are nearby, and the A2 and A12 motorways will get you to Amsterdam or The Hague in no time.\n' +
              '\n' +
              'Layout:\n' +
              'Through the central entrance with intercom panel, mailboxes, elevator, and stairwell, you reach the third floor.\n' +
              '\n' +
              'The apartment features a spacious hallway providing access to all rooms. The living room is bright and spacious, with direct access to the sunny balcony. The open kitchen has space for built-in appliances and connects seamlessly to the living area.\n' +
              '\n' +
              'There are two comfortable bedrooms, a neat bathroom with walk-in shower and sink, a separate toilet, and a convenient internal storage room with washing machine connection.\n' +
              '\n' +
              'Features:\n' +
              '\n' +
              '- Move-in ready, modern, and tastefully decorated\n' +
              '- Fitted with PVC flooring, lighting, and curtains\n' +
              '- Spacious southwest-facing balcony\n' +
              '- Private parking space in the underground garage\n' +
              '- Private storage unit for extra storage space\n' +
              '- Energy-efficient (Energy Label A+++)\n' +
              '- Excellent accessibility by public transport and car\n' +
              '- Suitable for long-term rental\n' +
              '\n' +
              'Rental conditions:\n' +
              '\n' +
              '- Rent: €2,600 per month, including service charges and excluding gas, water, electricity, internet, TV, and municipal taxes\n' +
              '- Income check required\n' +
              '- Security deposit: €5,200 (one-time)\n' +
              '- Pets not allowed\n' +
              '- Smoking not allowed\n' +
              '- Not suitable for students or house sharing\n' +
              '- Underfloor heating via district heating\n' +
              '- Long-term rental period\n' +
              '- Professional end-of-tenancy cleaning required at tenant’s expense\n' +
              '- Available from September 1, 2025\n' +
              '\n' +
              'The information has been compiled with due care. No liability is accepted for any incompleteness, inaccuracies, or otherwise, or the consequences thereof. All measurements and dimensions are indicative.',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-08-28T00:12:14.382Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-08-28 01:12:14'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:12:14.387Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:12:14'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1756339934385_tt3kx',
  source: 'funda',
  duration: '2ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:818:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: 'Een prachtig gestoffeerd appartement met groot balkon op het zuidwesten in Leidsche Rijn Centrum, grenzend aan het gezellige Brusselplein.\n' +
              '\n' +
              'De woning ligt centraal in Leidsche Rijn Centrum dat is ontworpen om te wonen, te werken en te leven. In een mediterrane sfeer met boulevards en promenades is dit centrum het kloppend hart van de wijk Leidsche Rijn met leuke winkels, gezellige terrasjes en pleinen.\n' +
              '\n' +
              'Hier kunt u ruiken, voelen, proeven en kopen. Beleving staat centraal. Daarnaast bevat het centrum een eigen NS-station en busstation. Er zijn niet alleen culturele voorzieningen in de buurt zoals de bioscoop en het Berlijnplein, maar ook tal van 1e lijns zorgvoorzieningen. Al deze voorzieningen zijn op loopafstand van het appartementencomplex.\n' +
              '\n' +
              'Begane grond:\n' +
              '\n' +
              'Centrale entree met brievenbussen en een bellentableau, met toegang tot de lift en het trappenhuis evenals de berging en de parkeergarage inclusief de overdekte fietsenstalling en eigen parkeerplek.\n' +
              '\n' +
              'Indeling:\n' +
              '\n' +
              'Het appartement is gesitueerd op de 2e verdieping. Entree in de hal die toegang geeft tot de andere ruimtes; een apart toilet met fonteintje, de badkamer met douche en wastafel met opberglades, een berging, de 2 slaapkamers met kiep/kantelraam en de woonkamer met semi-open keuken.\n' +
              '\n' +
              'De woonkamer biedt uitzicht op de Grauwaartsingel en geeft toegang tot het balkon. De semi-open keuken is van alle gemakken voorzien met inbouwapparatuur zoals een vaatwasser, koel-vries combinatie, combi-oven, kookplaat en afzuigkap. Daarnaast is er een berging met opstelplaats voor de wasmachine en de droger, de boiler en de vloerverwarmingsunit is eveneens daar.\n' +
              '\n' +
              'Het appartement beschikt over een moderne lichte laminaatvloer, offwhite gordijnen, de badkamer en het toilet hebben moderne accenten. Het appartement is gebouwd met de nieuwste, meest duurzame materialen en de afwerking is hoogwaardig en luxe. Tevens is het appartement zeer energiezuinig en heeft energielabel A; zeer goede isolatie, vloerverwarming- en koeling en een warmte-terugwininstallatie.\n' +
              '\n' +
              'Bijzonderheden:\n' +
              '\n' +
              'Huurprijs is inclusief de servicekosten en exclusief gas, water, elektra, internet, tv en gemeentelijke belastingen;\n' +
              '\n' +
              'Er wordt een inkomenscheck gedaan;\n' +
              '\n' +
              'Waarborg staat gelijk aan twee keer de maand huur;\n' +
              '\n' +
              'Huisdieren zijn niet toegestaan;\n' +
              '\n' +
              'Roken niet toegestaan;\n' +
              '\n' +
              'Niet geschikt voor studenten;\n' +
              '\n' +
              'Vloerverwarming via stadsverwarming;\n' +
              '\n' +
              'Woning kan per 1 september 2025 worden betrokken.\n' +
              '\n' +
              'Deze informatie is met de nodige zorgvuldigheid samengesteld. Geen aansprakelijkheid wordt aanvaard voor enige onvolledigheid, onjuistheid of anderszins, of de consequenties daarvan. Alle maten en afmetingen zijn indicatief.\n' +
              '\n' +
              '---------------------------------------------\n' +
              '\n' +
              'A beautifully furnished apartment with a large southwest-facing balcony in Leidsche Rijn Centrum, adjacent to the lively Brusselplein.\n' +
              '\n' +
              'The property is centrally located in Leidsche Rijn Centrum, designed for living, working, and enjoying life. With Mediterranean-inspired boulevards and promenades, this center is the heart of the Leidsche Rijn district, offering charming shops, cozy terraces, and squares. You can experience and enjoy everything here: the sights, smells, tastes, and experiences. Additionally, the center has its own NS train station and bus station. Nearby are not only cultural amenities like the cinema and Berlijnplein, but also many primary healthcare facilities. All these amenities are within walking distance of the apartment complex.\n' +
              '\n' +
              'Ground floor: Central entrance with mailboxes and an intercom panel, with access to the elevator and stairwell, as well as the storage area and parking garage, which includes a covered bike shed and your own parking space.\n' +
              '\n' +
              'Layout: The apartment is located on the 2nd floor. Entry through the hall, which provides access to the other rooms: a separate toilet with a small sink, the bathroom with a shower and sink with storage drawers, a storage room, two bedrooms with tilt/turn windows, and the living room with a semi-open kitchen. The living room overlooks the Grauwaartsingel and gives access to the balcony. The semi-open kitchen is fully equipped with built-in appliances, including a dishwasher, fridge-freezer combination, combi oven, hob, and extractor hood. There is also a storage room with space for a washing machine and dryer, as well as the boiler and floor heating unit. The apartment features a modern light laminate floor, off-white curtains, and the bathroom and toilet have modern accents. The apartment is built with the latest, most sustainable materials, and the finish is high-quality and luxurious. Furthermore, the apartment is very energy-efficient with energy label A; it has excellent insulation, floor heating and cooling, and a heat recovery system.\n' +
              '\n' +
              'Special features:\n' +
              '\n' +
              'Rent includes service costs and excludes gas, water, electricity, internet, TV, and municipal taxes;\n' +
              '\n' +
              'Income check will be performed;\n' +
              '\n' +
              "Deposit is equivalent to two months' rent;\n" +
              '\n' +
              'Pets are not allowed;\n' +
              '\n' +
              'Smoking is not allowed;\n' +
              '\n' +
              'Not suitable for students;\n' +
              '\n' +
              'Floor heating via district heating;\n' +
              '\n' +
              'Available from September 1, 2025.\n' +
              '\n' +
              'This information has been compiled with due care. No liability is accepted for any incompleteness, inaccuracies, or otherwise, or for the consequences thereof. All dimensions and measurements are indicative.',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-08-28T00:12:14.386Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-08-28 01:12:14'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:12:14.391Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:12:14'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:12:14.394Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:12:14'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:12:14.398Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:12:14'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:12:14.402Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:12:14'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:12:14.412Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:12:14'
}
{
  component: 'transformation',
  service: 'zakmakelaar-api',
  transformationId: 'transform_1756339934411_4c3af',
  source: 'funda',
  duration: '1ms',
  success: false,
  memoryUsed: '1MB',
  error: SchemaError: Validation failed after transformation: "description" length must be less than or equal to 5000 characters long
      at SchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\schemaTransformer.js:98:29)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async OptimizedSchemaTransformer.transform (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationOptimizer.js:343:20)
      at async validateAndNormalizeListingEnhanced (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\transformationIntegration.js:149:29)
      at async scrapeFunda (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\services\scrapers\fundaScraper.js:818:33)
      at async Promise.allSettled (index 0)
      at async Job.job (C:\Users\<USER>\Documents\FREELANCE\ZAKMAKELAAR\zakmakelaar-backend\src\index.js:648:21) {
    type: 'VALIDATION_ERROR',
    context: {
      source: 'funda',
      validationErrors: [
        {
          message: '"description" length must be less than or equal to 5000 characters long',
          path: [ 'description' ],
          type: 'string.max',
          context: {
            limit: 5000,
            value: '123WONEN DÉ VERHUURMAKELAAR VAN UTRECHT BIEDT AAN VOOR TIJDELIJKE VERHUUR VAN 9 MAANDEN:\n' +
              '\n' +
              'Charmant herenhuis aan de Badstraat – Utrecht (Buiten Wittevrouwen).\n' +
              '\n' +
              'Ben jij op zoek naar een ruim, sfeervol en karakteristiek woonhuis op een toplocatie in Utrecht? Dit prachtige herenhuis aan de Badstraat biedt comfortabel en stijlvol wonen in de populaire wijk Buiten Wittevrouwen. Met vier ruime slaapkamers, moderne badkamer, luxe keuken met groot eiland en een zonnige onderhoudsarme buitenruimte, plus een balkon op de eerste verdieping, is dit de ideale woning voor een stel, expat-gezin of werkende professional.\n' +
              '\n' +
              'Op loopafstand van het Griftpark en het Wilhelminapark, diverse winkels, supermarkten en horeca, slechts enkele minuten fietsen naar het centrum, CS Utrecht en Universiteit. Uitstekend bereikbaar met OV en nabij uitvalswegen (A27/A28)\n' +
              '\n' +
              'INDELING:\n' +
              '\n' +
              'BEGANE GROND:\n' +
              '\n' +
              'Bij binnenkomst in de hal heb je de trapopgang naar de 1e verdieping, toilet en de toegang naar de ruime woonkamer en open keuken. De vloer op de benedenverdieping is voorzien van een houten vloer. De woonkamer is ruim en licht en de open keuken is voorzien van een groot eiland met de gootsteen, inbouwkoelkast/vriezer, inbouw magnetron en oven en een 6-pits gaskookplaat en meer dan voldoende opbergruimte.\n' +
              '\n' +
              'EERSTE VERDIEPING:\n' +
              '\n' +
              'Op deze verdieping vindt je twee in grote variërende slaapkamers, de ruime badkamer met een bad en aparte douche en de trapopgang naar de tweede verdieping. De vloer op deze verdieping is net als op de begane grond een houten vloer. Op deze verdieping is ook een balkon.\n' +
              '\n' +
              'TWEEDE VERDIEPING:\n' +
              '\n' +
              'Op deze verdieping vindt je nog 2 in grote varierende slaapkamers, waar genoeg ruimte is om te slapen en/of te werken.\n' +
              '\n' +
              'BIJZONDERHEDEN:\n' +
              '\n' +
              '- De woning is beschikbaar per 6 oktober 2025;\n' +
              '- De huurperiode is een vaste periode van 9 maanden;\n' +
              '- Huurprijs is € 2400,- per maand;\n' +
              '- Waarborgsom 2 maanden huur;\n' +
              '- De huurprijs is exclusief G/W/E, tv/internet en Gemeentelijke heffingen deel huurder(s);\n' +
              '- De woning is gestoffeerd;\n' +
              '- Energielabel E;\n' +
              '- Parkeergelegenheid voor de deur (betaald);\n' +
              '- Niet roken;\n' +
              '- Geen huisdieren;\n' +
              '- Geen studenten / woningdelers;\n' +
              '\n' +
              'GEEN COURTAGE! 123WONEN WERKT ALS VERHUURMAKELAAR VOOR DE EIGENAAR;\n' +
              'BEZICHTIGINGEN KUNNEN ALLEEN ONLINE WORDEN AANGEVRAAGD.\n' +
              '\n' +
              'Vind je deze woning op een website waarop wij doorplaatsen?\n' +
              'Kijk dan op onze eigen website:  voor ons actuele aanbod!\n' +
              '\n' +
              'Vind je deze woning op een website waar wij doorverkopen?\n' +
              'Kijk dan op onze eigen website:  voor ons actuele aanbod!\n' +
              '\n' +
              'Neem contact op\n' +
              '\n' +
              '123WONEN UTRECHT\n' +
              'Ramstraat 31\n' +
              '3581 HD Utrecht\n' +
              '\n' +
              'T 030 - 760 33 94\n' +
              'E \n' +
              '\n' +
              '----\n' +
              '\n' +
              '123WONEN DÉ VERHUURMAKELAAR VAN UTRECHT OFFERS FOR TEMPORARY RENTAL OF 9 MONTHS:\n' +
              '\n' +
              'Charming townhouse on the Badstraat - Utrecht (Buiten Wittevrouwen).\n' +
              '\n' +
              'Are you looking for a spacious, attractive and characteristic house in a prime location in Utrecht? This beautiful townhouse on Badstraat offers comfortable and stylish living in the popular neighborhood of Buiten Wittevrouwen. With four spacious bedrooms, modern bathroom, luxury kitchen with large island and a sunny low maintenance outdoor area, plus a balcony on the second floor, this is the ideal home for a couple, expat family or working professional.\n' +
              '\n' +
              'Within walking distance of the Griftpark and Wilhelminapark, several stores, supermarkets and restaurants, just minutes by bike to the city center, CS Utrecht and University. Easily accessible by public transport and near roads (A27/A28)\n' +
              '\n' +
              'LAYOUT:\n' +
              '\n' +
              'GROUND FLOOR:\n' +
              '\n' +
              'Upon entering the hall you have the staircase to the 1st floor, toilet and access to the spacious living room and open kitchen. The floor on the ground floor has a wooden floor. The living room is spacious and bright and the open kitchen has a large island with the sink, built-in fridge/freezer, built-in microwave and oven and a 6-burner gas hob and more than enough storage space.\n' +
              '\n' +
              'FIRST FLOOR:\n' +
              '\n' +
              'On this floor you will find two bedrooms varying in size, the spacious bathroom with a tub and separate shower and the staircase to the second floor. The floor is also a wooden floor. There is also a balcony on this floor.\n' +
              '\n' +
              'SECOND FLOOR:\n' +
              '\n' +
              'On this floor you will find the 2 bedrooms varying in size, where there is enough space to sleep and/or work.\n' +
              '\n' +
              'PARTICULARS:\n' +
              '\n' +
              '- The property is available from October 6, 2025;\n' +
              '- The rental period is a fixed period of 9 months;\n' +
              '- Rent is € 2400,- per month;\n' +
              '- Security deposit 2 months rent;\n' +
              '- The rent is excluding G/W/E, TV/internet and Municipal levies share tenant(s);\n' +
              '- The property is unfurnished;\n' +
              '- Energy label E;\n' +
              '- Parking in front of the door (paid);\n' +
              '- No smoking;\n' +
              '- No pets;\n' +
              '- No students / house sharers\n' +
              '\n' +
              'NO COMMISSION! 123WONEN WORKS AS A RENTAL AGENT FOR THE OWNER;\n' +
              'VIEWINGS CAN ONLY BE REQUESTED ONLINE.\n' +
              '\n' +
              'Do you find this property on a website on which we post?\n' +
              'Please check our own website:  for our current offer!\n' +
              '\n' +
              'Do you find this property on a website where we resell?\n' +
              'Please check our own website:  for our current offer!\n' +
              '\n' +
              'Contact us\n' +
              '\n' +
              'EXPATRENTALS HOLLAND UTRECHT\n' +
              'Ramstraat 31\n' +
              '3581 HD Utrecht\n' +
              '\n' +
              'T 030 - 760 33 94\n' +
              'E',
            encoding: undefined,
            label: 'description',
            key: 'description'
          }
        }
      ]
    },
    timestamp: 2025-08-28T00:12:14.412Z
  },
  dataQuality: { completeness: 100, accuracy: 100 },
  level: 'error',
  message: 'Transformation completed',
  timestamp: '2025-08-28 01:12:14'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'critical',
    type: 'success_rate',
    message: 'Transformation success rate (34%) is below threshold (80%)',
    value: 34,
    threshold: 80,
    timestamp: '2025-08-28T00:12:14.418Z'
  },
  level: 'error',
  message: 'Transformation success rate (34%) is below threshold (80%)',
  timestamp: '2025-08-28 01:12:14'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  listings: 43,
  level: 'info',
  message: 'funda scraping completed',
  timestamp: '2025-08-28 01:12:14'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '734430ms',
  listingsProcessed: 43,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-08-28 01:12:14'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:12:16',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'success',
  details: 65,
  environment: 'production',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:12:17',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'success',
  details: 50,
  environment: 'production',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:12:19',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'success',
  details: 85,
  environment: 'production',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:12:19',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_summarization',
  status: 'error',
  details: "Cannot read properties of undefined (reading 'chat')",
  environment: 'production',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:12:21',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'success',
  details: 75,
  environment: 'production',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:12:21',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_summarization',
  status: 'error',
  details: "Cannot read properties of undefined (reading 'chat')",
  environment: 'production',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:12:23',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'success',
  details: 65,
  environment: 'production',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:12:25',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'success',
  details: 75,
  environment: 'production',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:12:25',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_summarization',
  status: 'error',
  details: "Cannot read properties of undefined (reading 'chat')",
  environment: 'production',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:12:27',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'success',
  details: 75,
  environment: 'production',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:12:27',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_summarization',
  status: 'error',
  details: "Cannot read properties of undefined (reading 'chat')",
  environment: 'production',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:12:29',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'success',
  details: 85,
  environment: 'production',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:12:29',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_summarization',
  status: 'error',
  details: "Cannot read properties of undefined (reading 'chat')",
  environment: 'production',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:12:30',
  level: 'info',
  category: 'ai_operation',
  operation: 'listing_matching',
  status: 'success',
  details: 40,
  environment: 'production',
  message: 'AI Operation'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:13:38',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:13:38',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:13:38',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  message: 'Error handling service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:13:48'
}
{
  message: 'Error handling service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:13:48'
}
{
  message: 'Error handling service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:13:48'
}
{
  message: 'Initializing ApplicationQueueManager',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:13:48'
}
{
  message: 'Notification queue processing started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:13:48'
}
{
  service: 'zakmakelaar-api',
  port: '3000',
  environment: 'production',
  endpoints: {
    api: 'http://localhost:3000',
    docs: 'http://localhost:3000/api-docs',
    health: 'http://localhost:3000/health'
  },
  level: 'info',
  message: '🚀 ZakMakelaar API Server Started',
  timestamp: '2025-08-28 01:13:48'
}
{
  message: 'WebSocket service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:13:48'
}
{
  message: 'NotificationScheduler initialized with cron jobs',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:13:48'
}
{
  message: 'Started scheduled job: dailySummary',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:13:48'
}
{
  message: 'Started scheduled job: weeklySummary',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:13:48'
}
{
  message: 'Started scheduled job: cleanup',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:13:48'
}
{
  message: 'NotificationScheduler started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:13:48'
}
{
  message: 'Auto-application processing started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:13:48'
}
{
  message: 'Auto-application service initialized and started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:13:48'
}
{
  message: 'Document vault directories initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:13:48'
}
{
  message: 'Starting queue processing',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:13:48'
}
{
  message: 'ApplicationQueueManager initialized successfully',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:13:48'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:15:00'
}
{
  component: 'scraper',
  message: 'Running enabled scrapers: funda',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:15:00'
}
{
  message: 'Error handling service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:18:22'
}
{
  message: 'Error handling service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:18:23'
}
{
  message: 'Error handling service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:18:23'
}
{
  message: 'Initializing ApplicationQueueManager',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:18:23'
}
{
  message: 'Notification queue processing started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:18:23'
}
{
  service: 'zakmakelaar-api',
  port: '3000',
  environment: 'production',
  endpoints: {
    api: 'http://localhost:3000',
    docs: 'http://localhost:3000/api-docs',
    health: 'http://localhost:3000/health'
  },
  level: 'info',
  message: '🚀 ZakMakelaar API Server Started',
  timestamp: '2025-08-28 01:18:23'
}
{
  message: 'WebSocket service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:18:23'
}
{
  message: 'NotificationScheduler initialized with cron jobs',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:18:23'
}
{
  message: 'Started scheduled job: dailySummary',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:18:23'
}
{
  message: 'Started scheduled job: weeklySummary',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:18:23'
}
{
  message: 'Started scheduled job: cleanup',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:18:23'
}
{
  message: 'NotificationScheduler started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:18:23'
}
{
  message: 'Auto-application processing started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:18:23'
}
{
  message: 'Auto-application service initialized and started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:18:23'
}
{
  message: 'Document vault directories initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:18:23'
}
{
  message: 'Starting queue processing',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:18:23'
}
{
  message: 'ApplicationQueueManager initialized successfully',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:18:23'
}
{
  message: 'Error handling service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:19:16'
}
{
  message: 'Error handling service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:19:16'
}
{
  message: 'Error handling service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:19:16'
}
{
  message: 'Initializing ApplicationQueueManager',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:19:16'
}
{
  message: 'Notification queue processing started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:19:16'
}
{
  service: 'zakmakelaar-api',
  port: '3000',
  environment: 'production',
  endpoints: {
    api: 'http://localhost:3000',
    docs: 'http://localhost:3000/api-docs',
    health: 'http://localhost:3000/health'
  },
  level: 'info',
  message: '🚀 ZakMakelaar API Server Started',
  timestamp: '2025-08-28 01:19:16'
}
{
  message: 'WebSocket service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:19:16'
}
{
  message: 'NotificationScheduler initialized with cron jobs',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:19:16'
}
{
  message: 'Started scheduled job: dailySummary',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:19:16'
}
{
  message: 'Started scheduled job: weeklySummary',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:19:16'
}
{
  message: 'Started scheduled job: cleanup',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:19:16'
}
{
  message: 'NotificationScheduler started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:19:16'
}
{
  message: 'Auto-application processing started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:19:16'
}
{
  message: 'Auto-application service initialized and started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:19:16'
}
{
  message: 'Document vault directories initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:19:16'
}
{
  message: 'Starting queue processing',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:19:16'
}
{
  message: 'ApplicationQueueManager initialized successfully',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:19:16'
}
{
  message: 'Error handling service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:19:36'
}
{
  message: 'Error handling service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:19:37'
}
{
  message: 'Error handling service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:19:37'
}
{
  message: 'Initializing ApplicationQueueManager',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:19:37'
}
{
  message: 'Notification queue processing started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:19:37'
}
{
  service: 'zakmakelaar-api',
  port: '3000',
  environment: 'production',
  endpoints: {
    api: 'http://localhost:3000',
    docs: 'http://localhost:3000/api-docs',
    health: 'http://localhost:3000/health'
  },
  level: 'info',
  message: '🚀 ZakMakelaar API Server Started',
  timestamp: '2025-08-28 01:19:37'
}
{
  message: 'WebSocket service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:19:37'
}
{
  message: 'NotificationScheduler initialized with cron jobs',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:19:37'
}
{
  message: 'Started scheduled job: dailySummary',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:19:37'
}
{
  message: 'Started scheduled job: weeklySummary',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:19:37'
}
{
  message: 'Started scheduled job: cleanup',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:19:37'
}
{
  message: 'NotificationScheduler started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:19:37'
}
{
  message: 'Auto-application processing started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:19:37'
}
{
  message: 'Auto-application service initialized and started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:19:37'
}
{
  message: 'Document vault directories initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:19:37'
}
{
  message: 'Starting queue processing',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:19:37'
}
{
  message: 'ApplicationQueueManager initialized successfully',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:19:37'
}
{
  message: 'ENCRYPTION_MASTER_KEY not set in environment. Using generated key.',
  level: 'warn',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:19:49'
}
{
  message: 'Screenshot saved: 2025-08-28T00-19-53-185Z_funda-1756340389384_www.funda.nl_03_funda_page_loaded.png',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:19:53'
}
{
  message: 'Found contact button with selector: [data-optimizely="contact-email"]',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:19:58'
}
{
  message: 'Screenshot saved: 2025-08-28T00-19-58-631Z_funda-1756340389384_www.funda.nl_04_funda_before_contact_click.png',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:19:59'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:20:00'
}
{
  component: 'scraper',
  message: 'Running enabled scrapers: funda',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:20:00'
}
{
  message: 'Screenshot saved: 2025-08-28T00-20-02-046Z_funda-1756340389384_www.funda.nl_05_funda_after_contact_click.png',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:20:02'
}
{
  message: 'Form not found with standard selectors, proceeding anyway',
  level: 'warn',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:20:22'
}
{
  service: 'zakmakelaar-api',
  formData: {
    firstName: 'Wellis',
    lastName: 'Hant',
    email: '<EMAIL>',
    phone: '030 686 62 00'
  },
  level: 'info',
  message: 'Starting Funda form filling process',
  timestamp: '2025-08-28 01:20:22'
}
{
  message: 'Question textarea not found',
  level: 'warn',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:20:22'
}
{
  message: 'Email field not found',
  level: 'warn',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:20:22'
}
{
  message: 'First name field not found',
  level: 'warn',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:20:22'
}
{
  message: 'Last name field not found',
  level: 'warn',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:20:22'
}
{
  message: 'Phone field not found',
  level: 'warn',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:20:22'
}
{
  message: 'Funda form filling completed',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:20:23'
}
{
  message: 'Screenshot saved: 2025-08-28T00-20-23-468Z_funda-1756340389384_www.funda.nl_06_funda_form_filled.png',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:20:24'
}
{
  message: 'Found submit button with selector: button[type="submit"]',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:20:24'
}
{
  message: 'Screenshot saved: 2025-08-28T00-20-27-057Z_funda-1756340389384_www.funda.nl_07_funda_after_submit.png',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:20:27'
}
{
  message: 'Error handling service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:22:54'
}
{
  message: 'Error handling service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:22:54'
}
{
  message: 'Error handling service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:22:54'
}
{
  message: 'Initializing ApplicationQueueManager',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:22:54'
}
{
  message: 'Notification queue processing started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:22:54'
}
{
  service: 'zakmakelaar-api',
  port: '3000',
  environment: 'production',
  endpoints: {
    api: 'http://localhost:3000',
    docs: 'http://localhost:3000/api-docs',
    health: 'http://localhost:3000/health'
  },
  level: 'info',
  message: '🚀 ZakMakelaar API Server Started',
  timestamp: '2025-08-28 01:22:54'
}
{
  message: 'WebSocket service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:22:54'
}
{
  message: 'NotificationScheduler initialized with cron jobs',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:22:54'
}
{
  message: 'Started scheduled job: dailySummary',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:22:54'
}
{
  message: 'Started scheduled job: weeklySummary',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:22:54'
}
{
  message: 'Started scheduled job: cleanup',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:22:54'
}
{
  message: 'NotificationScheduler started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:22:54'
}
{
  message: 'Auto-application processing started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:22:54'
}
{
  message: 'Auto-application service initialized and started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:22:54'
}
{
  message: 'Document vault directories initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:22:54'
}
{
  message: 'Starting queue processing',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:22:54'
}
{
  message: 'ApplicationQueueManager initialized successfully',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:22:54'
}
{
  message: 'Error handling service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:23:09'
}
{
  message: 'Error handling service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:23:09'
}
{
  message: 'Error handling service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:23:09'
}
{
  message: 'Initializing ApplicationQueueManager',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:23:09'
}
{
  message: 'Notification queue processing started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:23:09'
}
{
  service: 'zakmakelaar-api',
  port: '3000',
  environment: 'production',
  endpoints: {
    api: 'http://localhost:3000',
    docs: 'http://localhost:3000/api-docs',
    health: 'http://localhost:3000/health'
  },
  level: 'info',
  message: '🚀 ZakMakelaar API Server Started',
  timestamp: '2025-08-28 01:23:09'
}
{
  message: 'WebSocket service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:23:09'
}
{
  message: 'NotificationScheduler initialized with cron jobs',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:23:09'
}
{
  message: 'Started scheduled job: dailySummary',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:23:09'
}
{
  message: 'Started scheduled job: weeklySummary',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:23:09'
}
{
  message: 'Started scheduled job: cleanup',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:23:09'
}
{
  message: 'NotificationScheduler started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:23:09'
}
{
  message: 'Auto-application processing started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:23:09'
}
{
  message: 'Auto-application service initialized and started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:23:09'
}
{
  message: 'Document vault directories initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:23:09'
}
{
  message: 'Starting queue processing',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:23:09'
}
{
  message: 'ApplicationQueueManager initialized successfully',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:23:09'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:25:00'
}
{
  component: 'scraper',
  message: 'Running enabled scrapers: funda',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:25:00'
}
{
  message: 'ENCRYPTION_MASTER_KEY not set in environment. Using generated key.',
  level: 'warn',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:25:00'
}
{
  message: 'Error handling service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:25:30'
}
{
  message: 'Error handling service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:25:31'
}
{
  message: 'Error handling service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:25:31'
}
{
  message: 'Initializing ApplicationQueueManager',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:25:31'
}
{
  message: 'Notification queue processing started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:25:31'
}
{
  service: 'zakmakelaar-api',
  port: '3000',
  environment: 'production',
  endpoints: {
    api: 'http://localhost:3000',
    docs: 'http://localhost:3000/api-docs',
    health: 'http://localhost:3000/health'
  },
  level: 'info',
  message: '🚀 ZakMakelaar API Server Started',
  timestamp: '2025-08-28 01:25:31'
}
{
  message: 'WebSocket service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:25:31'
}
{
  message: 'NotificationScheduler initialized with cron jobs',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:25:31'
}
{
  message: 'Started scheduled job: dailySummary',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:25:31'
}
{
  message: 'Started scheduled job: weeklySummary',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:25:31'
}
{
  message: 'Started scheduled job: cleanup',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:25:31'
}
{
  message: 'NotificationScheduler started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:25:31'
}
{
  message: 'Auto-application processing started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:25:31'
}
{
  message: 'Auto-application service initialized and started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:25:31'
}
{
  message: 'Document vault directories initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:25:31'
}
{
  message: 'Starting queue processing',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:25:31'
}
{
  message: 'ApplicationQueueManager initialized successfully',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:25:31'
}
{
  message: 'Error handling service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:26:02'
}
{
  message: 'Error handling service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:26:02'
}
{
  message: 'Error handling service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:26:02'
}
{
  message: 'Initializing ApplicationQueueManager',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:26:02'
}
{
  message: 'Notification queue processing started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:26:02'
}
{
  service: 'zakmakelaar-api',
  port: '3000',
  environment: 'production',
  endpoints: {
    api: 'http://localhost:3000',
    docs: 'http://localhost:3000/api-docs',
    health: 'http://localhost:3000/health'
  },
  level: 'info',
  message: '🚀 ZakMakelaar API Server Started',
  timestamp: '2025-08-28 01:26:02'
}
{
  message: 'WebSocket service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:26:02'
}
{
  message: 'NotificationScheduler initialized with cron jobs',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:26:02'
}
{
  message: 'Started scheduled job: dailySummary',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:26:02'
}
{
  message: 'Started scheduled job: weeklySummary',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:26:02'
}
{
  message: 'Started scheduled job: cleanup',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:26:02'
}
{
  message: 'NotificationScheduler started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:26:02'
}
{
  message: 'Auto-application processing started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:26:02'
}
{
  message: 'Auto-application service initialized and started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:26:02'
}
{
  message: 'Document vault directories initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:26:02'
}
{
  message: 'Starting queue processing',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:26:02'
}
{
  message: 'ApplicationQueueManager initialized successfully',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:26:02'
}
{
  message: 'ENCRYPTION_MASTER_KEY not set in environment. Using generated key.',
  level: 'warn',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:27:22'
}
{
  message: 'Screenshot saved: 2025-08-28T00-27-34-192Z_funda-1756340843067_www.funda.nl_03_funda_page_loaded.png',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:27:35'
}
{
  message: 'Found contact button with selector: [data-optimizely="contact-email"]',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:27:44'
}
{
  message: 'Screenshot saved: 2025-08-28T00-27-44-192Z_funda-1756340843067_www.funda.nl_04_funda_before_contact_click.png',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:27:45'
}
{
  message: 'Screenshot saved: 2025-08-28T00-27-52-264Z_funda-1756340843067_www.funda.nl_05_funda_after_contact_click.png',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:27:53'
}
{
  message: 'Found form with selector: form[aria-busy="false"]',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:27:58'
}
{
  service: 'zakmakelaar-api',
  formData: {
    firstName: 'Wellis',
    lastName: 'Hant',
    email: '<EMAIL>',
    phone: '030 686 62 00'
  },
  level: 'info',
  message: 'Starting Funda form filling process',
  timestamp: '2025-08-28 01:27:58'
}
{
  service: 'zakmakelaar-api',
  elements: [
    {
      tagName: 'TEXTAREA',
      type: 'textarea',
      id: 'questionInput',
      name: 'N/A',
      className: 'block w-full rounded-sm border bg-white px-4 py-2 text-base font-normal outline-hidden placeholder:text-neutral-40 disabled:bg-neutral-10 border-neutral-30 focus:border-neutral-40 min-h-24',
      placeholder: 'Stel je vraag aan de makelaar'
    },
    {
      tagName: 'INPUT',
      type: 'checkbox',
      id: 'checkbox-viewingRequest',
      name: 'viewingRequest',
      className: 'my-1 checked:after:bg-white cursor-pointer border-secondary-70 checked:bg-secondary-70 checked:before:bg-white hover:border-secondary-70-darken-1 hover:before:bg-secondary-10-darken-2 checked:hover:before:bg-white focus-visible:outline-secondary-70-darken-1 checkbox',
      placeholder: 'N/A'
    },
    {
      tagName: 'INPUT',
      type: 'email',
      id: 'emailAddress',
      name: 'N/A',
      className: 'w-full grow bg-transparent px-4 py-2 text-base font-normal outline-hidden placeholder:text-neutral-40',
      placeholder: 'N/A'
    },
    {
      tagName: 'INPUT',
      type: 'text',
      id: 'firstName',
      name: 'N/A',
      className: 'w-full grow bg-transparent px-4 py-2 text-base font-normal outline-hidden placeholder:text-neutral-40',
      placeholder: 'N/A'
    },
    {
      tagName: 'INPUT',
      type: 'text',
      id: 'lastName',
      name: 'N/A',
      className: 'w-full grow bg-transparent px-4 py-2 text-base font-normal outline-hidden placeholder:text-neutral-40',
      placeholder: 'N/A'
    },
    {
      tagName: 'INPUT',
      type: 'tel',
      id: 'phoneNumber',
      name: 'N/A',
      className: 'w-full grow bg-transparent px-4 py-2 text-base font-normal outline-hidden placeholder:text-neutral-40',
      placeholder: 'N/A'
    }
  ],
  level: 'info',
  message: 'All form elements found on page:',
  timestamp: '2025-08-28 01:27:59'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:30:00'
}
{
  component: 'scraper',
  message: 'Running enabled scrapers: funda',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:30:00'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:31:02',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:31:02',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-08-28 01:31:02',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
