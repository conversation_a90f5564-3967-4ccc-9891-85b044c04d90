const { authService } = require('./zakmakelaar-frontend/services/authService');
const { listingsService } = require('./zakmakelaar-frontend/services/listingsService');

async function debugPreferencesFallback() {
  console.log('🔍 Debugging preferences fallback issue...\n');

  try {
    // 1. Check if user is authenticated and has preferences
    console.log('1. Checking user authentication and preferences...');
    const userData = await authService.getCachedUser();
    
    if (!userData) {
      console.log('❌ No user data found - user not authenticated');
      return;
    }

    console.log('✅ User authenticated:', {
      id: userData.id,
      email: userData.email,
      firstName: userData.firstName
    });

    if (!userData.preferences) {
      console.log('❌ No preferences found for user');
      return;
    }

    console.log('✅ User preferences found:', userData.preferences);

    // 2. Check if preferences have meaningful values
    const hasPreferences = userData.preferences &&
      (userData.preferences.maxPrice > 0 ||
        (userData.preferences.preferredLocations?.length ?? 0) > 0 ||
        (userData.preferences.propertyTypes?.length ?? 0) > 0);

    console.log('\n2. Checking if preferences have meaningful values...');
    console.log('Has meaningful preferences:', hasPreferences);
    console.log('Details:', {
      maxPrice: userData.preferences.maxPrice,
      minPrice: userData.preferences.minPrice,
      preferredLocations: userData.preferences.preferredLocations,
      propertyTypes: userData.preferences.propertyTypes,
      minRooms: userData.preferences.minRooms,
      maxRooms: userData.preferences.maxRooms
    });

    if (!hasPreferences) {
      console.log('❌ User preferences are empty or have no meaningful values');
      return;
    }

    // 3. Test the preference-based listings call
    console.log('\n3. Testing preference-based listings call...');
    const preferenceResponse = await listingsService.getPreferenceBasedListings(10);
    
    console.log('Preference-based response:', {
      success: preferenceResponse.success,
      listingsCount: preferenceResponse.data?.listings?.length || 0,
      fallbackUsed: preferenceResponse.fallbackUsed,
      message: preferenceResponse.message
    });

    if (preferenceResponse.data?.listings?.length > 0) {
      console.log('✅ Found listings with preferences!');
      console.log('Sample listing:', {
        title: preferenceResponse.data.listings[0].title,
        price: preferenceResponse.data.listings[0].price,
        location: preferenceResponse.data.listings[0].location,
        propertyType: preferenceResponse.data.listings[0].propertyType,
        rooms: preferenceResponse.data.listings[0].rooms
      });
    } else {
      console.log('❌ No listings found with preferences');
    }

    // 4. Test recent listings for comparison
    console.log('\n4. Testing recent listings for comparison...');
    const recentResponse = await listingsService.getRecentListings(10);
    
    console.log('Recent listings response:', {
      success: recentResponse.success,
      listingsCount: recentResponse.data?.listings?.length || 0,
      message: recentResponse.message
    });

    if (recentResponse.data?.listings?.length > 0) {
      console.log('✅ Found recent listings');
      console.log('Sample recent listing:', {
        title: recentResponse.data.listings[0].title,
        price: recentResponse.data.listings[0].price,
        location: recentResponse.data.listings[0].location,
        propertyType: recentResponse.data.listings[0].propertyType,
        rooms: recentResponse.data.listings[0].rooms
      });
    }

    // 5. Test direct API call with preferences
    console.log('\n5. Testing direct API call with preference filters...');
    
    const filters = {};
    if (userData.preferences.minPrice !== undefined) {
      filters.minPrice = userData.preferences.minPrice;
    }
    if (userData.preferences.maxPrice !== undefined) {
      filters.maxPrice = userData.preferences.maxPrice;
    }
    if (userData.preferences.minRooms !== undefined) {
      filters.minRooms = userData.preferences.minRooms;
    }
    if (userData.preferences.maxRooms !== undefined) {
      filters.maxRooms = userData.preferences.maxRooms;
    }
    if (userData.preferences.preferredLocations && userData.preferences.preferredLocations.length > 0) {
      filters.cities = userData.preferences.preferredLocations;
    }
    if (userData.preferences.propertyTypes && userData.preferences.propertyTypes.length > 0) {
      filters.propertyTypes = userData.preferences.propertyTypes;
    }

    console.log('Filters being used:', filters);

    const directResponse = await listingsService.getListings({
      ...filters,
      limit: 10,
      sortBy: "dateAdded",
      sortOrder: "desc"
    });

    console.log('Direct API response:', {
      success: directResponse.success,
      listingsCount: directResponse.data?.listings?.length || 0,
      message: directResponse.message
    });

    if (directResponse.data?.listings?.length > 0) {
      console.log('✅ Found listings with direct API call');
      console.log('Sample direct listing:', {
        title: directResponse.data.listings[0].title,
        price: directResponse.data.listings[0].price,
        location: directResponse.data.listings[0].location,
        propertyType: directResponse.data.listings[0].propertyType,
        rooms: directResponse.data.listings[0].rooms
      });
    } else {
      console.log('❌ No listings found with direct API call');
    }

    // 6. Check if there are any listings in the database at all
    console.log('\n6. Checking total listings in database...');
    const allListingsResponse = await listingsService.getListings({ limit: 5 });
    console.log('Total listings check:', {
      success: allListingsResponse.success,
      listingsCount: allListingsResponse.data?.listings?.length || 0,
      totalCount: allListingsResponse.pagination?.totalCount || 0
    });

  } catch (error) {
    console.error('❌ Error during debugging:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// Run the debug function
debugPreferencesFallback().then(() => {
  console.log('\n🏁 Debug complete');
  process.exit(0);
}).catch(error => {
  console.error('💥 Fatal error:', error);
  process.exit(1);
});