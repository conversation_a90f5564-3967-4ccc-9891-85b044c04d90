/**
 * Debug logger to trace redirect flows and identify the source of loops
 */

class DebugLogger {
  private logs: string[] = [];
  private startTime = Date.now();
  private enabled = false;

  constructor() {
    // Debug logs are gated; enable explicitly when needed
    this.enabled = false;
  }

  log(source: string, action: string, details?: any): void {
    if (!this.enabled) return;
    const timestamp = Date.now() - this.startTime;
    const logEntry = `[${timestamp}ms] ${source}: ${action}`;

    console.log(`🔍 ${logEntry}`, details || "");
    this.logs.push(logEntry + (details ? ` - ${JSON.stringify(details)}` : ""));
  }

  enable() {
    this.enabled = true;
  }

  disable() {
    this.enabled = false;
  }

  getLogs(): string[] {
    return [...this.logs];
  }

  clearLogs(): void {
    this.logs = [];
    this.startTime = Date.now();
  }

  printSummary(): void {
    if (!this.enabled) return;
    console.log("\n📋 REDIRECT DEBUG SUMMARY:");
    console.log("=".repeat(50));
    this.logs.forEach((log, index) => {
      console.log(`${index + 1}. ${log}`);
    });
    console.log("=".repeat(50));
  }
}

export const debugLogger = new DebugLogger();
export default debugLogger;
