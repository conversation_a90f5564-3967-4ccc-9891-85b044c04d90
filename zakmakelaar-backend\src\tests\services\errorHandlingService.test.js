const ErrorHandlingService = require('../../services/errorHandlingService');
const AutoApplicationSettings = require('../../models/AutoApplicationSettings');
const ApplicationQueue = require('../../models/ApplicationQueue');
const ApplicationResult = require('../../models/ApplicationResult');
const websocketService = require('../../services/websocketService');

// Mock dependencies
jest.mock('../../models/AutoApplicationSettings');
jest.mock('../../models/ApplicationQueue');
jest.mock('../../models/ApplicationResult');
jest.mock('../../services/websocketService');
jest.mock('../../services/logger', () => ({
  loggers: {
    app: {
      info: jest.fn(),
      warn: jest.fn(),
      error: jest.fn()
    },
    errorHandling: {
      info: jest.fn(),
      warn: jest.fn(),
      error: jest.fn()
    }
  }
}));

describe('ErrorHandlingService', () => {
  let errorHandlingService;
  let mockQueueItem;
  let mockSettings;

  beforeEach(() => {
    jest.clearAllMocks();
    errorHandlingService = new ErrorHandlingService();
    
    mockQueueItem = {
      _id: 'queue123',
      userId: 'user123',
      status: 'processing',
      attempts: 1,
      scheduledAt: new Date(),
      metadata: {},
      save: jest.fn().mockResolvedValue(true)
    };

    mockSettings = {
      userId: 'user123',
      status: {
        isActive: true,
        pausedReason: null,
        pausedUntil: null
      },
      save: jest.fn().mockResolvedValue(true)
    };

    ApplicationQueue.findById.mockResolvedValue(mockQueueItem);
    AutoApplicationSettings.findByUserId.mockResolvedValue(mockSettings);
  });

  describe('Error Categorization', () => {
    test('should categorize network errors correctly', () => {
      const networkError = new Error('ECONNRESET: Connection reset by peer');
      const context = { service: 'BrowserAutomation' };

      const result = errorHandlingService.categorizeError(networkError, context);

      expect(result.category).toBe('NETWORK');
      expect(result.retryable).toBe(true);
      expect(result.confidence).toBeGreaterThan(0.7);
    });

    test('should categorize form errors correctly', () => {
      const formError = new Error('selector not found: input[name="email"]');
      const context = { service: 'FormAutomationEngine' };

      const result = errorHandlingService.categorizeError(formError, context);

      expect(result.category).toBe('FORM');
      expect(result.retryable).toBe(true);
    });

    test('should categorize detection errors correctly', () => {
      const detectionError = new Error('CAPTCHA verification required');
      const context = { statusCode: 403 };

      const result = errorHandlingService.categorizeError(detectionError, context);

      expect(result.category).toBe('DETECTION');
      expect(result.retryable).toBe(false);
      expect(result.requiresManualIntervention).toBe(true);
    });

    test('should categorize rate limit errors correctly', () => {
      const rateLimitError = new Error('Too many requests');
      const context = { statusCode: 429 };

      const result = errorHandlingService.categorizeError(rateLimitError, context);

      expect(result.category).toBe('RATE_LIMIT');
      expect(result.confidence).toBe(1.0);
    });

    test('should categorize data errors correctly', () => {
      const dataError = new Error('User not found');
      const context = { service: 'UserProfileService' };

      const result = errorHandlingService.categorizeError(dataError, context);

      expect(result.category).toBe('DATA');
      expect(result.requiresManualIntervention).toBe(true);
    });

    test('should categorize system errors correctly', () => {
      const systemError = new Error('Database connection failed');
      const context = { service: 'DatabaseService' };

      const result = errorHandlingService.categorizeError(systemError, context);

      expect(result.category).toBe('SYSTEM');
      expect(result.retryable).toBe(true);
    });
  });

  describe('Recovery Strategies', () => {
    test('should handle network errors with retry logic', async () => {
      const networkError = new Error('ECONNRESET');
      const context = {
        queueItemId: 'queue123',
        userId: 'user123',
        attemptNumber: 1
      };

      const result = await errorHandlingService.handleError(networkError, context);

      expect(result.success).toBe(true);
      expect(result.action).toBe('RETRY_SCHEDULED');
      expect(result.retryScheduled).toBe(true);
      expect(result.retryAttempt).toBe(2);
      expect(mockQueueItem.save).toHaveBeenCalled();
    });

    test('should not retry network errors after max attempts', async () => {
      const networkError = new Error('ECONNRESET');
      const context = {
        queueItemId: 'queue123',
        userId: 'user123',
        attemptNumber: 4 // Exceeds max retries for network (3)
      };

      const result = await errorHandlingService.handleError(networkError, context);

      expect(result.success).toBe(false);
      expect(result.action).toBe('MAX_RETRIES_EXCEEDED');
      expect(result.retryScheduled).toBe(false);
    });

    test('should handle form errors with adaptation', async () => {
      const formError = new Error('form field not found');
      const context = {
        queueItemId: 'queue123',
        userId: 'user123',
        formType: 'funda_native',
        attemptNumber: 1
      };

      const result = await errorHandlingService.handleError(formError, context);

      expect(result.success).toBe(true);
      expect(result.action).toBe('RETRY_WITH_ADAPTATION');
      expect(result.adaptation).toBeDefined();
      expect(result.retryScheduled).toBe(true);
    });

    test('should handle detection errors by pausing user', async () => {
      const detectionError = new Error('Bot detected');
      const context = {
        queueItemId: 'queue123',
        userId: 'user123'
      };

      const result = await errorHandlingService.handleError(detectionError, context);

      expect(result.success).toBe(true);
      expect(result.action).toBe('USER_PAUSED_MANUAL_INTERVENTION');
      expect(result.manualInterventionRequired).toBe(true);
      expect(mockSettings.save).toHaveBeenCalled();
      expect(mockSettings.status.isActive).toBe(false);
      expect(websocketService.sendAutoApplicationUpdate).toHaveBeenCalled();
    });

    test('should handle data errors by notifying user', async () => {
      const dataError = new Error('Missing required field: monthlyIncome');
      const context = {
        userId: 'user123',
        missingData: ['monthlyIncome']
      };

      const result = await errorHandlingService.handleError(dataError, context);

      expect(result.success).toBe(true);
      expect(result.action).toBe('USER_NOTIFIED_PAUSED');
      expect(result.manualInterventionRequired).toBe(true);
      expect(websocketService.sendAutoApplicationUpdate).toHaveBeenCalledWith(
        'user123',
        expect.objectContaining({
          action: 'data_issue'
        })
      );
    });

    test('should handle rate limit errors with intelligent backoff', async () => {
      const rateLimitError = new Error('Rate limit exceeded');
      const context = {
        queueItemId: 'queue123',
        userId: 'user123',
        rateLimitInfo: { retryAfter: 300000 }, // 5 minutes
        attemptNumber: 1
      };

      const result = await errorHandlingService.handleError(rateLimitError, context);

      expect(result.success).toBe(true);
      expect(result.action).toBe('RATE_LIMIT_BACKOFF');
      expect(result.retryDelay).toBe(300000);
      expect(result.retryScheduled).toBe(true);
    });

    test('should handle CAPTCHA errors by requiring manual intervention', async () => {
      const captchaError = new Error('CAPTCHA verification required');
      const context = {
        queueItemId: 'queue123',
        userId: 'user123',
        url: 'https://funda.nl/property/123'
      };

      const result = await errorHandlingService.handleError(captchaError, context);

      expect(result.success).toBe(true);
      expect(result.action).toBe('CAPTCHA_MANUAL_INTERVENTION');
      expect(result.manualInterventionRequired).toBe(true);
      expect(websocketService.sendAutoApplicationUpdate).toHaveBeenCalledWith(
        'user123',
        expect.objectContaining({
          action: 'captcha_required'
        })
      );
    });

    test('should handle system errors with graceful degradation for system-wide failures', async () => {
      // Simulate multiple system failures
      errorHandlingService.healthMetrics.systemFailures = 3;
      errorHandlingService.healthMetrics.lastSystemFailure = new Date();

      const systemError = new Error('Database connection pool exhausted');
      const context = {
        service: 'core',
        attemptNumber: 1
      };

      ApplicationQueue.updateMany.mockResolvedValue({ modifiedCount: 5 });

      const result = await errorHandlingService.handleError(systemError, context);

      expect(result.success).toBe(true);
      expect(result.action).toBe('GRACEFUL_DEGRADATION');
      expect(result.degradationMode).toBe(true);
      expect(ApplicationQueue.updateMany).toHaveBeenCalledWith(
        { status: 'pending' },
        expect.objectContaining({
          status: 'paused'
        })
      );
    });
  });

  describe('Backoff Calculation', () => {
    test('should calculate exponential backoff correctly', () => {
      const categoryConfig = errorHandlingService.errorCategories.NETWORK;
      
      const delay1 = errorHandlingService.calculateBackoffDelay('NETWORK', 1, categoryConfig);
      const delay2 = errorHandlingService.calculateBackoffDelay('NETWORK', 2, categoryConfig);
      const delay3 = errorHandlingService.calculateBackoffDelay('NETWORK', 3, categoryConfig);

      expect(delay1).toBeGreaterThanOrEqual(categoryConfig.baseDelay * 0.9);
      expect(delay1).toBeLessThanOrEqual(categoryConfig.baseDelay * 1.1);
      
      expect(delay2).toBeGreaterThan(delay1);
      expect(delay3).toBeGreaterThan(delay2);
      
      // Should not exceed max delay
      expect(delay3).toBeLessThanOrEqual(categoryConfig.maxDelay);
    });

    test('should respect maximum delay limits', () => {
      const categoryConfig = errorHandlingService.errorCategories.NETWORK;
      
      const delay = errorHandlingService.calculateBackoffDelay('NETWORK', 10, categoryConfig);
      
      expect(delay).toBeLessThanOrEqual(categoryConfig.maxDelay);
    });

    test('should include jitter in backoff calculation', () => {
      const categoryConfig = errorHandlingService.errorCategories.NETWORK;
      
      const delays = [];
      for (let i = 0; i < 10; i++) {
        delays.push(errorHandlingService.calculateBackoffDelay('NETWORK', 2, categoryConfig));
      }

      // All delays should be different due to jitter
      const uniqueDelays = new Set(delays);
      expect(uniqueDelays.size).toBeGreaterThan(1);
    });
  });

  describe('Queue Item Management', () => {
    test('should schedule retry for queue item correctly', async () => {
      const delay = 60000; // 1 minute
      const attemptNumber = 2;

      await errorHandlingService.scheduleRetry('queue123', delay, attemptNumber);

      expect(mockQueueItem.status).toBe('retrying');
      expect(mockQueueItem.attempts).toBe(attemptNumber);
      expect(mockQueueItem.scheduledAt).toBeInstanceOf(Date);
      expect(mockQueueItem.delayUntil).toBeInstanceOf(Date);
      expect(mockQueueItem.metadata.retryInfo).toBeDefined();
      expect(mockQueueItem.save).toHaveBeenCalled();
    });

    test('should handle missing queue item gracefully', async () => {
      ApplicationQueue.findById.mockResolvedValue(null);

      await expect(
        errorHandlingService.scheduleRetry('nonexistent', 60000, 2)
      ).resolves.not.toThrow();
    });

    test('should mark queue item for manual intervention', async () => {
      const reason = 'CAPTCHA verification required';

      await errorHandlingService.markForManualIntervention('queue123', reason);

      expect(mockQueueItem.status).toBe('manual_intervention_required');
      expect(mockQueueItem.metadata.manualInterventionReason).toBe(reason);
      expect(mockQueueItem.metadata.manualInterventionRequiredAt).toBeInstanceOf(Date);
      expect(mockQueueItem.save).toHaveBeenCalled();
    });
  });

  describe('User Management', () => {
    test('should pause user auto-application with duration', async () => {
      const reason = 'Rate limit exceeded';
      const duration = 3600000; // 1 hour

      await errorHandlingService.pauseUserAutoApplication('user123', reason, duration);

      expect(mockSettings.status.isActive).toBe(false);
      expect(mockSettings.status.pausedReason).toBe(reason);
      expect(mockSettings.status.pausedUntil).toBeInstanceOf(Date);
      expect(mockSettings.status.pausedAt).toBeInstanceOf(Date);
      expect(mockSettings.save).toHaveBeenCalled();
      expect(websocketService.sendAutoApplicationUpdate).toHaveBeenCalledWith(
        'user123',
        expect.objectContaining({
          action: 'paused',
          reason
        })
      );
    });

    test('should pause user auto-application indefinitely', async () => {
      const reason = 'Missing required data';

      await errorHandlingService.pauseUserAutoApplication('user123', reason, null);

      expect(mockSettings.status.pausedUntil).toBeNull();
    });

    test('should handle missing user settings gracefully', async () => {
      AutoApplicationSettings.findByUserId.mockResolvedValue(null);

      await expect(
        errorHandlingService.pauseUserAutoApplication('user123', 'test reason')
      ).resolves.not.toThrow();
    });
  });

  describe('Error Metrics', () => {
    test('should update error metrics correctly', () => {
      const errorInfo = {
        category: 'NETWORK',
        confidence: 0.9
      };
      const context = {
        userId: 'user123'
      };

      const initialTotal = errorHandlingService.healthMetrics.totalErrors;
      const initialNetwork = errorHandlingService.healthMetrics.errorsByCategory.NETWORK;

      errorHandlingService.updateErrorMetrics(errorInfo, context);

      expect(errorHandlingService.healthMetrics.totalErrors).toBe(initialTotal + 1);
      expect(errorHandlingService.healthMetrics.errorsByCategory.NETWORK).toBe(initialNetwork + 1);
      expect(errorHandlingService.healthMetrics.errorsByUser['user123']).toBe(1);
    });

    test('should track system failures separately', () => {
      const errorInfo = {
        category: 'SYSTEM',
        confidence: 0.8
      };
      const context = {};

      const initialSystemFailures = errorHandlingService.healthMetrics.systemFailures;

      errorHandlingService.updateErrorMetrics(errorInfo, context);

      expect(errorHandlingService.healthMetrics.systemFailures).toBe(initialSystemFailures + 1);
      expect(errorHandlingService.healthMetrics.lastSystemFailure).toBeInstanceOf(Date);
    });

    test('should get error statistics', () => {
      const stats = errorHandlingService.getErrorStatistics();

      expect(stats).toHaveProperty('totalErrors');
      expect(stats).toHaveProperty('errorsByCategory');
      expect(stats).toHaveProperty('errorsByUser');
      expect(stats).toHaveProperty('systemFailures');
      expect(stats).toHaveProperty('errorCategories');
      expect(stats).toHaveProperty('recoveryStrategies');
    });

    test('should reset error statistics', () => {
      // Add some errors first
      errorHandlingService.healthMetrics.totalErrors = 10;
      errorHandlingService.healthMetrics.systemFailures = 2;

      errorHandlingService.resetErrorStatistics();

      expect(errorHandlingService.healthMetrics.totalErrors).toBe(0);
      expect(errorHandlingService.healthMetrics.systemFailures).toBe(0);
      expect(errorHandlingService.healthMetrics.lastSystemFailure).toBeNull();
    });
  });

  describe('System-wide Failure Detection', () => {
    test('should detect system-wide failure with multiple recent errors', async () => {
      errorHandlingService.healthMetrics.systemFailures = 3;
      errorHandlingService.healthMetrics.lastSystemFailure = new Date(Date.now() - 60000); // 1 minute ago

      const isSystemWide = await errorHandlingService.detectSystemWideFailure(new Error('test'));

      expect(isSystemWide).toBe(true);
    });

    test('should not detect system-wide failure with old errors', async () => {
      errorHandlingService.healthMetrics.systemFailures = 3;
      errorHandlingService.healthMetrics.lastSystemFailure = new Date(Date.now() - 600000); // 10 minutes ago

      const isSystemWide = await errorHandlingService.detectSystemWideFailure(new Error('test'));

      expect(isSystemWide).toBe(false);
    });

    test('should not detect system-wide failure with few errors', async () => {
      errorHandlingService.healthMetrics.systemFailures = 1;
      errorHandlingService.healthMetrics.lastSystemFailure = new Date();

      const isSystemWide = await errorHandlingService.detectSystemWideFailure(new Error('test'));

      expect(isSystemWide).toBe(false);
    });
  });

  describe('Health Check', () => {
    test('should perform health check and detect stuck items', async () => {
      ApplicationQueue.countDocuments.mockResolvedValue(2);

      await errorHandlingService.performHealthCheck();

      expect(ApplicationQueue.countDocuments).toHaveBeenCalledWith({
        status: 'processing',
        updatedAt: { $lt: expect.any(Date) }
      });
    });
  });

  describe('Error Severity Calculation', () => {
    test('should calculate critical severity for system errors in core service', () => {
      const errorInfo = { category: 'SYSTEM' };
      const context = { service: 'core' };

      const severity = errorHandlingService.calculateErrorSeverity(errorInfo, context);

      expect(severity).toBe('critical');
    });

    test('should calculate high severity for manual intervention errors', () => {
      const errorInfo = { 
        category: 'DETECTION',
        requiresManualIntervention: true
      };
      const context = {};

      const severity = errorHandlingService.calculateErrorSeverity(errorInfo, context);

      expect(severity).toBe('high');
    });

    test('should calculate medium severity for retryable errors', () => {
      const errorInfo = { 
        category: 'NETWORK',
        retryable: true,
        requiresManualIntervention: false
      };
      const context = {};

      const severity = errorHandlingService.calculateErrorSeverity(errorInfo, context);

      expect(severity).toBe('medium');
    });

    test('should calculate low severity for other errors', () => {
      const errorInfo = { 
        category: 'UNKNOWN',
        retryable: false,
        requiresManualIntervention: false
      };
      const context = {};

      const severity = errorHandlingService.calculateErrorSeverity(errorInfo, context);

      expect(severity).toBe('low');
    });
  });

  describe('User-friendly Error Messages', () => {
    test('should generate appropriate message for network errors', () => {
      const errorInfo = { category: 'NETWORK' };
      const recoveryResult = { retryScheduled: true };

      const message = errorHandlingService.generateUserFriendlyErrorMessage(errorInfo, recoveryResult);

      expect(message).toContain('connection issues');
      expect(message).toContain('retried automatically');
    });

    test('should generate appropriate message for CAPTCHA errors', () => {
      const errorInfo = { category: 'CAPTCHA' };
      const recoveryResult = { manualInterventionRequired: true };

      const message = errorHandlingService.generateUserFriendlyErrorMessage(errorInfo, recoveryResult);

      expect(message).toContain('Manual verification');
      expect(message).toContain('check the property listing');
    });

    test('should generate fallback message for unknown errors', () => {
      const errorInfo = { category: 'UNKNOWN' };
      const recoveryResult = {};

      const message = errorHandlingService.generateUserFriendlyErrorMessage(errorInfo, recoveryResult);

      expect(message).toContain('unexpected error');
      expect(message).toContain('team has been notified');
    });
  });

  describe('Error Handling Integration', () => {
    test('should handle complete error flow', async () => {
      const error = new Error('ECONNRESET: Connection reset');
      const context = {
        queueItemId: 'queue123',
        userId: 'user123',
        service: 'BrowserAutomation',
        attemptNumber: 1
      };

      const result = await errorHandlingService.handleError(error, context);

      // Verify error was categorized
      expect(result.success).toBe(true);
      expect(result.action).toBe('RETRY_SCHEDULED');

      // Verify metrics were updated
      expect(errorHandlingService.healthMetrics.totalErrors).toBeGreaterThan(0);
      expect(errorHandlingService.healthMetrics.errorsByCategory.NETWORK).toBeGreaterThan(0);

      // Verify queue item was updated
      expect(mockQueueItem.save).toHaveBeenCalled();
      expect(mockQueueItem.status).toBe('retrying');
    });

    test('should handle error when recovery strategy fails', async () => {
      // Mock a failing recovery strategy
      const originalStrategy = errorHandlingService.recoveryStrategies.NETWORK;
      errorHandlingService.recoveryStrategies.NETWORK = jest.fn().mockRejectedValue(new Error('Strategy failed'));

      const error = new Error('ECONNRESET');
      const context = { queueItemId: 'queue123', userId: 'user123' };

      const result = await errorHandlingService.handleError(error, context);

      expect(result.success).toBe(false);
      expect(result.action).toBe('STRATEGY_FAILED');
      expect(result.manualInterventionRequired).toBe(true);

      // Restore original strategy
      errorHandlingService.recoveryStrategies.NETWORK = originalStrategy;
    });

    test('should handle error when no recovery strategy exists', async () => {
      // Remove a recovery strategy
      delete errorHandlingService.recoveryStrategies.NETWORK;

      const error = new Error('ECONNRESET');
      const context = { queueItemId: 'queue123', userId: 'user123' };

      const result = await errorHandlingService.handleError(error, context);

      expect(result.success).toBe(false);
      expect(result.action).toBe('NO_STRATEGY');
      expect(result.manualInterventionRequired).toBe(true);
    });
  });

  describe('Service Lifecycle', () => {
    test('should initialize error tracking', () => {
      const service = new ErrorHandlingService();

      expect(service.healthMetrics.totalErrors).toBe(0);
      expect(service.healthMetrics.errorsByCategory).toBeDefined();
      Object.keys(service.errorCategories).forEach(category => {
        expect(service.healthMetrics.errorsByCategory[category]).toBe(0);
      });
    });

    test('should shutdown gracefully', () => {
      const service = new ErrorHandlingService();
      
      expect(() => service.shutdown()).not.toThrow();
    });
  });
});