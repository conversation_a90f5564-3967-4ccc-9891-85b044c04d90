/**
 * Validation Engine
 * 
 * This module provides comprehensive validation for property data against the unified schema.
 * It includes field-specific validators, error classification, and default value application.
 */

const Joi = require('joi');
const { 
  UnifiedPropertySchema, 
  ValidationOptions, 
  StrictValidationOptions 
} = require('../schemas/unifiedPropertySchema');
const { SchemaError, ErrorTypes } = require('../utils/schemaErrors');

/**
 * Field Types for specialized validation
 */
const FieldTypes = {
  STRING: 'string',
  NUMBER: 'number',
  BOOLEAN: 'boolean',
  DATE: 'date',
  ENUM: 'enum',
  OBJECT: 'object',
  ARRAY: 'array',
  LOCATION: 'location',
  PRICE: 'price',
  SIZE: 'size',
  ROOMS: 'rooms',
  URL: 'url',
  EMAIL: 'email'
};

/**
 * Validation Engine Class
 * Handles validation of property data against the unified schema
 */
class ValidationEngine {
  /**
   * Create a new ValidationEngine
   * @param {Object} schemaDefinition - Joi schema definition (defaults to UnifiedPropertySchema)
   */
  constructor(schemaDefinition = UnifiedPropertySchema) {
    this.schema = schemaDefinition;
    this.validators = new Map();
    this.defaultValues = new Map();
    this._initializeFieldValidators();
    this._initializeDefaultValues();
  }

  /**
   * Initialize field-specific validators
   * @private
   */
  _initializeFieldValidators() {
    // String validators
    this.validators.set('title', {
      type: FieldTypes.STRING,
      validate: (value) => {
        if (!value || typeof value !== 'string') return false;
        return value.length >= 1 && value.length <= 500;
      },
      message: 'Title must be a string between 1 and 500 characters'
    });

    this.validators.set('description', {
      type: FieldTypes.STRING,
      validate: (value) => {
        if (value === null || value === '') return true; // Optional
        if (typeof value !== 'string') return false;
        return value.length <= 5000;
      },
      message: 'Description must be a string of maximum 5000 characters'
    });

    // Source validators
    this.validators.set('source', {
      type: FieldTypes.ENUM,
      validate: (value) => {
        if (!value || typeof value !== 'string') return false;
        return ['funda.nl', 'huurwoningen.nl', 'pararius.nl'].includes(value);
      },
      message: 'Source must be one of: funda.nl, huurwoningen.nl, pararius.nl'
    });

    // URL validator
    this.validators.set('url', {
      type: FieldTypes.URL,
      validate: (value) => {
        if (!value || typeof value !== 'string') return false;
        try {
          new URL(value);
          return true;
        } catch (e) {
          return false;
        }
      },
      message: 'URL must be a valid URL string'
    });

    // Date validator
    this.validators.set('dateAdded', {
      type: FieldTypes.DATE,
      validate: (value) => {
        if (!value) return false;
        try {
          const date = new Date(value);
          return !isNaN(date.getTime());
        } catch (e) {
          return false;
        }
      },
      message: 'Date must be a valid ISO date string'
    });

    // Location validator (supports both string and object formats)
    this.validators.set('location', {
      type: FieldTypes.LOCATION,
      validate: (value) => {
        if (!value) return false;
        
        // String format
        if (typeof value === 'string') {
          return value.length >= 1 && value.length <= 200;
        }
        
        // Object format
        if (typeof value === 'object' && value !== null) {
          // Check if it has _unified or _legacy
          if (value._unified || value._legacy) {
            return true;
          }
          
          // Check if it has address components
          if (value.city || value.address || value.street) {
            return true;
          }
        }
        
        return false;
      },
      message: 'Location must be a string or a properly formatted location object'
    });

    // Property type validator
    this.validators.set('propertyType', {
      type: FieldTypes.ENUM,
      validate: (value) => {
        if (!value || typeof value !== 'string') return false;
        return [
          'apartment', 'appartement', 'house', 'huis', 
          'studio', 'room', 'kamer', 'woning'
        ].includes(value.toLowerCase());
      },
      message: 'Property type must be one of the valid property types'
    });

    // Size validators
    this.validators.set('size', {
      type: FieldTypes.SIZE,
      validate: (value) => {
        if (!value) return true; // Optional
        if (typeof value === 'string') {
          return /^\d+[\+]?(\s*m²?)?$/.test(value);
        }
        return false;
      },
      message: 'Size must be a string in format like "85 m²" or "85"'
    });

    this.validators.set('area', {
      type: FieldTypes.NUMBER,
      validate: (value) => {
        if (value === null || value === undefined) return true; // Optional
        if (typeof value !== 'number') return false;
        return value >= 10 && value <= 1000;
      },
      message: 'Area must be a number between 10 and 1000'
    });

    // Room validators
    this.validators.set('rooms', {
      type: FieldTypes.ROOMS,
      validate: (value) => {
        if (value === null || value === undefined) return true; // Optional
        
        if (typeof value === 'string') {
          return /^\d+[\+]?$/.test(value);
        }
        
        if (typeof value === 'number') {
          return value > 0 && value <= 20;
        }
        
        return false;
      },
      message: 'Rooms must be a positive number or string like "3" or "3+"'
    });

    this.validators.set('bedrooms', {
      type: FieldTypes.ROOMS,
      validate: (value) => {
        if (value === null || value === undefined) return true; // Optional
        
        if (typeof value === 'string') {
          return /^\d+[\+]?$/.test(value);
        }
        
        if (typeof value === 'number') {
          return value > 0 && value <= 15;
        }
        
        return false;
      },
      message: 'Bedrooms must be a positive number or string like "2" or "2+"'
    });

    this.validators.set('bathrooms', {
      type: FieldTypes.ROOMS,
      validate: (value) => {
        if (value === null || value === undefined) return true; // Optional
        
        if (typeof value === 'string') {
          return /^\d+[\+]?$/.test(value);
        }
        
        if (typeof value === 'number') {
          return value > 0 && value <= 10;
        }
        
        return false;
      },
      message: 'Bathrooms must be a positive number or string like "1" or "1+"'
    });

    // Year validator
    this.validators.set('year', {
      type: FieldTypes.STRING,
      validate: (value) => {
        if (value === null || value === '' || value === undefined) return true; // Optional
        if (typeof value !== 'string') return false;
        
        if (/^\d{4}$/.test(value)) {
          const year = parseInt(value);
          const currentYear = new Date().getFullYear();
          return year >= 1800 && year <= currentYear + 5;
        }
        
        return false;
      },
      message: 'Year must be a string in format "YYYY" between 1800 and current year + 5'
    });

    // Price validator
    this.validators.set('price', {
      type: FieldTypes.PRICE,
      validate: (value) => {
        if (!value) return false; // Required
        
        if (typeof value === 'string') {
          return value.length >= 1 && value.length <= 100;
        }
        
        if (typeof value === 'number') {
          return value > 0 && value <= 50000; // Reasonable upper bound for monthly rent
        }
        
        return false;
      },
      message: 'Price must be a positive number or non-empty string'
    });

    // Interior validator
    this.validators.set('interior', {
      type: FieldTypes.ENUM,
      validate: (value) => {
        if (value === null || value === '' || value === undefined) return true; // Optional
        if (typeof value !== 'string') return false;
        
        const validValues = [
          'Kaal', 'Gestoffeerd', 'Gemeubileerd', 
          'kaal', 'gestoffeerd', 'gemeubileerd',
          'furnished', 'unfurnished', 'semi-furnished'
        ];
        
        return validValues.includes(value);
      },
      message: 'Interior must be one of the valid interior types'
    });

    // Boolean validators
    const validateBoolean = (value) => {
      if (value === null || value === undefined) return true; // Optional
      return typeof value === 'boolean';
    };

    this.validators.set('furnished', {
      type: FieldTypes.BOOLEAN,
      validate: validateBoolean,
      message: 'Furnished must be a boolean'
    });

    this.validators.set('pets', {
      type: FieldTypes.BOOLEAN,
      validate: validateBoolean,
      message: 'Pets must be a boolean'
    });

    this.validators.set('smoking', {
      type: FieldTypes.BOOLEAN,
      validate: validateBoolean,
      message: 'Smoking must be a boolean'
    });

    this.validators.set('garden', {
      type: FieldTypes.BOOLEAN,
      validate: validateBoolean,
      message: 'Garden must be a boolean'
    });

    this.validators.set('balcony', {
      type: FieldTypes.BOOLEAN,
      validate: validateBoolean,
      message: 'Balcony must be a boolean'
    });

    this.validators.set('parking', {
      type: FieldTypes.BOOLEAN,
      validate: validateBoolean,
      message: 'Parking must be a boolean'
    });

    // Energy label validator
    this.validators.set('energyLabel', {
      type: FieldTypes.ENUM,
      validate: (value) => {
        if (value === null || value === '' || value === undefined) return true; // Optional
        if (typeof value !== 'string') return false;
        
        const validValues = ['A+++', 'A++', 'A+', 'A', 'B', 'C', 'D', 'E', 'F', 'G'];
        return validValues.includes(value);
      },
      message: 'Energy label must be one of the valid energy labels'
    });

    // Images validator
    this.validators.set('images', {
      type: FieldTypes.ARRAY,
      validate: (value) => {
        if (!value) return true; // Optional
        if (!Array.isArray(value)) return false;
        
        // Check that all items are strings
        return value.every(item => typeof item === 'string');
      },
      message: 'Images must be an array of strings'
    });

    // Contact info validator
    this.validators.set('contactInfo', {
      type: FieldTypes.OBJECT,
      validate: (value) => {
        if (!value) return true; // Optional
        if (typeof value !== 'object' || value === null) return false;
        
        // Validate email if present
        if (value.email && typeof value.email === 'string') {
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (!emailRegex.test(value.email)) return false;
        }
        
        return true;
      },
      message: 'Contact info must be an object with valid email if present'
    });
  }

  /**
   * Initialize default values for fields
   * @private
   */
  _initializeDefaultValues() {
    this.defaultValues.set('propertyType', 'woning');
    this.defaultValues.set('bathrooms', '1');
    this.defaultValues.set('dateAdded', () => new Date().toISOString());
    this.defaultValues.set('furnished', false);
    this.defaultValues.set('pets', false);
    this.defaultValues.set('smoking', false);
    this.defaultValues.set('garden', false);
    this.defaultValues.set('balcony', false);
    this.defaultValues.set('parking', false);
    this.defaultValues.set('isActive', true);
    this.defaultValues.set('images', []);
    this.defaultValues.set('features', []);
    this.defaultValues.set('contactInfo', {});
    this.defaultValues.set('_internal', {
      sourceMetadata: {
        scrapedAt: new Date(),
        lastUpdated: new Date(),
        version: 1
      },
      dataQuality: {
        completeness: 0,
        accuracy: 0,
        validationErrors: []
      }
    });
  }

  /**
   * Validate data against schema
   * @param {Object} data - Data to validate
   * @param {Object} options - Validation options
   * @param {boolean} options.applyDefaults - Whether to apply default values
   * @param {boolean} options.strict - Whether to use strict validation
   * @returns {Object} Validation result with value and errors
   */
  validate(data, options = {}) {
    const { 
      applyDefaults = true, 
      strict = false 
    } = options;
    
    if (!data || typeof data !== 'object') {
      return {
        valid: false,
        errors: [new SchemaError(
          ErrorTypes.VALIDATION_ERROR,
          'Data must be an object',
          { data }
        )],
        value: null
      };
    }
    
    // Apply defaults if requested
    let processedData = data;
    if (applyDefaults) {
      processedData = this.applyDefaults(data);
    }
    
    // Use Joi for schema validation
    const validationOptions = strict ? StrictValidationOptions : ValidationOptions;
    const joiValidation = this.schema.validate(processedData, validationOptions);
    
    if (joiValidation.error) {
      // Convert Joi errors to SchemaErrors
      const errors = joiValidation.error.details.map(detail => {
        return new SchemaError(
          ErrorTypes.VALIDATION_ERROR,
          detail.message,
          {
            field: detail.path.join('.'),
            type: detail.type,
            context: detail.context
          }
        );
      });
      
      return {
        valid: false,
        errors,
        value: joiValidation.value
      };
    }
    
    // Perform field-specific validation
    const fieldErrors = this.validateFields(joiValidation.value);
    
    if (fieldErrors.length > 0) {
      return {
        valid: false,
        errors: fieldErrors,
        value: joiValidation.value
      };
    }
    
    return {
      valid: true,
      errors: [],
      value: joiValidation.value
    };
  }

  /**
   * Validate individual fields with field-specific validators
   * @param {Object} data - Data to validate
   * @returns {Array<SchemaError>} Array of validation errors
   */
  validateFields(data) {
    const errors = [];
    
    for (const [field, validator] of this.validators.entries()) {
      // Skip validation if field is not present and not required
      if (data[field] === undefined) {
        // Check if field is required
        const isRequired = field === 'title' || field === 'source' || 
                          field === 'url' || field === 'location' || 
                          field === 'price';
        
        if (isRequired) {
          errors.push(new SchemaError(
            ErrorTypes.VALIDATION_ERROR,
            `Required field '${field}' is missing`,
            { field }
          ));
        }
        
        continue;
      }
      
      // Validate field
      if (!validator.validate(data[field])) {
        errors.push(new SchemaError(
          ErrorTypes.VALIDATION_ERROR,
          validator.message,
          {
            field,
            value: data[field],
            type: validator.type
          }
        ));
      }
    }
    
    return errors;
  }

  /**
   * Validate a specific field
   * @param {string} fieldName - Field name
   * @param {*} value - Field value
   * @returns {Object} Validation result with valid flag and error
   */
  validateField(fieldName, value) {
    const validator = this.validators.get(fieldName);
    
    if (!validator) {
      return {
        valid: false,
        error: new SchemaError(
          ErrorTypes.VALIDATION_ERROR,
          `No validator found for field '${fieldName}'`,
          { field: fieldName }
        )
      };
    }
    
    const isValid = validator.validate(value);
    
    if (!isValid) {
      return {
        valid: false,
        error: new SchemaError(
          ErrorTypes.VALIDATION_ERROR,
          validator.message,
          {
            field: fieldName,
            value,
            type: validator.type
          }
        )
      };
    }
    
    return {
      valid: true,
      error: null
    };
  }

  /**
   * Get detailed validation errors
   * @param {Object} data - Data to validate
   * @returns {Array<SchemaError>} Array of validation errors
   */
  getValidationErrors(data) {
    const { errors } = this.validate(data, { applyDefaults: false });
    return errors;
  }

  /**
   * Apply default values for missing fields
   * @param {Object} data - Data to apply defaults to
   * @returns {Object} Data with defaults applied
   */
  applyDefaults(data) {
    if (!data || typeof data !== 'object') {
      return data;
    }
    
    const result = { ...data };
    
    for (const [field, defaultValue] of this.defaultValues.entries()) {
      if (result[field] === undefined || result[field] === null) {
        result[field] = typeof defaultValue === 'function' ? defaultValue() : defaultValue;
      }
    }
    
    return result;
  }

  /**
   * Register a custom field validator
   * @param {string} fieldName - Field name
   * @param {Object} validator - Validator object with type, validate function, and message
   */
  registerFieldValidator(fieldName, validator) {
    if (!fieldName || typeof fieldName !== 'string') {
      throw new Error('Field name must be a non-empty string');
    }
    
    if (!validator || typeof validator !== 'object') {
      throw new Error('Validator must be an object');
    }
    
    if (!validator.type || !validator.validate || !validator.message) {
      throw new Error('Validator must have type, validate function, and message');
    }
    
    if (typeof validator.validate !== 'function') {
      throw new Error('Validator.validate must be a function');
    }
    
    this.validators.set(fieldName, validator);
  }

  /**
   * Register a default value for a field
   * @param {string} fieldName - Field name
   * @param {*|Function} defaultValue - Default value or function that returns default value
   */
  registerDefaultValue(fieldName, defaultValue) {
    if (!fieldName || typeof fieldName !== 'string') {
      throw new Error('Field name must be a non-empty string');
    }
    
    this.defaultValues.set(fieldName, defaultValue);
  }

  /**
   * Get field validator
   * @param {string} fieldName - Field name
   * @returns {Object|null} Validator object or null if not found
   */
  getFieldValidator(fieldName) {
    return this.validators.get(fieldName) || null;
  }

  /**
   * Get default value for a field
   * @param {string} fieldName - Field name
   * @returns {*|null} Default value or null if not found
   */
  getDefaultValue(fieldName) {
    const defaultValue = this.defaultValues.get(fieldName);
    return defaultValue !== undefined ? 
      (typeof defaultValue === 'function' ? defaultValue() : defaultValue) : 
      null;
  }

  /**
   * Get all field validators
   * @returns {Map} Map of field validators
   */
  getAllFieldValidators() {
    return new Map(this.validators);
  }

  /**
   * Get all default values
   * @returns {Map} Map of default values
   */
  getAllDefaultValues() {
    return new Map(this.defaultValues);
  }

  /**
   * Validate data and classify errors by severity
   * @param {Object} data - Data to validate
   * @returns {Object} Validation result with errors classified by severity
   */
  validateWithErrorClassification(data) {
    const { valid, errors, value } = this.validate(data);
    
    if (valid) {
      return {
        valid: true,
        value,
        errors: {
          critical: [],
          major: [],
          minor: []
        }
      };
    }
    
    // Classify errors by severity
    const classifiedErrors = {
      critical: [],
      major: [],
      minor: []
    };
    
    for (const error of errors) {
      const field = error.context?.field;
      
      // Critical errors: missing required fields or invalid types for core fields
      if (field === 'title' || field === 'source' || field === 'url' || 
          field === 'location' || field === 'price') {
        classifiedErrors.critical.push(error);
      }
      // Major errors: invalid data that affects functionality
      else if (field === 'propertyType' || field === 'rooms' || 
               field === 'bedrooms' || field === 'size' || 
               field === 'area' || field === 'images') {
        classifiedErrors.major.push(error);
      }
      // Minor errors: everything else
      else {
        classifiedErrors.minor.push(error);
      }
    }
    
    return {
      valid: false,
      value,
      errors: classifiedErrors
    };
  }

  /**
   * Calculate data quality score based on validation results
   * @param {Object} data - Data to validate
   * @returns {Object} Data quality metrics
   */
  calculateDataQuality(data) {
    const { valid, errors } = this.validate(data, { applyDefaults: false });
    
    if (valid) {
      return {
        completeness: 100,
        accuracy: 100,
        validationErrors: []
      };
    }
    
    // Define critical fields for completeness calculation
    const criticalFields = [
      'title', 'description', 'location', 'price', 
      'propertyType', 'size', 'rooms', 'images'
    ];
    
    // Count present critical fields
    let presentFields = 0;
    for (const field of criticalFields) {
      if (data[field] !== undefined && 
          data[field] !== null && 
          data[field] !== '') {
        presentFields++;
      }
    }
    
    // Calculate completeness
    const completeness = Math.round((presentFields / criticalFields.length) * 100);
    
    // Calculate accuracy based on validation errors
    const errorCount = errors.length;
    const accuracy = Math.max(0, 100 - (errorCount * 5)); // Reduce accuracy by 5% per error
    
    return {
      completeness,
      accuracy,
      validationErrors: errors.map(error => error.message)
    };
  }

  /**
   * Fix data by applying defaults and removing invalid fields
   * @param {Object} data - Data to fix
   * @returns {Object} Fixed data
   */
  fixData(data) {
    if (!data || typeof data !== 'object') {
      return this.createMinimalValidData();
    }
    
    // Start with minimal valid data to ensure required fields
    const minimalData = this.createMinimalValidData();
    
    // Apply defaults
    let fixedData = this.applyDefaults({...minimalData, ...data});
    
    // Validate each field and fix or remove invalid fields
    for (const [field, validator] of this.validators.entries()) {
      if (fixedData[field] !== undefined && !validator.validate(fixedData[field])) {
        // Try to fix the field
        const fixed = this._tryFixField(field, fixedData[field]);
        
        if (fixed !== null && validator.validate(fixed)) {
          fixedData[field] = fixed;
        } else {
          // If can't fix, use default or delete
          const defaultValue = this.defaultValues.get(field);
          if (defaultValue !== undefined) {
            fixedData[field] = typeof defaultValue === 'function' ? defaultValue() : defaultValue;
          } else if (minimalData[field] !== undefined) {
            // Use value from minimal data for required fields
            fixedData[field] = minimalData[field];
          } else {
            delete fixedData[field];
          }
        }
      }
    }
    
    // Ensure required fields are present
    const requiredFields = ['title', 'source', 'url', 'location', 'price'];
    for (const field of requiredFields) {
      if (fixedData[field] === undefined) {
        fixedData[field] = minimalData[field];
      }
    }
    
    return fixedData;
  }

  /**
   * Try to fix an invalid field value
   * @param {string} field - Field name
   * @param {*} value - Field value
   * @returns {*} Fixed value or null if can't fix
   * @private
   */
  _tryFixField(field, value) {
    if (value === undefined || value === null) {
      return null;
    }
    
    const validator = this.validators.get(field);
    if (!validator) {
      return null;
    }
    
    switch (validator.type) {
      case FieldTypes.STRING:
        return typeof value === 'string' ? value.trim().substring(0, 500) : String(value);
        
      case FieldTypes.NUMBER:
        if (typeof value === 'string') {
          const num = parseFloat(value.replace(/[^\d.,]/g, '').replace(',', '.'));
          return isNaN(num) ? null : num;
        }
        return typeof value === 'number' ? value : null;
        
      case FieldTypes.BOOLEAN:
        if (typeof value === 'string') {
          const str = value.toLowerCase();
          return str === 'true' || str === 'yes' || str === 'ja' || str === '1';
        }
        return Boolean(value);
        
      case FieldTypes.DATE:
        try {
          const date = new Date(value);
          return isNaN(date.getTime()) ? null : date.toISOString();
        } catch (e) {
          return null;
        }
        
      case FieldTypes.PRICE:
        if (typeof value === 'string') {
          const match = value.match(/[\d.,]+/);
          if (match) {
            const price = parseFloat(match[0].replace(',', '.'));
            return isNaN(price) ? value.substring(0, 100) : price;
          }
          return value.substring(0, 100);
        }
        return typeof value === 'number' ? value : null;
        
      case FieldTypes.SIZE:
        if (typeof value === 'string') {
          const match = value.match(/(\d+)/);
          return match ? `${match[1]} m²` : null;
        }
        if (typeof value === 'number') {
          return `${value} m²`;
        }
        return null;
        
      case FieldTypes.ROOMS:
        if (typeof value === 'string') {
          const match = value.match(/(\d+)/);
          return match ? match[1] : null;
        }
        if (typeof value === 'number') {
          return Math.floor(value).toString();
        }
        return null;
        
      case FieldTypes.ARRAY:
        if (Array.isArray(value)) {
          return value.filter(item => item !== null && item !== undefined);
        }
        return null;
        
      case FieldTypes.LOCATION:
        if (typeof value === 'string') {
          return value.substring(0, 200);
        }
        if (typeof value === 'object' && value !== null) {
          if (value.city) {
            return value.city;
          }
          if (value.address) {
            return value.address;
          }
        }
        return null;
        
      default:
        return null;
    }
  }

  /**
   * Create minimal valid data with required fields
   * @param {Object} basicData - Basic data to include
   * @returns {Object} Minimal valid data
   */
  createMinimalValidData(basicData = {}) {
    return {
      title: basicData.title || 'Untitled Property',
      source: basicData.source || 'funda.nl',
      url: basicData.url || 'https://example.com',
      location: basicData.location || 'Unknown Location',
      price: basicData.price || 'Prijs op aanvraag',
      propertyType: basicData.propertyType || 'woning',
      dateAdded: new Date().toISOString(),
      ...basicData
    };
  }
}

module.exports = {
  ValidationEngine,
  FieldTypes
};