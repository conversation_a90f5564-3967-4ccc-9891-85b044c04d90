import React, { useEffect } from 'react';
import { StyleSheet, View, Text, Dimensions } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Animated, { 
  useAnimatedStyle, 
  useSharedValue, 
  withDelay, 
  withTiming,
  withRepeat,
  withSequence,
  FadeIn,
  FadeInDown,
  SlideInRight,
  Easing
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';

const { width } = Dimensions.get('window');

interface AIFeatureHighlightProps {
  icon: keyof typeof Ionicons.glyphMap;
  title: string;
  description: string;
  delay?: number;
  primaryColor?: string;
  secondaryColor?: string;
  accentColor?: string;
}

const AnimatedIcon = Animated.createAnimatedComponent(Ionicons);
const AnimatedLinearGradient = Animated.createAnimatedComponent(LinearGradient);

export const AIFeatureHighlight: React.FC<AIFeatureHighlightProps> = ({
  icon,
  title,
  description,
  delay = 0,
  primaryColor = '#4361ee',
  secondaryColor = '#7209b7',
  accentColor = '#f72585'
}) => {
  // Animation values for pulse and glow effects
  const glowOpacity = useSharedValue(0.6);
  const iconScale = useSharedValue(1);
  const iconRotate = useSharedValue(0);
  const borderGlow = useSharedValue(0);
  const shimmerPosition = useSharedValue(-width);
  
  useEffect(() => {
    // Subtle pulse animation for the icon
    iconScale.value = withRepeat(
      withSequence(
        withTiming(1.15, { duration: 1500, easing: Easing.inOut(Easing.ease) }),
        withTiming(1, { duration: 1500, easing: Easing.inOut(Easing.ease) })
      ),
      -1,
      true
    );
    
    // Subtle rotation animation
    iconRotate.value = withRepeat(
      withSequence(
        withTiming(10, { duration: 2000, easing: Easing.inOut(Easing.ease) }),
        withTiming(-10, { duration: 2000, easing: Easing.inOut(Easing.ease) })
      ),
      -1,
      true
    );
    
    // Glow animation
    glowOpacity.value = withRepeat(
      withSequence(
        withTiming(0.9, { duration: 2000, easing: Easing.inOut(Easing.ease) }),
        withTiming(0.6, { duration: 2000, easing: Easing.inOut(Easing.ease) })
      ),
      -1,
      true
    );
    
    // Border glow animation
    borderGlow.value = withRepeat(
      withSequence(
        withTiming(1, { duration: 2500, easing: Easing.inOut(Easing.ease) }),
        withTiming(0.3, { duration: 2500, easing: Easing.inOut(Easing.ease) })
      ),
      -1,
      true
    );
    
    // Shimmer animation
    shimmerPosition.value = withRepeat(
      withTiming(width, { 
        duration: 3000, 
        easing: Easing.inOut(Easing.ease) 
      }),
      -1,
      false
    );
  }, []);
  
  const iconAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { scale: iconScale.value },
        { rotate: `${iconRotate.value}deg` }
      ]
    };
  });
  
  const glowAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: glowOpacity.value
    };
  });
  
  const borderAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: borderGlow.value
    };
  });
  
  const shimmerAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateX: shimmerPosition.value }]
    };
  });

  return (
    <Animated.View 
      style={styles.container}
      entering={FadeInDown.delay(delay).springify().damping(12)}
    >
      {/* Border glow effect */}
      <Animated.View style={[styles.borderGlow, borderAnimatedStyle]}>
        <LinearGradient
          colors={[primaryColor, accentColor, secondaryColor]}
          style={styles.borderGlowGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        />
      </Animated.View>
      
      {/* Shimmer effect */}
      <Animated.View style={[styles.shimmer, shimmerAnimatedStyle]}>
        <LinearGradient
          colors={['rgba(255,255,255,0)', 'rgba(255,255,255,0.1)', 'rgba(255,255,255,0)']}
          style={styles.shimmerGradient}
          start={{ x: 0, y: 0.5 }}
          end={{ x: 1, y: 0.5 }}
        />
      </Animated.View>
      
      <View style={styles.contentContainer}>
        <View style={styles.iconOuterContainer}>
          <Animated.View style={[styles.iconGlow, glowAnimatedStyle]}>
            <LinearGradient
              colors={[primaryColor, accentColor]}
              style={styles.iconGlowGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            />
          </Animated.View>
          
          <LinearGradient
            colors={['rgba(18, 18, 30, 0.9)', 'rgba(26, 26, 46, 0.9)']}
            style={styles.iconContainer}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <Animated.View style={iconAnimatedStyle}>
              <AnimatedIcon 
                name={icon} 
                size={24} 
                color={accentColor} 
                entering={FadeIn.delay(delay + 300)}
              />
            </Animated.View>
          </LinearGradient>
        </View>
        
        <View style={styles.textContainer}>
          <Animated.View 
            entering={SlideInRight.delay(delay + 100).duration(600)}
            style={styles.titleContainer}
          >
            <LinearGradient
              colors={[primaryColor, accentColor]}
              style={styles.titleGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
            >
              <Text style={styles.title}>{title}</Text>
            </LinearGradient>
          </Animated.View>
          
          <Animated.Text 
            style={styles.description}
            entering={FadeIn.delay(delay + 300).duration(800)}
          >
            {description}
          </Animated.Text>
        </View>
      </View>
      
      {/* Tech decoration elements */}
      <View style={styles.decorationContainer}>
        <View style={[styles.decorationDot, { top: 12, right: 12 }]} />
        <View style={[styles.decorationLine, { top: 12, right: 22, width: 15 }]} />
        <View style={[styles.decorationDot, { bottom: 12, right: 30 }]} />
        <View style={[styles.decorationLine, { bottom: 12, right: 40, width: 25 }]} />
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    marginBottom: 24,
    width: '100%',
    borderRadius: 16,
    overflow: 'hidden',
  },
  borderGlow: {
    position: 'absolute',
    top: -1,
    left: -1,
    right: -1,
    bottom: -1,
    borderRadius: 17,
    zIndex: -1,
  },
  borderGlowGradient: {
    width: '100%',
    height: '100%',
  },
  shimmer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 2,
  },
  shimmerGradient: {
    width: width * 0.6,
    height: '100%',
  },
  contentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 16,
    backgroundColor: 'rgba(10, 10, 24, 0.85)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.12)',
    borderRadius: 16,
    zIndex: 1,
  },
  iconOuterContainer: {
    position: 'relative',
    width: 56,
    height: 56,
    marginRight: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  iconGlow: {
    position: 'absolute',
    width: 56,
    height: 56,
    borderRadius: 16,
    overflow: 'hidden',
  },
  iconGlowGradient: {
    width: '100%',
    height: '100%',
    opacity: 0.7,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  textContainer: {
    flex: 1,
  },
  titleContainer: {
    alignSelf: 'flex-start',
    marginBottom: 8,
    borderRadius: 6,
    overflow: 'hidden',
  },
  titleGradient: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 6,
  },
  title: {
    fontSize: 18,
    fontWeight: '700',
    color: 'white',
  },
  description: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.85)',
    lineHeight: 20,
  },
  decorationContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 3,
    pointerEvents: 'none',
  },
  decorationDot: {
    position: 'absolute',
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
  },
  decorationLine: {
    position: 'absolute',
    height: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
  },
});