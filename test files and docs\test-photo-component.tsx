// Simple test to verify photo functionality compiles correctly
import React from 'react';
import { View, Text, TouchableOpacity, Image, FlatList } from 'react-native';
import * as ImagePicker from 'expo-image-picker';

interface Photo {
  uri: string;
  name: string;
  type: string;
  isPrimary: boolean;
}

const TestPhotoComponent = () => {
  const [photos, setPhotos] = React.useState<Photo[]>([]);

  const takePhoto = async () => {
    const { status } = await ImagePicker.requestCameraPermissionsAsync();
    if (status !== 'granted') return;

    const result = await ImagePicker.launchCameraAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [16, 9],
      quality: 0.8,
    });

    if (!result.canceled && result.assets[0]) {
      const newPhoto: Photo = {
        uri: result.assets[0].uri,
        name: `photo_${Date.now()}.jpg`,
        type: 'image/jpeg',
        isPrimary: photos.length === 0,
      };
      setPhotos(prev => [...prev, newPhoto]);
    }
  };

  return (
    <View>
      <Text>Photo Test Component</Text>
      <TouchableOpacity onPress={takePhoto}>
        <Text>Take Photo</Text>
      </TouchableOpacity>
      
      <FlatList
        data={photos}
        keyExtractor={(item, index) => index.toString()}
        renderItem={({ item }) => (
          <View>
            <Image source={{ uri: item.uri }} style={{ width: 100, height: 100 }} />
            {item.isPrimary && <Text>Primary</Text>}
          </View>
        )}
      />
    </View>
  );
};

export default TestPhotoComponent;