const { browserPool, setupPageStealth } = require('./src/services/scraperUtils');

async function delay(ms) { return new Promise(r => setTimeout(r, ms)); }

async function run() {
  const propertyUrl = 'https://www.funda.nl/detail/huur/utrecht/appartement-e<PERSON><PERSON><PERSON>an-46-d/43728488/';
  const applicant = {
    firstName: 'Wellis',
    lastName: 'Hant',
    email: '<EMAIL>',
    phone: '030 686 62 00',
    message: '<PERSON><PERSON> <PERSON>r<PERSON><PERSON><PERSON>,\n\nIk ben zeer geïnteresseerd in deze woning en zou graag meer informatie ontvangen over de bezichtigingsmogelijkheden en beschikbaarheid.\n\nAlvast hartelijk dank.\n\nMet vriendelijke groet,\nWellis Hant'
  };

  let browser, page;
  try {
    browser = await browserPool.getBrowser();
    page = await browser.newPage();
    await setupPageStealth(page);

    await page.setExtraHTTPHeaders({
      'Accept-Language': 'en-US,en;q=0.9,nl;q=0.8',
      'Accept-Encoding': 'gzip, deflate, br',
      'Upgrade-Insecure-Requests': '1'
    });

    console.log('Navigating to listing...');
    await page.goto(propertyUrl, { waitUntil: 'networkidle2', timeout: 45000 });
    await delay(1500);

    // Accept cookies if present
    try {
      await page.waitForSelector('[data-testid="accept-all-cookies-button"], .cookie-accept-all, #accept-cookies', { timeout: 5000 });
      await page.click('[data-testid="accept-all-cookies-button"], .cookie-accept-all, #accept-cookies');
      console.log('Accepted cookies');
      await delay(1000);
    } catch {}

    // Find contact button
    const contactSelectors = [
      '[data-optimizely="contact-email"]',
      'a[href*="makelaar-contact"]',
      '[data-testid="contact-button"]',
      '[data-testid="contact-form-button"]',
      '[data-testid="broker-contact-button"]',
      '.makelaar-contact-button', '.broker-contact', '.fd-button--contact',
      '.contact-button', 'button[data-testid*="contact"]'
    ];

    let contactButton = null;
    for (const sel of contactSelectors) {
      try {
        await page.waitForSelector(sel, { timeout: 2000 });
        contactButton = await page.$(sel);
        if (contactButton) { console.log('Found contact button:', sel); break; }
      } catch {}
    }

    if (!contactButton) {
      // Fallback by text/href
      const handle = await page.evaluateHandle(() => {
        const links = Array.from(document.querySelectorAll('a'));
        return links.find(link =>
          (link.textContent && (link.textContent.includes('Neem contact op') || link.textContent.includes('Contact')))
          || ((link.getAttribute('href') || '').includes('makelaar-contact'))
        );
      });
      if (handle && handle.asElement()) contactButton = handle.asElement();
    }

    if (!contactButton) throw new Error('Contact button not found');

    await contactButton.click();
    await delay(2500);

    // Fill fields
    async function fill(selector, value) {
      try {
        await page.waitForSelector(selector, { timeout: 3000 });
        const el = await page.$(selector);
        if (!el) return false;
        await page.evaluate(node => { try { node.value = ''; } catch(e) {} }, el);
        await el.type(value, { delay: 60 });
        return true;
      } catch { return false; }
    }

    // Message
    await (async () => {
      const sels = ['#questionInput','textarea[aria-labelledby="questionInput-label"]','textarea[data-testid*="message"]','textarea'];
      for (const s of sels) { if (await fill(s, applicant.message)) { console.log('Filled message'); break; } }
    })();

    // Email
    await (async () => {
      const sels = ['#emailAddress','input[type="email"]','input[name="email"]','input[data-testid*="email"]'];
      for (const s of sels) { if (await fill(s, applicant.email)) { console.log('Filled email'); break; } }
    })();

    // First/Last name
    await fill('#firstName', applicant.firstName);
    await fill('#lastName', applicant.lastName);

    // Phone
    await (async () => {
      const sels = ['#phoneNumber','input[type="tel"]','input[name="phone"]','input[name="telephone"]'];
      for (const s of sels) { if (await fill(s, applicant.phone)) { console.log('Filled phone'); break; } }
    })();

    // Viewing request checkbox
    try {
      const cb = await page.$('#checkbox-viewingRequest');
      if (cb) {
        const isChecked = await page.evaluate(el => el.checked, cb);
        if (!isChecked) { await cb.click(); console.log('Checked viewing request'); }
      }
    } catch {}

    await delay(800);

    // Submit
    const submitSelectors = [
      'button[type="submit"]',
      'button:has-text("Send message")',
      'button:has-text("Verstuur")',
      'button:has-text("Verzenden")'
    ];

    let submitted = false;
    for (const sel of submitSelectors) {
      try {
        const btn = await page.$(sel);
        if (btn) { await btn.click(); submitted = true; console.log('Clicked submit'); break; }
      } catch {}
    }

    if (!submitted) {
      // Fallback: click any primary button inside form
      try {
        const btn = await page.$('form button');
        if (btn) { await btn.click(); submitted = true; console.log('Clicked generic form button'); }
      } catch {}
    }

    await delay(3000);
    console.log('Done. Submitted:', submitted);
  } catch (e) {
    console.error('Error in direct runner:', e);
  } finally {
    try { if (page) await page.close(); } catch {}
    try { if (browser) await browser.close(); } catch {}
  }
}

if (require.main === module) run();

