import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  ActivityIndicator,
  Alert,
  Modal,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import notificationService, { NotificationHistoryItem } from '../services/notificationService';

interface FilterOption {
  key: string;
  label: string;
  value: string | null;
}

const NotificationHistoryScreen: React.FC = () => {
  const router = useRouter();
  const [notifications, setNotifications] = useState<NotificationHistoryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [selectedFilter, setSelectedFilter] = useState<string | null>(null);
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [selectedNotification, setSelectedNotification] = useState<NotificationHistoryItem | null>(null);
  const [showDetailModal, setShowDetailModal] = useState(false);

  const filterOptions: FilterOption[] = [
    { key: 'all', label: 'All Notifications', value: null },
    { key: 'application_status_update', label: 'Application Updates', value: 'application_status_update' },
    { key: 'daily_summary', label: 'Daily Summaries', value: 'daily_summary' },
    { key: 'weekly_summary', label: 'Weekly Reports', value: 'weekly_summary' },
    { key: 'urgent_alert', label: 'Urgent Alerts', value: 'urgent_alert' },
    { key: 'system_maintenance', label: 'System Maintenance', value: 'system_maintenance' },
  ];

  useEffect(() => {
    loadNotifications(true);
  }, [selectedFilter]);

  const loadNotifications = async (reset: boolean = false) => {
    try {
      if (reset) {
        setLoading(true);
        setNotifications([]);
      } else {
        setLoadingMore(true);
      }

      const offset = reset ? 0 : notifications.length;
      const response = await notificationService.getNotificationHistory({
        limit: 20,
        offset,
        type: selectedFilter || undefined,
      });

      if (reset) {
        setNotifications(response.notifications);
      } else {
        setNotifications(prev => [...prev, ...response.notifications]);
      }

      setHasMore(response.notifications.length === 20);
    } catch (error) {
      console.error('Error loading notifications:', error);
      Alert.alert(
        'Error',
        'Failed to load notifications. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setLoading(false);
      setLoadingMore(false);
      setRefreshing(false);
    }
  };

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    loadNotifications(true);
  }, [selectedFilter]);

  const loadMore = () => {
    if (!loadingMore && hasMore) {
      loadNotifications(false);
    }
  };

  const handleNotificationPress = async (notification: NotificationHistoryItem) => {
    setSelectedNotification(notification);
    setShowDetailModal(true);

    // Mark as read if not already read
    if (!notification.read) {
      try {
        await notificationService.markNotificationAsRead(notification.id);
        setNotifications(prev =>
          prev.map(n =>
            n.id === notification.id ? { ...n, read: true } : n
          )
        );
      } catch (error) {
        console.error('Error marking notification as read:', error);
      }
    }
  };

  const handleDeleteNotification = async (notificationId: string) => {
    Alert.alert(
      'Delete Notification',
      'Are you sure you want to delete this notification?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await notificationService.deleteNotification(notificationId);
              setNotifications(prev => prev.filter(n => n.id !== notificationId));
              setShowDetailModal(false);
            } catch (error) {
              console.error('Error deleting notification:', error);
              Alert.alert('Error', 'Failed to delete notification.');
            }
          },
        },
      ]
    );
  };

  const renderNotificationItem = ({ item }: { item: NotificationHistoryItem }) => {
    const displayData = notificationService.formatNotificationForDisplay(item);

    return (
      <TouchableOpacity
        style={[styles.notificationItem, !item.read && styles.unreadItem]}
        onPress={() => handleNotificationPress(item)}
        activeOpacity={0.7}
      >
        <View style={styles.notificationIcon}>
          <Text style={styles.iconText}>{displayData.icon}</Text>
          {!item.read && <View style={styles.unreadDot} />}
        </View>
        
        <View style={styles.notificationContent}>
          <View style={styles.notificationHeader}>
            <Text style={[styles.notificationTitle, !item.read && styles.unreadTitle]}>
              {displayData.title}
            </Text>
            <Text style={styles.timeAgo}>{displayData.timeAgo}</Text>
          </View>
          
          <Text style={styles.notificationMessage} numberOfLines={2}>
            {displayData.subtitle}
          </Text>
          
          <View style={styles.notificationFooter}>
            <View style={styles.channelTags}>
              {item.channels.map((channel, index) => (
                <View key={index} style={styles.channelTag}>
                  <Text style={styles.channelTagText}>{channel}</Text>
                </View>
              ))}
            </View>
          </View>
        </View>
        
        <Ionicons name="chevron-forward" size={16} color="#6c757d" />
      </TouchableOpacity>
    );
  };

  const renderFilterModal = () => (
    <Modal
      visible={showFilterModal}
      transparent
      animationType="slide"
      onRequestClose={() => setShowFilterModal(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.filterModal}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Filter Notifications</Text>
            <TouchableOpacity onPress={() => setShowFilterModal(false)}>
              <Ionicons name="close" size={24} color="#6c757d" />
            </TouchableOpacity>
          </View>
          
          <FlatList
            data={filterOptions}
            keyExtractor={(item) => item.key}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={[
                  styles.filterOption,
                  selectedFilter === item.value && styles.selectedFilterOption
                ]}
                onPress={() => {
                  setSelectedFilter(item.value);
                  setShowFilterModal(false);
                }}
              >
                <Text style={[
                  styles.filterOptionText,
                  selectedFilter === item.value && styles.selectedFilterOptionText
                ]}>
                  {item.label}
                </Text>
                {selectedFilter === item.value && (
                  <Ionicons name="checkmark" size={20} color="#007bff" />
                )}
              </TouchableOpacity>
            )}
          />
        </View>
      </View>
    </Modal>
  );

  const renderDetailModal = () => {
    if (!selectedNotification) return null;

    const displayData = notificationService.formatNotificationForDisplay(selectedNotification);

    return (
      <Modal
        visible={showDetailModal}
        transparent
        animationType="slide"
        onRequestClose={() => setShowDetailModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.detailModal}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Notification Details</Text>
              <TouchableOpacity onPress={() => setShowDetailModal(false)}>
                <Ionicons name="close" size={24} color="#6c757d" />
              </TouchableOpacity>
            </View>
            
            <View style={styles.detailContent}>
              <View style={styles.detailHeader}>
                <Text style={styles.detailIcon}>{displayData.icon}</Text>
                <View style={styles.detailHeaderText}>
                  <Text style={styles.detailTitle}>{displayData.title}</Text>
                  <Text style={styles.detailTime}>
                    {selectedNotification.timestamp.toLocaleString()}
                  </Text>
                </View>
              </View>
              
              <Text style={styles.detailMessage}>{selectedNotification.message}</Text>
              
              <View style={styles.detailMetadata}>
                <View style={styles.metadataRow}>
                  <Text style={styles.metadataLabel}>Type:</Text>
                  <Text style={styles.metadataValue}>
                    {selectedNotification.type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </Text>
                </View>
                
                <View style={styles.metadataRow}>
                  <Text style={styles.metadataLabel}>Channels:</Text>
                  <Text style={styles.metadataValue}>
                    {selectedNotification.channels.join(', ')}
                  </Text>
                </View>
                
                <View style={styles.metadataRow}>
                  <Text style={styles.metadataLabel}>Status:</Text>
                  <Text style={[
                    styles.metadataValue,
                    selectedNotification.read ? styles.readStatus : styles.unreadStatus
                  ]}>
                    {selectedNotification.read ? 'Read' : 'Unread'}
                  </Text>
                </View>
              </View>
              
              {selectedNotification.data && (
                <View style={styles.additionalData}>
                  <Text style={styles.additionalDataTitle}>Additional Information:</Text>
                  <Text style={styles.additionalDataText}>
                    {JSON.stringify(selectedNotification.data, null, 2)}
                  </Text>
                </View>
              )}
            </View>
            
            <View style={styles.detailActions}>
              <TouchableOpacity
                style={styles.deleteButton}
                onPress={() => handleDeleteNotification(selectedNotification.id)}
              >
                <Ionicons name="trash" size={20} color="#dc3545" />
                <Text style={styles.deleteButtonText}>Delete</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="notifications-off" size={64} color="#6c757d" />
      <Text style={styles.emptyStateTitle}>No Notifications</Text>
      <Text style={styles.emptyStateText}>
        {selectedFilter 
          ? 'No notifications found for the selected filter.'
          : 'You don\'t have any notifications yet. They will appear here when you receive them.'
        }
      </Text>
    </View>
  );

  const renderFooter = () => {
    if (!loadingMore) return null;
    
    return (
      <View style={styles.loadingFooter}>
        <ActivityIndicator size="small" color="#007bff" />
        <Text style={styles.loadingFooterText}>Loading more...</Text>
      </View>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
            <Ionicons name="arrow-back" size={24} color="#007bff" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Notifications</Text>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007bff" />
          <Text style={styles.loadingText}>Loading notifications...</Text>
        </View>
      </SafeAreaView>
    );
  }

  const currentFilter = filterOptions.find(f => f.value === selectedFilter);

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#007bff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Notifications</Text>
        <TouchableOpacity 
          onPress={() => setShowFilterModal(true)} 
          style={styles.filterButton}
        >
          <Ionicons name="filter" size={20} color="#007bff" />
        </TouchableOpacity>
      </View>

      {selectedFilter && (
        <View style={styles.filterIndicator}>
          <Text style={styles.filterIndicatorText}>
            Filtered by: {currentFilter?.label}
          </Text>
          <TouchableOpacity onPress={() => setSelectedFilter(null)}>
            <Ionicons name="close-circle" size={20} color="#6c757d" />
          </TouchableOpacity>
        </View>
      )}

      <FlatList
        data={notifications}
        keyExtractor={(item) => item.id}
        renderItem={renderNotificationItem}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        onEndReached={loadMore}
        onEndReachedThreshold={0.1}
        ListFooterComponent={renderFooter}
        ListEmptyComponent={renderEmptyState}
        contentContainerStyle={notifications.length === 0 ? styles.emptyContainer : undefined}
        showsVerticalScrollIndicator={false}
      />

      {renderFilterModal()}
      {renderDetailModal()}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  backButton: {
    padding: 8,
    marginLeft: -8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#212529',
    flex: 1,
    textAlign: 'center',
  },
  filterButton: {
    padding: 8,
    marginRight: -8,
  },
  filterIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 12,
    backgroundColor: '#e3f2fd',
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  filterIndicatorText: {
    fontSize: 14,
    color: '#007bff',
    fontWeight: '500',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6c757d',
  },
  notificationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#f1f3f4',
  },
  unreadItem: {
    backgroundColor: '#f8f9ff',
  },
  notificationIcon: {
    position: 'relative',
    marginRight: 16,
  },
  iconText: {
    fontSize: 24,
  },
  unreadDot: {
    position: 'absolute',
    top: -2,
    right: -2,
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#007bff',
  },
  notificationContent: {
    flex: 1,
  },
  notificationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 4,
  },
  notificationTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#212529',
    flex: 1,
    marginRight: 8,
  },
  unreadTitle: {
    fontWeight: '600',
  },
  timeAgo: {
    fontSize: 12,
    color: '#6c757d',
  },
  notificationMessage: {
    fontSize: 14,
    color: '#6c757d',
    lineHeight: 20,
    marginBottom: 8,
  },
  notificationFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  channelTags: {
    flexDirection: 'row',
    gap: 6,
  },
  channelTag: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    backgroundColor: '#e9ecef',
    borderRadius: 12,
  },
  channelTagText: {
    fontSize: 10,
    color: '#6c757d',
    fontWeight: '500',
    textTransform: 'uppercase',
  },
  emptyContainer: {
    flex: 1,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyStateTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#212529',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 16,
    color: '#6c757d',
    textAlign: 'center',
    lineHeight: 24,
  },
  loadingFooter: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 20,
  },
  loadingFooterText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#6c757d',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  filterModal: {
    backgroundColor: '#ffffff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '70%',
  },
  detailModal: {
    backgroundColor: '#ffffff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '90%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#212529',
  },
  filterOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f1f3f4',
  },
  selectedFilterOption: {
    backgroundColor: '#e3f2fd',
  },
  filterOptionText: {
    fontSize: 16,
    color: '#212529',
  },
  selectedFilterOptionText: {
    color: '#007bff',
    fontWeight: '500',
  },
  detailContent: {
    padding: 20,
  },
  detailHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  detailIcon: {
    fontSize: 32,
    marginRight: 16,
  },
  detailHeaderText: {
    flex: 1,
  },
  detailTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#212529',
    marginBottom: 4,
  },
  detailTime: {
    fontSize: 14,
    color: '#6c757d',
  },
  detailMessage: {
    fontSize: 16,
    color: '#212529',
    lineHeight: 24,
    marginBottom: 20,
  },
  detailMetadata: {
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  metadataRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  metadataLabel: {
    fontSize: 14,
    color: '#6c757d',
    fontWeight: '500',
  },
  metadataValue: {
    fontSize: 14,
    color: '#212529',
  },
  readStatus: {
    color: '#28a745',
  },
  unreadStatus: {
    color: '#007bff',
  },
  additionalData: {
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    padding: 16,
  },
  additionalDataTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#212529',
    marginBottom: 8,
  },
  additionalDataText: {
    fontSize: 12,
    color: '#6c757d',
    fontFamily: 'monospace',
  },
  detailActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: '#e9ecef',
  },
  deleteButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#fff5f5',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#fecaca',
  },
  deleteButtonText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#dc3545',
    fontWeight: '500',
  },
});

export default NotificationHistoryScreen;