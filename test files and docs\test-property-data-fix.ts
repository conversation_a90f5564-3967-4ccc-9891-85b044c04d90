// Test to verify the PropertyData interface fix
import { PropertyData } from '../zakmakelaar-frontend/services/propertyOwnerService';

// This should now compile without TypeScript errors
const testPropertyData: PropertyData = {
  title: 'Test Property',
  description: 'A test property',
  address: {
    street: 'Test Street',
    houseNumber: '123',
    postalCode: '1234AB',
    city: 'Amsterdam',
    province: 'Noord-Holland',
  },
  propertyType: 'apartment',
  size: 75,
  rooms: 3,
  bedrooms: 2,
  bathrooms: 1,
  rent: {
    amount: 2500,
    currency: 'EUR',
    deposit: 5000,
    additionalCosts: {
      utilities: 150,
      serviceCharges: 50,
      parking: 0,
      other: 0,
    },
  },
  features: {
    furnished: false,
    interior: 'kaal',
    parking: false,
    balcony: true,
    garden: false,
    elevator: true,
    energyLabel: 'C',
  },
  policies: {
    petsAllowed: false,
    smokingAllowed: false,
    studentsAllowed: true,
    expatFriendly: true,
    minimumIncome: 7500,
    maximumOccupants: 2,
  },
  status: 'draft',
  // This should now work correctly - array of image objects instead of strings
  images: [
    {
      url: 'photo1.jpg',
      caption: 'Living room',
      isPrimary: true,
    },
    {
      url: 'photo2.jpg', 
      caption: 'Kitchen',
      isPrimary: false,
    },
  ],
  availabilityDate: '2025-08-01',
};

console.log('✅ PropertyData interface fix verified - no TypeScript errors!');
console.log('Images structure:', testPropertyData.images);

export default testPropertyData;