const cron = require('node-cron');
const AutoApplicationSettings = require('../models/AutoApplicationSettings');
const ApplicationResult = require('../models/ApplicationResult');
const ApplicationQueue = require('../models/ApplicationQueue');
const User = require('../models/User');
const autoApplicationNotificationService = require('./autoApplicationNotificationService');
const applicationMonitor = require('./applicationMonitor');
const { loggers } = require('./logger');

/**
 * NotificationScheduler - Handles scheduled notifications for auto-application system
 * 
 * This service provides:
 * - Daily summary report scheduling
 * - Weekly summary report scheduling
 * - Automated notification delivery based on user preferences
 * - Performance analytics aggregation for reports
 */
class NotificationScheduler {
  constructor() {
    this.scheduledJobs = new Map();
    this.isInitialized = false;
  }

  /**
   * Initialize the scheduler with cron jobs
   */
  initialize() {
    if (this.isInitialized) {
      return;
    }

    try {
      // Daily summary at 8:00 PM every day
      const dailySummaryJob = cron.schedule('0 20 * * *', async () => {
        await this.sendDailySummaries();
      }, {
        scheduled: false,
        timezone: 'Europe/Amsterdam'
      });

      // Weekly summary on Sunday at 9:00 AM
      const weeklySummaryJob = cron.schedule('0 9 * * 0', async () => {
        await this.sendWeeklySummaries();
      }, {
        scheduled: false,
        timezone: 'Europe/Amsterdam'
      });

      // Cleanup old notifications daily at 2:00 AM
      const cleanupJob = cron.schedule('0 2 * * *', async () => {
        await this.cleanupOldData();
      }, {
        scheduled: false,
        timezone: 'Europe/Amsterdam'
      });

      this.scheduledJobs.set('dailySummary', dailySummaryJob);
      this.scheduledJobs.set('weeklySummary', weeklySummaryJob);
      this.scheduledJobs.set('cleanup', cleanupJob);

      this.isInitialized = true;
      loggers.app.info('NotificationScheduler initialized with cron jobs');

    } catch (error) {
      loggers.app.error('Error initializing NotificationScheduler:', error);
      throw error;
    }
  }

  /**
   * Start all scheduled jobs
   */
  start() {
    if (!this.isInitialized) {
      this.initialize();
    }

    this.scheduledJobs.forEach((job, name) => {
      job.start();
      loggers.app.info(`Started scheduled job: ${name}`);
    });

    loggers.app.info('NotificationScheduler started');
  }

  /**
   * Stop all scheduled jobs
   */
  stop() {
    this.scheduledJobs.forEach((job, name) => {
      job.stop();
      loggers.app.info(`Stopped scheduled job: ${name}`);
    });

    loggers.app.info('NotificationScheduler stopped');
  }

  /**
   * Send daily summaries to all eligible users
   */
  async sendDailySummaries() {
    try {
      loggers.app.info('Starting daily summary generation');

      // Get all users with daily notifications enabled
      const eligibleUsers = await this.getEligibleUsersForDailySummary();
      
      loggers.app.info(`Generating daily summaries for ${eligibleUsers.length} users`);

      for (const user of eligibleUsers) {
        try {
          const summaryData = await this.generateDailySummaryData(user._id);
          
          // Only send if there's meaningful activity
          if (summaryData.applicationsSubmitted > 0 || summaryData.applicationsPending > 0) {
            await autoApplicationNotificationService.sendDailySummary(user._id, summaryData);
            loggers.app.debug(`Daily summary sent to user ${user._id}`);
          }

        } catch (error) {
          loggers.app.error(`Error sending daily summary to user ${user._id}:`, error);
          // Continue with other users
        }
      }

      loggers.app.info(`Daily summary generation completed for ${eligibleUsers.length} users`);

    } catch (error) {
      loggers.app.error('Error in daily summary generation:', error);
    }
  }

  /**
   * Send weekly summaries to all eligible users
   */
  async sendWeeklySummaries() {
    try {
      loggers.app.info('Starting weekly summary generation');

      // Get all users with weekly notifications enabled
      const eligibleUsers = await this.getEligibleUsersForWeeklySummary();
      
      loggers.app.info(`Generating weekly summaries for ${eligibleUsers.length} users`);

      for (const user of eligibleUsers) {
        try {
          const summaryData = await this.generateWeeklySummaryData(user._id);
          
          // Only send if there's meaningful activity
          if (summaryData.totalApplications > 0) {
            await autoApplicationNotificationService.sendWeeklySummary(user._id, summaryData);
            loggers.app.debug(`Weekly summary sent to user ${user._id}`);
          }

        } catch (error) {
          loggers.app.error(`Error sending weekly summary to user ${user._id}:`, error);
          // Continue with other users
        }
      }

      loggers.app.info(`Weekly summary generation completed for ${eligibleUsers.length} users`);

    } catch (error) {
      loggers.app.error('Error in weekly summary generation:', error);
    }
  }

  /**
   * Get users eligible for daily summary notifications
   * @returns {Array} Array of user objects
   */
  async getEligibleUsersForDailySummary() {
    try {
      const settings = await AutoApplicationSettings.find({
        enabled: true,
        'settings.notificationPreferences.daily': true
      }).populate('userId', 'email profile');

      return settings
        .filter(setting => setting.userId && setting.userId.email)
        .map(setting => setting.userId);

    } catch (error) {
      loggers.app.error('Error getting eligible users for daily summary:', error);
      return [];
    }
  }

  /**
   * Get users eligible for weekly summary notifications
   * @returns {Array} Array of user objects
   */
  async getEligibleUsersForWeeklySummary() {
    try {
      const settings = await AutoApplicationSettings.find({
        enabled: true,
        'settings.notificationPreferences.weekly': true
      }).populate('userId', 'email profile');

      return settings
        .filter(setting => setting.userId && setting.userId.email)
        .map(setting => setting.userId);

    } catch (error) {
      loggers.app.error('Error getting eligible users for weekly summary:', error);
      return [];
    }
  }

  /**
   * Generate daily summary data for a user
   * @param {string} userId - User ID
   * @returns {Object} Daily summary data
   */
  async generateDailySummaryData(userId) {
    try {
      const today = new Date();
      const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
      const endOfDay = new Date(startOfDay.getTime() + 24 * 60 * 60 * 1000);

      // Get today's applications
      const [queueItems, results] = await Promise.all([
        ApplicationQueue.find({
          userId,
          createdAt: { $gte: startOfDay, $lt: endOfDay }
        }).populate('listingId', 'title location price'),
        
        ApplicationResult.find({
          userId,
          submittedAt: { $gte: startOfDay, $lt: endOfDay }
        })
      ]);

      // Calculate metrics
      const applicationsSubmitted = results.length;
      const applicationsSuccessful = results.filter(r => r.status === 'submitted' && r.response?.success).length;
      const applicationsRejected = results.filter(r => r.landlordResponse?.finalDecision === 'rejected').length;
      const applicationsPending = queueItems.filter(q => q.status === 'pending').length;
      
      const successRate = applicationsSubmitted > 0 ? 
        Math.round((applicationsSuccessful / applicationsSubmitted) * 100) : 0;

      // Get user settings for remaining applications
      const settings = await AutoApplicationSettings.findByUserId(userId);
      const dailyLimit = settings?.settings?.maxApplicationsPerDay || 5;
      const remainingApplications = Math.max(0, dailyLimit - applicationsSubmitted);

      // Analyze performance
      const topPerformingLocation = await this.getTopPerformingLocation(userId, startOfDay, endOfDay);
      const averageResponseTime = await this.getAverageResponseTime(userId, startOfDay, endOfDay);

      // Generate recommendations
      const recommendations = await this.generateDailyRecommendations(userId, {
        successRate,
        applicationsSubmitted,
        remainingApplications
      });

      // Get upcoming viewings (mock data for now)
      const upcomingViewings = await this.getUpcomingViewings(userId);

      return {
        applicationsSubmitted,
        applicationsSuccessful,
        applicationsRejected,
        applicationsPending,
        successRate,
        topPerformingLocation,
        averageResponseTime,
        remainingApplications,
        recommendations,
        upcomingViewings
      };

    } catch (error) {
      loggers.app.error(`Error generating daily summary data for user ${userId}:`, error);
      return {
        applicationsSubmitted: 0,
        applicationsSuccessful: 0,
        applicationsRejected: 0,
        applicationsPending: 0,
        successRate: 0,
        recommendations: [],
        upcomingViewings: []
      };
    }
  }

  /**
   * Generate weekly summary data for a user
   * @param {string} userId - User ID
   * @returns {Object} Weekly summary data
   */
  async generateWeeklySummaryData(userId) {
    try {
      const today = new Date();
      const weekStart = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
      const weekEnd = today;

      // Get week's applications
      const results = await ApplicationResult.find({
        userId,
        submittedAt: { $gte: weekStart, $lt: weekEnd }
      }).populate('listingId', 'title location price');

      // Calculate metrics
      const totalApplications = results.length;
      const successfulApplications = results.filter(r => r.status === 'submitted' && r.response?.success).length;
      const landlordResponses = results.filter(r => r.landlordResponse?.responseReceived).length;
      const viewingInvites = results.filter(r => r.landlordResponse?.viewingInvite?.invited).length;
      const acceptances = results.filter(r => r.landlordResponse?.finalDecision === 'accepted').length;

      const successRate = totalApplications > 0 ? 
        Math.round((successfulApplications / totalApplications) * 100) : 0;
      const responseRate = successfulApplications > 0 ? 
        Math.round((landlordResponses / successfulApplications) * 100) : 0;
      const acceptanceRate = landlordResponses > 0 ? 
        Math.round((acceptances / landlordResponses) * 100) : 0;

      // Analyze patterns
      const topPerformingLocations = await this.getTopPerformingLocations(userId, weekStart, weekEnd);
      const bestApplicationTimes = await this.getBestApplicationTimes(userId, weekStart, weekEnd);
      const marketInsights = await this.generateMarketInsights(userId, weekStart, weekEnd);

      // Performance comparison with previous week
      const performanceComparison = await this.getPerformanceComparison(userId, weekStart);

      // Generate action items
      const actionItems = await this.generateWeeklyActionItems(userId, {
        successRate,
        responseRate,
        acceptanceRate,
        totalApplications
      });

      return {
        weekStart: weekStart.toISOString().split('T')[0],
        weekEnd: weekEnd.toISOString().split('T')[0],
        totalApplications,
        successfulApplications,
        landlordResponses,
        viewingInvites,
        acceptances,
        successRate,
        responseRate,
        acceptanceRate,
        topPerformingLocations,
        bestApplicationTimes,
        marketInsights,
        performanceComparison,
        actionItems
      };

    } catch (error) {
      loggers.app.error(`Error generating weekly summary data for user ${userId}:`, error);
      return {
        weekStart: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        weekEnd: new Date().toISOString().split('T')[0],
        totalApplications: 0,
        successfulApplications: 0,
        landlordResponses: 0,
        viewingInvites: 0,
        acceptances: 0,
        successRate: 0,
        responseRate: 0,
        acceptanceRate: 0,
        topPerformingLocations: [],
        bestApplicationTimes: [],
        marketInsights: [],
        performanceComparison: {},
        actionItems: []
      };
    }
  }

  /**
   * Get top performing location for a time period
   * @param {string} userId - User ID
   * @param {Date} startDate - Start date
   * @param {Date} endDate - End date
   * @returns {string} Top performing location
   */
  async getTopPerformingLocation(userId, startDate, endDate) {
    try {
      const results = await ApplicationResult.aggregate([
        {
          $match: {
            userId: userId,
            submittedAt: { $gte: startDate, $lt: endDate },
            status: 'submitted',
            'response.success': true
          }
        },
        {
          $group: {
            _id: '$listingSnapshot.location',
            count: { $sum: 1 },
            successRate: { $avg: { $cond: ['$response.success', 100, 0] } }
          }
        },
        { $sort: { successRate: -1, count: -1 } },
        { $limit: 1 }
      ]);

      return results.length > 0 ? results[0]._id : null;

    } catch (error) {
      loggers.app.error('Error getting top performing location:', error);
      return null;
    }
  }

  /**
   * Get top performing locations for weekly summary
   * @param {string} userId - User ID
   * @param {Date} startDate - Start date
   * @param {Date} endDate - End date
   * @returns {Array} Top performing locations
   */
  async getTopPerformingLocations(userId, startDate, endDate) {
    try {
      const results = await ApplicationResult.aggregate([
        {
          $match: {
            userId: userId,
            submittedAt: { $gte: startDate, $lt: endDate },
            status: 'submitted'
          }
        },
        {
          $group: {
            _id: '$listingSnapshot.location',
            applications: { $sum: 1 },
            successful: { $sum: { $cond: ['$response.success', 1, 0] } }
          }
        },
        {
          $project: {
            location: '$_id',
            applications: 1,
            successRate: {
              $round: [
                { $multiply: [{ $divide: ['$successful', '$applications'] }, 100] },
                1
              ]
            }
          }
        },
        { $sort: { successRate: -1, applications: -1 } },
        { $limit: 5 }
      ]);

      return results.map(r => ({
        location: r.location,
        applications: r.applications,
        successRate: r.successRate
      }));

    } catch (error) {
      loggers.app.error('Error getting top performing locations:', error);
      return [];
    }
  }

  /**
   * Get average response time for applications
   * @param {string} userId - User ID
   * @param {Date} startDate - Start date
   * @param {Date} endDate - End date
   * @returns {string} Average response time
   */
  async getAverageResponseTime(userId, startDate, endDate) {
    try {
      const results = await ApplicationResult.aggregate([
        {
          $match: {
            userId: userId,
            submittedAt: { $gte: startDate, $lt: endDate },
            'landlordResponse.responseReceived': true,
            'landlordResponse.responseDate': { $exists: true }
          }
        },
        {
          $project: {
            responseTime: {
              $subtract: ['$landlordResponse.responseDate', '$submittedAt']
            }
          }
        },
        {
          $group: {
            _id: null,
            avgResponseTime: { $avg: '$responseTime' }
          }
        }
      ]);

      if (results.length > 0) {
        const avgMs = results[0].avgResponseTime;
        const avgHours = Math.round(avgMs / (1000 * 60 * 60));
        
        if (avgHours < 24) {
          return `${avgHours} hours`;
        } else {
          const avgDays = Math.round(avgHours / 24);
          return `${avgDays} days`;
        }
      }

      return null;

    } catch (error) {
      loggers.app.error('Error getting average response time:', error);
      return null;
    }
  }

  /**
   * Get best application times for weekly summary
   * @param {string} userId - User ID
   * @param {Date} startDate - Start date
   * @param {Date} endDate - End date
   * @returns {Array} Best application times
   */
  async getBestApplicationTimes(userId, startDate, endDate) {
    try {
      const results = await ApplicationResult.aggregate([
        {
          $match: {
            userId: userId,
            submittedAt: { $gte: startDate, $lt: endDate },
            status: 'submitted',
            'response.success': true
          }
        },
        {
          $group: {
            _id: {
              hour: { $hour: '$submittedAt' },
              dayOfWeek: { $dayOfWeek: '$submittedAt' }
            },
            count: { $sum: 1 },
            successRate: { $avg: { $cond: ['$response.success', 100, 0] } }
          }
        },
        { $sort: { successRate: -1, count: -1 } },
        { $limit: 3 }
      ]);

      const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
      
      return results.map(r => ({
        time: `${dayNames[r._id.dayOfWeek - 1]} at ${r._id.hour}:00`,
        applications: r.count,
        successRate: Math.round(r.successRate)
      }));

    } catch (error) {
      loggers.app.error('Error getting best application times:', error);
      return [];
    }
  }

  /**
   * Generate market insights for weekly summary
   * @param {string} userId - User ID
   * @param {Date} startDate - Start date
   * @param {Date} endDate - End date
   * @returns {Array} Market insights
   */
  async generateMarketInsights(userId, startDate, endDate) {
    try {
      const insights = [];

      // Analyze price trends
      const priceAnalysis = await ApplicationResult.aggregate([
        {
          $match: {
            userId: userId,
            submittedAt: { $gte: startDate, $lt: endDate },
            'listingSnapshot.price': { $exists: true, $ne: null }
          }
        },
        {
          $group: {
            _id: null,
            avgPrice: { $avg: '$listingSnapshot.price' },
            minPrice: { $min: '$listingSnapshot.price' },
            maxPrice: { $max: '$listingSnapshot.price' },
            successfulAvgPrice: {
              $avg: {
                $cond: ['$response.success', '$listingSnapshot.price', null]
              }
            }
          }
        }
      ]);

      if (priceAnalysis.length > 0) {
        const data = priceAnalysis[0];
        if (data.successfulAvgPrice && data.avgPrice) {
          const priceDiff = data.successfulAvgPrice - data.avgPrice;
          if (Math.abs(priceDiff) > 50) {
            insights.push(
              priceDiff > 0 
                ? `Your successful applications tend to be for higher-priced properties (€${Math.round(priceDiff)} above average)`
                : `Your successful applications tend to be for lower-priced properties (€${Math.round(Math.abs(priceDiff))} below average)`
            );
          }
        }
      }

      // Analyze competition levels
      const competitionInsight = await this.analyzeCompetitionLevels(userId, startDate, endDate);
      if (competitionInsight) {
        insights.push(competitionInsight);
      }

      // Add general market insights
      insights.push('The rental market remains competitive - quick applications continue to be crucial');

      return insights;

    } catch (error) {
      loggers.app.error('Error generating market insights:', error);
      return ['Market analysis temporarily unavailable'];
    }
  }

  /**
   * Analyze competition levels
   * @param {string} userId - User ID
   * @param {Date} startDate - Start date
   * @param {Date} endDate - End date
   * @returns {string} Competition insight
   */
  async analyzeCompetitionLevels(userId, startDate, endDate) {
    try {
      // This would analyze response times and success rates to infer competition
      const quickResponses = await ApplicationResult.countDocuments({
        userId: userId,
        submittedAt: { $gte: startDate, $lt: endDate },
        'landlordResponse.responseReceived': true,
        'landlordResponse.responseTime': { $lt: 24 * 60 * 60 * 1000 } // Less than 24 hours
      });

      const totalResponses = await ApplicationResult.countDocuments({
        userId: userId,
        submittedAt: { $gte: startDate, $lt: endDate },
        'landlordResponse.responseReceived': true
      });

      if (totalResponses > 0) {
        const quickResponseRate = (quickResponses / totalResponses) * 100;
        
        if (quickResponseRate > 70) {
          return 'High landlord response rate suggests strong market demand - continue applying quickly to new listings';
        } else if (quickResponseRate < 30) {
          return 'Lower response rates may indicate market saturation in your target areas - consider expanding your search criteria';
        }
      }

      return null;

    } catch (error) {
      loggers.app.error('Error analyzing competition levels:', error);
      return null;
    }
  }

  /**
   * Get performance comparison with previous period
   * @param {string} userId - User ID
   * @param {Date} currentWeekStart - Current week start
   * @returns {Object} Performance comparison
   */
  async getPerformanceComparison(userId, currentWeekStart) {
    try {
      const previousWeekStart = new Date(currentWeekStart.getTime() - 7 * 24 * 60 * 60 * 1000);
      const previousWeekEnd = currentWeekStart;

      const [currentWeek, previousWeek] = await Promise.all([
        this.getWeeklyMetrics(userId, currentWeekStart, new Date()),
        this.getWeeklyMetrics(userId, previousWeekStart, previousWeekEnd)
      ]);

      return {
        applications: {
          current: currentWeek.totalApplications,
          previous: previousWeek.totalApplications,
          change: currentWeek.totalApplications - previousWeek.totalApplications
        },
        successRate: {
          current: currentWeek.successRate,
          previous: previousWeek.successRate,
          change: currentWeek.successRate - previousWeek.successRate
        },
        responseRate: {
          current: currentWeek.responseRate,
          previous: previousWeek.responseRate,
          change: currentWeek.responseRate - previousWeek.responseRate
        }
      };

    } catch (error) {
      loggers.app.error('Error getting performance comparison:', error);
      return {};
    }
  }

  /**
   * Get weekly metrics for a specific period
   * @param {string} userId - User ID
   * @param {Date} startDate - Start date
   * @param {Date} endDate - End date
   * @returns {Object} Weekly metrics
   */
  async getWeeklyMetrics(userId, startDate, endDate) {
    try {
      const results = await ApplicationResult.find({
        userId,
        submittedAt: { $gte: startDate, $lt: endDate }
      });

      const totalApplications = results.length;
      const successfulApplications = results.filter(r => r.status === 'submitted' && r.response?.success).length;
      const landlordResponses = results.filter(r => r.landlordResponse?.responseReceived).length;

      return {
        totalApplications,
        successfulApplications,
        landlordResponses,
        successRate: totalApplications > 0 ? Math.round((successfulApplications / totalApplications) * 100) : 0,
        responseRate: successfulApplications > 0 ? Math.round((landlordResponses / successfulApplications) * 100) : 0
      };

    } catch (error) {
      loggers.app.error('Error getting weekly metrics:', error);
      return {
        totalApplications: 0,
        successfulApplications: 0,
        landlordResponses: 0,
        successRate: 0,
        responseRate: 0
      };
    }
  }

  /**
   * Generate daily recommendations
   * @param {string} userId - User ID
   * @param {Object} metrics - Daily metrics
   * @returns {Array} Recommendations
   */
  async generateDailyRecommendations(userId, metrics) {
    const recommendations = [];

    if (metrics.successRate < 50 && metrics.applicationsSubmitted > 2) {
      recommendations.push('Consider reviewing your application template - success rate is below average');
    }

    if (metrics.remainingApplications > 0 && metrics.applicationsSubmitted < 3) {
      recommendations.push(`You have ${metrics.remainingApplications} applications remaining today - consider applying to more properties`);
    }

    if (metrics.successRate > 80) {
      recommendations.push('Excellent success rate today! Your application strategy is working well');
    }

    if (recommendations.length === 0) {
      recommendations.push('Keep up the good work with your auto-applications!');
    }

    return recommendations;
  }

  /**
   * Generate weekly action items
   * @param {string} userId - User ID
   * @param {Object} metrics - Weekly metrics
   * @returns {Array} Action items
   */
  async generateWeeklyActionItems(userId, metrics) {
    const actionItems = [];

    if (metrics.successRate < 60) {
      actionItems.push({
        priority: 'high',
        action: 'Review and optimize your application templates',
        reason: `Success rate of ${metrics.successRate}% is below optimal`
      });
    }

    if (metrics.responseRate < 40) {
      actionItems.push({
        priority: 'medium',
        action: 'Consider expanding your search criteria or target locations',
        reason: `Response rate of ${metrics.responseRate}% suggests limited landlord engagement`
      });
    }

    if (metrics.totalApplications < 10) {
      actionItems.push({
        priority: 'medium',
        action: 'Increase your daily application limit or expand search criteria',
        reason: 'Low application volume may limit your chances of finding a property'
      });
    }

    if (metrics.acceptanceRate > 0) {
      actionItems.push({
        priority: 'low',
        action: 'Continue with current strategy',
        reason: 'You have received property acceptances - your approach is working'
      });
    }

    return actionItems;
  }

  /**
   * Get upcoming viewings (placeholder for future implementation)
   * @param {string} userId - User ID
   * @returns {Array} Upcoming viewings
   */
  async getUpcomingViewings(userId) {
    // This would integrate with a calendar/viewing system
    // For now, return empty array
    return [];
  }

  /**
   * Clean up old notification data
   */
  async cleanupOldData() {
    try {
      loggers.app.info('Starting notification data cleanup');

      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

      // Clean up old application results (keep for 30 days)
      const deletedResults = await ApplicationResult.deleteMany({
        submittedAt: { $lt: thirtyDaysAgo },
        'landlordResponse.finalDecision': { $in: ['rejected', 'expired'] }
      });

      // Clean up old queue items (keep completed/failed for 7 days)
      const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      const deletedQueueItems = await ApplicationQueue.deleteMany({
        processedAt: { $lt: sevenDaysAgo },
        status: { $in: ['completed', 'failed', 'cancelled'] }
      });

      loggers.app.info('Notification data cleanup completed', {
        deletedResults: deletedResults.deletedCount,
        deletedQueueItems: deletedQueueItems.deletedCount
      });

    } catch (error) {
      loggers.app.error('Error during notification data cleanup:', error);
    }
  }

  /**
   * Manually trigger daily summary for a specific user (for testing)
   * @param {string} userId - User ID
   */
  async triggerDailySummary(userId) {
    try {
      const summaryData = await this.generateDailySummaryData(userId);
      await autoApplicationNotificationService.sendDailySummary(userId, summaryData);
      loggers.app.info(`Manual daily summary triggered for user ${userId}`);
    } catch (error) {
      loggers.app.error(`Error triggering manual daily summary for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Manually trigger weekly summary for a specific user (for testing)
   * @param {string} userId - User ID
   */
  async triggerWeeklySummary(userId) {
    try {
      const summaryData = await this.generateWeeklySummaryData(userId);
      await autoApplicationNotificationService.sendWeeklySummary(userId, summaryData);
      loggers.app.info(`Manual weekly summary triggered for user ${userId}`);
    } catch (error) {
      loggers.app.error(`Error triggering manual weekly summary for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Get scheduler status
   * @returns {Object} Scheduler status
   */
  getStatus() {
    return {
      initialized: this.isInitialized,
      activeJobs: Array.from(this.scheduledJobs.keys()),
      runningJobs: Array.from(this.scheduledJobs.entries()).map(([name, job]) => ({
        name,
        running: job.running || false
      }))
    };
  }

  /**
   * Shutdown the scheduler gracefully
   */
  shutdown() {
    this.stop();
    this.scheduledJobs.clear();
    this.isInitialized = false;
    loggers.app.info('NotificationScheduler shutdown complete');
  }
}

module.exports = new NotificationScheduler();