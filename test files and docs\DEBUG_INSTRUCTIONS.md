# Debug Instructions - Find the Redirect Source

## What I've Added

I've added comprehensive debug logging to trace exactly what's happening with the redirects. Here's what to do:

### 1. Run the App
Start your app and go through the preferences completion flow.

### 2. Check the Console
Look at the console output. You should see logs like:
```
🔍 [0ms] PREFERENCES: handleSavePreferences called
🔍 [100ms] PREFERENCES: Preferences saved to backend successfully
🔍 [101ms] PREFERENCES: Redirect blocker activated for 30 seconds
🔍 [102ms] PREFERENCES: Calling router.replace(/dashboard)
🔍 [103ms] PREFERENCES: router.replace(/dashboard) completed
🔍 [200ms] DASHBOARD: DashboardScreen component mounted
🔍 [201ms] DASHBOARD: useEffect triggered
🔍 [202ms] DASHBOARD: Checked redirect blocker - areBlocked: true
🔍 [203ms] DASHBOARD: Redirects are blocked, staying on dashboard
```

### 3. Use the Debug Button
On the dashboard, there's now a "DEBUG" button in the top-right corner. Click it to print a summary of all redirect events.

### 4. Look for the Pattern
The logs will show you:
- **When each redirect attempt happens**
- **Which component is causing the redirect**
- **Whether the redirect blocker is working**
- **The exact sequence of events**

### 5. What to Look For

#### If the redirect blocker is working correctly:
```
🔍 PREFERENCES: Redirect blocker activated for 30 seconds
🔍 DASHBOARD: Checked redirect blocker - areBlocked: true
🔍 DASHBOARD: Redirects are blocked, staying on dashboard
```

#### If something is bypassing the blocker:
```
🔍 PREFERENCES: Redirect blocker activated for 30 seconds
🔍 DASHBOARD: Checked redirect blocker - areBlocked: true
🔍 DASHBOARD: Redirects are blocked, staying on dashboard
🔍 [UNKNOWN_SOURCE]: Calling router.replace(/preferences) ← This would show the culprit
```

#### If the blocker isn't working:
```
🔍 PREFERENCES: Redirect blocker activated for 30 seconds
🔍 DASHBOARD: Checked redirect blocker - areBlocked: false ← Problem here
🔍 DASHBOARD: No valid preferences, calling router.replace(/preferences)
```

### 6. Share the Logs
Once you run through the flow and click the DEBUG button, share the console output. This will tell us exactly:
- Which component is causing the redirects
- Whether the redirect blocker is working
- The timing of each redirect attempt
- Any errors or unexpected behavior

### 7. Expected Behavior
If everything is working correctly, you should see:
1. Preferences saved
2. Redirect blocker activated
3. Navigation to dashboard
4. Dashboard checks blocker → blocked → stays on dashboard
5. Layout guard checks blocker → blocked → no redirect
6. No further redirect attempts for 30 seconds

### 8. If You Still See Redirects
The debug logs will show us exactly which component is causing the redirects and whether it's checking the blocker correctly. This will help us identify any remaining sources we haven't covered.

## Files with Debug Logging Added
- `zakmakelaar-frontend/services/debugLogger.ts` - New debug service
- `zakmakelaar-frontend/app/preferences.tsx` - Logs preferences save and navigation
- `zakmakelaar-frontend/app/dashboard.tsx` - Logs redirect checks and decisions
- `zakmakelaar-frontend/app/_layout.tsx` - Logs navigation guard behavior

The debug logs will give us the exact information we need to identify and fix the remaining redirect source.