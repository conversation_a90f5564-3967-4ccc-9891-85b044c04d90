import React, { useState } from 'react';
import { 
  StyleSheet, 
  View, 
  Text, 
  TouchableOpacity, 
  Modal,
  ScrollView,
  Dimensions
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import Animated, { 
  FadeIn,
  FadeOut,
  SlideInDown,
  SlideOutDown
} from 'react-native-reanimated';

const { width, height } = Dimensions.get('window');

interface HelpItem {
  title: string;
  content: string;
  icon?: keyof typeof Ionicons.glyphMap;
}

interface ContextualHelpProps {
  items: HelpItem[];
  screenName: string;
}

export const ContextualHelp: React.FC<ContextualHelpProps> = ({
  items,
  screenName
}) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  
  const handleOpenHelp = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setIsModalVisible(true);
  };
  
  const handleCloseHelp = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setIsModalVisible(false);
  };

  return (
    <>
      <TouchableOpacity 
        style={styles.helpButton}
        onPress={handleOpenHelp}
        activeOpacity={0.8}
      >
        <Ionicons name="help-circle-outline" size={24} color="#6b7280" />
      </TouchableOpacity>
      
      <Modal
        visible={isModalVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={handleCloseHelp}
      >
        <View style={styles.modalOverlay}>
          <Animated.View 
            style={styles.modalContent}
            entering={SlideInDown.springify().damping(15)}
            exiting={SlideOutDown.springify().damping(15)}
          >
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Help with {screenName}</Text>
              <TouchableOpacity 
                onPress={handleCloseHelp}
                style={styles.closeButton}
              >
                <Ionicons name="close" size={24} color="#374151" />
              </TouchableOpacity>
            </View>
            
            <ScrollView 
              style={styles.scrollView}
              contentContainerStyle={styles.scrollContent}
              showsVerticalScrollIndicator={false}
            >
              {items.map((item, index) => (
                <Animated.View 
                  key={index}
                  style={styles.helpItem}
                  entering={FadeIn.delay(index * 100).duration(300)}
                >
                  <View style={styles.helpIconContainer}>
                    <Ionicons 
                      name={item.icon || "information-circle-outline"} 
                      size={24} 
                      color="#f72585" 
                    />
                  </View>
                  <View style={styles.helpTextContainer}>
                    <Text style={styles.helpTitle}>{item.title}</Text>
                    <Text style={styles.helpContent}>{item.content}</Text>
                  </View>
                </Animated.View>
              ))}
            </ScrollView>
            
            <TouchableOpacity 
              style={styles.doneButton}
              onPress={handleCloseHelp}
            >
              <Text style={styles.doneButtonText}>Got it</Text>
            </TouchableOpacity>
          </Animated.View>
        </View>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  helpButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(243, 244, 246, 0.8)',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#ffffff',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingTop: 16,
    paddingBottom: 32,
    maxHeight: height * 0.8,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
  },
  closeButton: {
    padding: 4,
  },
  scrollView: {
    maxHeight: height * 0.6,
  },
  scrollContent: {
    padding: 24,
  },
  helpItem: {
    flexDirection: 'row',
    marginBottom: 24,
  },
  helpIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(247, 37, 133, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  helpTextContainer: {
    flex: 1,
  },
  helpTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 4,
  },
  helpContent: {
    fontSize: 14,
    color: '#6b7280',
    lineHeight: 20,
  },
  doneButton: {
    backgroundColor: '#f72585',
    marginHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 12,
    alignItems: 'center',
    marginTop: 16,
  },
  doneButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
});