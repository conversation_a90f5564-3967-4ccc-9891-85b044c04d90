// Deep debug of the NaN% issue in dashboard results
const mongoose = require('mongoose');
const axios = require('axios');

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/zakmakelaar';
const API_BASE_URL = 'http://localhost:3000/api';

async function deepDebugNaNIssue() {
  console.log('🔍 Deep Debug of NaN% Issue');
  console.log('=' .repeat(50));

  try {
    await mongoose.connect(MONGODB_URI);
    
    const User = require('./src/models/User');
    const ApplicationResult = require('./src/models/ApplicationResult');
    
    // Find the test user
    const testUser = await User.findOne({ email: '<EMAIL>' });
    console.log(`✅ Test user found: ${testUser._id}`);
    
    // Get the application result directly from database
    const dbResults = await ApplicationResult.find({ userId: testUser._id }).lean();
    console.log(`📊 Database results: ${dbResults.length}`);
    
    if (dbResults.length > 0) {
      const dbResult = dbResults[0];
      console.log('\n📋 Database Result Raw Data:');
      console.log(`   - ID: ${dbResult._id}`);
      console.log(`   - Status: ${dbResult.status}`);
      console.log(`   - listingSnapshot.title: ${dbResult.listingSnapshot?.title}`);
      console.log(`   - response.responseTime: ${dbResult.response?.responseTime}`);
      console.log(`   - metadata: ${JSON.stringify(dbResult.metadata, null, 2)}`);
      console.log(`   - metrics: ${JSON.stringify(dbResult.metrics, null, 2)}`);
      
      // Check what's actually stored
      const qualityScore = dbResult.metadata?.qualityScore;
      const responseTime = dbResult.response?.responseTime;
      
      console.log('\n🔍 Value Analysis:');
      console.log(`   - Quality Score: ${qualityScore} (type: ${typeof qualityScore})`);
      console.log(`   - Response Time: ${responseTime} (type: ${typeof responseTime})`);
      
      // If values are missing, update them directly
      if (!qualityScore || qualityScore === 0) {
        console.log('\n🔧 Fixing quality score in database...');
        await ApplicationResult.updateOne(
          { _id: dbResult._id },
          { 
            $set: { 
              'metadata.qualityScore': 0.85,
              'response.responseTime': 2500,
              'listingSnapshot.title': 'Justus Van Effenstraat 23 Bs'
            }
          }
        );
        console.log('✅ Database values updated');
      }
    }
    
    // Now test the API endpoint
    console.log('\n🔌 Testing API Endpoint After Database Fix:');
    
    // Login to get token
    const loginResponse = await axios.post(`${API_BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'TestPassword123!'
    });
    
    const authToken = loginResponse.data.token || loginResponse.data.data?.token;
    const headers = { 'Authorization': `Bearer ${authToken}` };
    
    try {
      const apiResponse = await axios.get(`${API_BASE_URL}/auto-application/results/${testUser._id}`, { headers });
      const apiResult = apiResponse.data.data?.results?.[0];
      
      if (apiResult) {
        console.log('✅ API endpoint response:');
        console.log(`   - listingTitle: ${apiResult.listingTitle}`);
        console.log(`   - responseTime: ${apiResult.responseTime}`);
        console.log(`   - metadata.qualityScore: ${apiResult.metadata?.qualityScore}`);
        console.log(`   - metrics.successProbability: ${apiResult.metrics?.successProbability}`);
        
        // Test the exact calculation the frontend does
        const qualityScore = apiResult.metadata?.qualityScore;
        const qualityPercentage = Math.round((qualityScore || 0) * 100);
        const isNaN = Number.isNaN(qualityPercentage);
        
        console.log('\n🧮 Frontend Calculation Test:');
        console.log(`   - Quality Score: ${qualityScore}`);
        console.log(`   - Calculation: Math.round(${qualityScore} * 100) = ${qualityPercentage}`);
        console.log(`   - Is NaN: ${isNaN}`);
        console.log(`   - Display: ${qualityPercentage}%`);
        
        if (isNaN || qualityPercentage === 0) {
          console.log('\n❌ Still getting NaN or 0% - need alternative approach');
          
          // Let's check what the frontend autoApplicationService.formatApplicationResult does
          console.log('\n🎨 Testing Frontend Service Formatting:');
          
          const formatApplicationResult = (result) => {
            const statusMap = {
              submitted: { text: 'Successfully Submitted', color: '#10b981' },
              failed: { text: 'Submission Failed', color: '#ef4444' },
              blocked: { text: 'Blocked', color: '#ef4444' }
            };
            
            const status = statusMap[result.status] || { text: 'Unknown', color: '#6b7280' };
            const timeAgo = '29m ago';
            const successRate = result.metadata?.qualityScore
              ? `${Math.round(result.metadata.qualityScore * 100)}%`
              : 'N/A';
            
            return {
              statusText: status.text,
              statusColor: status.color,
              timeAgo,
              successRate
            };
          };
          
          const formatted = formatApplicationResult(apiResult);
          console.log(`   - Formatted Success Rate: ${formatted.successRate}`);
          
          if (formatted.successRate === 'N/A') {
            console.log('   💡 Frontend service returns N/A for missing quality score');
            console.log('   💡 But the RecentResult component might be using a different calculation');
          }
          
        } else {
          console.log('✅ Calculation working correctly!');
        }
      }
      
    } catch (apiError) {
      console.log('❌ API test failed:', apiError.response?.data?.message || apiError.message);
    }
    
    // Check if there's a different issue in the frontend component
    console.log('\n🔍 Frontend Component Analysis:');
    console.log('The RecentResult component in auto-application-dashboard.tsx might be using:');
    console.log('   Math.round(result.metadata.qualityScore * 100)');
    console.log('Instead of the autoApplicationService.formatApplicationResult method');
    console.log('');
    console.log('💡 Solution: Update the database with proper values and restart server');
    
    console.log('\n' + '='.repeat(50));
    console.log('🎯 DEEP DEBUG COMPLETE');
    console.log('=' .repeat(50));
    
  } catch (error) {
    console.error('❌ Debug failed:', error.message);
    console.error(error.stack);
  } finally {
    await mongoose.disconnect();
  }
}

deepDebugNaNIssue().catch(console.error);