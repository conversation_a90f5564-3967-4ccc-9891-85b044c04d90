const puppeteer = require("puppeteer");
const { loggers } = require("./logger");
const {
  AutoApplicationSettings,
} = require("../models/AutoApplicationSettings");
const { setupPageStealth } = require("./scraperUtils");
const path = require("path");
const fs = require("fs");

class FormAutomationService {
  constructor() {
    this.browser = null;
    this.isInitialized = false;
    this.screenshotDir = path.join(process.cwd(), "screenshots");
    this.ensureScreenshotDir();
  }

  // Ensure screenshot directory exists
  ensureScreenshotDir() {
    try {
      if (!fs.existsSync(this.screenshotDir)) {
        fs.mkdirSync(this.screenshotDir, { recursive: true });
        loggers.app.info(`Created screenshot directory: ${this.screenshotDir}`);
      }
    } catch (error) {
      loggers.app.error("Failed to create screenshot directory:", error);
    }
  }

  async initialize() {
    if (this.isInitialized) return;

    try {
      // Determine the best Chrome executable path
      let executablePath = null;

      // Check if we're in Docker or have environment variable set
      if (process.env.PUPPETEER_EXECUTABLE_PATH) {
        executablePath = process.env.PUPPETEER_EXECUTABLE_PATH;
      } else {
        // Try different Chrome paths
        const fs = require("fs");
        const possiblePaths = [
          puppeteer.executablePath(),
          "/usr/bin/google-chrome-stable",
          "/usr/bin/google-chrome",
          "/usr/bin/chromium-browser",
          "/usr/bin/chromium",
          "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
          "C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe",
        ];

        for (const path of possiblePaths) {
          try {
            if (fs.existsSync(path)) {
              executablePath = path;
              break;
            }
          } catch (e) {
            // Continue to next path
          }
        }
      }

      if (!executablePath) {
        throw new Error(
          "Chrome executable not found. Install Chrome or set PUPPETEER_EXECUTABLE_PATH"
        );
      }

      const launchOptions = {
        executablePath: executablePath,
        headless: true,
        args: [
          "--no-sandbox",
          "--disable-setuid-sandbox",
          "--disable-dev-shm-usage",
          "--disable-accelerated-2d-canvas",
          "--no-first-run",
          "--disable-gpu",
        ],
      };

      // Add Docker-specific args if we detect we're in a container
      const fs = require("fs");
      if (
        process.env.PUPPETEER_EXECUTABLE_PATH ||
        fs.existsSync("/.dockerenv")
      ) {
        launchOptions.args.push(
          "--disable-background-timer-throttling",
          "--disable-backgrounding-occluded-windows",
          "--disable-renderer-backgrounding"
        );
      }

      this.browser = await puppeteer.launch(launchOptions);
      this.isInitialized = true;
      loggers.app.info("Form automation service initialized");
    } catch (error) {
      loggers.app.error("Failed to initialize form automation service:", error);
      throw error;
    }
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
      this.isInitialized = false;
    }
  }

  // Small delay helper (compatible with Puppeteer v24+)
  sleep(ms) {
    return new Promise(function (resolve) {
      setTimeout(resolve, ms);
    });
  }

  // Launch browser for testing purposes
  async launchBrowser() {
    // Determine the best Chrome executable path
    let executablePath = null;

    // Check if we're in Docker or have environment variable set
    if (process.env.PUPPETEER_EXECUTABLE_PATH) {
      executablePath = process.env.PUPPETEER_EXECUTABLE_PATH;
    } else {
      // Try different Chrome paths
      const fs = require("fs");
      const possiblePaths = [
        puppeteer.executablePath(),
        "/usr/bin/google-chrome-stable",
        "/usr/bin/google-chrome",
        "/usr/bin/chromium-browser",
        "/usr/bin/chromium",
        "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
        "C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe",
      ];

      for (const path of possiblePaths) {
        try {
          if (fs.existsSync(path)) {
            executablePath = path;
            break;
          }
        } catch (e) {
          // Continue to next path
        }
      }
    }

    if (!executablePath) {
      throw new Error(
        "Chrome executable not found. Install Chrome or set PUPPETEER_EXECUTABLE_PATH"
      );
    }

    const launchOptions = {
      executablePath: executablePath,
      headless: true,
      args: [
        "--no-sandbox",
        "--disable-setuid-sandbox",
        "--disable-dev-shm-usage",
        "--disable-accelerated-2d-canvas",
        "--no-first-run",
        "--disable-gpu",
      ],
    };

    // Add Docker-specific args if we detect we're in a container
    const fs = require("fs");
    if (process.env.PUPPETEER_EXECUTABLE_PATH || fs.existsSync("/.dockerenv")) {
      launchOptions.args.push(
        "--disable-background-timer-throttling",
        "--disable-backgrounding-occluded-windows",
        "--disable-renderer-backgrounding"
      );
    }

    return await puppeteer.launch(launchOptions);
  }

  // Take screenshot with descriptive filename
  async takeScreenshot(page, application, step, additionalInfo = "") {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
      const domain = new URL(application.propertyUrl).hostname;
      const filename = `${timestamp}_${application._id}_${domain}_${step}${
        additionalInfo ? "_" + additionalInfo : ""
      }.png`;
      const filepath = path.join(this.screenshotDir, filename);

      await page.screenshot({
        path: filepath,
        fullPage: true,
        type: "png",
      });

      loggers.app.info(`Screenshot saved: ${filename}`);
      return filepath;
    } catch (error) {
      loggers.app.error("Failed to take screenshot:", error);
      return null;
    }
  }

  // Take screenshot on error with additional context
  async takeErrorScreenshot(page, application, error, step) {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
      const domain = new URL(application.propertyUrl).hostname;
      const filename = `ERROR_${timestamp}_${application._id}_${domain}_${step}.png`;
      const filepath = path.join(this.screenshotDir, filename);

      await page.screenshot({
        path: filepath,
        fullPage: true,
        type: "png",
      });

      // Also save page HTML for debugging
      const htmlFilename = `ERROR_${timestamp}_${application._id}_${domain}_${step}.html`;
      const htmlFilepath = path.join(this.screenshotDir, htmlFilename);
      const html = await page.content();
      fs.writeFileSync(htmlFilepath, html);

      loggers.app.error(
        `Error screenshot and HTML saved: ${filename}, ${htmlFilename}`,
        {
          error: error.message,
          step: step,
          url: application.propertyUrl,
        }
      );

      return { screenshot: filepath, html: htmlFilepath };
    } catch (screenshotError) {
      loggers.app.error("Failed to take error screenshot:", screenshotError);
      return null;
    }
  }

  async submitApplication(application) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const page = await this.browser.newPage();

    // Apply stealth measures before navigation
    try {
      await setupPageStealth(page);
      loggers.app.info("Applied stealth settings to page");
    } catch (e) {
      loggers.app.warn("Failed to apply stealth settings", {
        error: e?.message,
      });
    }

    try {
      // Extra HTTP headers to look more like a real browser
      await page.setExtraHTTPHeaders({
        "Accept-Language": "en-US,en;q=0.9,nl;q=0.8",
        "Accept-Encoding": "gzip, deflate, br",
        "Upgrade-Insecure-Requests": "1",
        "Sec-Fetch-Dest": "document",
        "Sec-Fetch-Mode": "navigate",
        "Sec-Fetch-Site": "none",
      });

      // Ensure viewport is realistic (setupPageStealth also sets one randomly)
      try {
        await page.setViewport({ width: 1366, height: 768 });
      } catch {}

      // Set user agent to avoid detection
      await page.setUserAgent(
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
      );

      // Take initial screenshot
      await this.takeScreenshot(page, application, "01_initial");

      // Get user settings for form data
      const userSettings = await AutoApplicationSettings.findByUserId(
        application.userId
      );
      if (!userSettings) {
        throw new Error("User auto-application settings not found");
      }

      // Navigate to property page
      loggers.app.info(`Navigating to: ${application.propertyUrl}`);
      await page.goto(application.propertyUrl, { waitUntil: "networkidle2" });

      // Take screenshot after navigation
      await this.takeScreenshot(page, application, "02_after_navigation");

      // Determine which platform to use based on property URL
      const platformHandler = this.getPlatformHandler(application.propertyUrl);

      const result = await platformHandler.call(
        this,
        page,
        application,
        userSettings
      );

      // Take final success screenshot
      await this.takeScreenshot(page, application, "99_success");

      loggers.app.info(
        `Successfully submitted application for property: ${application.propertyUrl}`
      );
      return result;
    } catch (error) {
      // Take error screenshot with context
      await this.takeErrorScreenshot(
        page,
        application,
        error,
        "submission_failed"
      );
      loggers.app.error(
        `Failed to submit application for ${application.propertyUrl}:`,
        error
      );
      throw error;
    } finally {
      await page.close();
    }
  }

  getPlatformHandler(propertyUrl) {
    const url = new URL(propertyUrl);
    const domain = url.hostname.toLowerCase();

    if (domain.includes("funda.nl")) {
      return this.handleFundaApplication;
    } else if (domain.includes("pararius.nl")) {
      return this.handlePariusApplication;
    } else if (domain.includes("kamernet.nl")) {
      return this.handleKamernetApplication;
    } else if (domain.includes("huurwoningen.nl")) {
      return this.handleHuurwoningenApplication;
    } else {
      return this.handleGenericApplication;
    }
  }
  // Funda.nl application handler - Updated for new form structure
  async handleFundaApplication(page, application, userSettings) {
    // Take screenshot before looking for contact button
    await this.takeScreenshot(page, application, "03_funda_page_loaded");

    // Accept cookies if banner appears
    try {
      // First check for the notice popup
      const noticePopup = await page.$('[data-testid="notice"]');
      if (noticePopup) {
        loggers.app.info("Found cookie notice popup");
        // Look for accept button with "Alles accepteren" text
        const acceptButton = await page.evaluateHandle(() => {
          const notice = document.querySelector('[data-testid="notice"]');
          if (notice) {
            const buttons = notice.querySelectorAll("button");
            for (const button of buttons) {
              if (
                button.textContent &&
                button.textContent.includes("Alles accepteren")
              ) {
                return button;
              }
            }
          }
          return null;
        });

        if (acceptButton && acceptButton.asElement()) {
          await acceptButton.asElement().click();
          loggers.app.info(
            "Accepted cookie banner via 'Alles accepteren' button"
          );
          await this.sleep(1500);
        } else {
          loggers.app.warn(
            "Found notice popup but no 'Alles accepteren' button"
          );
        }
      } else {
        // Fallback to original selectors
        await page.waitForSelector(
          '[data-testid="accept-all-cookies-button"], .cookie-accept-all, #accept-cookies',
          { timeout: 3000 }
        );
        await page.click(
          '[data-testid="accept-all-cookies-button"], .cookie-accept-all, #accept-cookies'
        );
        loggers.app.info("Accepted cookie banner via fallback selector");
        await this.sleep(1500);
      }
    } catch (e) {
      loggers.app.debug("No cookie banner found or already accepted");
    }

    // Try multiple selectors for the contact button
    const contactSelectors = [
      '[data-optimizely="contact-email"]',
      'a[href*="makelaar-contact"]',
      'a:contains("Contact")',
      'button:contains("Contact")',
      '[data-testid="contact-block-container"] button',
      '[data-testid="contact-button"]',
      '[data-testid="contact-form-button"]',
      '[data-testid="broker-contact-button"]',
      ".makelaar-contact-button",
      ".broker-contact",
      ".fd-button--contact",
      ".contact-button",
      'button[data-test-id*="contact"]',
      'button[data-testid*="contact"]',
    ];

    let contactButton = null;
    for (const selector of contactSelectors) {
      try {
        contactButton = await page.$(selector);
        if (contactButton) {
          loggers.app.info(`Found contact button with selector: ${selector}`);
          break;
        }
      } catch (error) {
        // Continue to next selector
      }
    }

    if (!contactButton) {
      // Try to scroll down to find the broker contact section
      await page.evaluate(() => {
        window.scrollTo(0, document.body.scrollHeight / 2);
      });
      await this.sleep(2000);

      // Try again after scrolling
      for (const selector of contactSelectors) {
        try {
          contactButton = await page.$(selector);
          if (contactButton) {
            loggers.app.info(
              `Found contact button after scrolling with selector: ${selector}`
            );
            break;
          }
        } catch (error) {
          // Continue to next selector
        }
      }
    }

    if (!contactButton) {
      // Fallback: search anchors by text/href
      try {
        const handle = await page.evaluateHandle(() => {
          const links = Array.from(document.querySelectorAll("a"));
          return links.find(
            (link) =>
              (link.textContent &&
                (link.textContent.includes("Neem contact op") ||
                  link.textContent.includes("Contact"))) ||
              (link.getAttribute("href") || "").includes("makelaar-contact")
          );
        });
        if (handle && handle.asElement()) {
          contactButton = handle.asElement();
          loggers.app.info("Found contact button via text/href evaluation");
        }
      } catch (e) {
        // ignore
      }
    }

    if (!contactButton) {
      await this.takeErrorScreenshot(
        page,
        application,
        new Error("Contact button not found"),
        "funda_no_contact_button"
      );
      throw new Error(
        "Contact button not found on Funda page. Tried multiple selectors."
      );
    }

    // Take screenshot before clicking contact button
    await this.takeScreenshot(
      page,
      application,
      "04_funda_before_contact_click"
    );

    await contactButton.click();
    await this.sleep(3000); // Wait for form to load

    // Take screenshot after clicking contact button
    await this.takeScreenshot(
      page,
      application,
      "05_funda_after_contact_click"
    );

    // Wait for the form to be visible - try multiple form selectors
    const formSelectors = [
      'form[aria-busy="false"]',
      "form",
      '[data-testid="contact-form"]',
      ".contact-form",
    ];

    let formFound = false;
    for (const formSelector of formSelectors) {
      try {
        await page.waitForSelector(formSelector, { timeout: 5000 });
        formFound = true;
        loggers.app.info(`Found form with selector: ${formSelector}`);
        break;
      } catch (error) {
        // Continue to next selector
      }
    }

    if (!formFound) {
      loggers.app.warn(
        "Form not found with standard selectors, proceeding anyway"
      );
    }

    // Fill out the new contact form structure
    await this.fillFundaContactForm(page, userSettings, application);

    // Take screenshot after filling form
    await this.takeScreenshot(page, application, "06_funda_form_filled");

    // Submit the form - look for the submit button
    let submitButton = null;

    // First try to find by type="submit"
    submitButton = await page.$('button[type="submit"]');
    if (submitButton) {
      loggers.app.info(
        'Found submit button with selector: button[type="submit"]'
      );
    } else {
      // Try to find by text content "Verstuur bericht"
      submitButton = await page.evaluateHandle(() => {
        const buttons = document.querySelectorAll("button");
        for (const button of buttons) {
          if (
            button.textContent &&
            button.textContent.includes("Verstuur bericht")
          ) {
            return button;
          }
        }
        return null;
      });

      if (submitButton && submitButton.asElement()) {
        loggers.app.info('Found submit button by "Verstuur bericht" text');
        submitButton = submitButton.asElement();
      } else {
        // Fallback selectors
        const submitSelectors = [
          'button:contains("Send message")',
          'button:contains("Verstuur")',
          'button:contains("Verzenden")',
          ".submit-button",
          'button[aria-disabled="false"][type="submit"]',
        ];

        for (const submitSelector of submitSelectors) {
          try {
            submitButton = await page.$(submitSelector);
            if (submitButton) {
              loggers.app.info(
                `Found submit button with fallback selector: ${submitSelector}`
              );
              break;
            }
          } catch (error) {
            // Continue to next selector
          }
        }
      }
    }

    if (submitButton) {
      await submitButton.click();
      await this.sleep(3000);

      // Take screenshot after submission
      await this.takeScreenshot(page, application, "07_funda_after_submit");
    } else {
      loggers.app.warn(
        "Submit button not found, form may have been submitted automatically"
      );
    }

    return { success: true, platform: "funda" };
  }

  // Pararius.nl application handler
  async handlePariusApplication(page, application, userSettings) {
    // Take screenshot of Pararius page
    await this.takeScreenshot(page, application, "03_pararius_page_loaded");

    // Look for contact/apply button
    const contactButton = await page.$(
      '.contact-button, button:contains("Contact"), .btn-contact'
    );

    if (!contactButton) {
      await this.takeErrorScreenshot(
        page,
        application,
        new Error("Contact button not found"),
        "pararius_no_contact_button"
      );
      throw new Error("Contact button not found on Pararius page");
    }

    await this.takeScreenshot(
      page,
      application,
      "04_pararius_before_contact_click"
    );
    await contactButton.click();
    await this.sleep(2000);

    await this.takeScreenshot(
      page,
      application,
      "05_pararius_after_contact_click"
    );

    await this.fillContactForm(
      page,
      userSettings,
      {
        nameSelector: 'input[name="name"], #contact_name',
        emailSelector: 'input[name="email"], #contact_email',
        phoneSelector: 'input[name="phone"], #contact_phone',
        messageSelector: 'textarea[name="message"], #contact_message',
      },
      application
    );

    await this.takeScreenshot(page, application, "06_pararius_form_filled");

    const submitButton = await page.$('button[type="submit"], .btn-submit');
    if (submitButton) {
      await submitButton.click();
      await this.sleep(3000);
    }

    return { success: true, platform: "pararius" };
  }

  // Kamernet.nl application handler
  async handleKamernetApplication(page, application, userSettings) {
    await page.goto(application.propertyUrl, { waitUntil: "networkidle2" });

    // Kamernet often requires login first
    const loginRequired = await page.$(".login-required, .member-only");
    if (loginRequired) {
      throw new Error("Kamernet requires login - manual intervention needed");
    }

    const reactButton = await page.$(
      '.react-button, button:contains("Reageren")'
    );
    if (!reactButton) {
      throw new Error("React button not found on Kamernet page");
    }

    await reactButton.click();
    await this.sleep(2000);

    await this.fillContactForm(page, userSettings, {
      nameSelector: 'input[name="name"], #name',
      emailSelector: 'input[name="email"], #email',
      phoneSelector: 'input[name="phone"], #phone',
      messageSelector: 'textarea[name="message"], #message',
    });

    const submitButton = await page.$('button[type="submit"], .submit-button');
    if (submitButton) {
      await submitButton.click();
      await this.sleep(3000);
    }

    return { success: true, platform: "kamernet" };
  }

  // Huurwoningen.nl application handler
  async handleHuurwoningenApplication(page, application, userSettings) {
    await page.goto(application.propertyUrl, { waitUntil: "networkidle2" });

    const applyButton = await page.$(
      '.apply-button, button:contains("Solliciteren"), .contact-button'
    );

    if (!applyButton) {
      throw new Error("Apply button not found on Huurwoningen page");
    }

    await applyButton.click();
    await this.sleep(2000);

    await this.fillContactForm(page, userSettings, {
      nameSelector: 'input[name="name"], #applicant_name',
      emailSelector: 'input[name="email"], #applicant_email',
      phoneSelector: 'input[name="phone"], #applicant_phone',
      messageSelector: 'textarea[name="message"], #applicant_message',
    });

    // Handle additional fields common on Huurwoningen
    await this.fillAdditionalFields(page, userSettings);

    const submitButton = await page.$(
      'button[type="submit"], .submit-application'
    );
    if (submitButton) {
      await submitButton.click();
      await this.sleep(3000);
    }

    return { success: true, platform: "huurwoningen" };
  }
  // Generic handler for unknown platforms
  async handleGenericApplication(page, application, userSettings) {
    await page.goto(application.propertyUrl, { waitUntil: "networkidle2" });

    // Try to find common contact/apply buttons
    const buttonSelectors = [
      'button:contains("Contact")',
      'button:contains("Apply")',
      'button:contains("Reageren")',
      'button:contains("Solliciteren")',
      ".contact-button",
      ".apply-button",
      ".btn-contact",
      ".btn-apply",
    ];

    let foundButton = false;
    for (const selector of buttonSelectors) {
      const button = await page.$(selector);
      if (button) {
        await button.click();
        await this.sleep(2000);
        foundButton = true;
        break;
      }
    }

    if (!foundButton) {
      throw new Error("No contact/apply button found on page");
    }

    // Try generic form filling
    await this.fillContactForm(page, userSettings, {
      nameSelector:
        'input[name="name"], input[name="full_name"], input[name="fullname"], #name, #full_name',
      emailSelector: 'input[name="email"], input[type="email"], #email',
      phoneSelector:
        'input[name="phone"], input[name="telephone"], input[type="tel"], #phone, #telephone',
      messageSelector:
        'textarea[name="message"], textarea[name="comment"], textarea[name="remarks"], #message, #comment',
    });

    // Try to submit
    const submitSelectors = [
      'button[type="submit"]',
      'input[type="submit"]',
      'button:contains("Send")',
      'button:contains("Verstuur")',
      'button:contains("Submit")',
      ".submit-button",
      ".btn-submit",
    ];

    for (const selector of submitSelectors) {
      const submitButton = await page.$(selector);
      if (submitButton) {
        await submitButton.click();
        await this.sleep(3000);
        break;
      }
    }

    return { success: true, platform: "generic" };
  }

  // Helper method to fill Funda's new contact form structure
  async fillFundaContactForm(page, userSettings, application) {
    const formData = userSettings.formData;

    loggers.app.info("Starting Funda form filling process", {
      formData: {
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        phone: formData.phone,
      },
    });

    try {
      // Debug: Log all form elements on the page
      const allFormElements = await page.evaluate(() => {
        const inputs = Array.from(
          document.querySelectorAll("input, textarea, select")
        );
        return inputs.map((el) => ({
          tagName: el.tagName,
          type: el.type || "N/A",
          id: el.id || "N/A",
          name: el.name || "N/A",
          className: el.className || "N/A",
          placeholder: el.placeholder || "N/A",
        }));
      });
      loggers.app.info("All form elements found on page:", {
        elements: allFormElements,
      });

      // Fill the question/message textarea
      loggers.app.info("Looking for question textarea...");
      const questionTextarea = await page.$("#questionInput");
      if (questionTextarea) {
        loggers.app.info("Found question textarea, clearing and filling...");
        const message = this.generateApplicationMessage(userSettings);
        loggers.app.info("Generated message, setting value directly...", {
          messageLength: message.length,
        });
        // Use faster method: set value directly instead of typing
        await page.evaluate(
          (el, msg) => {
            if (el) {
              el.value = msg;
              el.dispatchEvent(new Event("input", { bubbles: true }));
              el.dispatchEvent(new Event("change", { bubbles: true }));
            }
          },
          questionTextarea,
          message
        );
        loggers.app.info("Filled question textarea successfully");
      } else {
        loggers.app.warn("Question textarea not found");
      }

      // Check the viewing request checkbox if needed
      const viewingCheckbox = await page.$("#checkbox-viewingRequest");
      if (viewingCheckbox) {
        const isChecked = await page.evaluate(
          (el) => el.checked,
          viewingCheckbox
        );
        if (!isChecked) {
          await viewingCheckbox.click();
          loggers.app.info("Checked viewing request checkbox");
        }
      }

      // Fill email address (should be pre-filled but let's ensure it's correct)
      const emailField = await page.$("#emailAddress");
      if (emailField) {
        await page.evaluate(
          (el, email) => {
            if (el) {
              el.value = email;
              el.dispatchEvent(new Event("input", { bubbles: true }));
              el.dispatchEvent(new Event("change", { bubbles: true }));
            }
          },
          emailField,
          formData.email
        );
        loggers.app.info("Filled email field successfully");
      } else {
        // Fallback selectors
        const emailSelectors = [
          'input[type="email"]',
          'input[name="email"]',
          'input[data-testid*="email"]',
          'input[placeholder*="email" i]',
        ];
        let filled = false;
        for (const sel of emailSelectors) {
          try {
            const el = await page.$(sel);
            if (el) {
              await page.evaluate(
                (node, email) => {
                  if (node) {
                    node.value = email;
                    node.dispatchEvent(new Event("input", { bubbles: true }));
                    node.dispatchEvent(new Event("change", { bubbles: true }));
                  }
                },
                el,
                formData.email
              );
              loggers.app.info(`Filled email using fallback selector: ${sel}`);
              filled = true;
              break;
            }
          } catch {}
        }
        if (!filled) {
          loggers.app.warn("Email field not found");
        }
      }

      // Fill first name
      const firstNameField = await page.$("#firstName");
      if (firstNameField) {
        await page.evaluate(
          (el, name) => {
            if (el) {
              el.value = name;
              el.dispatchEvent(new Event("input", { bubbles: true }));
              el.dispatchEvent(new Event("change", { bubbles: true }));
            }
          },
          firstNameField,
          formData.firstName
        );
        loggers.app.info("Filled first name field successfully");
      } else {
        loggers.app.warn("First name field not found");
      }

      // Fill last name
      const lastNameField = await page.$("#lastName");
      if (lastNameField) {
        await page.evaluate(
          (el, name) => {
            if (el) {
              el.value = name;
              el.dispatchEvent(new Event("input", { bubbles: true }));
              el.dispatchEvent(new Event("change", { bubbles: true }));
            }
          },
          lastNameField,
          formData.lastName
        );
        loggers.app.info("Filled last name field successfully");
      } else {
        loggers.app.warn("Last name field not found");
      }

      // Fill phone number with multiple format attempts
      const phoneField = await page.$("#phoneNumber");
      if (phoneField) {
        // Try different phone number formats
        const phoneFormats = [
          formData.phone, // Original format
          formData.phone.replace(/[\s\-\+]/g, ""), // Remove all spaces, dashes, plus
          formData.phone.replace(/[\-\+]/g, "").replace(/\s+/g, " "), // Keep single spaces only
          `0${formData.phone
            .replace(/[\s\-\+]/g, "")
            .replace(/^(\+31|31|0)/, "")}`, // Ensure starts with 0
        ];

        let phoneSet = false;
        for (const phoneFormat of phoneFormats) {
          if (phoneSet) break;

          await page.evaluate(
            (el, phone) => {
              if (el) {
                el.value = phone;
                el.dispatchEvent(new Event("input", { bubbles: true }));
                el.dispatchEvent(new Event("change", { bubbles: true }));
                el.dispatchEvent(new Event("blur", { bubbles: true }));
              }
            },
            phoneField,
            phoneFormat
          );

          loggers.app.info(`Trying phone format: ${phoneFormat}`);

          // Wait a moment for validation to complete
          await this.sleep(800);

          // Check if there's a validation error with more comprehensive selectors
          const validationSelectors = [
            ".error",
            ".validation-error",
            '[class*="error"]',
            '[class*="invalid"]',
            ".field-error",
            ".input-error",
            '[role="alert"]',
            ".text-red-500",
            ".text-danger",
          ];

          let validationError = null;
          let errorText = "";

          for (const selector of validationSelectors) {
            validationError = await page.$(selector);
            if (validationError) {
              errorText = await page.evaluate(
                (el) => el.textContent,
                validationError
              );
              if (errorText && errorText.trim()) {
                loggers.app.warn("Validation error detected:", {
                  selector: selector,
                  error: errorText.trim(),
                  phoneFormat: phoneFormat,
                });
                break;
              }
            }
          }

          // If no validation error, we're good
          if (!errorText || !errorText.trim()) {
            loggers.app.info(`Phone format accepted: ${phoneFormat}`);
            phoneSet = true;
          }
        }

        if (!phoneSet) {
          loggers.app.warn("All phone formats failed validation");
        }
      } else {
        loggers.app.warn("Phone field not found");
      }

      await this.sleep(200); // Reduced sleep time for faster execution
      loggers.app.info("Funda form filling completed");
    } catch (error) {
      loggers.app.error("Error filling Funda form:", error);
      throw error;
    }
  }

  // Helper method to fill contact forms (legacy method for other platforms)
  async fillContactForm(page, userSettings, selectors, application) {
    const formData = userSettings.formData;

    loggers.app.info("Starting form filling process", {
      selectors: selectors,
      formData: {
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        phone: formData.phone,
      },
    });

    // Fill name
    const nameField = await page.$(selectors.nameSelector);
    if (nameField) {
      await page.evaluate((el) => {
        if (el) el.value = "";
      }, nameField);
      await nameField.type(`${formData.firstName} ${formData.lastName}`);
      loggers.app.info("Filled name field successfully");
    } else {
      loggers.app.warn("Name field not found", {
        selector: selectors.nameSelector,
      });
    }

    // Fill email
    const emailField = await page.$(selectors.emailSelector);
    if (emailField) {
      await page.evaluate((el) => {
        if (el) el.value = "";
      }, emailField);
      await emailField.type(formData.email);
      loggers.app.info("Filled email field successfully");
    } else {
      loggers.app.warn("Email field not found", {
        selector: selectors.emailSelector,
      });
    }

    // Fill phone
    const phoneField = await page.$(selectors.phoneSelector);
    if (phoneField) {
      await page.evaluate((el) => {
        if (el) el.value = "";
      }, phoneField);
      await phoneField.type(formData.phone);
      loggers.app.info("Filled phone field successfully");
    } else {
      loggers.app.warn("Phone field not found", {
        selector: selectors.phoneSelector,
      });
    }

    // Fill message
    const messageField = await page.$(selectors.messageSelector);
    if (messageField) {
      await page.evaluate((el) => {
        if (el) el.value = "";
      }, messageField);
      const message = this.generateApplicationMessage(userSettings);
      await messageField.type(message);
      loggers.app.info("Filled message field successfully", {
        messageLength: message.length,
      });
    } else {
      loggers.app.warn("Message field not found", {
        selector: selectors.messageSelector,
      });
    }

    await this.sleep(1000);
    loggers.app.info("Form filling completed");
  }

  // Helper method for additional fields
  async fillAdditionalFields(page, userSettings) {
    const formData = userSettings.formData;

    // Income field
    const incomeField = await page.$(
      'input[name="income"], input[name="salary"], #income, #salary'
    );
    if (incomeField && formData.monthlyIncome) {
      await page.evaluate((el) => {
        if (el) el.value = "";
      }, incomeField);
      await incomeField.type(formData.monthlyIncome.toString());
    }

    // Age/Birth date
    const ageField = await page.$(
      'input[name="age"], input[name="birth_date"], #age'
    );
    if (ageField && formData.age) {
      await page.evaluate((el) => {
        if (el) el.value = "";
      }, ageField);
      await ageField.type(formData.age.toString());
    }

    // Occupation
    const occupationField = await page.$(
      'input[name="occupation"], input[name="job"], #occupation'
    );
    if (occupationField && formData.occupation) {
      await page.evaluate((el) => {
        if (el) el.value = "";
      }, occupationField);
      await occupationField.type(formData.occupation);
    }

    // Move-in date
    const moveInField = await page.$(
      'input[name="move_in_date"], input[name="available_from"], #move_in_date'
    );
    if (moveInField && formData.preferredMoveInDate) {
      await page.evaluate((el) => {
        if (el) el.value = "";
      }, moveInField);
      await moveInField.type(formData.preferredMoveInDate);
    }
  }

  // Test form automation without actually submitting
  async testFormAutomation(listingUrl, options = {}) {
    const { dryRun = true, userId } = options;

    loggers.app.info("Starting form automation test", {
      listingUrl,
      dryRun,
      userId,
    });

    // Launch browser and create page with stealth
    // Note: testFormAutomation uses its own launcher; we still apply stealth at page level

    let browser = null;
    let page = null;
    const testResult = {
      success: false,
      platform: null,
      formType: null,
      fieldsFound: 0,
      formFields: [],
      screenshots: [],
      logs: [],
      errors: [],
      successProbability: 0,
    };

    try {
      // Launch browser
      browser = await this.launchBrowser();
      page = await browser.newPage();

      // Apply stealth to test page
      try {
        await setupPageStealth(page);
        testResult.logs.push("Applied stealth settings to test page");
      } catch (e) {
        testResult.logs.push(`Failed to apply stealth settings: ${e?.message}`);
      }

      // Extra HTTP headers
      await page.setExtraHTTPHeaders({
        "Accept-Language": "en-US,en;q=0.9,nl;q=0.8",
        "Accept-Encoding": "gzip, deflate, br",
        "Upgrade-Insecure-Requests": "1",
      });

      // Set user agent and viewport
      await page.setUserAgent(
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
      );
      await page.setViewport({ width: 1366, height: 768 });

      testResult.logs.push("Browser launched successfully");

      // Navigate to the listing
      await page.goto(listingUrl, {
        waitUntil: "networkidle2",
        timeout: 30000,
      });
      testResult.logs.push("Page loaded successfully");

      // Determine platform
      const url = new URL(listingUrl);
      const domain = url.hostname.toLowerCase();

      if (domain.includes("funda.nl")) {
        testResult.platform = "funda";
        await this.testFundaFormAutomation(page, testResult);
      } else {
        testResult.platform = "generic";
        testResult.logs.push(
          "Generic platform detected - limited testing available"
        );
      }

      testResult.success = true;
      testResult.logs.push("Form automation test completed successfully");
    } catch (error) {
      testResult.errors.push(error.message);
      testResult.logs.push(`Test failed: ${error.message}`);
      loggers.app.error("Form automation test failed:", error);
    } finally {
      if (page) {
        try {
          await page.close();
        } catch (error) {
          // Ignore close errors
        }
      }
      if (browser) {
        try {
          await browser.close();
        } catch (error) {
          // Ignore close errors
        }
      }
    }

    return testResult;
  }

  // Test Funda form automation specifically
  async testFundaFormAutomation(page, testResult) {
    try {
      // Take initial screenshot
      const screenshot1 = await page.screenshot({ encoding: "base64" });
      testResult.screenshots.push({
        name: "initial_page",
        data: screenshot1,
      });

      // Test contact button selectors
      const contactSelectors = [
        '[data-optimizely="contact-email"]',
        'a:contains("Contact")',
        'button:contains("Contact")',
        '[data-testid="contact-block-container"] button',
        ".contact-button",
        'button[data-test-id*="contact"]',
      ];

      let contactButtonFound = false;
      let workingSelector = null;

      for (const selector of contactSelectors) {
        try {
          const element = await page.$(selector);
          if (element) {
            contactButtonFound = true;
            workingSelector = selector;
            testResult.logs.push(
              `Contact button found with selector: ${selector}`
            );
            break;
          }
        } catch (error) {
          // Continue to next selector
        }
      }

      if (!contactButtonFound) {
        // Try scrolling and searching again
        await page.evaluate(() => {
          window.scrollTo(0, document.body.scrollHeight / 2);
        });
        await this.sleep(2000);

        for (const selector of contactSelectors) {
          try {
            const element = await page.$(selector);
            if (element) {
              contactButtonFound = true;
              workingSelector = selector;
              testResult.logs.push(
                `Contact button found after scrolling with selector: ${selector}`
              );
              break;
            }
          } catch (error) {
            // Continue to next selector
          }
        }
      }

      if (contactButtonFound) {
        testResult.formFields.push({
          name: "contactButton",
          type: "button",
          selector: workingSelector,
          required: true,
          found: true,
        });
        testResult.fieldsFound++;
        testResult.successProbability += 30;
      } else {
        testResult.errors.push("Contact button not found with any selector");
      }

      // Test form field selectors (these would appear after clicking contact button)
      const formFieldSelectors = {
        questionField: "#questionInput",
        viewingCheckbox: "#checkbox-viewingRequest",
        emailField: "#emailAddress",
        firstNameField: "#firstName",
        lastNameField: "#lastName",
        phoneField: "#phoneNumber",
        submitButton: 'button[type="submit"]',
      };

      Object.entries(formFieldSelectors).forEach(([fieldName, selector]) => {
        testResult.formFields.push({
          name: fieldName,
          type: fieldName.includes("button")
            ? "button"
            : fieldName.includes("checkbox")
            ? "checkbox"
            : "input",
          selector: selector,
          required: fieldName !== "viewingCheckbox",
          found: false, // Would be tested after clicking contact button
        });
      });

      testResult.fieldsFound += Object.keys(formFieldSelectors).length;
      testResult.successProbability += contactButtonFound ? 60 : 20;
      testResult.formType = "funda_contact_form";

      testResult.logs.push(
        `Form automation test completed for Funda. Success probability: ${testResult.successProbability}%`
      );
    } catch (error) {
      testResult.errors.push(`Funda test error: ${error.message}`);
      throw error;
    }
  }

  // Generate personalized application message
  generateApplicationMessage(userSettings) {
    const formData = userSettings.formData;
    const templates = userSettings.messageTemplates || {};

    if (templates.default) {
      return this.replacePlaceholders(templates.default, formData);
    }

    // Default template
    return `Beste verhuurder,

Ik ben zeer geïnteresseerd in deze woning. Ik ben ${
      formData.age
    } jaar oud, werk als ${formData.occupation} en heb een maandinkomen van €${
      formData.monthlyIncome
    }.

${
  formData.additionalInfo ||
  "Ik ben een betrouwbare huurder en zorg goed voor de woning."
}

Ik hoor graag van u.

Met vriendelijke groet,
${formData.firstName} ${formData.lastName}`;
  }

  // Replace placeholders in message templates
  replacePlaceholders(template, formData) {
    return template
      .replace(/\{firstName\}/g, formData.firstName)
      .replace(/\{lastName\}/g, formData.lastName)
      .replace(/\{age\}/g, formData.age)
      .replace(/\{occupation\}/g, formData.occupation)
      .replace(/\{monthlyIncome\}/g, formData.monthlyIncome)
      .replace(/\{email\}/g, formData.email)
      .replace(/\{phone\}/g, formData.phone)
      .replace(/\{additionalInfo\}/g, formData.additionalInfo || "");
  }

  // Handle CAPTCHA detection
  async handleCaptcha(page) {
    const captchaSelectors = [
      ".captcha",
      ".recaptcha",
      "#captcha",
      "[data-sitekey]",
      'iframe[src*="recaptcha"]',
    ];

    for (const selector of captchaSelectors) {
      const captcha = await page.$(selector);
      if (captcha) {
        loggers.app.warn("CAPTCHA detected - manual intervention required");
        throw new Error("CAPTCHA_DETECTED");
      }
    }
  }

  // Check if application was successful
  async verifySubmission(page) {
    const successIndicators = [
      ".success-message",
      ".confirmation",
      'text*="bedankt"',
      'text*="thank you"',
      'text*="verzonden"',
      'text*="sent"',
    ];

    for (const indicator of successIndicators) {
      const element = await page.$(indicator);
      if (element) {
        return true;
      }
    }

    return false;
  }
}

module.exports = FormAutomationService;
