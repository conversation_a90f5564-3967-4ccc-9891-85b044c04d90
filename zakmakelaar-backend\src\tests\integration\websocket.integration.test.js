const { Server } = require('socket.io');
const { createServer } = require('http');
const Client = require('socket.io-client');
const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const jwt = require('jsonwebtoken');
const config = require('../../config/config');
const User = require('../../models/User');
const websocketService = require('../../services/websocketService');

describe('WebSocket Integration Tests', () => {
  let mongoServer;
  let httpServer;
  let testUser;
  let authToken;
  let clientSocket;

  beforeAll(async () => {
    // Start in-memory MongoDB
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    await mongoose.connect(mongoUri);

    // Create HTTP server for WebSocket
    httpServer = createServer();
    websocketService.initialize(httpServer);
    
    await new Promise((resolve) => {
      httpServer.listen(0, resolve);
    });
  });

  afterAll(async () => {
    if (clientSocket) {
      clientSocket.close();
    }
    websocketService.close();
    httpServer.close();
    await mongoose.disconnect();
    await mongoServer.stop();
  });

  beforeEach(async () => {
    // Clear all collections
    await User.deleteMany({});

    // Create test user
    testUser = await User.create({
      email: '<EMAIL>',
      password: 'hashedpassword',
      role: 'user'
    });

    // Generate auth token
    authToken = jwt.sign(
      { id: testUser._id, email: testUser.email },
      config.jwtSecret,
      { expiresIn: '1h' }
    );
  });

  afterEach(() => {
    if (clientSocket) {
      clientSocket.close();
      clientSocket = null;
    }
  });

  describe('WebSocket Connection', () => {
    it('should establish connection with valid token', (done) => {
      const port = httpServer.address().port;
      clientSocket = new Client(`http://localhost:${port}`, {
        auth: {
          token: authToken
        }
      });

      clientSocket.on('connect', () => {
        expect(clientSocket.connected).toBe(true);
        done();
      });

      clientSocket.on('connect_error', (error) => {
        done(error);
      });
    });

    it('should reject connection without token', (done) => {
      const port = httpServer.address().port;
      clientSocket = new Client(`http://localhost:${port}`);

      clientSocket.on('connect', () => {
        done(new Error('Should not connect without token'));
      });

      clientSocket.on('connect_error', (error) => {
        expect(error.message).toContain('Authentication');
        done();
      });
    });

    it('should reject connection with invalid token', (done) => {
      const port = httpServer.address().port;
      clientSocket = new Client(`http://localhost:${port}`, {
        auth: {
          token: 'invalid-token'
        }
      });

      clientSocket.on('connect', () => {
        done(new Error('Should not connect with invalid token'));
      });

      clientSocket.on('connect_error', (error) => {
        expect(error.message).toContain('Authentication');
        done();
      });
    });

    it('should send connection confirmation', (done) => {
      const port = httpServer.address().port;
      clientSocket = new Client(`http://localhost:${port}`, {
        auth: {
          token: authToken
        }
      });

      clientSocket.on('connected', (data) => {
        expect(data.message).toBe('WebSocket connection established');
        expect(data.userId).toBe(testUser._id.toString());
        expect(data.timestamp).toBeDefined();
        done();
      });

      clientSocket.on('connect_error', done);
    });
  });

  describe('Auto-Application Subscriptions', () => {
    beforeEach((done) => {
      const port = httpServer.address().port;
      clientSocket = new Client(`http://localhost:${port}`, {
        auth: {
          token: authToken
        }
      });

      clientSocket.on('connect', () => {
        done();
      });

      clientSocket.on('connect_error', done);
    });

    it('should subscribe to auto-application updates', (done) => {
      clientSocket.emit('subscribe:auto-application');
      
      // Send a test update
      setTimeout(() => {
        websocketService.sendAutoApplicationUpdate(testUser._id.toString(), {
          action: 'enabled',
          message: 'Auto-application enabled'
        });
      }, 100);

      clientSocket.on('auto-application:status-update', (data) => {
        expect(data.type).toBe('auto-application-status');
        expect(data.action).toBe('enabled');
        expect(data.message).toBe('Auto-application enabled');
        expect(data.timestamp).toBeDefined();
        done();
      });
    });

    it('should subscribe to queue status updates', (done) => {
      clientSocket.emit('subscribe:queue-status');
      
      setTimeout(() => {
        websocketService.sendQueueUpdate(testUser._id.toString(), {
          pending: 3,
          processing: 1,
          nextScheduled: new Date().toISOString()
        });
      }, 100);

      clientSocket.on('queue:status-update', (data) => {
        expect(data.type).toBe('queue-status');
        expect(data.pending).toBe(3);
        expect(data.processing).toBe(1);
        expect(data.nextScheduled).toBeDefined();
        done();
      });
    });

    it('should subscribe to application monitoring', (done) => {
      clientSocket.emit('subscribe:application-monitor');
      
      setTimeout(() => {
        websocketService.sendMonitoringUpdate(testUser._id.toString(), {
          successRate: 85,
          totalApplications: 20,
          recentErrors: 2
        });
      }, 100);

      clientSocket.on('monitor:update', (data) => {
        expect(data.type).toBe('monitoring-update');
        expect(data.successRate).toBe(85);
        expect(data.totalApplications).toBe(20);
        expect(data.recentErrors).toBe(2);
        done();
      });
    });
  });

  describe('Application Result Updates', () => {
    beforeEach((done) => {
      const port = httpServer.address().port;
      clientSocket = new Client(`http://localhost:${port}`, {
        auth: {
          token: authToken
        }
      });

      clientSocket.on('connect', () => {
        done();
      });

      clientSocket.on('connect_error', done);
    });

    it('should receive application result updates', (done) => {
      setTimeout(() => {
        websocketService.sendApplicationResult(testUser._id.toString(), {
          applicationId: 'test-app-id',
          status: 'success',
          listingUrl: 'https://example.com/listing',
          submittedAt: new Date().toISOString()
        });
      }, 100);

      clientSocket.on('application:result', (data) => {
        expect(data.type).toBe('application-result');
        expect(data.applicationId).toBe('test-app-id');
        expect(data.status).toBe('success');
        expect(data.listingUrl).toBe('https://example.com/listing');
        done();
      });
    });

    it('should receive application progress updates', (done) => {
      setTimeout(() => {
        websocketService.sendApplicationProgress(testUser._id.toString(), {
          applicationId: 'test-app-id',
          step: 'form-filling',
          progress: 75,
          message: 'Filling application form'
        });
      }, 100);

      clientSocket.on('application:progress', (data) => {
        expect(data.type).toBe('application-progress');
        expect(data.applicationId).toBe('test-app-id');
        expect(data.step).toBe('form-filling');
        expect(data.progress).toBe(75);
        expect(data.message).toBe('Filling application form');
        done();
      });
    });
  });

  describe('System Alerts', () => {
    beforeEach((done) => {
      const port = httpServer.address().port;
      clientSocket = new Client(`http://localhost:${port}`, {
        auth: {
          token: authToken
        }
      });

      clientSocket.on('connect', () => {
        done();
      });

      clientSocket.on('connect_error', done);
    });

    it('should receive system alerts', (done) => {
      setTimeout(() => {
        websocketService.sendSystemAlert(testUser._id.toString(), {
          level: 'warning',
          title: 'Rate Limit Approaching',
          message: 'You have submitted 4 out of 5 daily applications',
          action: 'review-settings'
        });
      }, 100);

      clientSocket.on('system:alert', (data) => {
        expect(data.type).toBe('system-alert');
        expect(data.level).toBe('warning');
        expect(data.title).toBe('Rate Limit Approaching');
        expect(data.message).toBe('You have submitted 4 out of 5 daily applications');
        expect(data.action).toBe('review-settings');
        done();
      });
    });
  });

  describe('Document Updates', () => {
    beforeEach((done) => {
      const port = httpServer.address().port;
      clientSocket = new Client(`http://localhost:${port}`, {
        auth: {
          token: authToken
        }
      });

      clientSocket.on('connect', () => {
        done();
      });

      clientSocket.on('connect_error', done);
    });

    it('should receive document upload confirmations', (done) => {
      setTimeout(() => {
        websocketService.sendDocumentUpdate(testUser._id.toString(), {
          action: 'uploaded',
          documentId: 'test-doc-id',
          filename: 'income_proof.pdf',
          type: 'income_proof',
          profileCompleteness: 85
        });
      }, 100);

      clientSocket.on('document:update', (data) => {
        expect(data.type).toBe('document-update');
        expect(data.action).toBe('uploaded');
        expect(data.documentId).toBe('test-doc-id');
        expect(data.filename).toBe('income_proof.pdf');
        expect(data.type).toBe('income_proof');
        expect(data.profileCompleteness).toBe(85);
        done();
      });
    });
  });

  describe('Subscription Management', () => {
    beforeEach((done) => {
      const port = httpServer.address().port;
      clientSocket = new Client(`http://localhost:${port}`, {
        auth: {
          token: authToken
        }
      });

      clientSocket.on('connect', () => {
        done();
      });

      clientSocket.on('connect_error', done);
    });

    it('should handle unsubscribe from auto-application updates', (done) => {
      clientSocket.emit('subscribe:auto-application');
      
      setTimeout(() => {
        clientSocket.emit('unsubscribe:auto-application');
        
        // Send update after unsubscribe - should not receive
        websocketService.sendAutoApplicationUpdate(testUser._id.toString(), {
          action: 'test',
          message: 'Should not receive this'
        });
        
        // Wait and complete test if no message received
        setTimeout(() => {
          done();
        }, 200);
      }, 100);

      clientSocket.on('auto-application:status-update', () => {
        done(new Error('Should not receive update after unsubscribe'));
      });
    });

    it('should handle multiple subscriptions', (done) => {
      let receivedUpdates = 0;
      const expectedUpdates = 2;

      clientSocket.emit('subscribe:auto-application');
      clientSocket.emit('subscribe:queue-status');
      
      setTimeout(() => {
        websocketService.sendAutoApplicationUpdate(testUser._id.toString(), {
          action: 'test1'
        });
        
        websocketService.sendQueueUpdate(testUser._id.toString(), {
          pending: 1
        });
      }, 100);

      clientSocket.on('auto-application:status-update', () => {
        receivedUpdates++;
        if (receivedUpdates === expectedUpdates) {
          done();
        }
      });

      clientSocket.on('queue:status-update', () => {
        receivedUpdates++;
        if (receivedUpdates === expectedUpdates) {
          done();
        }
      });
    });
  });

  describe('Connection Management', () => {
    it('should track connected users', (done) => {
      const port = httpServer.address().port;
      clientSocket = new Client(`http://localhost:${port}`, {
        auth: {
          token: authToken
        }
      });

      clientSocket.on('connect', () => {
        expect(websocketService.isUserConnected(testUser._id.toString())).toBe(true);
        expect(websocketService.getConnectedUserCount()).toBeGreaterThan(0);
        done();
      });

      clientSocket.on('connect_error', done);
    });

    it('should handle disconnection', (done) => {
      const port = httpServer.address().port;
      clientSocket = new Client(`http://localhost:${port}`, {
        auth: {
          token: authToken
        }
      });

      clientSocket.on('connect', () => {
        expect(websocketService.isUserConnected(testUser._id.toString())).toBe(true);
        
        clientSocket.disconnect();
        
        setTimeout(() => {
          expect(websocketService.isUserConnected(testUser._id.toString())).toBe(false);
          done();
        }, 100);
      });

      clientSocket.on('connect_error', done);
    });
  });

  describe('Broadcast Messages', () => {
    beforeEach((done) => {
      const port = httpServer.address().port;
      clientSocket = new Client(`http://localhost:${port}`, {
        auth: {
          token: authToken
        }
      });

      clientSocket.on('connect', () => {
        done();
      });

      clientSocket.on('connect_error', done);
    });

    it('should receive system-wide notifications', (done) => {
      setTimeout(() => {
        websocketService.broadcastSystemNotification({
          title: 'System Maintenance',
          message: 'Scheduled maintenance in 30 minutes',
          level: 'info'
        });
      }, 100);

      clientSocket.on('system:notification', (data) => {
        expect(data.type).toBe('system-notification');
        expect(data.title).toBe('System Maintenance');
        expect(data.message).toBe('Scheduled maintenance in 30 minutes');
        expect(data.level).toBe('info');
        done();
      });
    });
  });
});