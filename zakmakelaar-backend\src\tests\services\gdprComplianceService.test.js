const gdprComplianceService = require('../../services/gdprComplianceService');
const User = require('../../models/User');
const AutoApplicationSettings = require('../../models/AutoApplicationSettings');
const ApplicationQueue = require('../../models/ApplicationQueue');
const ApplicationResult = require('../../models/ApplicationResult');
const Document = require('../../models/Document');
const mongoose = require('mongoose');

// Mock dependencies
jest.mock('../../services/auditLogService', () => ({
  logPrivacy: jest.fn(),
  getUserAuditLogs: jest.fn().mockResolvedValue([
    {
      id: 'log1',
      action: 'auto_application_enabled',
      category: 'auto_application',
      timestamp: new Date(),
      ipAddress: '***********'
    }
  ])
}));

jest.mock('../../services/encryptionService', () => ({
  encryptPersonalInfo: jest.fn((data) => ({ ...data, encrypted: true })),
  decryptPersonalInfo: jest.fn((data) => {
    const { encrypted, ...rest } = data;
    return rest;
  })
}));

jest.mock('../../services/documentVaultService', () => ({
  deleteDocument: jest.fn().mockResolvedValue(true)
}));

jest.mock('../../services/logger', () => ({
  loggers: {
    app: {
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn()
    }
  }
}));

describe('GDPRComplianceService', () => {
  let testUser;
  let testAutoAppSettings;

  beforeAll(async () => {
    await mongoose.connect(process.env.MONGO_URI || 'mongodb://localhost:27017/zakmakelaar-test');
  });

  afterAll(async () => {
    await mongoose.connection.dropDatabase();
    await mongoose.connection.close();
  });

  beforeEach(async () => {
    // Clear all collections
    await User.deleteMany({});
    await AutoApplicationSettings.deleteMany({});
    await ApplicationQueue.deleteMany({});
    await ApplicationResult.deleteMany({});
    await Document.deleteMany({});

    // Create test user
    testUser = new User({
      email: '<EMAIL>',
      password: 'hashedPassword',
      profile: {
        firstName: 'John',
        lastName: 'Doe',
        phoneNumber: '+31612345678'
      },
      preferences: {
        location: 'Amsterdam',
        budget: 2000
      },
      gdprConsent: {
        consents: new Map([
          ['data_processing', { granted: true, timestamp: new Date(), version: '1.0' }]
        ]),
        consentHistory: [],
        lastUpdated: new Date()
      }
    });
    await testUser.save();

    // Create test auto-application settings
    testAutoAppSettings = new AutoApplicationSettings({
      userId: testUser._id,
      enabled: true,
      personalInfo: {
        fullName: 'John Doe',
        email: '<EMAIL>',
        phone: '+31612345678',
        monthlyIncome: 5000
      },
      criteria: {
        maxPrice: 2000,
        minRooms: 2,
        maxRooms: 4
      }
    });
    await testAutoAppSettings.save();
  });

  describe('Consent Management', () => {
    test('should record user consent successfully', async () => {
      const consentRecord = await gdprComplianceService.recordConsent(
        testUser._id,
        'auto_application',
        true,
        {
          ipAddress: '***********',
          userAgent: 'Mozilla/5.0 Test',
          privacyPolicyVersion: '2.0',
          method: 'web_form'
        }
      );

      expect(consentRecord).toMatchObject({
        type: 'auto_application',
        granted: true,
        version: '2.0'
      });

      // Verify consent was saved to user
      const updatedUser = await User.findById(testUser._id);
      const autoAppConsent = updatedUser.gdprConsent.consents.get('auto_application');
      
      expect(autoAppConsent.granted).toBe(true);
      expect(autoAppConsent.version).toBe('2.0');
      expect(updatedUser.gdprConsent.consentHistory).toHaveLength(1);
    });

    test('should handle consent withdrawal', async () => {
      // First grant consent
      await gdprComplianceService.recordConsent(testUser._id, 'marketing', true);
      
      // Then withdraw it
      await gdprComplianceService.recordConsent(testUser._id, 'marketing', false);

      const consentStatus = await gdprComplianceService.getConsentStatus(testUser._id, 'marketing');
      expect(consentStatus.hasConsent).toBe(false);
    });

    test('should get consent status correctly', async () => {
      const consentStatus = await gdprComplianceService.getConsentStatus(testUser._id);
      
      expect(consentStatus.hasConsent).toBe(true);
      expect(consentStatus.consents).toHaveProperty('data_processing');
      expect(consentStatus.consents.data_processing.granted).toBe(true);
    });

    test('should check auto-application consent', async () => {
      // Initially no auto-application consent
      let hasConsent = await gdprComplianceService.hasAutoApplicationConsent(testUser._id);
      expect(hasConsent).toBe(false);

      // Grant auto-application consent
      await gdprComplianceService.recordConsent(testUser._id, 'auto_application', true);
      
      hasConsent = await gdprComplianceService.hasAutoApplicationConsent(testUser._id);
      expect(hasConsent).toBe(true);
    });

    test('should validate consent for operations', async () => {
      // Should pass for data processing (already granted)
      let isValid = await gdprComplianceService.validateConsentForOperation(testUser._id, 'profile_update');
      expect(isValid).toBe(true);

      // Should fail for auto-application (not granted)
      isValid = await gdprComplianceService.validateConsentForOperation(testUser._id, 'auto_application');
      expect(isValid).toBe(false);

      // Grant auto-application consent
      await gdprComplianceService.recordConsent(testUser._id, 'auto_application', true);
      
      isValid = await gdprComplianceService.validateConsentForOperation(testUser._id, 'auto_application');
      expect(isValid).toBe(true);
    });
  });

  describe('Data Export (Right to Access)', () => {
    beforeEach(async () => {
      // Create additional test data
      await new ApplicationQueue({
        userId: testUser._id,
        listingId: new mongoose.Types.ObjectId(),
        listingUrl: 'https://example.com/listing1',
        status: 'completed',
        priority: 1
      }).save();

      await new ApplicationResult({
        userId: testUser._id,
        listingId: new mongoose.Types.ObjectId(),
        status: 'submitted',
        submittedAt: new Date(),
        confirmationNumber: 'CONF123'
      }).save();

      await new Document({
        userId: testUser._id,
        filename: 'test-doc.pdf',
        originalName: 'test-document.pdf',
        type: 'income_proof',
        size: 1024,
        mimeType: 'application/pdf',
        encryptedPath: '/path/to/encrypted/file',
        encryptionKey: 'test-key'
      }).save();
    });

    test('should export complete user data', async () => {
      const exportData = await gdprComplianceService.exportUserData(testUser._id);

      expect(exportData.exportInfo.userId).toEqual(testUser._id);
      expect(exportData.exportInfo.dataCategories).toContain('personal_info');
      expect(exportData.exportInfo.dataCategories).toContain('auto_application_data');
      expect(exportData.exportInfo.dataCategories).toContain('application_history');

      // Check user data
      expect(exportData.userData.email).toBe('<EMAIL>');
      expect(exportData.userData.profile.firstName).toBe('John');

      // Check auto-application data
      expect(exportData.autoApplicationData.enabled).toBe(true);
      expect(exportData.autoApplicationData.personalInfo.fullName).toBe('John Doe');

      // Check application history
      expect(exportData.applicationHistory.queuedApplications).toHaveLength(1);
      expect(exportData.applicationHistory.applicationResults).toHaveLength(1);

      // Check documents
      expect(exportData.documents.documentList).toHaveLength(1);
      expect(exportData.documents.documentList[0].type).toBe('income_proof');

      // Check system logs
      expect(exportData.systemLogs.recentLogs).toHaveLength(1);

      // Check consent history
      expect(exportData.consentHistory.currentConsents).toHaveProperty('data_processing');
    });

    test('should export data in different formats', async () => {
      const jsonExport = await gdprComplianceService.exportUserData(testUser._id, { format: 'json' });
      expect(jsonExport.exportInfo.format).toBe('json');

      const csvExport = await gdprComplianceService.exportUserData(testUser._id, { format: 'csv' });
      expect(csvExport.exportInfo.format).toBe('csv');
    });

    test('should handle export with document inclusion', async () => {
      const exportData = await gdprComplianceService.exportUserData(testUser._id, { 
        includeDocuments: true 
      });

      expect(exportData.exportInfo.dataCategories).toContain('documents');
      expect(exportData.documents.note).toContain('Document files can be downloaded separately');
    });

    test('should handle user with no data gracefully', async () => {
      // Create user with minimal data
      const minimalUser = new User({
        email: '<EMAIL>',
        password: 'hashedPassword'
      });
      await minimalUser.save();

      const exportData = await gdprComplianceService.exportUserData(minimalUser._id);
      
      expect(exportData.exportInfo.userId).toEqual(minimalUser._id);
      expect(exportData.userData.email).toBe('<EMAIL>');
      expect(exportData.autoApplicationData).toEqual({});
    });
  });

  describe('Data Deletion (Right to Erasure)', () => {
    beforeEach(async () => {
      // Create test data to be deleted
      await new ApplicationQueue({
        userId: testUser._id,
        listingId: new mongoose.Types.ObjectId(),
        listingUrl: 'https://example.com/listing1',
        status: 'pending'
      }).save();

      await new ApplicationResult({
        userId: testUser._id,
        listingId: new mongoose.Types.ObjectId(),
        status: 'submitted',
        submittedAt: new Date()
      }).save();

      await new Document({
        userId: testUser._id,
        filename: 'test-doc.pdf',
        originalName: 'test-document.pdf',
        type: 'income_proof',
        size: 1024,
        mimeType: 'application/pdf',
        encryptedPath: '/path/to/encrypted/file',
        encryptionKey: 'test-key'
      }).save();
    });

    test('should delete all user data successfully', async () => {
      const deletionSummary = await gdprComplianceService.deleteUserData(testUser._id, {
        reason: 'user_request',
        keepAuditLogs: true,
        anonymizeInsteadOfDelete: false
      });

      expect(deletionSummary.userId).toEqual(testUser._id);
      expect(deletionSummary.reason).toBe('user_request');
      expect(deletionSummary.deletedCategories).toContain('personal_info');
      expect(deletionSummary.deletedCategories).toContain('auto_application_data');
      expect(deletionSummary.deletedCategories).toContain('application_history');
      expect(deletionSummary.deletedCategories).toContain('documents');

      // Verify data was actually deleted
      const deletedUser = await User.findById(testUser._id);
      expect(deletedUser).toBeNull();

      const deletedSettings = await AutoApplicationSettings.findOne({ userId: testUser._id });
      expect(deletedSettings).toBeNull();

      const deletedQueue = await ApplicationQueue.find({ userId: testUser._id });
      expect(deletedQueue).toHaveLength(0);

      const deletedResults = await ApplicationResult.find({ userId: testUser._id });
      expect(deletedResults).toHaveLength(0);

      const deletedDocs = await Document.find({ userId: testUser._id });
      expect(deletedDocs).toHaveLength(0);
    });

    test('should anonymize data instead of deleting when requested', async () => {
      const deletionSummary = await gdprComplianceService.deleteUserData(testUser._id, {
        anonymizeInsteadOfDelete: true
      });

      expect(deletionSummary.deletedCategories).toContain('personal_info');
      expect(deletionSummary.deletedCategories).toContain('auto_application_data');

      // Verify user still exists but is anonymized
      const anonymizedUser = await User.findById(testUser._id);
      expect(anonymizedUser).toBeTruthy();
      expect(anonymizedUser.email).toBe(`deleted-user-${testUser._id}@anonymized.local`);
      expect(anonymizedUser.profile.firstName).toBe('ANONYMIZED');

      // Verify auto-application settings are anonymized
      const anonymizedSettings = await AutoApplicationSettings.findOne({ userId: testUser._id });
      expect(anonymizedSettings).toBeTruthy();
      expect(anonymizedSettings.personalInfo.fullName).toBe('ANONYMIZED');
      expect(anonymizedSettings.enabled).toBe(false);
    });

    test('should handle deletion errors gracefully', async () => {
      // Mock document deletion to fail
      const documentVaultService = require('../../services/documentVaultService');
      documentVaultService.deleteDocument.mockRejectedValueOnce(new Error('Delete failed'));

      const deletionSummary = await gdprComplianceService.deleteUserData(testUser._id);

      expect(deletionSummary.errors).toHaveLength(1);
      expect(deletionSummary.errors[0]).toContain('Delete failed');
    });

    test('should handle audit log deletion when requested', async () => {
      const deletionSummary = await gdprComplianceService.deleteUserData(testUser._id, {
        keepAuditLogs: false
      });

      expect(deletionSummary.deletedCategories).toContain('system_logs');
    });
  });

  describe('Privacy Transparency', () => {
    test('should generate comprehensive privacy transparency report', async () => {
      const report = await gdprComplianceService.getPrivacyTransparencyReport(testUser._id);

      expect(report.userId).toEqual(testUser._id);
      expect(report.generatedAt).toBeInstanceOf(Date);

      // Check data processing purposes
      expect(report.dataProcessing.purposes).toHaveLength(3);
      expect(report.dataProcessing.purposes[0].purpose).toBe('Auto-application to rental properties');
      expect(report.dataProcessing.purposes[0].legalBasis).toContain('Consent');

      // Check data sharing information
      expect(report.dataSharing.thirdParties).toHaveLength(1);
      expect(report.dataSharing.thirdParties[0].name).toBe('Funda.nl');

      // Check user rights
      expect(report.userRights.access.available).toBe(true);
      expect(report.userRights.erasure.available).toBe(true);
      expect(report.userRights.portability.available).toBe(true);

      // Check consent status
      expect(report.consentStatus.hasConsent).toBe(true);

      // Check data retention policies
      expect(report.dataRetention.personalData).toBe('3 years after account deletion');
      expect(report.dataRetention.auditLogs).toBe('7 years (legal requirement)');

      // Check contact information
      expect(report.contact.dataProtectionOfficer).toBe('<EMAIL>');
      expect(report.contact.supervisoryAuthority).toBe('Autoriteit Persoonsgegevens (AP)');
    });

    test('should handle user not found error', async () => {
      const nonExistentUserId = new mongoose.Types.ObjectId();
      
      await expect(
        gdprComplianceService.getPrivacyTransparencyReport(nonExistentUserId)
      ).rejects.toThrow('User not found');
    });
  });

  describe('Consent Validation', () => {
    test('should validate consent for different operations', async () => {
      // Grant specific consents
      await gdprComplianceService.recordConsent(testUser._id, 'auto_application', true);
      await gdprComplianceService.recordConsent(testUser._id, 'marketing', true);

      // Test various operations
      expect(await gdprComplianceService.validateConsentForOperation(testUser._id, 'auto_application')).toBe(true);
      expect(await gdprComplianceService.validateConsentForOperation(testUser._id, 'marketing_email')).toBe(true);
      expect(await gdprComplianceService.validateConsentForOperation(testUser._id, 'document_upload')).toBe(true);
      expect(await gdprComplianceService.validateConsentForOperation(testUser._id, 'analytics')).toBe(false);
    });

    test('should handle missing consent gracefully', async () => {
      const userWithoutConsent = new User({
        email: '<EMAIL>',
        password: 'hashedPassword'
      });
      await userWithoutConsent.save();

      const hasConsent = await gdprComplianceService.hasAutoApplicationConsent(userWithoutConsent._id);
      expect(hasConsent).toBe(false);

      const isValid = await gdprComplianceService.validateConsentForOperation(
        userWithoutConsent._id, 
        'auto_application'
      );
      expect(isValid).toBe(false);
    });
  });

  describe('Error Handling', () => {
    test('should handle invalid user ID in consent recording', async () => {
      const invalidUserId = new mongoose.Types.ObjectId();
      
      await expect(
        gdprComplianceService.recordConsent(invalidUserId, 'data_processing', true)
      ).rejects.toThrow('User not found');
    });

    test('should handle database errors gracefully', async () => {
      // Mock database error
      const originalFindById = User.findById;
      User.findById = jest.fn().mockRejectedValue(new Error('Database error'));

      await expect(
        gdprComplianceService.getConsentStatus(testUser._id)
      ).rejects.toThrow('Database error');

      // Restore original method
      User.findById = originalFindById;
    });

    test('should handle export errors', async () => {
      // Mock encryption service error
      const encryptionService = require('../../services/encryptionService');
      encryptionService.decryptPersonalInfo.mockImplementationOnce(() => {
        throw new Error('Decryption failed');
      });

      // Should still complete export but log error
      const exportData = await gdprComplianceService.exportUserData(testUser._id);
      expect(exportData.exportInfo.userId).toEqual(testUser._id);
    });
  });

  describe('Data Anonymization', () => {
    test('should properly anonymize personal information', async () => {
      const personalInfo = {
        fullName: 'John Doe',
        email: '<EMAIL>',
        phone: '+31612345678',
        monthlyIncome: 5000,
        guarantorInfo: {
          name: 'Jane Doe',
          email: '<EMAIL>'
        }
      };

      // Access private method through service instance
      const anonymized = gdprComplianceService._anonymizePersonalInfo(personalInfo);

      expect(anonymized.fullName).toBe('ANONYMIZED');
      expect(anonymized.email).toBe('<EMAIL>');
      expect(anonymized.phone).toBe('ANONYMIZED');
      expect(anonymized.monthlyIncome).toBe(0);
      expect(anonymized.guarantorInfo).toEqual({});
    });

    test('should properly anonymize user profile', async () => {
      const profile = {
        firstName: 'John',
        lastName: 'Doe',
        phoneNumber: '+31612345678',
        employment: {
          occupation: 'Developer',
          employer: 'Tech Corp',
          monthlyIncome: 5000
        }
      };

      const anonymized = gdprComplianceService._anonymizeUserProfile(profile);

      expect(anonymized.firstName).toBe('ANONYMIZED');
      expect(anonymized.lastName).toBe('USER');
      expect(anonymized.phoneNumber).toBe('ANONYMIZED');
      expect(anonymized.employment.occupation).toBe('ANONYMIZED');
      expect(anonymized.employment.employer).toBe('ANONYMIZED');
      expect(anonymized.employment.monthlyIncome).toBe(0);
    });
  });
});