import React, { useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import * as Haptics from 'expo-haptics';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
  withRepeat,
  withSequence,
  Easing,
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import { useAIStore } from '../../store/aiStore';

// Define theme colors
const THEME = {
  primary: '#4361ee',
  secondary: '#7209b7',
  accent: '#f72585',
  dark: '#0a0a18',
  light: '#ffffff',
  gray: '#6b7280',
  lightGray: '#f3f4f6',
  success: '#10b981',
  warning: '#f59e0b',
  danger: '#ef4444',
};

interface AutonomousStatusIndicatorProps {
  compact?: boolean;
  showControls?: boolean;
  onPress?: () => void;
}

export const AutonomousStatusIndicator: React.FC<AutonomousStatusIndicatorProps> = ({
  compact = false,
  showControls = false,
  onPress,
}) => {
  const router = useRouter();
  const {
    autonomousStatus,
    pauseAutonomousMode,
    resumeAutonomousMode,
  } = useAIStore();
  
  const pulseScale = useSharedValue(1);
  const glowOpacity = useSharedValue(0.5);
  
  useEffect(() => {
    if (autonomousStatus.isActive) {
      // Pulse animation for active status
      pulseScale.value = withRepeat(
        withSequence(
          withTiming(1.05, { duration: 1500, easing: Easing.inOut(Easing.ease) }),
          withTiming(1, { duration: 1500, easing: Easing.inOut(Easing.ease) })
        ),
        -1,
        true
      );
      
      // Glow animation
      glowOpacity.value = withRepeat(
        withSequence(
          withTiming(0.8, { duration: 2000, easing: Easing.inOut(Easing.ease) }),
          withTiming(0.5, { duration: 2000, easing: Easing.inOut(Easing.ease) })
        ),
        -1,
        true
      );
    } else {
      pulseScale.value = withTiming(1, { duration: 300 });
      glowOpacity.value = withTiming(0, { duration: 300 });
    }
  }, [autonomousStatus.isActive]);
  
  const pulseStyle = useAnimatedStyle(() => ({
    transform: [{ scale: pulseScale.value }],
  }));
  
  const glowStyle = useAnimatedStyle(() => ({
    opacity: glowOpacity.value,
  }));
  
  const handlePress = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    if (onPress) {
      onPress();
    } else {
      router.push('/autonomous-status');
    }
  };
  
  const handlePause = async () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    try {
      await pauseAutonomousMode('Manually paused by user');
    } catch (error) {
      console.error('Failed to pause autonomous mode:', error);
    }
  };
  
  const handleResume = async () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    try {
      await resumeAutonomousMode();
    } catch (error) {
      console.error('Failed to resume autonomous mode:', error);
    }
  };
  
  if (compact) {
    return (
      <TouchableOpacity
        style={styles.compactContainer}
        onPress={handlePress}
        activeOpacity={0.8}
      >
        <Animated.View style={[styles.compactIndicator, pulseStyle]}>
          {autonomousStatus.isActive && (
            <Animated.View style={[styles.compactGlow, glowStyle]}>
              <LinearGradient
                colors={[THEME.success, THEME.primary]}
                style={styles.compactGlowGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
              />
            </Animated.View>
          )}
          
          <View style={[
            styles.compactDot,
            { backgroundColor: autonomousStatus.isActive ? THEME.success : THEME.gray }
          ]}>
            <Ionicons 
              name={autonomousStatus.isActive ? "flash" : "flash-off"} 
              size={12} 
              color={THEME.light} 
            />
          </View>
        </Animated.View>
        
        <Text style={[
          styles.compactText,
          { color: autonomousStatus.isActive ? THEME.success : THEME.gray }
        ]}>
          Auto
        </Text>
      </TouchableOpacity>
    );
  }
  
  return (
    <TouchableOpacity
      style={styles.container}
      onPress={handlePress}
      activeOpacity={0.9}
    >
      {/* Glow effect for active status */}
      {autonomousStatus.isActive && (
        <Animated.View style={[styles.glow, glowStyle]}>
          <LinearGradient
            colors={[THEME.success, THEME.primary]}
            style={styles.glowGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          />
        </Animated.View>
      )}
      
      <LinearGradient
        colors={autonomousStatus.isActive ? [THEME.success, THEME.primary] : [THEME.lightGray, THEME.gray]}
        style={styles.gradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
      >
        <View style={styles.content}>
          <Animated.View style={[styles.iconContainer, pulseStyle]}>
            <View style={[
              styles.icon,
              { backgroundColor: autonomousStatus.isActive ? THEME.light : THEME.dark }
            ]}>
              <Ionicons 
                name={autonomousStatus.isActive ? "flash" : "flash-off"} 
                size={20} 
                color={autonomousStatus.isActive ? THEME.success : THEME.light} 
              />
            </View>
          </Animated.View>
          
          <View style={styles.info}>
            <Text style={[
              styles.title,
              { color: autonomousStatus.isActive ? THEME.light : THEME.dark }
            ]}>
              Autonomous Mode
            </Text>
            <Text style={[
              styles.subtitle,
              { color: autonomousStatus.isActive ? 'rgba(255,255,255,0.9)' : THEME.gray }
            ]}>
              {autonomousStatus.isActive ? 'Active' : autonomousStatus.pausedReason || 'Inactive'}
            </Text>
            {autonomousStatus.currentActivity && (
              <Text style={[
                styles.activity,
                { color: autonomousStatus.isActive ? 'rgba(255,255,255,0.8)' : THEME.gray }
              ]}>
                {autonomousStatus.currentActivity}
              </Text>
            )}
          </View>
          
          {showControls && (
            <View style={styles.controls}>
              {autonomousStatus.isActive ? (
                <TouchableOpacity
                  style={[styles.controlButton, styles.pauseButton]}
                  onPress={handlePause}
                >
                  <Ionicons name="pause" size={16} color={THEME.light} />
                </TouchableOpacity>
              ) : (
                <TouchableOpacity
                  style={[styles.controlButton, styles.resumeButton]}
                  onPress={handleResume}
                >
                  <Ionicons name="play" size={16} color={THEME.light} />
                </TouchableOpacity>
              )}
            </View>
          )}
          
          <View style={styles.metrics}>
            <View style={styles.metric}>
              <Text style={[
                styles.metricValue,
                { color: autonomousStatus.isActive ? THEME.light : THEME.dark }
              ]}>
                {autonomousStatus.applicationsToday}
              </Text>
              <Text style={[
                styles.metricLabel,
                { color: autonomousStatus.isActive ? 'rgba(255,255,255,0.8)' : THEME.gray }
              ]}>
                Today
              </Text>
            </View>
          </View>
        </View>
      </LinearGradient>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  // Compact styles
  compactContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  compactIndicator: {
    position: 'relative',
    marginRight: 6,
  },
  compactGlow: {
    position: 'absolute',
    top: -2,
    left: -2,
    right: -2,
    bottom: -2,
    borderRadius: 10,
    overflow: 'hidden',
  },
  compactGlowGradient: {
    width: '100%',
    height: '100%',
  },
  compactDot: {
    width: 16,
    height: 16,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  compactText: {
    fontSize: 12,
    fontWeight: '600',
  },
  
  // Full styles
  container: {
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 12,
    overflow: 'hidden',
    position: 'relative',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  glow: {
    position: 'absolute',
    top: -2,
    left: -2,
    right: -2,
    bottom: -2,
    borderRadius: 14,
    overflow: 'hidden',
  },
  glowGradient: {
    width: '100%',
    height: '100%',
  },
  gradient: {
    padding: 16,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    marginRight: 12,
  },
  icon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  info: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  subtitle: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 2,
  },
  activity: {
    fontSize: 12,
    fontStyle: 'italic',
  },
  controls: {
    marginHorizontal: 12,
  },
  controlButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  pauseButton: {
    backgroundColor: THEME.warning,
  },
  resumeButton: {
    backgroundColor: THEME.success,
  },
  metrics: {
    alignItems: 'center',
  },
  metric: {
    alignItems: 'center',
  },
  metricValue: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 2,
  },
  metricLabel: {
    fontSize: 10,
    fontWeight: '500',
  },
});