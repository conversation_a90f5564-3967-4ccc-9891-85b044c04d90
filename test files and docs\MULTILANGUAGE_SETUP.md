# Multilanguage Setup with react-i18next

This project now supports multiple languages using react-i18next. Currently supported languages are:
- Dutch (nl) - Default
- English (en)

## Installation

The required dependencies have been added to package.json:
- `i18next`: Core internationalization library
- `react-i18next`: React bindings for i18next

Run `npm install` to install the new dependencies.

## Project Structure

```
zakmakelaar-frontend/
├── i18n/
│   ├── index.ts              # i18n configuration
│   └── locales/
│       ├── en.json          # English translations
│       └── nl.json          # Dutch translations
├── components/
│   ├── LanguageSelector.tsx  # Language switcher component
│   └── TranslationExample.tsx # Example usage component
├── hooks/
│   └── useTranslation.ts     # Custom translation hook
└── utils/
    └── translationUtils.ts   # Translation utilities
```

## Usage

### Basic Translation

```tsx
import { useTranslation } from '@/hooks/useTranslation';

const MyComponent = () => {
  const { t } = useTranslation();
  
  return (
    <Text>{t('common.loading')}</Text>
  );
};
```

### Language Selector

```tsx
import LanguageSelector from '@/components/LanguageSelector';

const SettingsScreen = () => {
  return (
    <View>
      <LanguageSelector />
    </View>
  );
};
```

### Programmatic Language Change

```tsx
import { useTranslation } from '@/hooks/useTranslation';

const MyComponent = () => {
  const { changeLanguage } = useTranslation();
  
  const switchToEnglish = () => {
    changeLanguage('en');
  };
  
  return (
    <TouchableOpacity onPress={switchToEnglish}>
      <Text>Switch to English</Text>
    </TouchableOpacity>
  );
};
```

## Available Translation Keys

### Common
- `common.loading`, `common.error`, `common.success`
- `common.cancel`, `common.confirm`, `common.save`
- `common.delete`, `common.edit`, `common.add`

### Navigation
- `navigation.home`, `navigation.properties`, `navigation.favorites`
- `navigation.profile`, `navigation.settings`

### Authentication
- `auth.login`, `auth.logout`, `auth.register`
- `auth.email`, `auth.password`, `auth.confirmPassword`

### Properties
- `properties.title`, `properties.price`, `properties.location`
- `properties.bedrooms`, `properties.bathrooms`, `properties.area`
- `properties.propertyTypes.apartment`, `properties.propertyTypes.house`
- `properties.filters.priceRange`, `properties.filters.furnished`

### Settings
- `settings.title`, `settings.language`, `settings.notifications`
- `settings.privacy`, `settings.terms`, `settings.about`

## Adding New Translations

### 1. Add to Translation Files

Add new keys to both `i18n/locales/en.json` and `i18n/locales/nl.json`:

```json
// en.json
{
  "newSection": {
    "newKey": "English text"
  }
}

// nl.json
{
  "newSection": {
    "newKey": "Nederlandse tekst"
  }
}
```

### 2. Use in Components

```tsx
const { t } = useTranslation();
return <Text>{t('newSection.newKey')}</Text>;
```

## Utility Functions

### Currency Formatting
```tsx
import { formatCurrency } from '@/utils/translationUtils';

const price = formatCurrency(500000); // €500.000 (NL) or €500,000 (EN)
```

### Date Formatting
```tsx
import { formatDate } from '@/utils/translationUtils';

const date = formatDate(new Date()); // Localized date format
```

### Adding Runtime Translations
```tsx
import { addTranslations } from '@/utils/translationUtils';

addTranslations('en', 'newFeature', {
  title: 'New Feature',
  description: 'This is a new feature'
});
```

## Language Persistence

The selected language is automatically saved to AsyncStorage and restored when the app restarts.

## Configuration

The i18n configuration is in `i18n/index.ts`:
- Default language: Dutch (nl)
- Fallback language: Dutch (nl)
- Debug mode: Enabled in development
- Language detection: AsyncStorage with fallback to Dutch

## Testing

A test component `TranslationExample.tsx` is available to see all translations in action.

## Best Practices

1. **Namespace your keys**: Use nested objects to organize translations
2. **Use descriptive keys**: `auth.loginButton` instead of `button1`
3. **Keep translations short**: Mobile screens have limited space
4. **Test both languages**: Always verify translations in both languages
5. **Use interpolation**: For dynamic content like `t('welcome', { name: 'John' })`

## Adding New Languages

To add a new language (e.g., German):

1. Create `i18n/locales/de.json` with all translation keys
2. Import and add to resources in `i18n/index.ts`:
   ```ts
   import de from './locales/de.json';
   
   resources: {
     en: { translation: en },
     nl: { translation: nl },
     de: { translation: de },
   }
   ```
3. Update `LanguageSelector.tsx` to include the new language option
4. Add the language to `languages` section in translation files

## Troubleshooting

- **Missing translations**: Check browser/debugger console for missing key warnings
- **Language not changing**: Ensure AsyncStorage permissions are granted
- **Translations not loading**: Verify import paths and JSON syntax
- **Performance issues**: Consider lazy loading for large translation files