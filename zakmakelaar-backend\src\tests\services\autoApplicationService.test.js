const autoApplicationService = require('../../services/autoApplicationService');
const AutoApplicationSettings = require('../../models/AutoApplicationSettings');
const ApplicationQueue = require('../../models/ApplicationQueue');
const ApplicationResult = require('../../models/ApplicationResult');
const User = require('../../models/User');
const Listing = require('../../models/Listing');
const aiService = require('../../services/aiService');

// Mock dependencies
jest.mock('../../models/AutoApplicationSettings');
jest.mock('../../models/ApplicationQueue');
jest.mock('../../models/ApplicationResult');
jest.mock('../../models/User');
jest.mock('../../models/Listing');
jest.mock('../../services/aiService');
jest.mock('../../services/logger', () => ({
  loggers: {
    app: {
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn()
    }
  }
}));

describe('AutoApplicationService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('enableAutoApplication', () => {
    const mockUserId = '507f1f77bcf86cd799439011';
    const mockUser = {
      _id: mockUserId,
      email: '<EMAIL>',
      profile: {
        fullName: 'Test User',
        employment: {
          occupation: 'Software Developer',
          monthlyIncome: 5000
        }
      }
    };

    const mockSettings = {
      maxApplicationsPerDay: 5,
      applicationTemplate: 'professional',
      autoSubmit: true,
      requireManualReview: false,
      notificationPreferences: {
        immediate: true,
        daily: true,
        weekly: false
      },
      language: 'english',
      criteria: {
        maxPrice: 2000,
        minRooms: 2,
        maxRooms: 4,
        propertyTypes: ['apartment'],
        locations: ['Amsterdam'],
        excludeKeywords: ['shared'],
        includeKeywords: ['balcony']
      },
      personalInfo: {
        fullName: 'Test User',
        email: '<EMAIL>',
        phone: '+31612345678',
        monthlyIncome: 5000,
        occupation: 'Software Developer'
      }
    };

    it('should enable auto-application for new user', async () => {
      User.findById.mockResolvedValue(mockUser);
      AutoApplicationSettings.findByUserId.mockResolvedValue(null);
      
      const mockAutoSettings = {
        _id: 'settings123',
        userId: mockUserId,
        enabled: true,
        settings: {},
        criteria: {},
        personalInfo: {},
        documents: [],
        statistics: {},
        status: {},
        canAutoApply: true,
        dailyApplicationsRemaining: 5,
        isProfileComplete: true,
        documentsComplete: false,
        createdAt: new Date(),
        updatedAt: new Date(),
        save: jest.fn().mockResolvedValue(true)
      };
      
      AutoApplicationSettings.mockImplementation(() => mockAutoSettings);

      const result = await autoApplicationService.enableAutoApplication(mockUserId, mockSettings);

      expect(User.findById).toHaveBeenCalledWith(mockUserId);
      expect(AutoApplicationSettings.findByUserId).toHaveBeenCalledWith(mockUserId);
      expect(mockAutoSettings.save).toHaveBeenCalled();
      expect(result).toHaveProperty('enabled', true);
      expect(result).toHaveProperty('userId', mockUserId);
    });

    it('should update existing auto-application settings', async () => {
      User.findById.mockResolvedValue(mockUser);
      
      const existingSettings = {
        _id: 'settings123',
        userId: mockUserId,
        enabled: false,
        settings: {},
        criteria: {},
        personalInfo: {},
        documents: [],
        statistics: {},
        status: {},
        canAutoApply: true,
        dailyApplicationsRemaining: 5,
        isProfileComplete: true,
        documentsComplete: false,
        createdAt: new Date(),
        updatedAt: new Date(),
        save: jest.fn().mockResolvedValue(true)
      };
      
      AutoApplicationSettings.findByUserId.mockResolvedValue(existingSettings);

      const result = await autoApplicationService.enableAutoApplication(mockUserId, mockSettings);

      expect(existingSettings.enabled).toBe(true);
      expect(existingSettings.save).toHaveBeenCalled();
      expect(result).toHaveProperty('enabled', true);
    });

    it('should throw error if user not found', async () => {
      User.findById.mockResolvedValue(null);

      await expect(
        autoApplicationService.enableAutoApplication(mockUserId, mockSettings)
      ).rejects.toThrow('User not found');
    });

    it('should validate settings and throw error for invalid data', async () => {
      User.findById.mockResolvedValue(mockUser);
      
      const invalidSettings = {
        ...mockSettings,
        maxApplicationsPerDay: 25 // Exceeds maximum
      };

      await expect(
        autoApplicationService.enableAutoApplication(mockUserId, invalidSettings)
      ).rejects.toThrow('Max applications per day must be between 1 and 20');
    });

    it('should validate criteria and throw error for missing max price', async () => {
      User.findById.mockResolvedValue(mockUser);
      
      const invalidSettings = {
        ...mockSettings,
        criteria: {
          ...mockSettings.criteria,
          maxPrice: undefined
        }
      };

      await expect(
        autoApplicationService.enableAutoApplication(mockUserId, invalidSettings)
      ).rejects.toThrow('Max price is required and must be greater than 0');
    });
  });

  describe('disableAutoApplication', () => {
    const mockUserId = '507f1f77bcf86cd799439011';

    it('should disable auto-application successfully', async () => {
      const mockAutoSettings = {
        _id: 'settings123',
        userId: mockUserId,
        enabled: true,
        settings: {},
        criteria: {},
        personalInfo: {},
        documents: [],
        statistics: {},
        status: {},
        canAutoApply: true,
        dailyApplicationsRemaining: 5,
        isProfileComplete: true,
        documentsComplete: false,
        createdAt: new Date(),
        updatedAt: new Date(),
        save: jest.fn().mockResolvedValue(true)
      };

      AutoApplicationSettings.findByUserId.mockResolvedValue(mockAutoSettings);
      ApplicationQueue.updateMany.mockResolvedValue({ modifiedCount: 2 });

      const result = await autoApplicationService.disableAutoApplication(mockUserId);

      expect(mockAutoSettings.enabled).toBe(false);
      expect(mockAutoSettings.status.isActive).toBe(false);
      expect(mockAutoSettings.status.pausedReason).toBe('Disabled by user');
      expect(mockAutoSettings.save).toHaveBeenCalled();
      expect(ApplicationQueue.updateMany).toHaveBeenCalledWith(
        { userId: mockUserId, status: 'pending' },
        { status: 'cancelled', processedAt: expect.any(Date) }
      );
    });

    it('should throw error if settings not found', async () => {
      AutoApplicationSettings.findByUserId.mockResolvedValue(null);

      await expect(
        autoApplicationService.disableAutoApplication(mockUserId)
      ).rejects.toThrow('Auto-application settings not found');
    });
  });

  describe('updateUserSettings', () => {
    const mockUserId = '507f1f77bcf86cd799439011';

    it('should update user settings successfully', async () => {
      const existingSettings = {
        _id: 'settings123',
        userId: mockUserId,
        settings: {
          maxApplicationsPerDay: 5,
          applicationTemplate: 'professional'
        },
        criteria: {
          maxPrice: 2000
        },
        personalInfo: {
          fullName: 'Test User'
        },
        documents: [],
        statistics: {},
        status: {},
        canAutoApply: true,
        dailyApplicationsRemaining: 5,
        isProfileComplete: true,
        documentsComplete: false,
        createdAt: new Date(),
        updatedAt: new Date(),
        save: jest.fn().mockResolvedValue(true)
      };

      AutoApplicationSettings.findByUserId.mockResolvedValue(existingSettings);

      const updates = {
        settings: {
          maxApplicationsPerDay: 8,
          applicationTemplate: 'casual'
        },
        criteria: {
          maxPrice: 2500,
          minRooms: 3
        }
      };

      const result = await autoApplicationService.updateUserSettings(mockUserId, updates);

      expect(existingSettings.settings.maxApplicationsPerDay).toBe(8);
      expect(existingSettings.settings.applicationTemplate).toBe('casual');
      expect(existingSettings.criteria.maxPrice).toBe(2500);
      expect(existingSettings.criteria.minRooms).toBe(3);
      expect(existingSettings.save).toHaveBeenCalled();
    });

    it('should enforce maximum daily applications limit', async () => {
      const existingSettings = {
        _id: 'settings123',
        userId: mockUserId,
        settings: {
          maxApplicationsPerDay: 5
        },
        criteria: {},
        personalInfo: {},
        documents: [],
        statistics: {},
        status: {},
        canAutoApply: true,
        dailyApplicationsRemaining: 5,
        isProfileComplete: true,
        documentsComplete: false,
        createdAt: new Date(),
        updatedAt: new Date(),
        save: jest.fn().mockResolvedValue(true)
      };

      AutoApplicationSettings.findByUserId.mockResolvedValue(existingSettings);

      const updates = {
        settings: {
          maxApplicationsPerDay: 25 // Exceeds maximum
        }
      };

      await expect(
        autoApplicationService.updateUserSettings(mockUserId, updates)
      ).rejects.toThrow('Max applications per day must be between 1 and 20');
    });
  });

  describe('processNewListing', () => {
    const mockListing = {
      _id: 'listing123',
      title: 'Nice Apartment',
      price: '€ 1,800 per maand',
      location: 'Amsterdam',
      url: 'https://example.com/listing123',
      propertyType: 'apartment',
      rooms: '3',
      dateAdded: new Date()
    };

    it('should create applications for matching users', async () => {
      const mockActiveUsers = [
        {
          userId: 'user1',
          settings: {
            maxApplicationsPerDay: 5,
            applicationTemplate: 'professional',
            language: 'english'
          },
          criteria: {
            maxPrice: 2000,
            propertyTypes: ['apartment'],
            locations: ['Amsterdam']
          },
          personalInfo: {
            fullName: 'User One',
            monthlyIncome: 5000
          },
          documents: [],
          statistics: {
            dailyApplicationCount: 2
          },
          matchesCriteria: jest.fn().mockReturnValue(true)
        }
      ];

      const mockUser = {
        _id: 'user1',
        email: '<EMAIL>',
        profile: {
          fullName: 'User One',
          employment: {
            occupation: 'Developer'
          }
        }
      };

      const mockQueueItem = {
        _id: 'queue123',
        userId: 'user1',
        listingId: 'listing123',
        save: jest.fn().mockResolvedValue(true)
      };

      AutoApplicationSettings.findActiveUsers.mockResolvedValue(mockActiveUsers);
      ApplicationQueue.findOne.mockResolvedValue(null); // No existing application
      User.findById.mockResolvedValue(mockUser);
      aiService.generateAutoApplicationLetter.mockResolvedValue({
        subject: 'Application for Nice Apartment',
        message: 'I am interested in this property...',
        personalizedElements: ['Property-specific details'],
        template: 'professional',
        language: 'dutch',
        wordCount: 150,
        estimatedReadTime: 1,
        generatedAt: new Date().toISOString()
      });
      ApplicationQueue.mockImplementation(() => mockQueueItem);

      const result = await autoApplicationService.processNewListing(mockListing);

      expect(AutoApplicationSettings.findActiveUsers).toHaveBeenCalled();
      expect(mockActiveUsers[0].matchesCriteria).toHaveBeenCalledWith(mockListing);
      expect(ApplicationQueue.findOne).toHaveBeenCalledWith({
        userId: 'user1',
        listingId: 'listing123'
      });
      expect(aiService.generateAutoApplicationLetter).toHaveBeenCalled();
      expect(mockQueueItem.save).toHaveBeenCalled();
      expect(result).toHaveLength(1);
    });

    it('should skip users who have reached daily limit', async () => {
      const mockActiveUsers = [
        {
          userId: 'user1',
          settings: {
            maxApplicationsPerDay: 5
          },
          statistics: {
            dailyApplicationCount: 5 // At limit
          },
          matchesCriteria: jest.fn().mockReturnValue(true)
        }
      ];

      AutoApplicationSettings.findActiveUsers.mockResolvedValue(mockActiveUsers);

      const result = await autoApplicationService.processNewListing(mockListing);

      expect(result).toHaveLength(0);
      expect(mockActiveUsers[0].matchesCriteria).not.toHaveBeenCalled();
    });

    it('should skip users whose criteria do not match', async () => {
      const mockActiveUsers = [
        {
          userId: 'user1',
          settings: {
            maxApplicationsPerDay: 5
          },
          statistics: {
            dailyApplicationCount: 2
          },
          matchesCriteria: jest.fn().mockReturnValue(false) // Does not match
        }
      ];

      AutoApplicationSettings.findActiveUsers.mockResolvedValue(mockActiveUsers);

      const result = await autoApplicationService.processNewListing(mockListing);

      expect(result).toHaveLength(0);
      expect(mockActiveUsers[0].matchesCriteria).toHaveBeenCalledWith(mockListing);
    });

    it('should skip users who already applied to the listing', async () => {
      const mockActiveUsers = [
        {
          userId: 'user1',
          settings: {
            maxApplicationsPerDay: 5
          },
          statistics: {
            dailyApplicationCount: 2
          },
          matchesCriteria: jest.fn().mockReturnValue(true)
        }
      ];

      const existingApplication = {
        _id: 'existing123',
        userId: 'user1',
        listingId: 'listing123'
      };

      AutoApplicationSettings.findActiveUsers.mockResolvedValue(mockActiveUsers);
      ApplicationQueue.findOne.mockResolvedValue(existingApplication);

      const result = await autoApplicationService.processNewListing(mockListing);

      expect(result).toHaveLength(0);
    });

    it('should use fallback content when AI service fails', async () => {
      // For this test, let's just verify that the fallback method works
      // by testing it directly rather than through the complex processNewListing flow
      const mockListing = {
        _id: 'listing123',
        title: 'Nice Apartment',
        location: 'Amsterdam'
      };

      const mockUser = {
        _id: 'user1',
        email: '<EMAIL>',
        profile: {
          fullName: 'User One'
        }
      };

      const mockSettings = {
        personalInfo: {
          fullName: 'User One'
        },
        settings: {
          applicationTemplate: 'professional',
          language: 'english'
        }
      };

      // Test the fallback content generation directly
      const fallbackContent = autoApplicationService._generateFallbackContent(mockListing, mockUser, mockSettings);

      expect(fallbackContent).toHaveProperty('subject');
      expect(fallbackContent).toHaveProperty('message');
      expect(fallbackContent).toHaveProperty('template', 'fallback');
      expect(fallbackContent.subject).toContain('Nice Apartment');
      expect(fallbackContent.message).toContain('User One');
      expect(fallbackContent.message).toContain('Amsterdam');
    });
  });

  describe('getUserSettings', () => {
    const mockUserId = '507f1f77bcf86cd799439011';

    it('should return user settings if they exist', async () => {
      const mockAutoSettings = {
        _id: 'settings123',
        userId: mockUserId,
        enabled: true,
        settings: {
          maxApplicationsPerDay: 5
        },
        criteria: {
          maxPrice: 2000
        },
        personalInfo: {
          fullName: 'Test User',
          monthlyIncome: 5000
        },
        documents: [],
        statistics: {},
        status: {},
        canAutoApply: true,
        dailyApplicationsRemaining: 3,
        isProfileComplete: true,
        documentsComplete: false,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      AutoApplicationSettings.findByUserId.mockResolvedValue(mockAutoSettings);

      const result = await autoApplicationService.getUserSettings(mockUserId);

      expect(AutoApplicationSettings.findByUserId).toHaveBeenCalledWith(mockUserId);
      expect(result).toHaveProperty('enabled', true);
      expect(result).toHaveProperty('userId', mockUserId);
      expect(result.personalInfo.monthlyIncome).toBe('[HIDDEN]'); // Should hide sensitive info
    });

    it('should return null if settings do not exist', async () => {
      AutoApplicationSettings.findByUserId.mockResolvedValue(null);

      const result = await autoApplicationService.getUserSettings(mockUserId);

      expect(result).toBeNull();
    });
  });

  describe('getApplicationStatus', () => {
    const mockUserId = '507f1f77bcf86cd799439011';

    it('should return application status with recent applications', async () => {
      const mockAutoSettings = {
        _id: 'settings123',
        userId: mockUserId,
        enabled: true,
        status: {
          isActive: true
        },
        canAutoApply: true,
        dailyApplicationsRemaining: 3,
        statistics: {
          totalApplications: 10,
          successfulApplications: 8,
          pendingApplications: 1,
          dailyApplicationCount: 2,
          successRate: 80
        }
      };

      const mockRecentApplications = [
        {
          _id: 'app1',
          listingId: {
            title: 'Nice Apartment',
            location: 'Amsterdam',
            price: '€ 1,800'
          },
          status: 'completed',
          scheduledAt: new Date(),
          processedAt: new Date(),
          attempts: 1
        }
      ];

      AutoApplicationSettings.findByUserId.mockResolvedValue(mockAutoSettings);
      ApplicationQueue.find.mockReturnValue({
        sort: jest.fn().mockReturnValue({
          limit: jest.fn().mockReturnValue({
            populate: jest.fn().mockResolvedValue(mockRecentApplications)
          })
        })
      });

      const result = await autoApplicationService.getApplicationStatus(mockUserId);

      expect(result).toHaveProperty('enabled', true);
      expect(result).toHaveProperty('isActive', true);
      expect(result).toHaveProperty('statistics');
      expect(result.recentApplications).toHaveLength(1);
      expect(result.recentApplications[0]).toHaveProperty('listingTitle', 'Nice Apartment');
    });

    it('should return default status if settings do not exist', async () => {
      AutoApplicationSettings.findByUserId.mockResolvedValue(null);

      const result = await autoApplicationService.getApplicationStatus(mockUserId);

      expect(result).toHaveProperty('enabled', false);
      expect(result).toHaveProperty('isActive', false);
      expect(result.statistics.totalApplications).toBe(0);
    });
  });

  describe('_validateSettings', () => {
    it('should throw error for missing settings', () => {
      expect(() => {
        autoApplicationService._validateSettings(null);
      }).toThrow('Settings are required');
    });

    it('should throw error for invalid max applications per day', () => {
      expect(() => {
        autoApplicationService._validateSettings({
          maxApplicationsPerDay: 25
        });
      }).toThrow('Max applications per day must be between 1 and 20');
    });

    it('should throw error for invalid application template', () => {
      expect(() => {
        autoApplicationService._validateSettings({
          applicationTemplate: 'invalid'
        });
      }).toThrow('Invalid application template');
    });

    it('should throw error for invalid language', () => {
      expect(() => {
        autoApplicationService._validateSettings({
          language: 'invalid'
        });
      }).toThrow('Invalid language');
    });

    it('should throw error for missing max price in criteria', () => {
      expect(() => {
        autoApplicationService._validateSettings({
          criteria: {
            minRooms: 2
          }
        });
      }).toThrow('Max price is required and must be greater than 0');
    });

    it('should throw error when min rooms > max rooms', () => {
      expect(() => {
        autoApplicationService._validateSettings({
          criteria: {
            maxPrice: 2000,
            minRooms: 5,
            maxRooms: 3
          }
        });
      }).toThrow('Min rooms cannot be greater than max rooms');
    });
  });

  describe('_calculatePriority', () => {
    const mockSettings = {
      criteria: {
        maxPrice: 2000,
        locations: ['Amsterdam'],
        propertyTypes: ['apartment']
      }
    };

    it('should calculate higher priority for preferred location', () => {
      const listing = {
        location: 'Amsterdam Center',
        price: '€ 1,500',
        propertyType: 'apartment',
        dateAdded: new Date()
      };

      const priority = autoApplicationService._calculatePriority(listing, mockSettings);
      expect(priority).toBeGreaterThan(50); // Base priority
    });

    it('should calculate higher priority for newer listings', () => {
      const recentListing = {
        location: 'Amsterdam',
        price: '€ 1,500',
        propertyType: 'apartment',
        dateAdded: new Date(Date.now() - 30 * 60 * 1000) // 30 minutes ago
      };

      const oldListing = {
        location: 'Amsterdam',
        price: '€ 1,500',
        propertyType: 'apartment',
        dateAdded: new Date(Date.now() - 24 * 60 * 60 * 1000) // 24 hours ago
      };

      const recentPriority = autoApplicationService._calculatePriority(recentListing, mockSettings);
      const oldPriority = autoApplicationService._calculatePriority(oldListing, mockSettings);

      expect(recentPriority).toBeGreaterThan(oldPriority);
    });
  });

  describe('_calculateScheduledTime', () => {
    it('should return a future date within the delay range', () => {
      const scheduledTime = autoApplicationService._calculateScheduledTime();
      const now = new Date();
      const minDelay = 2 * 60 * 1000; // 2 minutes
      const maxDelay = 10 * 60 * 1000; // 10 minutes

      expect(scheduledTime.getTime()).toBeGreaterThan(now.getTime() + minDelay - 1000); // Allow 1s tolerance
      expect(scheduledTime.getTime()).toBeLessThan(now.getTime() + maxDelay + 1000); // Allow 1s tolerance
    });
  });
});