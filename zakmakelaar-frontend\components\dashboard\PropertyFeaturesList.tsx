import React from 'react';
import {
  StyleSheet,
  View,
  Text,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Listing } from '../../services/listingsService';

// Define theme colors
const THEME = {
  primary: '#4361ee',
  secondary: '#7209b7',
  accent: '#f72585',
  dark: '#0a0a18',
  light: '#ffffff',
  gray: '#6b7280',
};

interface PropertyFeaturesListProps {
  listing: Listing;
}

export const PropertyFeaturesList: React.FC<PropertyFeaturesListProps> = ({
  listing,
}) => {
  // Define features to check
  const features = [
    {
      key: 'furnished',
      label: 'Furnished',
      icon: 'bed-outline',
      value: listing.furnished || listing.interior === 'furnished' || listing.interior === 'gemeubileerd',
    },
    {
      key: 'pets',
      label: 'Pets Allowed',
      icon: 'paw-outline',
      value: listing.pets,
    },
    {
      key: 'smoking',
      label: 'Smoking Allowed',
      icon: 'flame-outline',
      value: listing.smoking,
    },
    {
      key: 'garden',
      label: 'Garden',
      icon: 'leaf-outline',
      value: listing.garden,
    },
    {
      key: 'balcony',
      label: 'Balcony',
      icon: 'sunny-outline',
      value: listing.balcony,
    },
    {
      key: 'parking',
      label: 'Parking',
      icon: 'car-outline',
      value: listing.parking,
    },
  ];
  
  // Filter out features that are not defined
  const availableFeatures = features.filter(feature => feature.value !== undefined);
  
  // If no features are available, don't render the component
  if (availableFeatures.length === 0) {
    return null;
  }
  
  return (
    <View style={styles.container}>
      <Text style={styles.title}>Features</Text>
      <View style={styles.featuresGrid}>
        {availableFeatures.map((feature) => (
          <View key={feature.key} style={styles.featureItem}>
            <View 
              style={[
                styles.iconContainer,
                { backgroundColor: feature.value ? 'rgba(67, 97, 238, 0.1)' : 'rgba(107, 114, 128, 0.1)' }
              ]}
            >
              <Ionicons 
                name={feature.icon as any} 
                size={20} 
                color={feature.value ? THEME.primary : THEME.gray} 
              />
            </View>
            <Text 
              style={[
                styles.featureLabel,
                { color: feature.value ? THEME.dark : THEME.gray }
              ]}
            >
              {feature.label}
            </Text>
            <Ionicons 
              name={feature.value ? "checkmark-circle" : "close-circle"} 
              size={16} 
              color={feature.value ? "#10b981" : "#ef4444"} 
            />
          </View>
        ))}
      </View>
      
      {/* Additional features from listing.features array */}
      {listing.features && listing.features.length > 0 && (
        <View style={styles.additionalFeatures}>
          {listing.features.map((feature, index) => (
            <View key={`feature-${index}`} style={styles.additionalFeatureItem}>
              <Ionicons name="checkmark-circle" size={16} color="#10b981" />
              <Text style={styles.additionalFeatureText}>{feature}</Text>
            </View>
          ))}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 24,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: THEME.dark,
    marginBottom: 16,
  },
  featuresGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -8,
  },
  featureItem: {
    width: '50%',
    paddingHorizontal: 8,
    marginBottom: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  featureLabel: {
    flex: 1,
    fontSize: 14,
    fontWeight: '500',
  },
  additionalFeatures: {
    marginTop: 8,
  },
  additionalFeatureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  additionalFeatureText: {
    fontSize: 14,
    color: THEME.dark,
    marginLeft: 8,
  },
});