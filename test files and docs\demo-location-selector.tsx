/**
 * Demo Component for LocationSelector
 * A simple demo to test the LocationSelector functionality
 */

import React, { useState } from 'react';
import {
  StyleSheet,
  Text,
  View,
  SafeAreaView,
  ScrollView,
} from 'react-native';
import { LocationSelector } from '../zakmakelaar-frontend/components/LocationSelector';

const THEME = {
  primary: '#4361ee',
  dark: '#0a0a18',
  light: '#ffffff',
  gray: '#6b7280',
  lightGray: '#f3f4f6',
};

export default function LocationSelectorDemo() {
  const [selectedLocations, setSelectedLocations] = useState<string[]>([
    'Amsterdam', 'Utrecht', 'Rotterdam'
  ]);
  const [selectedLocationsText, setSelectedLocationsText] = useState<string[]>([
    'Den Haag', 'Eindhoven'
  ]);

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content}>
        <Text style={styles.title}>Location Selector Demo</Text>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Current Selection (Chips):</Text>
          <Text style={styles.selectionText}>
            {selectedLocations.length === 0 
              ? 'No locations selected' 
              : selectedLocations.join(', ')
            }
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Chip Display Mode:</Text>
          <LocationSelector
            selectedLocations={selectedLocations}
            onSelectionChange={setSelectedLocations}
            placeholder="Choose your preferred cities..."
            maxSelections={8}
            displayMode="chips"
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Current Selection (Text):</Text>
          <Text style={styles.selectionText}>
            {selectedLocationsText.length === 0 
              ? 'No locations selected' 
              : selectedLocationsText.join(', ')
            }
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Text Display Mode:</Text>
          <LocationSelector
            selectedLocations={selectedLocationsText}
            onSelectionChange={setSelectedLocationsText}
            placeholder="Choose your preferred cities..."
            maxSelections={8}
            displayMode="text"
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Features:</Text>
          <Text style={styles.featureText}>• Fetches cities from /api/listings/cities</Text>
          <Text style={styles.featureText}>• Search functionality</Text>
          <Text style={styles.featureText}>• Multiple selection with limit</Text>
          <Text style={styles.featureText}>• Select all / Clear all options</Text>
          <Text style={styles.featureText}>• Fallback cities if API fails</Text>
          <Text style={styles.featureText}>• Responsive modal interface</Text>
          <Text style={styles.featureText}>• Chip display with individual removal</Text>
          <Text style={styles.featureText}>• Text display mode for compact view</Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Selected Counts:</Text>
          <Text style={styles.countText}>
            Chips: {selectedLocations.length} locations
          </Text>
          <Text style={styles.countText}>
            Text: {selectedLocationsText.length} locations
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: THEME.lightGray,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: THEME.dark,
    marginBottom: 30,
    textAlign: 'center',
  },
  section: {
    backgroundColor: THEME.light,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: THEME.dark,
    marginBottom: 8,
  },
  selectionText: {
    fontSize: 14,
    color: THEME.primary,
    fontWeight: '500',
  },
  featureText: {
    fontSize: 14,
    color: THEME.gray,
    marginBottom: 4,
  },
  countText: {
    fontSize: 18,
    fontWeight: '600',
    color: THEME.primary,
  },
});