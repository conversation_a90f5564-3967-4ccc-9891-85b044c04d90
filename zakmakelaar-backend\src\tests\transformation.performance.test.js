/**
 * Transformation Performance Tests
 * 
 * This file contains performance tests to verify that the transformation pipeline
 * meets the performance requirements specified in the requirements document.
 * 
 * Requirements:
 * - <PERSON><PERSON><PERSON> data transformation occurs THEN the system SHALL complete processing within 100ms per property record
 * - WHEN multiple scrapers run concurrently THEN the system SHALL handle schema normalization without resource contention
 * - WHEN large batches of data are processed THEN the system SHALL maintain memory usage below 500MB for transformation operations
 */

const { SchemaTransformer } = require('../services/schemaTransformer');
const { FieldMappingRegistry } = require('../services/fieldMappingRegistry');
const { MappingConfigLoader } = require('../services/mappingConfigLoader');
const { ValidationEngine } = require('../services/validationEngine');
const { testDataSets, rawScraperData } = require('./testData/unifiedSchemaTestData');

describe('Transformation Performance', () => {
  let registry;
  let transformer;
  let validationEngine;
  
  beforeEach(() => {
    // Set up the transformation pipeline components
    registry = new FieldMappingRegistry();
    const loader = new MappingConfigLoader(registry);
    
    // Load mappings for all sources
    loader.loadFromObject('funda.nl', {
      'title': 'title',
      'description': 'description',
      'url': 'url',
      'source': { value: 'funda.nl' },
      'location': {
        path: 'location',
        transform: 'normalizeLocation'
      },
      'price': {
        path: 'price',
        transform: 'normalizePrice'
      },
      'propertyType': {
        path: 'propertyType',
        transform: 'normalizePropertyType'
      },
      'size': {
        path: 'size',
        transform: 'normalizeSize'
      },
      'area': {
        path: 'size',
        transform: 'extractNumericSize'
      },
      'rooms': {
        path: 'rooms',
        transform: 'normalizeRooms'
      },
      'bedrooms': {
        path: 'bedrooms',
        transform: 'normalizeRooms'
      },
      'year': {
        path: 'year',
        transform: 'normalizeYear'
      },
      'interior': {
        path: 'interior',
        transform: 'normalizeInterior'
      },
      'furnished': {
        path: 'interior',
        transform: 'inferFurnishedStatus'
      },
      'images': {
        path: 'images',
        transform: 'normalizeImageArray'
      },
      'energyLabel': {
        path: 'energyLabel',
        transform: 'normalizeEnergyLabel'
      },
      'dateAdded': {
        transform: 'getCurrentISOString'
      }
    });
    
    transformer = new SchemaTransformer(registry);
    validationEngine = new ValidationEngine();
  });
  
  test('should transform a single property within 100ms', async () => {
    // Measure the time it takes to transform a single property
    const startTime = process.hrtime();
    
    await transformer.transform(rawScraperData.funda, 'funda.nl');
    
    const endTime = process.hrtime(startTime);
    const executionTimeMs = (endTime[0] * 1000) + (endTime[1] / 1000000);
    
    // Verify that the transformation took less than 100ms
    expect(executionTimeMs).toBeLessThan(100);
  });
  
  test('should transform 100 properties within 10 seconds', async () => {
    // Create an array of 100 properties
    const properties = Array(100).fill().map((_, i) => ({
      ...rawScraperData.funda,
      title: `Property ${i}`,
      url: `https://www.funda.nl/huur/amsterdam/appartement-${i}/`
    }));
    
    // Measure the time it takes to transform 100 properties
    const startTime = process.hrtime();
    
    await transformer.batchTransform(properties, 'funda.nl');
    
    const endTime = process.hrtime(startTime);
    const executionTimeMs = (endTime[0] * 1000) + (endTime[1] / 1000000);
    
    // Verify that the transformation took less than 10 seconds (100ms per property)
    expect(executionTimeMs).toBeLessThan(10000);
  });
  
  test('should handle concurrent transformations from multiple scrapers', async () => {
    // Create arrays of properties for each scraper
    const fundaProperties = Array(10).fill().map((_, i) => ({
      ...rawScraperData.funda,
      title: `Funda Property ${i}`,
      url: `https://www.funda.nl/huur/amsterdam/appartement-${i}/`
    }));
    
    const huurwoningenProperties = Array(10).fill().map((_, i) => ({
      ...rawScraperData.huurwoningen,
      title: `Huurwoningen Property ${i}`,
      url: `https://www.huurwoningen.nl/huren/rotterdam/studio-${i}/`
    }));
    
    const parariusProperties = Array(10).fill().map((_, i) => ({
      ...rawScraperData.pararius,
      title: `Pararius Property ${i}`,
      url: `https://www.pararius.nl/huurwoningen/utrecht/huis-${i}/`
    }));
    
    // Measure the time it takes to transform properties from multiple scrapers concurrently
    const startTime = process.hrtime();
    
    // Run transformations concurrently
    await Promise.all([
      transformer.batchTransform(fundaProperties, 'funda.nl'),
      transformer.batchTransform(huurwoningenProperties, 'huurwoningen.nl'),
      transformer.batchTransform(parariusProperties, 'pararius.nl')
    ]);
    
    const endTime = process.hrtime(startTime);
    const executionTimeMs = (endTime[0] * 1000) + (endTime[1] / 1000000);
    
    // Verify that the concurrent transformations completed successfully
    // and took less than 3 seconds (100ms per property * 30 properties)
    expect(executionTimeMs).toBeLessThan(3000);
  });
  
  test('should maintain reasonable memory usage for large batches', async () => {
    // Create a large batch of properties (500 properties)
    const properties = Array(500).fill().map((_, i) => ({
      ...rawScraperData.funda,
      title: `Property ${i}`,
      url: `https://www.funda.nl/huur/amsterdam/appartement-${i}/`,
      description: `A detailed description of property ${i}. `.repeat(20) // Add some bulk
    }));
    
    // Measure memory usage before transformation
    const memoryBefore = process.memoryUsage();
    
    // Transform the large batch
    await transformer.batchTransform(properties, 'funda.nl');
    
    // Measure memory usage after transformation
    const memoryAfter = process.memoryUsage();
    
    // Calculate memory increase in MB
    const memoryIncreaseMB = (memoryAfter.heapUsed - memoryBefore.heapUsed) / (1024 * 1024);
    
    // Verify that memory usage increase is below 500MB
    expect(memoryIncreaseMB).toBeLessThan(500);
  });
  
  test('should handle validation of large batches efficiently', async () => {
    // Create a large batch of properties (100 properties)
    const properties = Array(100).fill().map((_, i) => ({
      ...testDataSets.funda.complete,
      title: `Property ${i}`,
      url: `https://www.funda.nl/huur/amsterdam/appartement-${i}/`
    }));
    
    // Measure the time it takes to validate 100 properties
    const startTime = process.hrtime();
    
    // Validate each property
    for (const property of properties) {
      validationEngine.validate(property);
    }
    
    const endTime = process.hrtime(startTime);
    const executionTimeMs = (endTime[0] * 1000) + (endTime[1] / 1000000);
    
    // Verify that the validation took less than 5 seconds (50ms per property)
    expect(executionTimeMs).toBeLessThan(5000);
  });
});