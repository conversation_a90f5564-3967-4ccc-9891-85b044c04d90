// Add missing results endpoint to auto-application routes
const fs = require('fs');
const path = require('path');

async function addMissingResultsEndpoint() {
  console.log('🔧 Adding Missing Results Endpoint');
  console.log('=' .repeat(50));

  try {
    const routesFilePath = path.join(__dirname, 'src/routes/autoApplication.js');
    
    // Read the current routes file
    let routesContent = fs.readFileSync(routesFilePath, 'utf8');
    
    // Check if results endpoint already exists
    if (routesContent.includes('/results')) {
      console.log('✅ Results endpoint already exists');
      return;
    }
    
    // Find a good place to insert the results endpoints (after stats endpoints)
    const insertAfter = "router.get('/stats', auth, async (req, res, next) => {";
    const insertIndex = routesContent.indexOf(insertAfter);
    
    if (insertIndex === -1) {
      console.log('❌ Could not find insertion point in routes file');
      return;
    }
    
    // Find the end of the stats endpoint
    let braceCount = 0;
    let endIndex = insertIndex;
    let inFunction = false;
    
    for (let i = insertIndex; i < routesContent.length; i++) {
      if (routesContent[i] === '{') {
        braceCount++;
        inFunction = true;
      } else if (routesContent[i] === '}') {
        braceCount--;
        if (inFunction && braceCount === 0) {
          endIndex = i + 1;
          break;
        }
      }
    }
    
    // Find the next line after the closing brace
    while (endIndex < routesContent.length && routesContent[endIndex] !== '\n') {
      endIndex++;
    }
    endIndex++; // Move past the newline
    
    // Results endpoints to add
    const resultsEndpoints = `
/**
 * @swagger
 * /api/auto-application/results/{userId}:
 *   get:
 *     summary: Get application results for specific user
 *     description: Retrieve results of completed auto-applications for a specific user
 *     tags: [Auto Application]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID to get results for
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: Number of results to return
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [submitted, failed, blocked, captcha_required, form_changed, duplicate, expired]
 *         description: Filter by result status
 *     responses:
 *       200:
 *         description: Application results retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     results:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/ApplicationResult'
 *                     pagination:
 *                       type: object
 *                       properties:
 *                         page:
 *                           type: number
 *                         limit:
 *                           type: number
 *                         total:
 *                           type: number
 *                         totalPages:
 *                           type: number
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       500:
 *         description: Internal server error
 */
router.get('/results/:userId', auth, async (req, res, next) => {
  try {
    // Ensure user can only access their own results or is admin
    if (req.user.id !== req.params.userId && req.user.role !== 'admin') {
      return res.status(403).json({
        status: 'error',
        message: 'Access denied'
      });
    }

    const { page = 1, limit = 20, status } = req.query;
    
    // Get ApplicationResult model
    const ApplicationResult = require('../models/ApplicationResult');
    
    // Build query
    const query = { userId: req.params.userId };
    if (status) query.status = status;
    
    // Get results with pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const results = await ApplicationResult.find(query)
      .sort({ submittedAt: -1 })
      .skip(skip)
      .limit(parseInt(limit))
      .populate('listingId', 'title location price')
      .lean();
    
    // Get total count
    const total = await ApplicationResult.countDocuments(query);
    
    // Format results for frontend
    const formattedResults = results.map(result => ({
      ...result,
      listingTitle: result.listingSnapshot?.title || result.listingId?.title || 'Unknown Property',
      responseTime: result.response?.responseTime || 0,
      // Ensure metadata exists and has qualityScore
      metadata: {
        ...result.metadata,
        qualityScore: result.metadata?.qualityScore || 0.85,
        processingTime: result.metadata?.processingTime || result.metrics?.processingTime || 2000
      },
      // Ensure metrics exists
      metrics: {
        ...result.metrics,
        successProbability: result.metrics?.successProbability || 85
      }
    }));
    
    res.json({
      status: 'success',
      data: {
        results: formattedResults,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          totalPages: Math.ceil(total / parseInt(limit))
        }
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/auto-application/results:
 *   get:
 *     summary: Get application results
 *     description: Retrieve results of completed auto-applications for the authenticated user
 *     tags: [Auto Application]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [submitted, failed, blocked, captcha_required, form_changed, duplicate, expired]
 *         description: Filter by result status
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: Number of results to return
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *           minimum: 0
 *           default: 0
 *         description: Number of results to skip
 *       - in: query
 *         name: dateFrom
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter results from this date
 *       - in: query
 *         name: dateTo
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter results to this date
 *     responses:
 *       200:
 *         description: Application results retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     results:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/ApplicationResult'
 *                     total:
 *                       type: number
 *                     limit:
 *                       type: number
 *                     offset:
 *                       type: number
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get('/results', auth, async (req, res, next) => {
  try {
    const { status, limit = 20, offset = 0, dateFrom, dateTo } = req.query;
    
    // Get ApplicationResult model
    const ApplicationResult = require('../models/ApplicationResult');
    
    // Build query
    const query = { userId: req.user.id };
    if (status) query.status = status;
    if (dateFrom || dateTo) {
      query.submittedAt = {};
      if (dateFrom) query.submittedAt.$gte = new Date(dateFrom);
      if (dateTo) query.submittedAt.$lte = new Date(dateTo);
    }
    
    // Get results
    const results = await ApplicationResult.find(query)
      .sort({ submittedAt: -1 })
      .skip(parseInt(offset))
      .limit(parseInt(limit))
      .populate('listingId', 'title location price')
      .lean();
    
    // Get total count
    const total = await ApplicationResult.countDocuments(query);
    
    // Format results for frontend
    const formattedResults = results.map(result => ({
      ...result,
      listingTitle: result.listingSnapshot?.title || result.listingId?.title || 'Unknown Property',
      responseTime: result.response?.responseTime || 0,
      // Ensure metadata exists and has qualityScore
      metadata: {
        ...result.metadata,
        qualityScore: result.metadata?.qualityScore || 0.85,
        processingTime: result.metadata?.processingTime || result.metrics?.processingTime || 2000
      },
      // Ensure metrics exists
      metrics: {
        ...result.metrics,
        successProbability: result.metrics?.successProbability || 85
      }
    }));
    
    res.json({
      status: 'success',
      data: {
        results: formattedResults,
        total,
        limit: parseInt(limit),
        offset: parseInt(offset)
      }
    });
  } catch (error) {
    next(error);
  }
});

`;
    
    // Insert the results endpoints
    const newContent = routesContent.slice(0, endIndex) + resultsEndpoints + routesContent.slice(endIndex);
    
    // Write the updated content back to the file
    fs.writeFileSync(routesFilePath, newContent, 'utf8');
    
    console.log('✅ Results endpoints added successfully');
    console.log('   - GET /api/auto-application/results/:userId');
    console.log('   - GET /api/auto-application/results');
    console.log('');
    console.log('🔧 Key Features Added:');
    console.log('   - Proper data formatting for frontend');
    console.log('   - Default values for missing fields');
    console.log('   - Quality score fallback (85%)');
    console.log('   - Response time fallback (0ms)');
    console.log('   - Pagination support');
    console.log('   - Status filtering');
    console.log('');
    console.log('🚀 Server restart required to apply changes');
    
  } catch (error) {
    console.error('❌ Failed to add results endpoint:', error.message);
  }
}

addMissingResultsEndpoint().catch(console.error);