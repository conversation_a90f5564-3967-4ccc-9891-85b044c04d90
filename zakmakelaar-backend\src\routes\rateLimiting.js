const express = require('express');
const router = express.Router();
const RateLimitingService = require('../services/rateLimitingService');
const { auth: authenticateToken, requireRole } = require('../middleware/auth');
const { loggers } = require('../services/logger');

// Initialize rate limiting service
const rateLimitingService = new RateLimitingService();

/**
 * @swagger
 * /api/rate-limiting/status:
 *   get:
 *     summary: Get current rate limiting status
 *     tags: [Rate Limiting]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Rate limiting status retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 globalLimits:
 *                   type: object
 *                 userLimits:
 *                   type: object
 *                 globalCounters:
 *                   type: object
 *                 activeUsers:
 *                   type: number
 *                 pausedUsers:
 *                   type: number
 *                 complianceMetrics:
 *                   type: object
 */
router.get('/status', authenticateToken, async (req, res) => {
  try {
    const status = rateLimitingService.getRateLimitingStatus();
    
    res.json({
      success: true,
      data: status
    });
  } catch (error) {
    loggers.app.error('Error getting rate limiting status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get rate limiting status',
      error: error.message
    });
  }
});

/**
 * @swagger
 * /api/rate-limiting/check/{userId}:
 *   get:
 *     summary: Check rate limits for a specific user
 *     tags: [Rate Limiting]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID to check rate limits for
 *       - in: query
 *         name: priority
 *         schema:
 *           type: string
 *           enum: [low, normal, high]
 *         description: Application priority level
 *     responses:
 *       200:
 *         description: Rate limit check completed
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 allowed:
 *                   type: boolean
 *                 reason:
 *                   type: string
 *                 message:
 *                   type: string
 *                 retryAfter:
 *                   type: string
 *                   format: date-time
 */
router.get('/check/:userId', authenticateToken, async (req, res) => {
  try {
    const { userId } = req.params;
    const { priority = 'normal' } = req.query;

    // Verify user can check this user's rate limits (admin or own user)
    if (req.user.role !== 'admin' && req.user.id !== userId) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    const rateLimitCheck = await rateLimitingService.checkRateLimit(userId, { priority });
    
    res.json({
      success: true,
      data: rateLimitCheck
    });
  } catch (error) {
    loggers.app.error('Error checking rate limits:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to check rate limits',
      error: error.message
    });
  }
});

/**
 * @swagger
 * /api/rate-limiting/pause/{userId}:
 *   post:
 *     summary: Pause auto-application for a user
 *     tags: [Rate Limiting]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID to pause
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               reason:
 *                 type: string
 *                 description: Reason for pausing
 *               duration:
 *                 type: number
 *                 description: Pause duration in milliseconds
 *               requiresManualResume:
 *                 type: boolean
 *                 description: Whether manual resume is required
 *     responses:
 *       200:
 *         description: User paused successfully
 */
router.post('/pause/:userId', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { userId } = req.params;
    const { reason = 'Manual pause', duration = 60 * 60 * 1000, requiresManualResume = false } = req.body;

    await rateLimitingService.pauseUser(userId, reason, duration, requiresManualResume);
    
    res.json({
      success: true,
      message: `User ${userId} paused successfully`,
      data: {
        userId,
        reason,
        duration,
        requiresManualResume
      }
    });
  } catch (error) {
    loggers.app.error('Error pausing user:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to pause user',
      error: error.message
    });
  }
});

/**
 * @swagger
 * /api/rate-limiting/resume/{userId}:
 *   post:
 *     summary: Resume auto-application for a user
 *     tags: [Rate Limiting]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID to resume
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               resumeReason:
 *                 type: string
 *                 description: Reason for resuming
 *     responses:
 *       200:
 *         description: User resumed successfully
 */
router.post('/resume/:userId', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { userId } = req.params;
    const { resumeReason = 'Manual resume' } = req.body;

    await rateLimitingService.resumeUser(userId, resumeReason);
    
    res.json({
      success: true,
      message: `User ${userId} resumed successfully`,
      data: {
        userId,
        resumeReason
      }
    });
  } catch (error) {
    loggers.app.error('Error resuming user:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to resume user',
      error: error.message
    });
  }
});

/**
 * @swagger
 * /api/rate-limiting/compliance-report:
 *   get:
 *     summary: Get compliance report
 *     tags: [Rate Limiting]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date
 *         description: Start date for report
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date
 *         description: End date for report
 *       - in: query
 *         name: userId
 *         schema:
 *           type: string
 *         description: Specific user ID for report
 *     responses:
 *       200:
 *         description: Compliance report generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 reportGeneratedAt:
 *                   type: string
 *                   format: date-time
 *                 period:
 *                   type: object
 *                 applicationStatistics:
 *                   type: object
 *                 rateLimitingStatistics:
 *                   type: object
 *                 complianceMetrics:
 *                   type: object
 *                 pausedUsers:
 *                   type: array
 *                 recommendations:
 *                   type: array
 */
router.get('/compliance-report', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { startDate, endDate, userId } = req.query;
    
    const report = await rateLimitingService.getComplianceReport({
      startDate,
      endDate,
      userId
    });
    
    res.json({
      success: true,
      data: report
    });
  } catch (error) {
    loggers.app.error('Error generating compliance report:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate compliance report',
      error: error.message
    });
  }
});

/**
 * @swagger
 * /api/rate-limiting/detect-funda-limit:
 *   post:
 *     summary: Detect Funda rate limiting from response
 *     tags: [Rate Limiting]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               responseText:
 *                 type: string
 *                 description: Response text from Funda
 *               responseHeaders:
 *                 type: object
 *                 description: Response headers
 *               statusCode:
 *                 type: number
 *                 description: HTTP status code
 *               userId:
 *                 type: string
 *                 description: User ID for automatic pause if needed
 *     responses:
 *       200:
 *         description: Rate limit detection completed
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 isRateLimit:
 *                   type: boolean
 *                 severity:
 *                   type: string
 *                 reason:
 *                   type: string
 *                 recommendedAction:
 *                   type: string
 *                 pauseDuration:
 *                   type: number
 */
router.post('/detect-funda-limit', authenticateToken, requireRole(['admin', 'system']), async (req, res) => {
  try {
    const { responseText, responseHeaders = {}, statusCode = 200, userId } = req.body;
    
    const detection = rateLimitingService.detectFundaRateLimit(responseText, responseHeaders, statusCode);
    
    // If rate limiting is detected and userId is provided, automatically pause the user
    if (detection.isRateLimit && userId) {
      if (detection.recommendedAction === 'pause_user') {
        await rateLimitingService.pauseUser(
          userId,
          `Auto-pause: ${detection.reason}`,
          detection.pauseDuration
        );
      } else if (detection.recommendedAction === 'pause_user_manual') {
        await rateLimitingService.pauseUser(
          userId,
          `Manual intervention required: ${detection.reason}`,
          detection.pauseDuration,
          true
        );
      }
    }
    
    res.json({
      success: true,
      data: {
        ...detection,
        userPaused: detection.isRateLimit && userId ? true : false
      }
    });
  } catch (error) {
    loggers.app.error('Error detecting Funda rate limit:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to detect rate limit',
      error: error.message
    });
  }
});

/**
 * @swagger
 * /api/rate-limiting/limits:
 *   put:
 *     summary: Update rate limiting configuration
 *     tags: [Rate Limiting]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               global:
 *                 type: object
 *                 properties:
 *                   maxApplicationsPerHour:
 *                     type: number
 *                   maxApplicationsPerDay:
 *                     type: number
 *                   maxConcurrentApplications:
 *                     type: number
 *               user:
 *                 type: object
 *                 properties:
 *                   maxApplicationsPerHour:
 *                     type: number
 *                   maxApplicationsPerDay:
 *                     type: number
 *                   maxConcurrentApplications:
 *                     type: number
 *     responses:
 *       200:
 *         description: Rate limits updated successfully
 */
router.put('/limits', authenticateToken, requireRole(['admin']), async (req, res) => {
  try {
    const { global, user } = req.body;
    
    rateLimitingService.updateRateLimits({ global, user });
    
    res.json({
      success: true,
      message: 'Rate limits updated successfully',
      data: rateLimitingService.getRateLimitingStatus()
    });
  } catch (error) {
    loggers.app.error('Error updating rate limits:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update rate limits',
      error: error.message
    });
  }
});

/**
 * @swagger
 * /api/rate-limiting/intelligent-scheduling/{userId}:
 *   get:
 *     summary: Get intelligent scheduling recommendation for a user
 *     tags: [Rate Limiting]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID
 *       - in: query
 *         name: priority
 *         schema:
 *           type: string
 *           enum: [low, normal, high]
 *         description: Application priority level
 *     responses:
 *       200:
 *         description: Scheduling recommendation retrieved
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 allowed:
 *                   type: boolean
 *                 recommendedDelay:
 *                   type: number
 *                 applicationsToday:
 *                   type: number
 *                 remainingToday:
 *                   type: number
 *                 optimalSpacing:
 *                   type: number
 */
router.get('/intelligent-scheduling/:userId', authenticateToken, async (req, res) => {
  try {
    const { userId } = req.params;
    const { priority = 'normal' } = req.query;

    // Verify user can check this user's scheduling (admin or own user)
    if (req.user.role !== 'admin' && req.user.id !== userId) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    const schedulingResult = await rateLimitingService.checkIntelligentScheduling(userId, priority);
    
    res.json({
      success: true,
      data: schedulingResult
    });
  } catch (error) {
    loggers.app.error('Error getting intelligent scheduling:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get scheduling recommendation',
      error: error.message
    });
  }
});

module.exports = router;