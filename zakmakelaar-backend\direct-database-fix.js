// Direct database approach to fix auto-application settings
const mongoose = require('mongoose');

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/zakmakelaar';

async function directDatabaseFix() {
  console.log('🔧 Direct Database Fix for Auto-Application Settings');
  console.log('=' .repeat(60));

  try {
    await mongoose.connect(MONGODB_URI);
    
    const AutoApplicationSettings = require('./src/models/AutoApplicationSettings');
    const User = require('./src/models/User');
    
    // Find our test user
    const testUser = await User.findOne({ email: '<EMAIL>' });
    if (!testUser) {
      console.log('❌ Test user not found');
      return;
    }
    
    console.log(`✅ Found test user: ${testUser._id}`);
    
    // Find or create auto-application settings
    let autoSettings = await AutoApplicationSettings.findByUserId(testUser._id);
    
    if (!autoSettings) {
      console.log('Creating new auto-application settings...');
      autoSettings = new AutoApplicationSettings({
        userId: testUser._id,
        enabled: false
      });
    }
    
    console.log('✅ Found/created auto-application settings');
    
    // Update settings directly
    autoSettings.enabled = true;
    
    // Update settings object
    autoSettings.settings = {
      maxApplicationsPerDay: 10,
      applicationTemplate: 'professional',
      autoSubmit: false,
      requireManualReview: true,
      notificationPreferences: {
        immediate: true,
        daily: true,
        weekly: false
      },
      language: 'english'
    };
    
    // Update criteria to match our target listing
    autoSettings.criteria = {
      maxPrice: 3500, // Higher than €3,100
      minRooms: 1,
      maxRooms: 25,   // Accommodate the "20" rooms
      propertyTypes: ['apartment', 'house', 'studio', 'room'], // All valid enum values
      locations: ['Utrecht'],
      excludeKeywords: [],
      includeKeywords: [],
      minSize: 100,
      maxSize: 200,
      furnished: true,
      petsAllowed: false,
      smokingAllowed: false
    };
    
    // Update personal info
    autoSettings.personalInfo = {
      fullName: 'Target Tester',
      email: '<EMAIL>',
      phone: '+31612345678',
      dateOfBirth: new Date('1990-01-01'),
      nationality: 'Dutch',
      occupation: 'Software Developer',
      employer: 'Tech Company',
      monthlyIncome: 5000,
      moveInDate: new Date('2025-09-01'),
      leaseDuration: 12,
      numberOfOccupants: 1,
      hasGuarantor: false,
      guarantorInfo: {
        name: 'Guarantor Name',
        email: '<EMAIL>',
        phone: '+31687654321',
        relationship: 'Parent',
        monthlyIncome: 6000
      },
      emergencyContact: {
        name: 'Emergency Contact',
        phone: '+31687654321',
        email: '<EMAIL>',
        relationship: 'Friend'
      }
    };
    
    // Initialize statistics if not present
    if (!autoSettings.statistics) {
      autoSettings.statistics = {
        totalApplications: 0,
        successfulApplications: 0,
        rejectedApplications: 0,
        pendingApplications: 0,
        dailyApplicationCount: 0,
        lastDailyReset: new Date()
      };
    }
    
    // Initialize status if not present
    if (!autoSettings.status) {
      autoSettings.status = {
        isActive: false,
        errorCount: 0
      };
    }
    
    // Save the settings
    await autoSettings.save();
    
    console.log('✅ Settings saved successfully');
    
    // Verify the settings
    const updatedSettings = await AutoApplicationSettings.findByUserId(testUser._id);
    
    console.log('\n🔍 Verification:');
    console.log(`- Enabled: ${updatedSettings.enabled}`);
    console.log(`- Max Price: ${updatedSettings.criteria.maxPrice}`);
    console.log(`- Rooms: ${updatedSettings.criteria.minRooms}-${updatedSettings.criteria.maxRooms}`);
    console.log(`- Property Types: ${updatedSettings.criteria.propertyTypes.join(', ')}`);
    console.log(`- Locations: ${updatedSettings.criteria.locations.join(', ')}`);
    console.log(`- Monthly Income: ${updatedSettings.personalInfo.monthlyIncome}`);
    console.log(`- Profile Complete: ${updatedSettings.isProfileComplete}`);
    console.log(`- Documents Complete: ${updatedSettings.documentsComplete}`);
    console.log(`- Can Auto Apply: ${updatedSettings.canAutoApply}`);
    
    // Test matching with our target listing
    console.log('\n🎯 Testing matching with target listing...');
    
    const testListing = {
      _id: "68914deb334fc4af08bc9b11",
      title: "Justus Van Effenstraat 23 Bs",
      price: 3100,
      location: "Utrecht",
      propertyType: "house", // Convert "woning" to "house" for matching
      rooms: 20,
      size: 125
    };
    
    const matchResult = updatedSettings.matchesCriteria(testListing);
    console.log(`🔧 Matching Result: ${matchResult ? '✅ MATCH' : '❌ NO MATCH'}`);
    
    if (matchResult) {
      console.log('\n🚀 Testing auto-application processing...');
      
      const autoApplicationService = require('./src/services/autoApplicationService');
      const result = await autoApplicationService.processNewListing(testListing);
      
      console.log(`✅ Processing result: ${result.length} applications created`);
      
      if (result.length > 0) {
        console.log('🎉 SUCCESS: Auto-application created!');
        result.forEach((app, index) => {
          console.log(`${index + 1}. Queue ID: ${app._id}`);
          console.log(`   Status: ${app.status}`);
          console.log(`   Priority: ${app.priority}`);
          console.log(`   Scheduled: ${app.scheduledAt}`);
        });
        
        // Check the application queue
        const ApplicationQueue = require('./src/models/ApplicationQueue');
        const queueItems = await ApplicationQueue.find({ userId: testUser._id });
        console.log(`\n📋 Total queue items for user: ${queueItems.length}`);
        
        queueItems.forEach((item, index) => {
          console.log(`${index + 1}. Status: ${item.status}, Priority: ${item.priority}, Scheduled: ${item.scheduledAt}`);
        });
      }
    } else {
      console.log('\n❌ Listing does not match criteria');
      
      // Debug why it doesn't match
      console.log('\nDetailed matching analysis:');
      console.log(`- Price: ${testListing.price} <= ${updatedSettings.criteria.maxPrice} = ${testListing.price <= updatedSettings.criteria.maxPrice}`);
      console.log(`- Rooms: ${testListing.rooms} between ${updatedSettings.criteria.minRooms}-${updatedSettings.criteria.maxRooms} = ${testListing.rooms >= updatedSettings.criteria.minRooms && testListing.rooms <= updatedSettings.criteria.maxRooms}`);
      console.log(`- Property Type: "${testListing.propertyType}" in [${updatedSettings.criteria.propertyTypes.join(', ')}] = ${updatedSettings.criteria.propertyTypes.includes(testListing.propertyType)}`);
      console.log(`- Location: "${testListing.location}" matches [${updatedSettings.criteria.locations.join(', ')}] = ${updatedSettings.criteria.locations.some(loc => testListing.location.toLowerCase().includes(loc.toLowerCase()))}`);
    }
    
    console.log('\n' + '='.repeat(60));
    console.log('🎯 DIRECT DATABASE FIX COMPLETE');
    
  } catch (error) {
    console.error('❌ Fix failed:', error.message);
    console.error(error.stack);
  } finally {
    await mongoose.disconnect();
  }
}

directDatabaseFix().catch(console.error);