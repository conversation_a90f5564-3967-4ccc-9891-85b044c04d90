const fetch = require('node-fetch');

// Test the update property functionality
async function testUpdateProperty() {
  const baseUrl = 'http://localhost:3001/api/property-owner';
  
  // Mock property data for testing
  const propertyId = 'test-property-id';
  const updateData = {
    title: 'Updated Property Title',
    description: 'Updated property description',
    rent: {
      amount: 1500,
      deposit: 3000
    },
    features: {
      furnished: true,
      parking: true
    }
  };
  
  try {
    console.log('Testing property update...');
    console.log('Property ID:', propertyId);
    console.log('Update data:', JSON.stringify(updateData, null, 2));
    
    const response = await fetch(`${baseUrl}/properties/${propertyId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token' // You'll need a real token
      },
      body: JSON.stringify(updateData)
    });
    
    const result = await response.json();
    
    console.log('Response status:', response.status);
    console.log('Response:', JSON.stringify(result, null, 2));
    
    if (response.ok) {
      console.log('✅ Property update successful!');
    } else {
      console.log('❌ Property update failed:', result.message);
    }
    
  } catch (error) {
    console.error('❌ Error testing property update:', error.message);
  }
}

// Run the test
testUpdateProperty();