const NodeCache = require('node-cache');
const { loggers } = require('./logger');

class RateLimitingService {
  constructor() {
    // Cache with 1 hour TTL
    this.cache = new NodeCache({ stdTTL: 3600 });
    this.config = {
      maxApplicationsPerHour: 10,
      maxApplicationsPerDomain: 5,
      delayBetweenApplications: 30000, // 30 seconds
      cooldownPeriod: 300000 // 5 minutes after hitting limit
    };
  }

  // Check if user can submit application
  canSubmitApplication(userId, domain) {
    const userKey = `user_${userId}`;
    const domainKey = `domain_${domain}`;
    const userDomainKey = `user_${userId}_domain_${domain}`;

    // Check user hourly limit
    const userCount = this.cache.get(userKey) || 0;
    if (userCount >= this.config.maxApplicationsPerHour) {
      loggers.app.warn(`User ${userId} hit hourly application limit`);
      return {
        allowed: false,
        reason: 'HOURLY_LIMIT_EXCEEDED',
        retryAfter: this.cache.getTtl(userKey)
      };
    }

    // Check domain-specific limit for user
    const userDomainCount = this.cache.get(userDomainKey) || 0;
    if (userDomainCount >= this.config.maxApplicationsPerDomain) {
      loggers.app.warn(`User ${userId} hit domain limit for ${domain}`);
      return {
        allowed: false,
        reason: 'DOMAIN_LIMIT_EXCEEDED',
        retryAfter: this.cache.getTtl(userDomainKey)
      };
    }

    // Check last application time for delay
    const lastApplicationKey = `last_app_${userId}`;
    const lastApplicationTime = this.cache.get(lastApplicationKey);
    if (lastApplicationTime) {
      const timeSinceLastApp = Date.now() - lastApplicationTime;
      if (timeSinceLastApp < this.config.delayBetweenApplications) {
        const waitTime = this.config.delayBetweenApplications - timeSinceLastApp;
        return {
          allowed: false,
          reason: 'TOO_FREQUENT',
          retryAfter: waitTime
        };
      }
    }

    return { allowed: true };
  }

  // Record application submission
  recordApplication(userId, domain) {
    const userKey = `user_${userId}`;
    const domainKey = `domain_${domain}`;
    const userDomainKey = `user_${userId}_domain_${domain}`;
    const lastApplicationKey = `last_app_${userId}`;

    // Increment counters
    const userCount = (this.cache.get(userKey) || 0) + 1;
    const userDomainCount = (this.cache.get(userDomainKey) || 0) + 1;

    this.cache.set(userKey, userCount);
    this.cache.set(userDomainKey, userDomainCount);
    this.cache.set(lastApplicationKey, Date.now());

    loggers.app.info(`Recorded application for user ${userId} on ${domain}. Count: ${userCount}/hour, ${userDomainCount}/domain`);
  }

  // Get current usage stats for user
  getUserStats(userId) {
    const userKey = `user_${userId}`;
    const lastApplicationKey = `last_app_${userId}`;

    return {
      applicationsThisHour: this.cache.get(userKey) || 0,
      maxApplicationsPerHour: this.config.maxApplicationsPerHour,
      lastApplicationTime: this.cache.get(lastApplicationKey),
      nextAllowedTime: this.getNextAllowedTime(userId)
    };
  }

  // Calculate when user can submit next application
  getNextAllowedTime(userId) {
    const lastApplicationKey = `last_app_${userId}`;
    const lastApplicationTime = this.cache.get(lastApplicationKey);
    
    if (!lastApplicationTime) {
      return Date.now(); // Can submit immediately
    }

    return lastApplicationTime + this.config.delayBetweenApplications;
  }

  // Extract domain from URL
  extractDomain(url) {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname.toLowerCase();
    } catch (error) {
      loggers.app.error('Invalid URL for domain extraction:', url);
      return 'unknown';
    }
  }

  // Reset limits for user (admin function)
  resetUserLimits(userId) {
    const userKey = `user_${userId}`;
    const lastApplicationKey = `last_app_${userId}`;
    
    this.cache.del(userKey);
    this.cache.del(lastApplicationKey);
    
    // Also reset domain-specific limits
    const keys = this.cache.keys();
    keys.forEach(key => {
      if (key.startsWith(`user_${userId}_domain_`)) {
        this.cache.del(key);
      }
    });

    loggers.app.info(`Reset rate limits for user ${userId}`);
  }

  // Get global stats
  getGlobalStats() {
    const keys = this.cache.keys();
    const userKeys = keys.filter(key => key.startsWith('user_') && !key.includes('_domain_') && !key.includes('last_app_'));
    
    const totalApplications = userKeys.reduce((sum, key) => {
      return sum + (this.cache.get(key) || 0);
    }, 0);

    return {
      totalApplicationsThisHour: totalApplications,
      activeUsers: userKeys.length,
      cacheKeys: keys.length
    };
  }

  // Alias method for queue manager compatibility
  async checkRateLimit(userId, domain = 'default') {
    return this.canSubmitApplication(userId, domain);
  }

  // Record application attempt for queue manager
  async recordApplicationAttempt(userId, applicationData) {
    const domain = applicationData.listingUrl ? this.extractDomain(applicationData.listingUrl) : 'default';
    this.recordApplication(userId, domain);
  }
}

module.exports = RateLimitingService;