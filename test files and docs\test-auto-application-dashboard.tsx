/**
 * Auto-Application Dashboard Component Test
 * Tests the React Native dashboard component functionality
 */

import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { Alert } from 'react-native';
import AutoApplicationDashboardScreen from '../zakmakelaar-frontend/app/auto-application-dashboard';
import { autoApplicationService } from '../zakmakelaar-frontend/services/autoApplicationService';

// Mock dependencies
jest.mock('../zakmakelaar-frontend/services/autoApplicationService');
jest.mock('../zakmakelaar-frontend/store/authStore', () => ({
  useAuthStore: () => ({
    user: { id: 'test-user-123', email: '<EMAIL>' }
  })
}));

jest.mock('expo-router', () => ({
  useRouter: () => ({
    back: jest.fn(),
    push: jest.fn()
  })
}));

jest.mock('react-native-safe-area-context', () => ({
  useSafeAreaInsets: () => ({ top: 44, bottom: 34, left: 0, right: 0 })
}));

// Mock data
const mockStats = {
  totalApplications: 25,
  successfulApplications: 18,
  failedApplications: 7,
  pendingApplications: 3,
  successRate: 72,
  averageResponseTime: 1250,
  applicationsToday: 2,
  applicationsThisWeek: 8,
  applicationsThisMonth: 25,
  lastApplicationDate: new Date('2025-01-06T10:30:00Z'),
  topPerformingCriteria: [
    { criteria: 'Amsterdam', successRate: 80, applications: 15 },
    { criteria: 'Furnished', successRate: 75, applications: 12 }
  ],
  recentActivity: [
    { date: new Date('2025-01-06'), applications: 2, successes: 1 },
    { date: new Date('2025-01-05'), applications: 3, successes: 2 }
  ]
};

const mockQueue = [
  {
    _id: 'queue-1',
    userId: 'test-user-123',
    listingId: 'listing-1',
    listingUrl: 'https://example.com/listing/1',
    listingTitle: 'Beautiful 2-room apartment in Amsterdam',
    status: 'pending',
    priority: 8,
    scheduledFor: new Date('2025-01-07T14:00:00Z'),
    attempts: 0,
    maxAttempts: 3,
    qualityScore: 0.85,
    createdAt: new Date('2025-01-06T12:00:00Z'),
    updatedAt: new Date('2025-01-06T12:00:00Z')
  }
];const mo
ckResults = [
  {
    _id: 'result-1',
    userId: 'test-user-123',
    queueId: 'queue-1',
    listingId: 'listing-1',
    listingUrl: 'https://example.com/listing/1',
    listingTitle: 'Cozy studio in Utrecht',
    status: 'success',
    submittedAt: new Date('2025-01-06T09:15:00Z'),
    responseTime: 1100,
    formData: { name: 'Test User', email: '<EMAIL>' },
    screenshots: ['screenshot1.png'],
    landlordResponse: {
      status: 'pending',
      message: 'We will review your application',
      receivedAt: new Date('2025-01-06T10:00:00Z')
    },
    metadata: {
      browserProfile: 'chrome-profile-1',
      ipAddress: '*************',
      submissionMethod: 'auto',
      qualityScore: 0.92,
      processingTime: 2500,
      retentionDate: new Date('2025-07-06T09:15:00Z'),
      archived: false
    },
    createdAt: new Date('2025-01-06T09:15:00Z')
  }
];

const mockScraperStats = {
  processedListingsCount: 150,
  qualityScoreCacheSize: 75,
  currentlyProcessingCount: 3,
  cacheExpiryMs: 3600000,
  minQualityScore: 0.6,
  autoApplicationsTriggered: 12,
  duplicatesSkipped: 8,
  lastProcessingTime: new Date('2025-01-06T11:30:00Z')
};

describe('AutoApplicationDashboardScreen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup default mocks
    (autoApplicationService.getStats as jest.Mock).mockResolvedValue({
      success: true,
      data: mockStats
    });
    
    (autoApplicationService.getQueue as jest.Mock).mockResolvedValue({
      success: true,
      data: mockQueue
    });
    
    (autoApplicationService.getResults as jest.Mock).mockResolvedValue({
      success: true,
      data: { results: mockResults, pagination: { page: 1, limit: 10, total: 1, totalPages: 1 } }
    });
    
    (autoApplicationService.getScraperIntegrationStats as jest.Mock).mockResolvedValue({
      success: true,
      data: mockScraperStats
    });
  });

  it('renders dashboard with loading state initially', () => {
    const { getByText } = render(<AutoApplicationDashboardScreen />);
    expect(getByText('Loading dashboard...')).toBeTruthy();
  });

  it('loads and displays stats correctly', async () => {
    const { getByText } = render(<AutoApplicationDashboardScreen />);
    
    await waitFor(() => {
      expect(getByText('25')).toBeTruthy(); // Total applications
      expect(getByText('72%')).toBeTruthy(); // Success rate
      expect(getByText('8')).toBeTruthy(); // Applications this week
      expect(getByText('1250ms')).toBeTruthy(); // Average response time
    });
  });

  it('displays scraper integration stats', async () => {
    const { getByText } = render(<AutoApplicationDashboardScreen />);
    
    await waitFor(() => {
      expect(getByText('12')).toBeTruthy(); // Auto-applications triggered
      expect(getByText('150')).toBeTruthy(); // Processed listings
      expect(getByText('8')).toBeTruthy(); // Duplicates skipped
    });
  });

  it('switches between tabs correctly', async () => {
    const { getByText } = render(<AutoApplicationDashboardScreen />);
    
    await waitFor(() => {
      expect(getByText('Statistics')).toBeTruthy();
    });
    
    // Switch to Queue tab
    fireEvent.press(getByText('Queue'));
    await waitFor(() => {
      expect(getByText('Application Queue (1)')).toBeTruthy();
      expect(getByText('Beautiful 2-room apartment in Amsterdam')).toBeTruthy();
    });
    
    // Switch to Results tab
    fireEvent.press(getByText('Results'));
    await waitFor(() => {
      expect(getByText('Recent Results')).toBeTruthy();
      expect(getByText('Cozy studio in Utrecht')).toBeTruthy();
    });
  });
});