const FormAutomationService = require("./src/services/formAutomationService");
const { setupPageStealth } = require("./src/services/scraperUtils");
const puppeteer = require("puppeteer");

// Debug runner to see what's happening with Funda form automation
(async () => {
  const service = new FormAutomationService();

  // Input data from user
  const propertyUrl =
    "https://www.funda.nl/detail/huur/utrecht/appartement-eendrachtlaan-46-d/43728488/";
  const applicant = {
    fullName: "Wellis Hant",
    email: "<EMAIL>",
    phone: "06 53123456", // Dutch mobile in allocated range with spaces
  };

  // Split name
  const [firstName, ...rest] = applicant.fullName.trim().split(/\s+/);
  const lastName = rest.join(" ") || "";

  // Provide a concise polite template to avoid undefined placeholders
  const messageTemplates = {
    default: `<PERSON><PERSON> verhuurder,\n\nIk ben zeer geïnteresseerd in deze woning en zou graag meer informatie ontvangen over de bezichtigingsmogelijkheden en beschikbaarheid.\n\nAlvast hartelijk dank.\n\nMet vriendelijke groet,\n{firstName} {lastName}`,
  };

  // Build user settings object expected by the service
  const userSettings = {
    formData: {
      firstName,
      lastName,
      email: applicant.email,
      phone: applicant.phone,
      // Optional fields
      age: "28",
      occupation: "Software Developer",
      monthlyIncome: "4500",
      additionalInfo:
        "Ik ben een betrouwbare huurder en zorg goed voor de woning.",
    },
    messageTemplates,
  };

  // Application object
  const application = {
    _id: `funda-${Date.now()}`,
    userId: "manual-runner",
    propertyUrl,
    status: "processing",
  };

  let browser;
  let page;
  try {
    console.log("Launching visible browser for debugging...");

    // Try to find Chrome executable
    let executablePath;
    const fs = require("fs");
    const possiblePaths = [
      "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
      "C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe",
    ];

    for (const path of possiblePaths) {
      if (fs.existsSync(path)) {
        executablePath = path;
        break;
      }
    }

    if (!executablePath) {
      console.log("Chrome not found, using default Puppeteer browser");
    }

    const launchOptions = {
      headless: false, // Make browser visible
      devtools: false, // Don't open devtools automatically
      // Remove slowMo for faster execution
      args: [
        "--no-sandbox",
        "--disable-setuid-sandbox",
        "--disable-dev-shm-usage",
        "--start-maximized",
        "--disable-web-security",
        "--disable-features=VizDisplayCompositor",
      ],
    };

    if (executablePath) {
      launchOptions.executablePath = executablePath;
    }

    browser = await puppeteer.launch(launchOptions);
    page = await browser.newPage();

    // Set viewport
    await page.setViewport({ width: 1366, height: 768 });

    // Apply stealth to the page
    try {
      await setupPageStealth(page);
      console.log("Applied stealth settings");
    } catch (e) {
      console.log("Stealth setup warning:", e?.message);
    }

    console.log("Navigating to property page...");
    await page.goto(propertyUrl, { waitUntil: "networkidle2", timeout: 45000 });

    console.log("Running Funda application handler...");
    const result = await service.handleFundaApplication(
      page,
      application,
      userSettings
    );

    console.log("\n=== RESULT ===");
    console.log(result);
    console.log("If success is true, the inquiry should be submitted.");

    // Keep browser open for inspection
    console.log("\nBrowser will stay open for 30 seconds for inspection...");
    await new Promise((resolve) => setTimeout(resolve, 30000));
  } catch (err) {
    console.error("Debug runner failed:", err);

    // Keep browser open even on error for inspection
    if (browser && page) {
      console.log(
        "\nBrowser will stay open for 30 seconds for error inspection..."
      );
      await new Promise((resolve) => setTimeout(resolve, 30000));
    }
  } finally {
    try {
      if (page) await page.close();
    } catch {}
    try {
      if (browser) await browser.close();
    } catch {}
  }
})();
