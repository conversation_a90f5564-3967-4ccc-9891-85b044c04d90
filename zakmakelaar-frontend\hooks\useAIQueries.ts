import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  aiService,
  MatchingRequest,
  ApplicationGenerationRequest,
  ContractAnalysisRequest,
  MarketAnalysisRequest,
  TranslationRequest,
} from '../services/aiService';
import { queryKeys, cacheUtils } from '../services/queryClient';
import { User, UserPreferences } from '../services/authService';
import { Listing } from '../services/listingsService';

// AI Property Matching
export const usePropertyMatches = (
  preferences: UserPreferences | undefined,
  user: User | undefined,
  enabled = true
) => {
  return useQuery({
    queryKey: queryKeys.ai.match(preferences),
    queryFn: async () => {
      if (!preferences || !user) {
        throw new Error('User preferences and profile required for matching');
      }
      
      const request: MatchingRequest = {
        userProfile: user,
        preferences,
        maxResults: 50,
      };
      
      const response = await aiService.getPropertyMatches(request);
      if (!response.success) {
        throw new Error(response.message || 'Failed to get property matches');
      }
      return response.data;
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
    enabled: enabled && !!preferences && !!user,
    retry: 2,
  });
};

export const useRefreshMatches = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ preferences, user }: { preferences: UserPreferences; user: User }) => {
      const request: MatchingRequest = {
        userProfile: user,
        preferences,
        maxResults: 50,
      };
      
      const response = await aiService.getPropertyMatches(request);
      if (!response.success) {
        throw new Error(response.message || 'Failed to refresh matches');
      }
      return response.data;
    },
    onSuccess: (data, variables) => {
      // Update the cache with new matches
      queryClient.setQueryData(queryKeys.ai.match(variables.preferences), data);
      
      // Invalidate related queries
      cacheUtils.invalidateMatches();
    },
    onError: (error) => {
      console.error('Refresh matches error:', error);
    },
  });
};

// AI Application Generation
export const useGenerateApplication = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({
      listing,
      user,
      template = 'professional',
      customMessage,
    }: {
      listing: Listing;
      user: User;
      template?: 'professional' | 'personal' | 'creative' | 'student' | 'expat';
      customMessage?: string;
    }) => {
      const request: ApplicationGenerationRequest = {
        listing,
        userProfile: user,
        template,
        customMessage,
        includeDocuments: true,
      };
      
      const response = await aiService.generateApplication(request);
      if (!response.success) {
        throw new Error(response.message || 'Failed to generate application');
      }
      return response.data;
    },
    onSuccess: (data, variables) => {
      // Cache the generated application
      const applicationId = `${variables.listing._id}_${Date.now()}`;
      queryClient.setQueryData(queryKeys.ai.application(applicationId), data);
      
      // Invalidate applications list
      queryClient.invalidateQueries({ queryKey: queryKeys.ai.applications() });
    },
    onError: (error) => {
      console.error('Generate application error:', error);
    },
  });
};

// Contract Analysis
export const useAnalyzeContract = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (request: ContractAnalysisRequest) => {
      const response = await aiService.analyzeContract(request);
      if (!response.success) {
        throw new Error(response.message || 'Failed to analyze contract');
      }
      return response.data;
    },
    onSuccess: (data) => {
      // Cache the analysis result
      const analysisId = `analysis_${Date.now()}`;
      queryClient.setQueryData(queryKeys.ai.analysis(analysisId), data);
      
      // Invalidate contract analyses list
      queryClient.invalidateQueries({ queryKey: queryKeys.ai.contractAnalysis() });
    },
    onError: (error) => {
      console.error('Contract analysis error:', error);
    },
  });
};

// Market Insights
export const useMarketInsights = (location: string, enabled = true) => {
  return useQuery({
    queryKey: queryKeys.ai.insights(location),
    queryFn: async () => {
      const request: MarketAnalysisRequest = {
        location,
        timeframe: '3months',
      };
      
      const response = await aiService.getMarketAnalysis(request);
      console.log('useAIQueries - Market insights response:', response);
      if (!response.success) {
        console.log('useAIQueries - Market insights failed, response.success is false');
        throw new Error(response.message || 'Failed to get market insights');
      }
      return response.data;
    },
    staleTime: 30 * 60 * 1000, // 30 minutes
    enabled: enabled && !!location,
    retry: 2,
  });
};

export const useRefreshMarketInsights = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({
      location,
      propertyType,
      priceRange,
    }: {
      location: string;
      propertyType?: string;
      priceRange?: { min: number; max: number };
    }) => {
      const request: MarketAnalysisRequest = {
        location,
        propertyType,
        priceRange,
        timeframe: '3months',
      };
      
      const response = await aiService.getMarketAnalysis(request);
      if (!response.success) {
        throw new Error(response.message || 'Failed to refresh market insights');
      }
      return response.data;
    },
    onSuccess: (data, variables) => {
      // Update the cache with new insights
      queryClient.setQueryData(queryKeys.ai.insights(variables.location), data);
      
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: queryKeys.ai.marketInsights() });
    },
    onError: (error) => {
      console.error('Refresh market insights error:', error);
    },
  });
};

// Translation
export const useTranslation = (text: string, targetLanguage: 'en' | 'nl', enabled = true) => {
  return useQuery({
    queryKey: queryKeys.ai.translate(text, targetLanguage),
    queryFn: async () => {
      const request: TranslationRequest = {
        text,
        targetLanguage,
      };
      
      const response = await aiService.translateText(request);
      if (!response.success) {
        throw new Error(response.message || 'Translation failed');
      }
      return response.data;
    },
    staleTime: 60 * 60 * 1000, // 1 hour (translations don't change)
    enabled: enabled && !!text.trim() && text.length > 0,
    retry: 1,
  });
};

export const useTranslateText = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({
      text,
      targetLanguage,
      sourceLanguage,
    }: {
      text: string;
      targetLanguage: 'en' | 'nl' | 'de' | 'fr' | 'es';
      sourceLanguage?: string;
    }) => {
      const request: TranslationRequest = {
        text,
        targetLanguage,
        sourceLanguage,
      };
      
      const response = await aiService.translateText(request);
      if (!response.success) {
        throw new Error(response.message || 'Translation failed');
      }
      return response.data;
    },
    onSuccess: (data, variables) => {
      // Cache the translation
      queryClient.setQueryData(
        queryKeys.ai.translate(variables.text, variables.targetLanguage),
        data
      );
    },
    onError: (error) => {
      console.error('Translation error:', error);
    },
  });
};

// Personalized Recommendations
export const usePersonalizedRecommendations = (userId?: string, limit = 10, enabled = true) => {
  return useQuery({
    queryKey: [...queryKeys.ai.all, 'recommendations', userId, limit],
    queryFn: async () => {
      const response = await aiService.getPersonalizedRecommendations(userId, limit);
      if (!response.success) {
        throw new Error(response.message || 'Failed to get recommendations');
      }
      return response.data;
    },
    staleTime: 15 * 60 * 1000, // 15 minutes
    enabled: enabled && !!userId,
    retry: 2,
  });
};

// Listing Description Analysis
export const useAnalyzeDescription = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (description: string) => {
      const response = await aiService.analyzeListingDescription(description);
      if (!response.success) {
        throw new Error(response.message || 'Failed to analyze description');
      }
      return response.data;
    },
    onSuccess: (data, description) => {
      // Cache the analysis
      queryClient.setQueryData(
        [...queryKeys.ai.all, 'description-analysis', description],
        data
      );
    },
    onError: (error) => {
      console.error('Description analysis error:', error);
    },
  });
};

// Search Suggestions
export const useSearchSuggestions = (query: string, enabled = true) => {
  return useQuery({
    queryKey: [...queryKeys.ai.all, 'search-suggestions', query],
    queryFn: async () => {
      const response = await aiService.getSearchSuggestions(query, 5);
      if (!response.success) {
        throw new Error(response.message || 'Failed to get search suggestions');
      }
      return response.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: enabled && !!query.trim() && query.length >= 2,
    retry: 1,
  });
};

// Property Comparison
export const useCompareProperties = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (listingIds: string[]) => {
      const response = await aiService.compareProperties(listingIds);
      if (!response.success) {
        throw new Error(response.message || 'Failed to compare properties');
      }
      return response.data;
    },
    onSuccess: (data, listingIds) => {
      // Cache the comparison
      queryClient.setQueryData(
        [...queryKeys.ai.all, 'property-comparison', listingIds.sort().join(',')],
        data
      );
    },
    onError: (error) => {
      console.error('Property comparison error:', error);
    },
  });
};

// AI Chat
export const useChatResponse = () => {
  return useMutation({
    mutationFn: async ({
      message,
      context,
    }: {
      message: string;
      context?: {
        listing?: Listing;
        userPreferences?: UserPreferences;
        conversationHistory?: { role: 'user' | 'assistant'; content: string }[];
      };
    }) => {
      const response = await aiService.getChatResponse(message, context);
      if (!response.success) {
        throw new Error(response.message || 'Failed to get chat response');
      }
      return response.data;
    },
    onError: (error) => {
      console.error('Chat response error:', error);
    },
  });
};

// Utility hooks
export const useAIFeatures = (user?: User, preferences?: UserPreferences) => {
  const matchesQuery = usePropertyMatches(preferences, user);
  const refreshMatches = useRefreshMatches();
  const generateApplication = useGenerateApplication();
  const analyzeContract = useAnalyzeContract();
  const translateText = useTranslateText();
  
  return {
    // Property matching
    matches: matchesQuery.data,
    isLoadingMatches: matchesQuery.isLoading,
    matchesError: matchesQuery.error,
    refreshMatches: (preferences: UserPreferences, user: User) =>
      refreshMatches.mutate({ preferences, user }),
    isRefreshingMatches: refreshMatches.isPending,
    
    // Application generation
    generateApplication: generateApplication.mutate,
    isGeneratingApplication: generateApplication.isPending,
    applicationError: generateApplication.error,
    
    // Contract analysis
    analyzeContract: analyzeContract.mutate,
    isAnalyzingContract: analyzeContract.isPending,
    contractError: analyzeContract.error,
    
    // Translation
    translateText: translateText.mutate,
    isTranslating: translateText.isPending,
    translationError: translateText.error,
  };
};

// Prefetch utilities
export const usePrefetchAI = () => {
  const queryClient = useQueryClient();
  
  return {
    prefetchMatches: (preferences: UserPreferences, user: User) => {
      queryClient.prefetchQuery({
        queryKey: queryKeys.ai.match(preferences),
        queryFn: async () => {
          const request: MatchingRequest = {
            userProfile: user,
            preferences,
            maxResults: 50,
          };
          
          const response = await aiService.getPropertyMatches(request);
          if (!response.success) {
            throw new Error(response.message || 'Failed to get property matches');
          }
          return response.data;
        },
        staleTime: 10 * 60 * 1000,
      });
    },
    
    prefetchMarketInsights: (location: string) => {
      queryClient.prefetchQuery({
        queryKey: queryKeys.ai.insights(location),
        queryFn: async () => {
          const request: MarketAnalysisRequest = {
            location,
            timeframe: '3months',
          };
          
          const response = await aiService.getMarketAnalysis(request);
          if (!response.success) {
            throw new Error(response.message || 'Failed to get market insights');
          }
          return response.data;
        },
        staleTime: 30 * 60 * 1000,
      });
    },
  };
};