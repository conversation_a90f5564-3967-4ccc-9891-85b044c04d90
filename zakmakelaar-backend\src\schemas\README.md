# Unified Property Schema

This directory contains the unified property schema definition and validation system for the Zakmakelaar application. The schema standardizes property data across all scraping sources (Funda, Huurwoningen, Pararius) while maintaining full compatibility with the frontend expectations.

## Overview

The unified property schema solves the problem of inconsistent data formats from different property listing websites by providing:

- **Standardized data structure** across all sources
- **Frontend compatibility** with existing React Native components
- **Comprehensive validation** with detailed error reporting
- **Type safety** with TypeScript definitions
- **Flexible field mapping** for different source formats

## Files

- `unifiedPropertySchema.js` - Main schema definition and validation functions
- `unifiedPropertySchema.d.ts` - TypeScript type definitions
- `README.md` - This documentation file
- `../tests/unifiedPropertySchema.test.js` - Comprehensive test suite

## Key Features

### Frontend Compatibility

The schema is designed to work seamlessly with the existing frontend:

- **Price handling**: Supports both string (`"€ 1500 per maand"`) and number (`1500`) formats
- **Room counts**: Accepts both string (`"3"`) and number (`3`) formats for rooms/bedrooms
- **Location data**: Supports both simple string and structured object formats
- **Dutch terminology**: Uses proper Dutch terms (`Kaal`, `Gestoffeerd`, `Gemeubileerd`)
- **Image arrays**: Simple string array format as expected by frontend components

### Data Validation

- **Required fields**: `title`, `source`, `url`, `location`, `price`
- **Source validation**: Only allows valid sources (`funda.nl`, `huurwoningen.nl`, `pararius.nl`)
- **URL validation**: Ensures proper URL format
- **Type coercion**: Automatically converts compatible types
- **Default values**: Provides sensible defaults for optional fields

### Flexible Structure

```javascript
const property = {
  // Basic Information
  title: "Beautiful Apartment in Amsterdam",
  description: "Spacious 2-bedroom apartment...",
  
  // Source Information
  source: "funda.nl",
  url: "https://www.funda.nl/...",
  dateAdded: "2024-01-15T10:30:00.000Z",
  
  // Location (supports both formats)
  location: "Amsterdam" // Simple string
  // OR
  location: {
    _unified: {
      address: {
        street: "Damrak",
        houseNumber: "123",
        city: "Amsterdam",
        postalCode: "1012 LG"
      },
      coordinates: { lat: 52.3676, lng: 4.9041 }
    },
    _legacy: "Amsterdam"
  },
  
  // Property Details
  propertyType: "appartement",
  size: "85 m²",
  area: 85,
  rooms: 3,
  bedrooms: 2,
  bathrooms: 1,
  year: "2020",
  
  // Financial
  price: "€ 1800 per maand", // or 1800
  deposit: 3600,
  utilities: 150,
  
  // Features
  interior: "Gemeubileerd",
  furnished: true,
  garden: false,
  balcony: true,
  parking: true,
  energyLabel: "A",
  
  // Media
  images: [
    "https://example.com/image1.jpg",
    "https://example.com/image2.jpg"
  ],
  
  // Frontend fields
  isActive: true,
  features: ["balcony", "parking"],
  
  // Contact
  contactInfo: {
    name: "John Doe",
    email: "<EMAIL>",
    phone: "+31612345678"
  }
};
```

## Usage

### Basic Validation

```javascript
const { validateProperty } = require('./unifiedPropertySchema');

const property = {
  title: 'Test Property',
  source: 'funda.nl',
  url: 'https://www.funda.nl/test',
  location: 'Amsterdam',
  price: 1500
};

const result = validateProperty(property);

if (result.error) {
  console.error('Validation failed:', result.error.details);
} else {
  console.log('Valid property:', result.value);
}
```

### Strict Validation

```javascript
const { validatePropertyStrict } = require('./unifiedPropertySchema');

// Requires all fields to be present
const result = validatePropertyStrict(property);
```

### Creating Minimal Properties

```javascript
const { createMinimalProperty } = require('./unifiedPropertySchema');

// Create with defaults
const minimal = createMinimalProperty();

// Create with custom data
const custom = createMinimalProperty({
  title: 'My Property',
  source: 'pararius.nl',
  price: 2000
});
```

### Getting Schema Information

```javascript
const { getSchema, getValidationOptions } = require('./unifiedPropertySchema');

const schema = getSchema(); // Returns Joi schema object
const options = getValidationOptions(); // Returns validation options
const strictOptions = getValidationOptions(true); // Strict options
```

## Validation Rules

### Required Fields
- `title`: Property title (1-500 characters)
- `source`: Must be one of `funda.nl`, `huurwoningen.nl`, `pararius.nl`
- `url`: Valid URL format
- `location`: String or structured location object
- `price`: String or positive number

### Optional Fields with Validation
- `propertyType`: Valid property types (apartment, house, studio, etc.)
- `size`: Must match pattern like "85 m²"
- `area`: Positive number
- `rooms`, `bedrooms`, `bathrooms`: String or positive number
- `year`: 4-digit year string
- `interior`: Valid interior types (Dutch or English)
- `energyLabel`: Valid energy labels (A+++ to G)
- `images`: Array of valid URLs
- `dateAdded`, `dateAvailable`: ISO date strings
- `contactInfo.email`: Valid email format

### Default Values
- `propertyType`: "woning"
- `bathrooms`: "1"
- `furnished`: false
- `pets`, `smoking`, `garden`, `balcony`, `parking`: false
- `isActive`: true
- `images`, `features`: []
- `contactInfo`: {}
- `dateAdded`: Current ISO timestamp

## Error Handling

The validation system provides detailed error information:

```javascript
const result = validateProperty(invalidProperty);

if (result.error) {
  result.error.details.forEach(error => {
    console.log(`Field: ${error.path.join('.')}`);
    console.log(`Error: ${error.message}`);
    console.log(`Type: ${error.type}`);
  });
}
```

Common error types:
- `any.required`: Required field missing
- `string.uri`: Invalid URL format
- `any.only`: Invalid enum value
- `string.pattern.base`: String doesn't match required pattern
- `number.positive`: Number must be positive

## Integration with Scrapers

The schema is designed to work with the transformation pipeline:

1. **Raw data extraction** from scraping sources
2. **Field mapping** using source-specific configurations
3. **Data transformation** to unified format
4. **Validation** against unified schema
5. **Storage** in database with consistent structure

## Frontend Integration

The schema maintains full compatibility with existing frontend components:

- `ListingsService.formatPrice()` works with both string and number prices
- `ListingDetailsScreen` displays all fields correctly
- `listingsStore` state management handles the data structure
- All filter and search functionality continues to work

## Testing

Comprehensive test suite covers:
- Basic validation scenarios
- All field types and formats
- Error conditions
- Edge cases
- Frontend compatibility
- Dutch terminology support

Run tests:
```bash
npm test src/tests/unifiedPropertySchema.test.js
```

## Migration Support

The schema supports migration from existing Listing model:

```javascript
// Legacy Listing format is automatically compatible
const legacyListing = {
  title: "Property",
  price: "€ 1500",
  location: "Amsterdam",
  url: "https://example.com",
  source: "funda.nl",
  size: "85 m²",
  rooms: "3",
  bedrooms: "2"
};

// Validates successfully with unified schema
const result = validateProperty(legacyListing);
```

## Performance

- **Validation speed**: < 1ms per property for typical data
- **Memory usage**: Minimal overhead with efficient Joi validation
- **Type coercion**: Automatic conversion reduces processing time
- **Caching**: Schema compilation is cached for performance

## Future Extensibility

The schema is designed for easy extension:

- Add new source websites by updating the `source` enum
- Add new property types or features as needed
- Extend internal metadata for additional processing information
- Support new frontend requirements without breaking changes

## Best Practices

1. **Always validate** property data before storage
2. **Use TypeScript definitions** for better IDE support
3. **Handle validation errors** gracefully in your application
4. **Preserve raw data** in `_internal.rawData` for debugging
5. **Use appropriate validation mode** (normal vs strict) based on context
6. **Test with real data** from all supported sources