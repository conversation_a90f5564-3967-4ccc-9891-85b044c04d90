/**
 * Performance Monitor Module
 * 
 * This module provides performance monitoring for the unified schema transformation pipeline.
 * It tracks transformation speed, memory usage, and other performance metrics.
 */

const os = require('os');
const { EventEmitter } = require('events');
const { loggers } = require('../services/logger');

/**
 * Performance Monitor Class
 * Tracks performance metrics for the transformation pipeline
 */
class PerformanceMonitor extends EventEmitter {
  constructor() {
    super();
    this.metrics = {
      transformations: {
        total: 0,
        successful: 0,
        failed: 0,
        cached: 0
      },
      timing: {
        average: 0,
        min: Number.MAX_VALUE,
        max: 0,
        total: 0
      },
      memory: {
        baseline: process.memoryUsage().heapUsed,
        current: process.memoryUsage().heapUsed,
        peak: process.memoryUsage().heapUsed
      },
      batches: {
        total: 0,
        averageSize: 0,
        maxSize: 0
      },
      sources: {}
    };
    
    this.startTime = Date.now();
    this.lastResetTime = this.startTime;
    
    // Track memory usage periodically
    this.memoryInterval = setInterval(() => {
      this.updateMemoryMetrics();
    }, 5000);
  }

  /**
   * Record a transformation operation
   * @param {Object} data - Transformation data
   * @param {string} data.source - Source identifier
   * @param {number} data.duration - Duration in milliseconds
   * @param {boolean} data.success - Whether the transformation was successful
   * @param {boolean} data.cached - Whether the result was from cache
   * @param {Object} data.quality - Data quality metrics
   */
  recordTransformation(data) {
    // Update general metrics
    this.metrics.transformations.total++;
    
    if (data.success) {
      this.metrics.transformations.successful++;
    } else {
      this.metrics.transformations.failed++;
    }
    
    if (data.cached) {
      this.metrics.transformations.cached++;
    }
    
    // Update timing metrics
    if (data.duration) {
      this.metrics.timing.total += data.duration;
      this.metrics.timing.average = this.metrics.timing.total / this.metrics.transformations.total;
      this.metrics.timing.min = Math.min(this.metrics.timing.min, data.duration);
      this.metrics.timing.max = Math.max(this.metrics.timing.max, data.duration);
    }
    
    // Update source-specific metrics
    if (data.source) {
      if (!this.metrics.sources[data.source]) {
        this.metrics.sources[data.source] = {
          total: 0,
          successful: 0,
          failed: 0,
          averageDuration: 0,
          totalDuration: 0
        };
      }
      
      const sourceMetrics = this.metrics.sources[data.source];
      sourceMetrics.total++;
      
      if (data.success) {
        sourceMetrics.successful++;
      } else {
        sourceMetrics.failed++;
      }
      
      if (data.duration) {
        sourceMetrics.totalDuration += data.duration;
        sourceMetrics.averageDuration = sourceMetrics.totalDuration / sourceMetrics.total;
      }
    }
    
    // Update memory metrics
    this.updateMemoryMetrics();
    
    // Emit metrics update event
    this.emit('metrics', this.getMetrics());
  }

  /**
   * Record a batch transformation operation
   * @param {Object} data - Batch data
   * @param {string} data.source - Source identifier
   * @param {number} data.size - Batch size
   * @param {number} data.duration - Duration in milliseconds
   * @param {number} data.successful - Number of successful transformations
   * @param {number} data.failed - Number of failed transformations
   */
  recordBatch(data) {
    // Update batch metrics
    this.metrics.batches.total++;
    
    const totalSize = (this.metrics.batches.averageSize * (this.metrics.batches.total - 1)) + data.size;
    this.metrics.batches.averageSize = totalSize / this.metrics.batches.total;
    this.metrics.batches.maxSize = Math.max(this.metrics.batches.maxSize, data.size);
    
    // Update transformation metrics
    this.metrics.transformations.total += data.size;
    this.metrics.transformations.successful += data.successful || 0;
    this.metrics.transformations.failed += data.failed || 0;
    
    // Update memory metrics
    this.updateMemoryMetrics();
    
    // Emit metrics update event
    this.emit('metrics', this.getMetrics());
  }

  /**
   * Update memory usage metrics
   */
  updateMemoryMetrics() {
    const memoryUsage = process.memoryUsage();
    this.metrics.memory.current = memoryUsage.heapUsed;
    this.metrics.memory.peak = Math.max(this.metrics.memory.peak, memoryUsage.heapUsed);
  }

  /**
   * Get current performance metrics
   * @returns {Object} Performance metrics
   */
  getMetrics() {
    const currentTime = Date.now();
    const uptime = currentTime - this.startTime;
    const timeSinceReset = currentTime - this.lastResetTime;
    
    // Calculate derived metrics
    const transformationsPerSecond = this.metrics.transformations.total / (timeSinceReset / 1000);
    const cacheHitRate = this.metrics.transformations.total > 0 
      ? (this.metrics.transformations.cached / this.metrics.transformations.total) * 100 
      : 0;
    const successRate = this.metrics.transformations.total > 0 
      ? (this.metrics.transformations.successful / this.metrics.transformations.total) * 100 
      : 0;
    
    // Get system metrics
    const systemMetrics = {
      cpuUsage: os.loadavg()[0], // 1-minute load average
      totalMemory: os.totalmem(),
      freeMemory: os.freemem(),
      uptime: os.uptime()
    };
    
    return {
      ...this.metrics,
      derived: {
        transformationsPerSecond,
        cacheHitRate,
        successRate,
        memoryUsageMB: Math.round((this.metrics.memory.current - this.metrics.memory.baseline) / (1024 * 1024)),
        peakMemoryUsageMB: Math.round((this.metrics.memory.peak - this.metrics.memory.baseline) / (1024 * 1024))
      },
      system: systemMetrics,
      timestamp: currentTime,
      uptime,
      timeSinceReset
    };
  }

  /**
   * Reset performance metrics
   */
  resetMetrics() {
    this.metrics = {
      transformations: {
        total: 0,
        successful: 0,
        failed: 0,
        cached: 0
      },
      timing: {
        average: 0,
        min: Number.MAX_VALUE,
        max: 0,
        total: 0
      },
      memory: {
        baseline: process.memoryUsage().heapUsed,
        current: process.memoryUsage().heapUsed,
        peak: process.memoryUsage().heapUsed
      },
      batches: {
        total: 0,
        averageSize: 0,
        maxSize: 0
      },
      sources: {}
    };
    
    this.lastResetTime = Date.now();
    this.emit('reset');
  }

  /**
   * Stop the performance monitor
   */
  stop() {
    clearInterval(this.memoryInterval);
    this.removeAllListeners();
  }
}

// Create a singleton instance
const performanceMonitor = new PerformanceMonitor();

module.exports = {
  performanceMonitor,
  PerformanceMonitor
};