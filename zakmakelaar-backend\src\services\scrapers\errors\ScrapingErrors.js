/**
 * Enhanced error handling for scrapers
 */

class ScrapingError extends Error {
  constructor(message, type, retryable = false, siteName = null) {
    super(message);
    this.name = 'ScrapingError';
    this.type = type;
    this.retryable = retryable;
    this.siteName = siteName;
    this.timestamp = new Date().toISOString();
  }
}

class NetworkError extends ScrapingError {
  constructor(message, siteName = null) {
    super(message, 'NETWORK', true, siteName);
    this.name = 'NetworkError';
  }
}

class TimeoutError extends ScrapingError {
  constructor(message, siteName = null) {
    super(message, 'TIMEOUT', true, siteName);
    this.name = 'TimeoutError';
  }
}

class BlockedError extends ScrapingError {
  constructor(message, siteName = null) {
    super(message, 'BLOCKED', false, siteName);
    this.name = 'BlockedError';
  }
}

class ParsingError extends ScrapingError {
  constructor(message, siteName = null) {
    super(message, 'PARSING', false, siteName);
    this.name = 'ParsingError';
  }
}

class RateLimitError extends ScrapingError {
  constructor(message, siteName = null) {
    super(message, 'RATE_LIMIT', true, siteName);
    this.name = 'RateLimitError';
  }
}

class ValidationError extends ScrapingError {
  constructor(message, siteName = null) {
    super(message, 'VALIDATION', false, siteName);
    this.name = 'ValidationError';
  }
}

/**
 * Error classifier that determines error type and recovery strategy
 */
class ErrorClassifier {
  static classify(error, siteName = null) {
    const message = error.message.toLowerCase();
    
    // Network-related errors
    if (this.isNetworkError(message)) {
      return new NetworkError(error.message, siteName);
    }
    
    // Timeout errors
    if (this.isTimeoutError(message)) {
      return new TimeoutError(error.message, siteName);
    }
    
    // Blocked/captcha errors
    if (this.isBlockedError(message)) {
      return new BlockedError(error.message, siteName);
    }
    
    // Rate limiting errors
    if (this.isRateLimitError(message)) {
      return new RateLimitError(error.message, siteName);
    }
    
    // Parsing errors
    if (this.isParsingError(message)) {
      return new ParsingError(error.message, siteName);
    }
    
    // Default to generic scraping error
    return new ScrapingError(error.message, 'UNKNOWN', true, siteName);
  }
  
  static isNetworkError(message) {
    const networkKeywords = [
      'econnreset',
      'enotfound',
      'econnrefused',
      'network error',
      'connection failed',
      'dns lookup failed',
      'socket hang up'
    ];
    return networkKeywords.some(keyword => message.includes(keyword));
  }
  
  static isTimeoutError(message) {
    const timeoutKeywords = [
      'timeout',
      'etimedout',
      'navigation timeout',
      'waiting for selector',
      'page.goto: timeout'
    ];
    return timeoutKeywords.some(keyword => message.includes(keyword));
  }
  
  static isBlockedError(message) {
    const blockedKeywords = [
      'blocked',
      'captcha',
      'access denied',
      'forbidden',
      'bot detected',
      'cloudflare',
      'security check',
      '403',
      'ip banned'
    ];
    return blockedKeywords.some(keyword => message.includes(keyword));
  }
  
  static isRateLimitError(message) {
    const rateLimitKeywords = [
      'rate limit',
      'too many requests',
      '429',
      'quota exceeded',
      'throttled'
    ];
    return rateLimitKeywords.some(keyword => message.includes(keyword));
  }
  
  static isParsingError(message) {
    const parsingKeywords = [
      'parsing error',
      'invalid html',
      'selector not found',
      'element not found',
      'cheerio error'
    ];
    return parsingKeywords.some(keyword => message.includes(keyword));
  }
}

/**
 * Recovery strategies for different error types
 */
class ErrorRecovery {
  static async handleError(error, context = {}) {
    const { siteName, retryCount = 0, maxRetries = 3 } = context;
    
    console.log(`🔧 Handling error: ${error.type} for ${siteName || 'unknown site'}`);
    
    switch (error.type) {
      case 'NETWORK':
        return this.handleNetworkError(error, context);
        
      case 'TIMEOUT':
        return this.handleTimeoutError(error, context);
        
      case 'BLOCKED':
        return this.handleBlockedError(error, context);
        
      case 'RATE_LIMIT':
        return this.handleRateLimitError(error, context);
        
      case 'PARSING':
        return this.handleParsingError(error, context);
        
      default:
        return this.handleGenericError(error, context);
    }
  }
  
  static async handleNetworkError(error, context) {
    const { retryCount, maxRetries } = context;
    
    if (retryCount < maxRetries) {
      const delay = Math.min(1000 * Math.pow(2, retryCount), 30000); // Exponential backoff
      console.log(`🔄 Network error - retrying in ${delay}ms`);
      await new Promise(resolve => setTimeout(resolve, delay));
      return { shouldRetry: true, delay };
    }
    
    return { shouldRetry: false, reason: 'Max network retries exceeded' };
  }
  
  static async handleTimeoutError(error, context) {
    const { retryCount, maxRetries } = context;
    
    if (retryCount < maxRetries) {
      const delay = 5000 + (retryCount * 2000); // Linear backoff for timeouts
      console.log(`⏱️  Timeout error - retrying in ${delay}ms with increased timeout`);
      await new Promise(resolve => setTimeout(resolve, delay));
      return { shouldRetry: true, delay, increaseTimeout: true };
    }
    
    return { shouldRetry: false, reason: 'Max timeout retries exceeded' };
  }
  
  static async handleBlockedError(error, context) {
    console.log(`🚫 Blocked error - switching strategy`);
    
    // For blocked errors, we need to change approach
    const strategies = [
      'switchUserAgent',
      'useProxy',
      'increaseDelay',
      'skipSite'
    ];
    
    return {
      shouldRetry: true,
      strategies,
      delay: 60000 // Wait 1 minute before retry
    };
  }
  
  static async handleRateLimitError(error, context) {
    const delay = 30000; // Wait 30 seconds for rate limits
    console.log(`🐌 Rate limit error - waiting ${delay}ms`);
    await new Promise(resolve => setTimeout(resolve, delay));
    
    return {
      shouldRetry: true,
      delay,
      reduceSpeed: true
    };
  }
  
  static async handleParsingError(error, context) {
    console.log(`📝 Parsing error - may need selector updates`);
    
    // Log parsing errors for analysis
    this.logParsingError(error, context);
    
    return {
      shouldRetry: false,
      reason: 'Parsing error - selectors may need updating',
      requiresManualFix: true
    };
  }
  
  static async handleGenericError(error, context) {
    const { retryCount, maxRetries } = context;
    
    if (retryCount < maxRetries) {
      const delay = 5000;
      console.log(`❓ Generic error - retrying in ${delay}ms`);
      await new Promise(resolve => setTimeout(resolve, delay));
      return { shouldRetry: true, delay };
    }
    
    return { shouldRetry: false, reason: 'Max generic retries exceeded' };
  }
  
  static logParsingError(error, context) {
    const errorLog = {
      timestamp: new Date().toISOString(),
      siteName: context.siteName,
      error: error.message,
      type: 'PARSING',
      url: context.url,
      selector: context.selector
    };
    
    // In production, this would go to a logging service
    console.error('PARSING_ERROR_LOG:', JSON.stringify(errorLog, null, 2));
  }
}

/**
 * Error metrics and monitoring
 */
class ErrorMetrics {
  constructor() {
    this.errors = new Map();
    this.errorCounts = new Map();
  }
  
  recordError(error, siteName) {
    const key = `${siteName}_${error.type}`;
    
    if (!this.errorCounts.has(key)) {
      this.errorCounts.set(key, 0);
    }
    
    this.errorCounts.set(key, this.errorCounts.get(key) + 1);
    
    // Store recent errors for analysis
    if (!this.errors.has(siteName)) {
      this.errors.set(siteName, []);
    }
    
    const siteErrors = this.errors.get(siteName);
    siteErrors.push({
      timestamp: new Date().toISOString(),
      type: error.type,
      message: error.message,
      retryable: error.retryable
    });
    
    // Keep only last 100 errors per site
    if (siteErrors.length > 100) {
      siteErrors.splice(0, siteErrors.length - 100);
    }
  }
  
  getErrorStats(siteName = null) {
    if (siteName) {
      const siteErrors = this.errors.get(siteName) || [];
      const errorTypes = {};
      
      siteErrors.forEach(error => {
        errorTypes[error.type] = (errorTypes[error.type] || 0) + 1;
      });
      
      return {
        siteName,
        totalErrors: siteErrors.length,
        errorTypes,
        recentErrors: siteErrors.slice(-10)
      };
    }
    
    // Return stats for all sites
    const allStats = {};
    for (const [site, errors] of this.errors.entries()) {
      allStats[site] = this.getErrorStats(site);
    }
    
    return allStats;
  }
  
  shouldAlertOnErrors(siteName) {
    const siteErrors = this.errors.get(siteName) || [];
    const recentErrors = siteErrors.filter(
      error => Date.now() - new Date(error.timestamp).getTime() < 3600000 // Last hour
    );
    
    // Alert if more than 10 errors in the last hour
    return recentErrors.length > 10;
  }
}

const errorMetrics = new ErrorMetrics();

module.exports = {
  ScrapingError,
  NetworkError,
  TimeoutError,
  BlockedError,
  ParsingError,
  RateLimitError,
  ValidationError,
  ErrorClassifier,
  ErrorRecovery,
  ErrorMetrics,
  errorMetrics
};
