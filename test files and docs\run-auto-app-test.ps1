# PowerShell script to run auto-application test
Write-Host "🚀 Running Auto-Application Test..." -ForegroundColor Green
Write-Host ""

# Change to backend directory where dependencies are installed
Push-Location zakmakelaar-backend

try {
    # Run the test script from the parent directory
    node ../test-auto-application-simple.js
} catch {
    Write-Host "❌ Error running test: $_" -ForegroundColor Red
} finally {
    # Return to original directory
    Pop-Location
}

Write-Host ""
Write-Host "✅ Test completed!" -ForegroundColor Green