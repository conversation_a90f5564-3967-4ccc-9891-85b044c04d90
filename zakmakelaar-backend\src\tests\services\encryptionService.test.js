const encryptionService = require('../../services/encryptionService');
const crypto = require('crypto');

describe('EncryptionService', () => {
  const testUserId = '507f1f77bcf86cd799439011';
  const testData = 'sensitive information';
  const testPersonalInfo = {
    fullName: '<PERSON>',
    email: '<EMAIL>',
    phone: '+31612345678',
    monthlyIncome: 5000,
    guarantorInfo: {
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '+31687654321',
      monthlyIncome: 6000
    },
    emergencyContact: {
      name: 'Emergency Contact',
      email: '<EMAIL>',
      phone: '+31600000000'
    }
  };

  describe('Basic Encryption/Decryption', () => {
    test('should encrypt and decrypt data correctly', () => {
      const encrypted = encryptionService.encrypt(testData, testUserId);
      expect(encrypted).toBeDefined();
      expect(encrypted).not.toBe(testData);
      expect(typeof encrypted).toBe('string');

      const decrypted = encryptionService.decrypt(encrypted, testUserId);
      expect(decrypted).toBe(testData);
    });

    test('should return different encrypted values for same data', () => {
      const encrypted1 = encryptionService.encrypt(testData, testUserId);
      const encrypted2 = encryptionService.encrypt(testData, testUserId);
      
      expect(encrypted1).not.toBe(encrypted2);
      
      // But both should decrypt to the same value
      expect(encryptionService.decrypt(encrypted1, testUserId)).toBe(testData);
      expect(encryptionService.decrypt(encrypted2, testUserId)).toBe(testData);
    });

    test('should fail to decrypt with wrong user ID', () => {
      const encrypted = encryptionService.encrypt(testData, testUserId);
      const wrongUserId = '507f1f77bcf86cd799439012';
      
      expect(() => {
        encryptionService.decrypt(encrypted, wrongUserId);
      }).toThrow('Failed to decrypt data');
    });

    test('should handle empty or null data gracefully', () => {
      expect(encryptionService.encrypt('', testUserId)).toBe('');
      expect(encryptionService.encrypt(null, testUserId)).toBe(null);
      expect(encryptionService.encrypt(undefined, testUserId)).toBe(undefined);
      
      expect(encryptionService.decrypt('', testUserId)).toBe('');
      expect(encryptionService.decrypt(null, testUserId)).toBe(null);
      expect(encryptionService.decrypt(undefined, testUserId)).toBe(undefined);
    });
  });

  describe('Object Encryption/Decryption', () => {
    test('should encrypt and decrypt object fields', () => {
      const testObj = {
        publicField: 'public data',
        sensitiveField: 'sensitive data',
        anotherSensitiveField: 'more sensitive data'
      };
      const sensitiveFields = ['sensitiveField', 'anotherSensitiveField'];

      const encrypted = encryptionService.encryptObject(testObj, sensitiveFields, testUserId);
      
      expect(encrypted.publicField).toBe('public data');
      expect(encrypted.sensitiveField).not.toBe('sensitive data');
      expect(encrypted.anotherSensitiveField).not.toBe('more sensitive data');

      const decrypted = encryptionService.decryptObject(encrypted, sensitiveFields, testUserId);
      
      expect(decrypted.publicField).toBe('public data');
      expect(decrypted.sensitiveField).toBe('sensitive data');
      expect(decrypted.anotherSensitiveField).toBe('more sensitive data');
    });

    test('should handle null or undefined objects', () => {
      const sensitiveFields = ['field1'];
      
      expect(encryptionService.encryptObject(null, sensitiveFields, testUserId)).toBe(null);
      expect(encryptionService.encryptObject(undefined, sensitiveFields, testUserId)).toBe(undefined);
      expect(encryptionService.decryptObject(null, sensitiveFields, testUserId)).toBe(null);
      expect(encryptionService.decryptObject(undefined, sensitiveFields, testUserId)).toBe(undefined);
    });
  });

  describe('Personal Info Encryption', () => {
    test('should encrypt and decrypt personal info correctly', () => {
      const encrypted = encryptionService.encryptPersonalInfo(testPersonalInfo, testUserId);
      
      // Check that sensitive fields are encrypted
      expect(encrypted.fullName).not.toBe(testPersonalInfo.fullName);
      expect(encrypted.email).not.toBe(testPersonalInfo.email);
      expect(encrypted.phone).not.toBe(testPersonalInfo.phone);
      expect(encrypted.guarantorInfo.name).not.toBe(testPersonalInfo.guarantorInfo.name);
      expect(encrypted.emergencyContact.email).not.toBe(testPersonalInfo.emergencyContact.email);

      const decrypted = encryptionService.decryptPersonalInfo(encrypted, testUserId);
      
      // Check that all fields are correctly decrypted
      expect(decrypted.fullName).toBe(testPersonalInfo.fullName);
      expect(decrypted.email).toBe(testPersonalInfo.email);
      expect(decrypted.phone).toBe(testPersonalInfo.phone);
      expect(decrypted.monthlyIncome).toBe(testPersonalInfo.monthlyIncome);
      expect(decrypted.guarantorInfo.name).toBe(testPersonalInfo.guarantorInfo.name);
      expect(decrypted.guarantorInfo.email).toBe(testPersonalInfo.guarantorInfo.email);
      expect(decrypted.emergencyContact.name).toBe(testPersonalInfo.emergencyContact.name);
    });

    test('should handle partial personal info', () => {
      const partialInfo = {
        fullName: 'John Doe',
        email: '<EMAIL>'
      };

      const encrypted = encryptionService.encryptPersonalInfo(partialInfo, testUserId);
      const decrypted = encryptionService.decryptPersonalInfo(encrypted, testUserId);
      
      expect(decrypted.fullName).toBe(partialInfo.fullName);
      expect(decrypted.email).toBe(partialInfo.email);
    });
  });

  describe('Password Hashing', () => {
    test('should hash passwords securely', async () => {
      const password = 'testPassword123!';
      const hash = await encryptionService.hashPassword(password);
      
      expect(hash).toBeDefined();
      expect(hash).not.toBe(password);
      expect(hash.length).toBeGreaterThan(50); // bcrypt hashes are typically 60 characters
    });

    test('should verify passwords correctly', async () => {
      const password = 'testPassword123!';
      const hash = await encryptionService.hashPassword(password);
      
      const isValid = await encryptionService.verifyPassword(password, hash);
      expect(isValid).toBe(true);
      
      const isInvalid = await encryptionService.verifyPassword('wrongPassword', hash);
      expect(isInvalid).toBe(false);
    });

    test('should generate different hashes for same password', async () => {
      const password = 'testPassword123!';
      const hash1 = await encryptionService.hashPassword(password);
      const hash2 = await encryptionService.hashPassword(password);
      
      expect(hash1).not.toBe(hash2);
      
      // But both should verify correctly
      expect(await encryptionService.verifyPassword(password, hash1)).toBe(true);
      expect(await encryptionService.verifyPassword(password, hash2)).toBe(true);
    });
  });

  describe('Token Generation', () => {
    test('should generate secure random tokens', () => {
      const token1 = encryptionService.generateSecureToken();
      const token2 = encryptionService.generateSecureToken();
      
      expect(token1).toBeDefined();
      expect(token2).toBeDefined();
      expect(token1).not.toBe(token2);
      expect(token1.length).toBe(64); // 32 bytes = 64 hex characters
      expect(token2.length).toBe(64);
    });

    test('should generate tokens of specified length', () => {
      const token = encryptionService.generateSecureToken(16);
      expect(token.length).toBe(32); // 16 bytes = 32 hex characters
    });

    test('should generate secure IDs', () => {
      const id1 = encryptionService.generateSecureId();
      const id2 = encryptionService.generateSecureId();
      
      expect(id1).toBeDefined();
      expect(id2).toBeDefined();
      expect(id1).not.toBe(id2);
      expect(id1.length).toBe(16);
      expect(id2.length).toBe(16);
      
      // Should be URL-safe (no +, /, or = characters)
      expect(id1).not.toMatch(/[+/=]/);
      expect(id2).not.toMatch(/[+/=]/);
    });
  });

  describe('HMAC Signatures', () => {
    test('should create and verify HMAC signatures', () => {
      const data = 'test data to sign';
      const secret = 'secret key';
      
      const signature = encryptionService.createSignature(data, secret);
      expect(signature).toBeDefined();
      expect(typeof signature).toBe('string');
      expect(signature.length).toBe(64); // SHA256 hex = 64 characters
      
      const isValid = encryptionService.verifySignature(data, signature, secret);
      expect(isValid).toBe(true);
      
      const isInvalid = encryptionService.verifySignature('tampered data', signature, secret);
      expect(isInvalid).toBe(false);
    });

    test('should fail verification with wrong secret', () => {
      const data = 'test data';
      const secret = 'secret key';
      const wrongSecret = 'wrong secret';
      
      const signature = encryptionService.createSignature(data, secret);
      const isValid = encryptionService.verifySignature(data, signature, wrongSecret);
      
      expect(isValid).toBe(false);
    });
  });

  describe('Error Handling', () => {
    test('should handle encryption errors gracefully', () => {
      // Test with invalid user ID
      expect(() => {
        encryptionService.encrypt(testData, null);
      }).toThrow();
    });

    test('should handle decryption of invalid data', () => {
      expect(() => {
        encryptionService.decrypt('invalid-encrypted-data', testUserId);
      }).toThrow('Failed to decrypt data');
    });

    test('should handle password verification errors', async () => {
      const result = await encryptionService.verifyPassword('password', 'invalid-hash');
      expect(result).toBe(false);
    });
  });

  describe('Security Properties', () => {
    test('should use different IVs for each encryption', () => {
      const data = 'same data';
      const encrypted1 = encryptionService.encrypt(data, testUserId);
      const encrypted2 = encryptionService.encrypt(data, testUserId);
      
      // Different IVs should result in different ciphertexts
      expect(encrypted1).not.toBe(encrypted2);
      
      // But both should decrypt to the same plaintext
      expect(encryptionService.decrypt(encrypted1, testUserId)).toBe(data);
      expect(encryptionService.decrypt(encrypted2, testUserId)).toBe(data);
    });

    test('should derive different keys for different users', () => {
      const data = 'same data';
      const userId1 = '507f1f77bcf86cd799439011';
      const userId2 = '507f1f77bcf86cd799439012';
      
      const encrypted1 = encryptionService.encrypt(data, userId1);
      const encrypted2 = encryptionService.encrypt(data, userId2);
      
      // Same data encrypted with different user keys should be different
      expect(encrypted1).not.toBe(encrypted2);
      
      // Each should only decrypt with the correct user ID
      expect(encryptionService.decrypt(encrypted1, userId1)).toBe(data);
      expect(encryptionService.decrypt(encrypted2, userId2)).toBe(data);
      
      expect(() => {
        encryptionService.decrypt(encrypted1, userId2);
      }).toThrow();
      
      expect(() => {
        encryptionService.decrypt(encrypted2, userId1);
      }).toThrow();
    });
  });
});