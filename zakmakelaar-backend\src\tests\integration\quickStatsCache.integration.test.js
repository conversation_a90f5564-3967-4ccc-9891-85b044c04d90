const searchService = require('../../services/searchService');

// Mock the dependencies
jest.mock('../../models/Listing');
jest.mock('../../services/cacheService');
jest.mock('../../services/logger', () => ({
  logHelpers: {
    logDbOperation: jest.fn()
  }
}));

const cacheService = require('../../services/cacheService');

describe('Quick Stats Caching Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  // No need for afterEach since we're mocking the cache service

  it('should demonstrate complete caching workflow', async () => {
    const Listing = require('../../models/Listing');
    
    // Mock database response
    const mockAggregationResult = [{
      totalCount: [{ count: 100 }],
      avgPrice: [{ avgPrice: 2500.5 }],
      newToday: [{ count: 5 }]
    }];
    
    const expectedStats = {
      totalListings: 100,
      averagePrice: 2501, // Rounded
      newToday: 5
    };
    
    Listing.aggregate.mockResolvedValue(mockAggregationResult);
    
    // Mock cache miss on first call, then cache hit on second call
    cacheService.get
      .mockResolvedValueOnce(null) // First call - cache miss
      .mockResolvedValueOnce(expectedStats); // Second call - cache hit
    
    cacheService.set.mockResolvedValue(true);

    // First call - should hit database and cache result
    const firstResult = await searchService.getQuickStats();
    
    expect(firstResult).toEqual({
      ...expectedStats,
      cached: false
    });
    
    expect(Listing.aggregate).toHaveBeenCalledTimes(1);
    expect(cacheService.set).toHaveBeenCalledWith('quick-stats', expectedStats, 300);

    // Second call - should hit cache
    const secondResult = await searchService.getQuickStats();
    
    expect(secondResult).toEqual({
      ...expectedStats,
      cached: true
    });
    
    // Database should not be called again
    expect(Listing.aggregate).toHaveBeenCalledTimes(1);
  });

  it('should verify cache expiration behavior', async () => {
    const Listing = require('../../models/Listing');
    
    cacheService.get.mockResolvedValue(null); // Cache miss
    cacheService.set.mockResolvedValue(true);
    
    Listing.aggregate.mockResolvedValue([{
      totalCount: [{ count: 50 }],
      avgPrice: [{ avgPrice: 3000 }],
      newToday: [{ count: 2 }]
    }]);

    await searchService.getQuickStats();

    // Verify cache was set with 5-minute expiration (300 seconds)
    expect(cacheService.set).toHaveBeenCalledWith('quick-stats', {
      totalListings: 50,
      averagePrice: 3000,
      newToday: 2
    }, 300);
  });

  it('should handle cache service unavailability gracefully', async () => {
    const Listing = require('../../models/Listing');
    
    // Mock cache service to be unavailable
    cacheService.get.mockResolvedValue(null);
    cacheService.set.mockResolvedValue(false);
    
    Listing.aggregate.mockResolvedValue([{
      totalCount: [{ count: 75 }],
      avgPrice: [{ avgPrice: 2750 }],
      newToday: [{ count: 8 }]
    }]);

    const result = await searchService.getQuickStats();

    // Should still return results even if cache is unavailable
    expect(result).toEqual({
      totalListings: 75,
      averagePrice: 2750,
      newToday: 8,
      cached: false
    });
  });
});