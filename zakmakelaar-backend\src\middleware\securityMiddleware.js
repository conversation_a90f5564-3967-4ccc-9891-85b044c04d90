const rateLimit = require('express-rate-limit');
const helmet = require('helmet');
const cors = require('cors');
const { auth, requireRole } = require('./auth');
const auditLogService = require('../services/auditLogService');
const gdprComplianceService = require('../services/gdprComplianceService');
const { loggers } = require('../services/logger');
const config = require('../config/config');

/**
 * Security Middleware Collection
 * 
 * Provides comprehensive security measures for auto-application features:
 * - Rate limiting and DDoS protection
 * - Access control and authorization
 * - GDPR consent validation
 * - Security headers and CORS
 * - Audit logging integration
 */

/**
 * Enhanced rate limiting for auto-application endpoints
 */
const createAutoApplicationRateLimit = (windowMs = 15 * 60 * 1000, max = 100) => {
  return rateLimit({
    windowMs,
    max,
    message: {
      status: 'error',
      message: 'Too many requests from this IP, please try again later.',
      retryAfter: Math.ceil(windowMs / 1000)
    },
    standardHeaders: true,
    legacyHeaders: false,
    handler: async (req, res, next) => {
      // Log rate limit violation
      await auditLogService.logSecurity(
        req.user?.id || null,
        'rate_limit_exceeded',
        {
          endpoint: req.path,
          method: req.method,
          limit: max,
          windowMs
        },
        {
          ipAddress: req.ip,
          userAgent: req.get('User-Agent')
        }
      );

      res.status(429).json({
        status: 'error',
        message: 'Too many requests from this IP, please try again later.',
        retryAfter: Math.ceil(windowMs / 1000)
      });
    },
    skip: (req) => {
      // Skip rate limiting for admin users in development
      return config.nodeEnv === 'development' && req.user?.role === 'admin';
    }
  });
};

/**
 * Strict rate limiting for sensitive operations
 */
const strictRateLimit = createAutoApplicationRateLimit(5 * 60 * 1000, 10); // 10 requests per 5 minutes

/**
 * Standard rate limiting for regular operations
 */
const standardRateLimit = createAutoApplicationRateLimit(15 * 60 * 1000, 100); // 100 requests per 15 minutes

/**
 * Lenient rate limiting for read operations
 */
const lenientRateLimit = createAutoApplicationRateLimit(15 * 60 * 1000, 500); // 500 requests per 15 minutes

/**
 * Security headers middleware
 */
const securityHeaders = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  crossOriginEmbedderPolicy: false, // Disable for API compatibility
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
});

/**
 * CORS configuration for auto-application API
 */
const corsOptions = {
  origin: (origin, callback) => {
    // Allow requests with no origin (mobile apps, Postman, etc.)
    if (!origin) return callback(null, true);
    
    if (config.corsOrigin.includes(origin)) {
      return callback(null, true);
    }
    
    // Log unauthorized CORS attempt
    loggers.security.warn('Unauthorized CORS origin attempt', { origin });
    callback(new Error('Not allowed by CORS'));
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  exposedHeaders: ['X-Total-Count', 'X-Rate-Limit-Remaining']
};

/**
 * Enhanced authentication middleware with audit logging
 */
const authenticateWithAudit = async (req, res, next) => {
  try {
    // First run standard auth
    await new Promise((resolve, reject) => {
      auth(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    // Log successful authentication
    await auditLogService.logSecurity(
      req.user.id,
      'login_success',
      {
        endpoint: req.path,
        method: req.method
      },
      {
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        sessionId: req.sessionID
      }
    );

    next();

  } catch (error) {
    // Log failed authentication
    await auditLogService.logSecurity(
      null,
      'login_failed',
      {
        endpoint: req.path,
        method: req.method,
        error: error.message
      },
      {
        ipAddress: req.ip,
        userAgent: req.get('User-Agent')
      }
    );

    res.status(401).json({
      status: 'error',
      message: 'Authentication required'
    });
  }
};

/**
 * Auto-application specific authorization
 */
const requireAutoApplicationAccess = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        status: 'error',
        message: 'Authentication required'
      });
    }

    // Check if user has auto-application consent
    const hasConsent = await gdprComplianceService.hasAutoApplicationConsent(req.user.id);
    if (!hasConsent) {
      await auditLogService.logSecurity(
        req.user.id,
        'access_denied_no_consent',
        {
          endpoint: req.path,
          reason: 'Missing auto-application consent'
        },
        {
          ipAddress: req.ip,
          userAgent: req.get('User-Agent')
        }
      );

      return res.status(403).json({
        status: 'error',
        message: 'Auto-application consent required',
        code: 'CONSENT_REQUIRED'
      });
    }

    // Log authorized access
    await auditLogService.logAutoApplication(
      req.user.id,
      'auto_application_access_granted',
      {
        endpoint: req.path,
        method: req.method
      },
      {
        ipAddress: req.ip,
        userAgent: req.get('User-Agent')
      }
    );

    next();

  } catch (error) {
    loggers.app.error('Auto-application authorization error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Authorization check failed'
    });
  }
};

/**
 * Document access authorization
 */
const requireDocumentAccess = async (req, res, next) => {
  try {
    const documentId = req.params.documentId;
    const userId = req.user.id;
    const userRole = req.user.role;

    // Check document ownership or admin access
    const Document = require('../models/Document');
    const document = await Document.findById(documentId);

    if (!document) {
      return res.status(404).json({
        status: 'error',
        message: 'Document not found'
      });
    }

    const hasAccess = document.canAccess(userId, userRole);
    if (!hasAccess) {
      await auditLogService.logSecurity(
        userId,
        'document_access_denied',
        {
          documentId,
          ownerId: document.userId,
          reason: 'Insufficient permissions'
        },
        {
          ipAddress: req.ip,
          userAgent: req.get('User-Agent')
        }
      );

      return res.status(403).json({
        status: 'error',
        message: 'Access denied'
      });
    }

    req.document = document;
    next();

  } catch (error) {
    loggers.app.error('Document authorization error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Authorization check failed'
    });
  }
};

/**
 * Admin access with enhanced logging
 */
const requireAdminWithAudit = async (req, res, next) => {
  try {
    if (!req.user || req.user.role !== 'admin') {
      await auditLogService.logSecurity(
        req.user?.id || null,
        'admin_access_denied',
        {
          endpoint: req.path,
          method: req.method,
          userRole: req.user?.role || 'anonymous'
        },
        {
          ipAddress: req.ip,
          userAgent: req.get('User-Agent')
        }
      );

      return res.status(403).json({
        status: 'error',
        message: 'Admin access required'
      });
    }

    // Log admin access
    await auditLogService.logSecurity(
      req.user.id,
      'admin_access',
      {
        endpoint: req.path,
        method: req.method
      },
      {
        ipAddress: req.ip,
        userAgent: req.get('User-Agent')
      }
    );

    next();

  } catch (error) {
    loggers.app.error('Admin authorization error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Authorization check failed'
    });
  }
};

/**
 * Consent validation middleware
 */
const requireConsent = (operation) => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          status: 'error',
          message: 'Authentication required'
        });
      }

      const hasConsent = await gdprComplianceService.validateConsentForOperation(
        req.user.id,
        operation
      );

      if (!hasConsent) {
        await auditLogService.logPrivacy(
          req.user.id,
          'consent_validation_failed',
          {
            operation,
            endpoint: req.path
          },
          {
            ipAddress: req.ip,
            userAgent: req.get('User-Agent')
          }
        );

        return res.status(403).json({
          status: 'error',
          message: 'Required consent not provided',
          code: 'CONSENT_REQUIRED',
          operation
        });
      }

      next();

    } catch (error) {
      loggers.app.error('Consent validation error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Consent validation failed'
      });
    }
  };
};

/**
 * Request logging middleware
 */
const requestLogger = async (req, res, next) => {
  const startTime = Date.now();

  // Log request
  loggers.app.info('Incoming request', {
    method: req.method,
    url: req.url,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.id
  });

  // Override res.json to log response
  const originalJson = res.json;
  res.json = function(data) {
    const responseTime = Date.now() - startTime;
    
    // Log response
    loggers.app.info('Outgoing response', {
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      responseTime,
      userId: req.user?.id
    });

    return originalJson.call(this, data);
  };

  next();
};

/**
 * Error handling middleware with security logging
 */
const securityErrorHandler = async (error, req, res, next) => {
  // Log security-related errors
  if (error.name === 'UnauthorizedError' || 
      error.message.includes('auth') || 
      error.message.includes('permission')) {
    
    await auditLogService.logSecurity(
      req.user?.id || null,
      'security_error',
      {
        error: error.message,
        endpoint: req.path,
        method: req.method
      },
      {
        ipAddress: req.ip,
        userAgent: req.get('User-Agent')
      }
    );
  }

  // Don't expose internal error details in production
  const isDevelopment = config.nodeEnv === 'development';
  
  res.status(error.status || 500).json({
    status: 'error',
    message: isDevelopment ? error.message : 'Internal server error',
    ...(isDevelopment && { stack: error.stack })
  });
};

/**
 * Suspicious activity detection
 */
const detectSuspiciousActivity = async (req, res, next) => {
  try {
    const suspiciousPatterns = [
      // Multiple rapid requests from same IP
      { pattern: 'rapid_requests', threshold: 50, window: 60000 }, // 50 requests in 1 minute
      // Multiple failed auth attempts
      { pattern: 'failed_auth', threshold: 5, window: 300000 }, // 5 failures in 5 minutes
      // Unusual user agent patterns
      { pattern: 'bot_user_agent', regex: /bot|crawler|spider|scraper/i }
    ];

    // Simple in-memory tracking (in production, use Redis)
    if (!global.suspiciousActivityTracker) {
      global.suspiciousActivityTracker = new Map();
    }

    const tracker = global.suspiciousActivityTracker;
    const clientKey = req.ip + (req.user?.id || 'anonymous');
    const now = Date.now();

    // Clean old entries
    for (const [key, data] of tracker.entries()) {
      if (now - data.lastActivity > 300000) { // 5 minutes
        tracker.delete(key);
      }
    }

    // Track current request
    if (!tracker.has(clientKey)) {
      tracker.set(clientKey, { requests: [], lastActivity: now });
    }

    const clientData = tracker.get(clientKey);
    clientData.requests.push(now);
    clientData.lastActivity = now;

    // Check for suspicious patterns
    const recentRequests = clientData.requests.filter(time => now - time < 60000);
    
    if (recentRequests.length > 50) {
      await auditLogService.logSecurity(
        req.user?.id || null,
        'suspicious_activity',
        {
          pattern: 'rapid_requests',
          count: recentRequests.length,
          timeWindow: '1 minute'
        },
        {
          ipAddress: req.ip,
          userAgent: req.get('User-Agent')
        }
      );

      return res.status(429).json({
        status: 'error',
        message: 'Suspicious activity detected. Please try again later.'
      });
    }

    next();

  } catch (error) {
    loggers.app.error('Suspicious activity detection error:', error);
    next(); // Don't block request on detection error
  }
};

module.exports = {
  // Rate limiting
  strictRateLimit,
  standardRateLimit,
  lenientRateLimit,
  
  // Security headers and CORS
  securityHeaders,
  cors: cors(corsOptions),
  
  // Authentication and authorization
  authenticateWithAudit,
  requireAutoApplicationAccess,
  requireDocumentAccess,
  requireAdminWithAudit,
  requireConsent,
  
  // Logging and monitoring
  requestLogger,
  securityErrorHandler,
  detectSuspiciousActivity,
  
  // Utility functions
  createAutoApplicationRateLimit
};