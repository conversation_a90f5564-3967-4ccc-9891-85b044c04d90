/**
 * Database Optimizer Tests
 * 
 * This file contains tests for the database optimizer module that optimizes
 * database queries and storage for unified schema data.
 */

const { DatabaseOptimizer } = require('../services/databaseOptimizer');
const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');

describe('Database Optimizer', () => {
  let mongoServer;
  let connection;
  let PropertyModel;
  
  beforeAll(async () => {
    // Set up in-memory MongoDB server
    mongoServer = await MongoMemoryServer.create();
    const uri = mongoServer.getUri();
    
    // Connect to in-memory database
    connection = await mongoose.connect(uri, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    
    // Create test model
    const PropertySchema = new mongoose.Schema({
      title: String,
      description: String,
      url: String,
      source: String,
      location: mongoose.Schema.Types.Mixed,
      price: mongoose.Schema.Types.Mixed,
      propertyType: String,
      size: String,
      area: Number,
      rooms: mongoose.Schema.Types.Mixed,
      bedrooms: mongoose.Schema.Types.Mixed,
      bathrooms: mongoose.Schema.Types.Mixed,
      year: String,
      interior: String,
      furnished: Boolean,
      images: [String],
      dateAdded: Date,
      _internal: {
        sourceMetadata: {
          website: String,
          externalId: String,
          scrapedAt: Date,
          lastUpdated: Date,
          version: Number
        },
        rawData: mongoose.Schema.Types.Mixed,
        dataQuality: {
          completeness: Number,
          accuracy: Number,
          lastValidated: Date,
          validationErrors: [String]
        }
      }
    });
    
    PropertyModel = mongoose.model('Property', PropertySchema);
  });
  
  afterAll(async () => {
    // Disconnect and stop MongoDB server
    await mongoose.disconnect();
    await mongoServer.stop();
  });
  
  beforeEach(async () => {
    // Clear the collection before each test
    await PropertyModel.deleteMany({});
  });
  
  test('should create optimal indexes', async () => {
    // Create optimal indexes
    await DatabaseOptimizer.createOptimalIndexes(PropertyModel);
    
    // Get index information
    const indexInfo = await PropertyModel.collection.indexInformation();
    
    // Verify that the expected indexes were created
    expect(indexInfo).toHaveProperty('location_price_idx');
    expect(indexInfo).toHaveProperty('property_type_price_idx');
    expect(indexInfo).toHaveProperty('features_idx');
    expect(indexInfo).toHaveProperty('source_quality_idx');
    expect(indexInfo).toHaveProperty('date_added_idx');
    expect(indexInfo).toHaveProperty('text_search_idx');
  });
  
  test('should optimize queries', () => {
    // Test location query optimization
    const locationQuery = {
      location: 'Amsterdam'
    };
    
    const optimizedLocationQuery = DatabaseOptimizer.optimizeQuery(locationQuery);
    
    expect(optimizedLocationQuery.query).not.toHaveProperty('location');
    expect(optimizedLocationQuery.query).toHaveProperty('$or');
    expect(optimizedLocationQuery.query.$or).toHaveLength(2);
    expect(optimizedLocationQuery.query.$or[0]).toHaveProperty('location._legacy');
    expect(optimizedLocationQuery.query.$or[1]).toHaveProperty('location._unified.address.city');
    
    // Test price query optimization
    const priceQuery = {
      price: '€ 1.500 per maand'
    };
    
    const optimizedPriceQuery = DatabaseOptimizer.optimizeQuery(priceQuery);
    
    expect(optimizedPriceQuery.query).toHaveProperty('price');
    expect(optimizedPriceQuery.query.price).toBe(1500);
    
    // Test projection optimization
    const queryWithoutProjection = {
      propertyType: 'apartment'
    };
    
    const optimizedQueryWithoutProjection = DatabaseOptimizer.optimizeQuery(queryWithoutProjection);
    
    expect(optimizedQueryWithoutProjection.options).toHaveProperty('projection');
    expect(optimizedQueryWithoutProjection.options.projection).toHaveProperty('_internal.rawData', 0);
    
    // Test sort optimization
    const queryWithSort = {
      propertyType: 'apartment'
    };
    
    const optimizedQueryWithSort = DatabaseOptimizer.optimizeQuery(queryWithSort, {
      sort: { location: 1 }
    });
    
    expect(optimizedQueryWithSort.options).toHaveProperty('sort');
    expect(optimizedQueryWithSort.options.sort).toHaveProperty('location._unified.address.city', 1);
  });
  
  test('should optimize document for storage', () => {
    // Create a test document with large raw data
    const document = {
      title: 'Test Property',
      description: 'A test property description',
      url: 'https://www.funda.nl/huur/amsterdam/test-property/',
      source: 'funda.nl',
      _internal: {
        rawData: {
          original: {
            title: 'Test Property',
            description: 'A test property description',
            url: 'https://www.funda.nl/huur/amsterdam/test-property/',
            source: 'funda.nl',
            externalId: '12345',
            largeField1: 'x'.repeat(1000),
            largeField2: 'y'.repeat(1000),
            largeField3: 'z'.repeat(1000)
          },
          timestamp: new Date()
        }
      }
    };
    
    // Optimize the document for storage
    const optimizedDocument = DatabaseOptimizer.optimizeForStorage(document);
    
    // Verify that the raw data was optimized
    expect(optimizedDocument).toHaveProperty('_internal.rawData');
    expect(optimizedDocument._internal.rawData).toHaveProperty('minimal');
    expect(optimizedDocument._internal.rawData.minimal).toHaveProperty('url');
    expect(optimizedDocument._internal.rawData.minimal).toHaveProperty('source');
    expect(optimizedDocument._internal.rawData.minimal).toHaveProperty('externalId');
    expect(optimizedDocument._internal.rawData.minimal).not.toHaveProperty('largeField1');
    expect(optimizedDocument._internal.rawData.minimal).not.toHaveProperty('largeField2');
    expect(optimizedDocument._internal.rawData.minimal).not.toHaveProperty('largeField3');
  });
  
  test('should get database statistics', async () => {
    // Create some test data
    await PropertyModel.create([
      {
        title: 'Property 1',
        source: 'funda.nl',
        dateAdded: new Date()
      },
      {
        title: 'Property 2',
        source: 'huurwoningen.nl',
        dateAdded: new Date()
      },
      {
        title: 'Property 3',
        source: 'funda.nl',
        dateAdded: new Date()
      }
    ]);
    
    // Create indexes
    await DatabaseOptimizer.createOptimalIndexes(PropertyModel);
    
    // Get database statistics
    const stats = await DatabaseOptimizer.getDatabaseStats(PropertyModel);
    
    // Verify statistics
    expect(stats).toHaveProperty('totalDocuments', 3);
    expect(stats).toHaveProperty('storageSize');
    expect(stats).toHaveProperty('indexSize');
    expect(stats).toHaveProperty('avgDocumentSize');
    expect(stats).toHaveProperty('indexCount');
    expect(stats).toHaveProperty('sourceCounts');
    
    // Verify source counts
    const fundaCount = stats.sourceCounts.find(item => item.source === 'funda.nl');
    const huurwoningenCount = stats.sourceCounts.find(item => item.source === 'huurwoningen.nl');
    
    expect(fundaCount).toHaveProperty('count', 2);
    expect(huurwoningenCount).toHaveProperty('count', 1);
  });
});