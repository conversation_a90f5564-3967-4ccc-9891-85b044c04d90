/**
 * Validation Engine Example
 * 
 * This file demonstrates how to use the ValidationEngine to validate property data.
 */

const { ValidationEngine } = require('../services/validationEngine');
const { SchemaTransformer } = require('../services/schemaTransformer');
const { FieldMappingRegistry } = require('../services/fieldMappingRegistry');

/**
 * Example usage of ValidationEngine
 */
async function validationEngineExample() {
  console.log('=== Validation Engine Example ===');
  
  // Create validation engine
  const validationEngine = new ValidationEngine();
  console.log('Validation engine created');
  
  // Example property data
  const propertyData = {
    title: 'Beautiful Apartment in Amsterdam',
    description: 'A spacious apartment in the heart of Amsterdam',
    source: 'funda.nl',
    url: 'https://funda.nl/apartment/123',
    dateAdded: new Date().toISOString(),
    location: 'Amsterdam, Netherlands',
    propertyType: 'apartment',
    size: '85 m²',
    area: 85,
    rooms: '3',
    bedrooms: 2,
    bathrooms: '1',
    year: '2010',
    price: 1500,
    interior: 'Gemeubileerd',
    furnished: true,
    pets: false,
    smoking: false,
    garden: true,
    balcony: false,
    parking: true,
    energyLabel: 'A',
    images: ['https://example.com/image1.jpg', 'https://example.com/image2.jpg'],
    isActive: true,
    features: ['Dishwasher', 'Washing Machine'],
    deposit: 1500,
    utilities: 150,
    dateAvailable: new Date().toISOString(),
    contactInfo: {
      name: 'Test Agent',
      phone: '123456789',
      email: '<EMAIL>'
    }
  };
  
  console.log('\n1. Validating valid property data:');
  const validResult = validationEngine.validate(propertyData);
  console.log(`Valid: ${validResult.valid}`);
  console.log(`Errors: ${validResult.errors.length}`);
  
  // Create invalid property data
  const invalidPropertyData = {
    title: '', // Invalid: empty title
    description: 'An invalid property',
    source: 'invalid-source', // Invalid: not in allowed sources
    url: 'not-a-url', // Invalid: not a valid URL
    location: 'Amsterdam',
    propertyType: 'invalid-type', // Invalid: not in allowed types
    size: 85, // Invalid: should be string
    rooms: -3, // Invalid: negative rooms
    year: '9999', // Invalid: year too far in future
    price: -100, // Invalid: negative price
    interior: 'invalid', // Invalid: not in allowed interiors
    furnished: 'yes' // Invalid: should be boolean
  };
  
  console.log('\n2. Validating invalid property data:');
  const invalidResult = validationEngine.validate(invalidPropertyData);
  console.log(`Valid: ${invalidResult.valid}`);
  console.log(`Errors: ${invalidResult.errors.length}`);
  console.log('Error examples:');
  invalidResult.errors.slice(0, 3).forEach(error => {
    console.log(`- ${error.message} (Field: ${error.context.field})`);
  });
  
  console.log('\n3. Applying defaults to incomplete data:');
  const incompleteData = {
    title: 'Incomplete Property',
    source: 'funda.nl',
    url: 'https://example.com/incomplete',
    location: 'Rotterdam',
    price: 1200
  };
  
  const withDefaults = validationEngine.applyDefaults(incompleteData);
  console.log('Applied defaults:');
  console.log(`- propertyType: ${withDefaults.propertyType}`);
  console.log(`- bathrooms: ${withDefaults.bathrooms}`);
  console.log(`- furnished: ${withDefaults.furnished}`);
  console.log(`- isActive: ${withDefaults.isActive}`);
  
  console.log('\n4. Validating with error classification:');
  const classifiedResult = validationEngine.validateWithErrorClassification(invalidPropertyData);
  console.log(`Critical errors: ${classifiedResult.errors.critical.length}`);
  console.log(`Major errors: ${classifiedResult.errors.major.length}`);
  console.log(`Minor errors: ${classifiedResult.errors.minor.length}`);
  
  console.log('\n5. Calculating data quality:');
  const qualityValid = validationEngine.calculateDataQuality(propertyData);
  console.log(`Valid data - Completeness: ${qualityValid.completeness}%, Accuracy: ${qualityValid.accuracy}%`);
  
  const qualityInvalid = validationEngine.calculateDataQuality(invalidPropertyData);
  console.log(`Invalid data - Completeness: ${qualityInvalid.completeness}%, Accuracy: ${qualityInvalid.accuracy}%`);
  
  console.log('\n6. Fixing invalid data:');
  const fixedData = validationEngine.fixData(invalidPropertyData);
  console.log('Fixed fields:');
  console.log(`- title: '${invalidPropertyData.title}' -> '${fixedData.title}'`);
  console.log(`- source: '${invalidPropertyData.source}' -> '${fixedData.source}'`);
  console.log(`- propertyType: '${invalidPropertyData.propertyType}' -> '${fixedData.propertyType}'`);
  console.log(`- size: ${invalidPropertyData.size} -> '${fixedData.size}'`);
  console.log(`- rooms: ${invalidPropertyData.rooms} -> ${fixedData.rooms}`);
  console.log(`- price: ${invalidPropertyData.price} -> ${fixedData.price}`);
  console.log(`- furnished: ${invalidPropertyData.furnished} -> ${fixedData.furnished}`);
  
  // Validate the fixed data
  const fixedResult = validationEngine.validate(fixedData);
  console.log(`Fixed data valid: ${fixedResult.valid}`);
  
  console.log('\n7. Integration with SchemaTransformer:');
  // Create mapping registry and schema transformer
  const mappingRegistry = new FieldMappingRegistry();
  
  // Define sample Funda mapping
  const fundaMapping = {
    'title': 'title',
    'description': 'description',
    'url': 'url',
    'source': { value: 'funda.nl' },
    'propertyType': {
      path: 'propertyType',
      transform: 'normalizePropertyType'
    },
    'price': {
      path: 'price',
      transform: 'normalizePrice'
    },
    'location': {
      path: 'location',
      transform: 'normalizeLocation'
    },
    'size': {
      path: 'size',
      transform: 'formatSizeString'
    },
    'area': {
      path: 'size',
      transform: 'extractNumericSize'
    },
    'rooms': {
      path: 'rooms',
      transform: 'normalizeRooms'
    }
  };
  
  // Register mapping
  mappingRegistry.registerMapping('funda', fundaMapping);
  
  // Create schema transformer
  const schemaTransformer = new SchemaTransformer(mappingRegistry);
  
  // Sample raw data from Funda
  const fundaRawData = {
    title: 'Beautiful Apartment in Amsterdam',
    description: 'A spacious apartment in the heart of Amsterdam',
    url: 'https://funda.nl/apartment/123',
    propertyType: 'appartement',
    price: '€1500 per month',
    location: 'Amsterdam, Netherlands',
    size: '85m2',
    rooms: '3'
  };
  
  // Transform raw data
  const transformed = await schemaTransformer.transform(fundaRawData, 'funda', {
    validateOutput: false // Skip validation in transformer
  });
  
  // Validate with validation engine
  const transformedResult = validationEngine.validate(transformed);
  console.log(`Transformed data valid: ${transformedResult.valid}`);
  if (!transformedResult.valid) {
    console.log(`Errors: ${transformedResult.errors.length}`);
    transformedResult.errors.forEach(error => {
      console.log(`- ${error.message} (Field: ${error.context.field})`);
    });
  }
  
  // Apply defaults and validate again
  const transformedWithDefaults = validationEngine.applyDefaults(transformed);
  const finalResult = validationEngine.validate(transformedWithDefaults);
  console.log(`Transformed data with defaults valid: ${finalResult.valid}`);
  
  console.log('\n=== End of Example ===');
}

// Run the example
validationEngineExample().catch(console.error);

module.exports = {
  validationEngineExample
};