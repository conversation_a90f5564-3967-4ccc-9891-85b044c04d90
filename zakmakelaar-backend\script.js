const puppeteer = require('puppeteer');

// =============================================================================
// CONFIGURATION SECTION
// =============================================================================
const CONFIG = {
    // Human-in-the-loop settings
    CAPTCHA_TIMEOUT: 90, // seconds to wait for manual captcha solving
    MANUAL_INTERVENTION_TIMEOUT: 120, // seconds for general manual interventions
    MANUAL_SUBMISSION: true, // Form filling only - submission is manual
    
    // Timing settings (in milliseconds)
    MIN_HUMAN_DELAY: 1000,
    MAX_HUMAN_DELAY: 3000,
    PAGE_LOAD_DELAY: 3000,
    
    // Browser settings
    HEADLESS: false, // Must be false for manual submission
    SLOW_MO: 50, // Milliseconds between actions
    
    // Form data (customize as needed)
    CONTACT_DATA: {
        name: '<PERSON>e',
        email: '<EMAIL>',
        phone: '+31612345678',
        message: 'Hello, I am interested in this property. Could you please provide more information about the viewing schedule and availability? Thank you.',
        requestViewing: true // Check the "viewing request" checkbox
    },
    
    // Target URL
    PROPERTY_URL: 'https://www.funda.nl/detail/huur/voorburg/appartement-nieuwe-havenstraat-132/89378960/'
};
// =============================================================================

// Helper function to wait for a specified delay
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Human-in-the-loop captcha handler
async function handleCaptcha(page) {
    try {
        console.log('🔍 Checking for captcha...');
        
        // Common captcha selectors
        const captchaSelectors = [
            'iframe[src*="recaptcha"]',
            'iframe[src*="hcaptcha"]',
            '.captcha',
            '.g-recaptcha',
            '.h-captcha',
            '[data-testid*="captcha"]',
            '[id*="captcha"]',
            '[class*="captcha"]',
            '.cf-turnstile',
            '#cf-chl-widget'
        ];
        
        let captchaFound = false;
        let captchaType = 'unknown';
        
        // Check for each type of captcha
        for (const selector of captchaSelectors) {
            try {
                const captcha = await page.$(selector);
                if (captcha) {
                    captchaFound = true;
                    captchaType = selector;
                    console.log(`⚠️ Captcha detected: ${selector}`);
                    break;
                }
            } catch (error) {
                // Continue checking other selectors
            }
        }
        
        // Also check for challenge pages (like Cloudflare)
        const challengeSelectors = [
            '.cf-browser-verification',
            '#challenge-stage',
            '.challenge-form',
            '[data-ray]' // Cloudflare ray ID
        ];
        
        for (const selector of challengeSelectors) {
            try {
                const challenge = await page.$(selector);
                if (challenge) {
                    captchaFound = true;
                    captchaType = 'challenge page';
                    console.log(`⚠️ Challenge page detected: ${selector}`);
                    break;
                }
            } catch (error) {
                // Continue checking
            }
        }
        
        if (captchaFound) {
            console.log('🤖➡️👤 HUMAN INTERVENTION REQUIRED');
            console.log('━'.repeat(50));
            console.log(`📋 Captcha Type: ${captchaType}`);
            console.log('🔧 What to do:');
            console.log('   1. The browser window should be visible');
            console.log('   2. Please solve the captcha manually');
            console.log('   3. Complete any verification steps');
            console.log('   4. The script will automatically continue after 90 seconds');
            console.log('━'.repeat(50));
            
            // Take a screenshot for reference
            try {
                await page.screenshot({ path: 'captcha-detected.png', fullPage: true });
                console.log('📸 Screenshot saved as captcha-detected.png for reference');
            } catch (error) {
                console.log('Could not take captcha screenshot');
            }
            
            // Wait for human intervention (90 seconds)
            console.log('⏳ Waiting 90 seconds for manual captcha solving...');
            
            // Check every 5 seconds if captcha is solved
            for (let i = 0; i < 18; i++) { // 18 * 5 = 90 seconds
                await delay(5000);
                
                let stillPresent = false;
                for (const selector of [...captchaSelectors, ...challengeSelectors]) {
                    try {
                        const element = await page.$(selector);
                        if (element) {
                            // Check if element is visible
                            const isVisible = await page.evaluate(el => {
                                const rect = el.getBoundingClientRect();
                                return rect.width > 0 && rect.height > 0;
                            }, element);
                            
                            if (isVisible) {
                                stillPresent = true;
                                break;
                            }
                        }
                    } catch (error) {
                        // Element might be removed, which is good
                    }
                }
                
                if (!stillPresent) {
                    console.log('✅ Captcha appears to be solved! Continuing automation...');
                    return true;
                }
                
                const remainingTime = (18 - i - 1) * 5;
                if (remainingTime > 0) {
                    console.log(`⏳ Still waiting... ${remainingTime} seconds remaining`);
                }
            }
            
            // Final check after timeout
            let finalCheck = false;
            for (const selector of [...captchaSelectors, ...challengeSelectors]) {
                try {
                    const element = await page.$(selector);
                    if (element) {
                        const isVisible = await page.evaluate(el => {
                            const rect = el.getBoundingClientRect();
                            return rect.width > 0 && rect.height > 0;
                        }, element);
                        if (isVisible) {
                            finalCheck = true;
                            break;
                        }
                    }
                } catch (error) {
                    // Continue
                }
            }
            
            if (finalCheck) {
                console.log('❌ Captcha still present after timeout. You may need more time.');
                console.log('💡 Tip: The script will continue, but you can manually solve it in the background.');
                return false;
            } else {
                console.log('✅ Captcha resolved! Continuing with automation...');
                return true;
            }
        } else {
            console.log('✅ No captcha detected. Proceeding with automation...');
            return true;
        }
        
    } catch (error) {
        console.log('⚠️ Error during captcha check:', error.message);
        console.log('📝 Assuming no captcha and continuing...');
        return true;
    }
}

// Helper function for human-like delays
async function humanLikeDelay(minMs = 1000, maxMs = 3000) {
    const randomDelay = Math.random() * (maxMs - minMs) + minMs;
    await delay(randomDelay);
}

// Interactive pause function for manual intervention
async function manualIntervention(page, reason = 'Manual intervention needed', timeoutSeconds = 120) {
    console.log('\n' + '🤖➡️👤 MANUAL INTERVENTION REQUIRED');
    console.log('─'.repeat(60));
    console.log(`📋 Reason: ${reason}`);
    console.log('🔧 Instructions:');
    console.log('   • The browser window should be visible');
    console.log('   • Please complete the required action manually');
    console.log('   • The script will continue automatically or you can press Ctrl+C to stop');
    console.log(`⏳ Timeout: ${timeoutSeconds} seconds`);
    console.log('─'.repeat(60));
    
    // Take screenshot for reference
    try {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `manual-intervention-${timestamp}.png`;
        await page.screenshot({ path: filename, fullPage: true });
        console.log(`📸 Screenshot saved as ${filename} for reference`);
    } catch (error) {
        console.log('Could not take screenshot');
    }
    
    console.log(`⏳ Waiting ${timeoutSeconds} seconds for manual completion...`);
    
    // Wait for the specified timeout
    for (let i = 0; i < timeoutSeconds / 5; i++) {
        await delay(5000);
        const remaining = timeoutSeconds - ((i + 1) * 5);
        if (remaining > 0 && remaining % 15 === 0) {
            console.log(`⏳ ${remaining} seconds remaining...`);
        }
    }
    
    console.log('✅ Manual intervention timeout completed. Continuing automation...');
}

async function automateFundaContact() {
    let browser;
    
    try {
        // Launch browser with enhanced stealth settings
        browser = await puppeteer.launch({
            headless: CONFIG.HEADLESS,
            defaultViewport: null,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--disable-gpu',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--start-maximized',
                '--no-first-run',
                '--no-default-browser-check',
                '--disable-infobars',
                '--disable-blink-features=AutomationControlled'
            ],
            ignoreDefaultArgs: ['--enable-automation'],
            slowMo: CONFIG.SLOW_MO
        });

        const page = await browser.newPage();
        
        // Enhanced stealth measures
        await page.evaluateOnNewDocument(() => {
            // Remove webdriver property
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
            
            // Mock plugins
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });
            
            // Mock languages
            Object.defineProperty(navigator, 'languages', {
                get: () => ['en-US', 'en'],
            });
        });
        
        // Set realistic viewport
        await page.setViewport({ width: 1366, height: 768 });
        
        // Set enhanced user agent
        await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');
        
        // Add extra headers to appear more legitimate
        await page.setExtraHTTPHeaders({
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none'
        });
        
        console.log('Navigating to the property page...');
        await page.goto(CONFIG.PROPERTY_URL, {
            waitUntil: 'networkidle2',
            timeout: 30000
        });

        // Wait for page to load completely
        await delay(3000);
        
        // Check for captcha after page load
        const captchaHandled = await handleCaptcha(page);
        if (!captchaHandled) {
            console.log('⚠️ Proceeding despite captcha presence...');
        }

        // Accept cookies if the banner appears
        try {
            await page.waitForSelector('[data-testid="accept-all-cookies-button"], .cookie-accept-all, #accept-cookies', { timeout: 5000 });
            await page.click('[data-testid="accept-all-cookies-button"], .cookie-accept-all, #accept-cookies');
            console.log('Accepted cookies');
            await humanLikeDelay(2000, 4000);
        } catch (error) {
            console.log('No cookie banner found or already accepted');
        }

        // Look for different types of contact buttons/forms
        console.log('🔍 Looking for contact buttons...');
        const contactSelectors = [
            // Specific selector for "Neem contact op" button
            'a[data-optimizely="contact-email"]',
            'a[href*="makelaar-contact"]',
            // Generic selectors as fallback
            '[data-testid="contact-button"]',
            '[data-testid="contact-form-button"]',
            '.contact-button',
            '.makelaar-contact-button',
            '[data-testid="broker-contact-button"]',
            '.broker-contact',
            'button[data-testid*="contact"]',
            '.fd-button--contact'
        ];

        let contactButton = null;
        
        // Try to find a contact button
        console.log('🔍 Trying specific selectors...');
        for (const selector of contactSelectors) {
            try {
                console.log(`  Checking selector: ${selector}`);
                await page.waitForSelector(selector, { timeout: 2000 });
                contactButton = await page.$(selector);
                if (contactButton) {
                    console.log(`✅ Found contact button with selector: ${selector}`);
                    break;
                }
            } catch (error) {
                console.log(`  ❌ Selector not found: ${selector}`);
                // Continue to next selector
            }
        }
        
        // If no specific button found, look for "Neem contact op" text
        if (!contactButton) {
            console.log('Looking for "Neem contact op" text...');
            try {
                contactButton = await page.evaluateHandle(() => {
                    const links = Array.from(document.querySelectorAll('a'));
                    return links.find(link => 
                        link.textContent.includes('Neem contact op') ||
                        link.textContent.includes('Contact') ||
                        link.getAttribute('href')?.includes('makelaar-contact')
                    );
                });
                
                if (contactButton.asElement()) {
                    console.log('Found contact button by text content');
                    contactButton = contactButton.asElement();
                } else {
                    contactButton = null;
                }
            } catch (error) {
                console.log('Could not find contact button by text content');
                contactButton = null;
            }
        }

        // If no direct button found, look for broker/agent section
        if (!contactButton) {
            console.log('Looking for broker/agent contact section...');
            try {
                // Scroll down to find the broker section
                await page.evaluate(() => {
                    window.scrollTo(0, document.body.scrollHeight / 2);
                });
                await delay(2000);

                const brokerSelectors = [
                    '.makelaar-contact',
                    '.broker-details',
                    '[data-testid="broker-contact"]',
                    '.agent-contact'
                ];

                for (const selector of brokerSelectors) {
                    try {
                        contactButton = await page.$(selector + ' button, ' + selector + ' a');
                        if (contactButton) {
                            console.log(`Found broker contact button in: ${selector}`);
                            break;
                        }
                    } catch (error) {
                        // Continue
                    }
                }
            } catch (error) {
                console.log('Could not find broker section');
            }
        }

        if (!contactButton) {
            console.log('⚠️ No contact button found automatically.');
            console.log('💡 Triggering manual intervention to locate contact options...');
            
            await manualIntervention(page, 
                'Contact button not found automatically. Please locate and click the contact/inquiry button manually.', 
                60
            );
            
            // After manual intervention, try to find form elements
            console.log('🔍 Continuing with form detection after manual intervention...');
        } else {
            // Scroll to the contact button to ensure it's visible
            try {
                await page.evaluate((element) => {
                    element.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }, contactButton);
                await humanLikeDelay(1000, 2000);
                console.log('Scrolled to contact button');
            } catch (error) {
                console.log('Could not scroll to contact button');
            }
            
            // Click the contact button
            console.log('Clicking contact button...');
            await contactButton.click();
            await humanLikeDelay(3000, 5000);
            
            // Check if we've been redirected to a contact form page
            const currentUrl = page.url();
            console.log(`🔗 Current URL after click: ${currentUrl}`);
            
            if (currentUrl.includes('makelaar-contact') || currentUrl.includes('contact')) {
                console.log('✅ Successfully navigated to contact form page!');
                // Wait for the new page to load
                await page.waitForFunction(() => document.readyState === 'complete');
                await humanLikeDelay(2000, 3000);
            }
            
            // Check for captcha after clicking contact button
            const contactCaptchaHandled = await handleCaptcha(page);
            if (!contactCaptchaHandled) {
                console.log('⚠️ Proceeding despite captcha presence...');
            }
        }

        // Look for contact form (could be modal or inline)
        const formSelectors = [
            'form[data-testid*="contact"]',
            '.contact-form',
            '.modal-content form',
            '[data-testid="contact-modal"] form',
            'form.fd-form'
        ];

        let contactForm = null;
        for (const selector of formSelectors) {
            try {
                await page.waitForSelector(selector, { timeout: 5000 });
                contactForm = await page.$(selector);
                if (contactForm) {
                    console.log(`Found contact form with selector: ${selector}`);
                    break;
                }
            } catch (error) {
                // Continue to next selector
            }
        }

        if (!contactForm) {
            console.log('Contact form not found, looking for individual input fields...');
        }

        // Fill out the form
        const formData = CONFIG.CONTACT_DATA;

        console.log('Filling out the contact form...');
        
        // Track which fields were filled
        const filledFields = {
            name: false,
            email: false,
            phone: false,
            message: false,
            viewingRequest: false
        };
        
        // Wait for form to be fully loaded
        console.log('Waiting for form elements to load...');
        try {
            await page.waitForSelector('form', { timeout: 10000 });
            await delay(2000); // Give extra time for dynamic content
            console.log('Form detected, proceeding with field filling...');
        } catch (error) {
            console.log('Form not found, but proceeding anyway...');
        }

        // Try different field selectors for message/question
        console.log('🔍 Looking for question/message field...');
        const messageSelectors = [
            '#questionInput', // Specific Funda form question field
            'textarea[placeholder*="Stel je vraag"]',
            'textarea[aria-labelledby="questionInput-label"]',
            'textarea[name="message"]',
            'textarea[name="bericht"]',
            'textarea[data-testid*="message"]',
            'textarea[placeholder*="message"]',
            'textarea[placeholder*="bericht"]',
            'textarea'
        ];

        for (const selector of messageSelectors) {
            try {
                console.log(`  Trying selector: ${selector}`);
                await page.waitForSelector(selector, { timeout: 3000 });
                const messageField = await page.$(selector);
                if (messageField) {
                    // Check if field is visible and enabled
                    const isVisible = await page.evaluate(el => {
                        const rect = el.getBoundingClientRect();
                        return rect.width > 0 && rect.height > 0 && !el.disabled;
                    }, messageField);
                    
                    if (isVisible) {
                        console.log(`  ✅ Found visible message field: ${selector}`);
                        await messageField.click();
                        await humanLikeDelay(500, 1000);
                        
                        // Clear field properly
                        await messageField.evaluate(el => el.value = '');
                        await delay(200);
                        
                        await messageField.type(formData.message, { delay: 50 + Math.random() * 50 });
                        
                        // Verify content was added
                        const fieldValue = await page.evaluate(el => el.value, messageField);
                        if (fieldValue && fieldValue.includes(formData.message.substring(0, 20))) {
                            console.log('✅ Successfully filled message/question field');
                            filledFields.message = true;
                            break;
                        } else {
                            console.log('⚠️ Message field may not have been filled properly');
                        }
                    }
                }
            } catch (error) {
                console.log(`  ❌ Selector failed: ${selector}`);
                // Continue
            }
        }

        // Try different field selectors for email
        console.log('🔍 Looking for email field...');
        const emailSelectors = [
            '#emailAddress', // Specific Funda form email field
            'input[type="email"]',
            'input[name="email"]',
            'input[data-testid*="email"]',
            'input[placeholder*="email"]',
            'input[placeholder*="e-mail"]'
        ];

        for (const selector of emailSelectors) {
            try {
                console.log(`  Trying selector: ${selector}`);
                await page.waitForSelector(selector, { timeout: 3000 });
                const emailField = await page.$(selector);
                if (emailField) {
                    const isVisible = await page.evaluate(el => {
                        const rect = el.getBoundingClientRect();
                        return rect.width > 0 && rect.height > 0 && !el.disabled;
                    }, emailField);
                    
                    if (isVisible) {
                        console.log(`  ✅ Found visible email field: ${selector}`);
                        await emailField.click();
                        await humanLikeDelay(500, 1000);
                        
                        // Clear field properly
                        await emailField.evaluate(el => el.value = '');
                        await delay(200);
                        
                        await emailField.type(formData.email, { delay: 100 + Math.random() * 100 });
                        
                        // Verify content was added
                        const fieldValue = await page.evaluate(el => el.value, emailField);
                        if (fieldValue === formData.email) {
                            console.log('✅ Successfully filled email field');
                            filledFields.email = true;
                            break;
                        } else {
                            console.log('⚠️ Email field may not have been filled properly');
                        }
                    }
                }
            } catch (error) {
                console.log(`  ❌ Selector failed: ${selector}`);
                // Continue
            }
        }

        // Fill first name field
        console.log('🔍 Looking for first name field...');
        const firstNameSelectors = [
            '#firstName', // Specific Funda form first name field
            'input[name="firstName"]',
            'input[name="voornaam"]',
            'input[placeholder*="Voornaam"]'
        ];

        for (const selector of firstNameSelectors) {
            try {
                console.log(`  Trying selector: ${selector}`);
                await page.waitForSelector(selector, { timeout: 3000 });
                const firstNameField = await page.$(selector);
                if (firstNameField) {
                    const isVisible = await page.evaluate(el => {
                        const rect = el.getBoundingClientRect();
                        return rect.width > 0 && rect.height > 0 && !el.disabled;
                    }, firstNameField);
                    
                    if (isVisible) {
                        console.log(`  ✅ Found visible first name field: ${selector}`);
                        await firstNameField.click();
                        await humanLikeDelay(500, 1000);
                        
                        // Clear field properly
                        await firstNameField.evaluate(el => el.value = '');
                        await delay(200);
                        
                        // Split the full name for first name
                        const firstName = formData.name.split(' ')[0];
                        await firstNameField.type(firstName, { delay: 100 + Math.random() * 100 });
                        
                        // Verify content was added
                        const fieldValue = await page.evaluate(el => el.value, firstNameField);
                        if (fieldValue === firstName) {
                            console.log('✅ Successfully filled first name field');
                            filledFields.name = true; // Mark as filled since we're handling the name
                            break;
                        } else {
                            console.log('⚠️ First name field may not have been filled properly');
                        }
                    }
                }
            } catch (error) {
                console.log(`  ❌ Selector failed: ${selector}`);
                // Continue
            }
        }

        // Fill last name field
        console.log('🔍 Looking for last name field...');
        const lastNameSelectors = [
            '#lastName', // Specific Funda form last name field
            'input[name="lastName"]',
            'input[name="achternaam"]',
            'input[placeholder*="Achternaam"]'
        ];

        for (const selector of lastNameSelectors) {
            try {
                console.log(`  Trying selector: ${selector}`);
                await page.waitForSelector(selector, { timeout: 3000 });
                const lastNameField = await page.$(selector);
                if (lastNameField) {
                    const isVisible = await page.evaluate(el => {
                        const rect = el.getBoundingClientRect();
                        return rect.width > 0 && rect.height > 0 && !el.disabled;
                    }, lastNameField);
                    
                    if (isVisible) {
                        console.log(`  ✅ Found visible last name field: ${selector}`);
                        await lastNameField.click();
                        await humanLikeDelay(500, 1000);
                        
                        // Clear field properly
                        await lastNameField.evaluate(el => el.value = '');
                        await delay(200);
                        
                        // Split the full name for last name
                        const nameParts = formData.name.split(' ');
                        const lastName = nameParts.slice(1).join(' ') || nameParts[0]; // Handle single names
                        await lastNameField.type(lastName, { delay: 100 + Math.random() * 100 });
                        
                        // Verify content was added
                        const fieldValue = await page.evaluate(el => el.value, lastNameField);
                        if (fieldValue === lastName) {
                            console.log('✅ Successfully filled last name field');
                            break;
                        } else {
                            console.log('⚠️ Last name field may not have been filled properly');
                        }
                    }
                }
            } catch (error) {
                console.log(`  ❌ Selector failed: ${selector}`);
                // Continue
            }
        }

        // Try different field selectors for phone
        console.log('🔍 Looking for phone field...');
        const phoneSelectors = [
            '#phoneNumber', // Specific Funda form phone field
            'input[type="tel"]',
            'input[name="phone"]',
            'input[name="telephone"]',
            'input[name="telefoon"]',
            'input[data-testid*="phone"]',
            'input[placeholder*="phone"]',
            'input[placeholder*="telefoon"]'
        ];

        for (const selector of phoneSelectors) {
            try {
                console.log(`  Trying selector: ${selector}`);
                await page.waitForSelector(selector, { timeout: 3000 });
                const phoneField = await page.$(selector);
                if (phoneField) {
                    const isVisible = await page.evaluate(el => {
                        const rect = el.getBoundingClientRect();
                        return rect.width > 0 && rect.height > 0 && !el.disabled;
                    }, phoneField);
                    
                    if (isVisible) {
                        console.log(`  ✅ Found visible phone field: ${selector}`);
                        await phoneField.click();
                        await humanLikeDelay(500, 1000);
                        
                        // Clear field properly
                        await phoneField.evaluate(el => el.value = '');
                        await delay(200);
                        
                        await phoneField.type(formData.phone, { delay: 100 + Math.random() * 100 });
                        
                        // Verify content was added
                        const fieldValue = await page.evaluate(el => el.value, phoneField);
                        if (fieldValue === formData.phone) {
                            console.log('✅ Successfully filled phone field');
                            filledFields.phone = true;
                            break;
                        } else {
                            console.log('⚠️ Phone field may not have been filled properly');
                        }
                    }
                }
            } catch (error) {
                console.log(`  ❌ Selector failed: ${selector}`);
                // Continue
            }
        }

        // Handle the viewing request checkbox if needed
        try {
            const viewingCheckbox = await page.$('#checkbox-viewingRequest');
            if (viewingCheckbox) {
                const isChecked = await page.evaluate(el => el.checked, viewingCheckbox);
                if (!isChecked && CONFIG.CONTACT_DATA.requestViewing) {
                    await viewingCheckbox.click();
                    console.log('Checked viewing request checkbox');
                    filledFields.viewingRequest = true;
                } else if (isChecked) {
                    console.log('Viewing request checkbox already checked');
                    filledFields.viewingRequest = true;
                }
            }
        } catch (error) {
            console.log('Could not find or handle viewing request checkbox');
        }

        // Handle any checkboxes (privacy policy, etc.)
        const checkboxSelectors = [
            'input[type="checkbox"][required]',
            'input[data-testid*="privacy"]',
            'input[data-testid*="agree"]',
            '.checkbox input[type="checkbox"]'
        ];

        for (const selector of checkboxSelectors) {
            try {
                const checkbox = await page.$(selector);
                if (checkbox) {
                    const isChecked = await page.evaluate(el => el.checked, checkbox);
                    if (!isChecked) {
                        await checkbox.click();
                        console.log('Checked required checkbox');
                    }
                }
            } catch (error) {
                // Continue
            }
        }

        await humanLikeDelay(2000, 4000);
        
        // Take debug screenshot after filling attempts
        try {
            await page.screenshot({ path: 'debug-form-after-filling.png', fullPage: true });
            console.log('📸 Debug screenshot saved as debug-form-after-filling.png');
        } catch (error) {
            console.log('Could not take debug screenshot');
        }
        
        // Final captcha check before form submission
        console.log('🔍 Final check for captcha before preparing for submission...');
        const finalCaptchaHandled = await handleCaptcha(page);
        if (!finalCaptchaHandled) {
            console.log('⚠️ Captcha present - please resolve manually before submission');
        }

        // Manual submission intervention
        console.log('\n' + '✅ FORM FILLING COMPLETED');
        console.log('═'.repeat(60));
        console.log('📝 Form Fields Status:');
        console.log(`   • Question/Message: ${filledFields.message ? '✅ Filled' : '❌ Not filled'} (${CONFIG.CONTACT_DATA.message.substring(0, 40)}...)`);
        console.log(`   • Email: ${filledFields.email ? '✅ Filled' : '❌ Not filled'} (${CONFIG.CONTACT_DATA.email})`);
        console.log(`   • First/Last Name: ${filledFields.name ? '✅ Filled' : '❌ Not filled'} (${CONFIG.CONTACT_DATA.name})`);
        console.log(`   • Phone: ${filledFields.phone ? '✅ Filled' : '❌ Not filled'} (${CONFIG.CONTACT_DATA.phone})`);
        console.log(`   • Viewing Request: ${filledFields.viewingRequest ? '✅ Checked' : '❌ Not checked'} (${CONFIG.CONTACT_DATA.requestViewing ? 'Enabled' : 'Disabled'} in config)`);
        console.log('🔍 Please review the form data for accuracy');
        console.log('👤 MANUAL SUBMISSION REQUIRED');
        console.log('═'.repeat(60));
        console.log('🔧 Instructions:');
        console.log('   • Review all filled form fields');
        console.log('   • Make any necessary corrections');
        console.log('   • Click the submit button when ready');
        console.log('   • The script will pause here indefinitely');
        console.log('═'.repeat(60));
        
        await manualIntervention(page, 
            'Form filling completed. Please review the form and submit manually when ready.', 
            300 // 5 minutes timeout
        );

        // Take a screenshot for verification before manual submission
        await page.screenshot({ path: 'funda-form-filled.png', fullPage: true });
        console.log('📸 Screenshot saved as funda-form-filled.png for review');
        
        console.log('\n✅ AUTOMATION COMPLETED SUCCESSFULLY!');
        console.log('📋 Form has been filled and is ready for manual submission');
        console.log('👤 Please submit the form when you are satisfied with the data');
        
        // Keep browser open for manual submission
        console.log('\n🔄 Browser will remain open for manual submission...');
        console.log('🛍️ Press Ctrl+C to close when finished');
        
        // Wait indefinitely (until user manually closes)
        await new Promise(() => {}); // This will keep the script running

    } catch (error) {
        console.error('❌ Error during automation:', error.message);
        
        // Take screenshot on error
        try {
            await page.screenshot({ path: 'funda-contact-error.png', fullPage: true });
            console.log('Error screenshot saved as funda-contact-error.png');
        } catch (screenshotError) {
            console.log('Could not take error screenshot');
        }
    } finally {
        if (browser) {
            await browser.close();
        }
    }
}

// Configuration function to customize form data
function customizeFormData(name, email, phone, message) {
    // You can call this function with your own data before running the automation
    return {
        name: name || 'John Doe',
        email: email || '<EMAIL>',
        phone: phone || '+31612345678',
        message: message || 'Hello, I am interested in this property. Could you please provide more information about the viewing schedule and availability? Thank you.'
    };
}

// Run the automation
if (require.main === module) {
    automateFundaContact();
}

module.exports = { automateFundaContact, customizeFormData };