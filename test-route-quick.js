const axios = require('axios');

async function quickTest() {
  try {
    console.log('Testing route accessibility...');
    
    // Test the status endpoint first
    const response = await axios.get('http://localhost:3000/api/scraper/status');
    console.log('✅ Status endpoint works:', response.data);
    
    // Test disable endpoint
    const disableResponse = await axios.post('http://localhost:3000/api/scraper/disable/pararius');
    console.log('✅ Disable endpoint works:', disableResponse.data);
    
  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
    console.error('Status:', error.response?.status);
    console.error('URL:', error.config?.url);
  }
}

quickTest();