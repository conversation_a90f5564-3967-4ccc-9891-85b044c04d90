import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Switch,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import notificationService, { NotificationPreferences } from '../services/notificationService';

interface SettingItem {
  key: keyof NotificationPreferences;
  title: string;
  description: string;
  icon: string;
  category: 'timing' | 'channels';
}

const NotificationSettingsScreen: React.FC = () => {
  const router = useRouter();
  const [preferences, setPreferences] = useState<NotificationPreferences>({
    immediate: true,
    daily: true,
    weekly: false,
    email: true,
    sms: false,
    push: true,
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [pushNotificationSupported, setPushNotificationSupported] = useState(true);

  const timingSettings: SettingItem[] = [
    {
      key: 'immediate',
      title: 'Immediate Notifications',
      description: 'Get notified instantly when application status changes',
      icon: 'flash',
      category: 'timing',
    },
    {
      key: 'daily',
      title: 'Daily Summary',
      description: 'Receive daily reports of your application activity',
      icon: 'calendar-outline',
      category: 'timing',
    },
    {
      key: 'weekly',
      title: 'Weekly Report',
      description: 'Get comprehensive weekly performance reports',
      icon: 'stats-chart',
      category: 'timing',
    },
  ];

  const channelSettings: SettingItem[] = [
    {
      key: 'email',
      title: 'Email Notifications',
      description: 'Receive notifications via email',
      icon: 'mail',
      category: 'channels',
    },
    {
      key: 'sms',
      title: 'SMS Notifications',
      description: 'Get urgent alerts via text message',
      icon: 'chatbubble',
      category: 'channels',
    },
    {
      key: 'push',
      title: 'Push Notifications',
      description: 'Receive notifications on your device',
      icon: 'notifications',
      category: 'channels',
    },
  ];

  useEffect(() => {
    loadPreferences();
    checkPushNotificationSupport();
  }, []);

  const checkPushNotificationSupport = () => {
    const isSupported = notificationService.isPushNotificationSupported();
    setPushNotificationSupported(isSupported);
  };

  const loadPreferences = async () => {
    try {
      setLoading(true);
      const userPreferences = await notificationService.getNotificationPreferences();
      setPreferences(userPreferences);
    } catch (error) {
      console.error('Error loading notification preferences:', error);
      Alert.alert(
        'Error',
        'Failed to load notification preferences. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setLoading(false);
    }
  };

  const handleToggle = (key: keyof NotificationPreferences) => {
    const newPreferences = {
      ...preferences,
      [key]: !preferences[key],
    };
    setPreferences(newPreferences);
    setHasChanges(true);
  };

  const savePreferences = async () => {
    try {
      setSaving(true);
      await notificationService.updateNotificationPreferences(preferences);
      setHasChanges(false);
      Alert.alert(
        'Success',
        'Notification preferences updated successfully!',
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('Error saving notification preferences:', error);
      Alert.alert(
        'Error',
        'Failed to save notification preferences. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setSaving(false);
    }
  };

  const sendTestNotification = async (type: 'daily' | 'weekly' | 'urgent') => {
    try {
      switch (type) {
        case 'daily':
          await notificationService.sendTestDailySummary();
          break;
        case 'weekly':
          await notificationService.sendTestWeeklySummary();
          break;
        case 'urgent':
          await notificationService.sendTestUrgentAlert('system_error', 'This is a test urgent alert');
          break;
      }
      
      Alert.alert(
        'Test Sent',
        `Test ${type} notification has been sent. Check your enabled notification channels.`,
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error(`Error sending test ${type} notification:`, error);
      Alert.alert(
        'Error',
        `Failed to send test ${type} notification. Please try again.`,
        [{ text: 'OK' }]
      );
    }
  };

  const requestPermissions = async () => {
    try {
      const granted = await notificationService.requestNotificationPermissions();
      if (granted) {
        Alert.alert(
          'Permissions Granted',
          'Notification permissions have been granted successfully.',
          [{ text: 'OK' }]
        );
      } else {
        Alert.alert(
          'Permissions Denied',
          'Notification permissions were denied. You can enable them in your device settings.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Error requesting permissions:', error);
      Alert.alert(
        'Error',
        'Failed to request notification permissions.',
        [{ text: 'OK' }]
      );
    }
  };

  const renderSettingItem = (setting: SettingItem) => {
    const isDisabled = setting.key === 'push' && !pushNotificationSupported;
    
    return (
      <View key={setting.key} style={[styles.settingItem, isDisabled && styles.disabledSetting]}>
        <View style={styles.settingInfo}>
          <View style={styles.settingHeader}>
            <Ionicons 
              name={setting.icon as any} 
              size={24} 
              color={isDisabled ? "#6c757d" : "#007bff"} 
              style={styles.settingIcon}
            />
            <Text style={[styles.settingTitle, isDisabled && styles.disabledText]}>
              {setting.title}
              {isDisabled && " (Not Available)"}
            </Text>
          </View>
          <Text style={[styles.settingDescription, isDisabled && styles.disabledText]}>
            {isDisabled 
              ? "Push notifications are not supported in Expo Go. Use a development build instead."
              : setting.description
            }
          </Text>
        </View>
        <Switch
          value={isDisabled ? false : preferences[setting.key]}
          onValueChange={isDisabled ? undefined : () => handleToggle(setting.key)}
          trackColor={{ false: '#e9ecef', true: '#007bff' }}
          thumbColor={preferences[setting.key] ? '#ffffff' : '#ffffff'}
          ios_backgroundColor="#e9ecef"
          disabled={isDisabled}
        />
      </View>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
            <Ionicons name="arrow-back" size={24} color="#007bff" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Notification Settings</Text>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007bff" />
          <Text style={styles.loadingText}>Loading preferences...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#007bff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Notification Settings</Text>
        {hasChanges && (
          <TouchableOpacity 
            onPress={savePreferences} 
            style={styles.saveButton}
            disabled={saving}
          >
            {saving ? (
              <ActivityIndicator size="small" color="#007bff" />
            ) : (
              <Text style={styles.saveButtonText}>Save</Text>
            )}
          </TouchableOpacity>
        )}
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Expo Go Warning Banner */}
        {!pushNotificationSupported && (
          <View style={styles.warningBanner}>
            <Ionicons name="warning" size={24} color="#ffc107" />
            <View style={styles.warningContent}>
              <Text style={styles.warningTitle}>Limited Notification Support</Text>
              <Text style={styles.warningText}>
                Push notifications are not supported in Expo Go. To enable full notification functionality, 
                create a development build or use the production app.
              </Text>
            </View>
          </View>
        )}

        {/* Notification Timing Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Notification Timing</Text>
          <Text style={styles.sectionDescription}>
            Choose when you want to receive notifications about your auto-applications
          </Text>
          <View style={styles.settingsGroup}>
            {timingSettings.map(renderSettingItem)}
          </View>
        </View>

        {/* Notification Channels Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Notification Channels</Text>
          <Text style={styles.sectionDescription}>
            Select how you want to receive notifications
          </Text>
          <View style={styles.settingsGroup}>
            {channelSettings.map(renderSettingItem)}
          </View>
        </View>

        {/* Permissions Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Permissions</Text>
          <TouchableOpacity style={styles.permissionButton} onPress={requestPermissions}>
            <Ionicons name="shield-checkmark" size={24} color="#007bff" />
            <View style={styles.permissionInfo}>
              <Text style={styles.permissionTitle}>Notification Permissions</Text>
              <Text style={styles.permissionDescription}>
                Ensure notifications are enabled for this app
              </Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#6c757d" />
          </TouchableOpacity>
        </View>

        {/* Test Notifications Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Test Notifications</Text>
          <Text style={styles.sectionDescription}>
            Send test notifications to verify your settings
          </Text>
          <View style={styles.testButtonsContainer}>
            <TouchableOpacity 
              style={styles.testButton} 
              onPress={() => sendTestNotification('daily')}
            >
              <Ionicons name="calendar-outline" size={20} color="#007bff" />
              <Text style={styles.testButtonText}>Test Daily Summary</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.testButton} 
              onPress={() => sendTestNotification('weekly')}
            >
              <Ionicons name="stats-chart" size={20} color="#007bff" />
              <Text style={styles.testButtonText}>Test Weekly Report</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.testButton} 
              onPress={() => sendTestNotification('urgent')}
            >
              <Ionicons name="warning" size={20} color="#dc3545" />
              <Text style={[styles.testButtonText, { color: '#dc3545' }]}>Test Urgent Alert</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Information Section */}
        <View style={styles.section}>
          <View style={styles.infoBox}>
            <Ionicons name="information-circle" size={24} color="#17a2b8" />
            <View style={styles.infoContent}>
              <Text style={styles.infoTitle}>About Notifications</Text>
              <Text style={styles.infoText}>
                • Immediate notifications keep you updated on application status changes{'\n'}
                • Daily summaries provide an overview of your daily activity{'\n'}
                • Weekly reports offer detailed performance insights{'\n'}
                • SMS notifications are only sent for urgent alerts{'\n'}
                • You can change these settings anytime
              </Text>
            </View>
          </View>
        </View>

        <View style={styles.bottomSpacing} />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  backButton: {
    padding: 8,
    marginLeft: -8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#212529',
    flex: 1,
    textAlign: 'center',
    marginRight: 40,
  },
  saveButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#e3f2fd',
    borderRadius: 8,
    minWidth: 60,
    alignItems: 'center',
  },
  saveButtonText: {
    color: '#007bff',
    fontWeight: '600',
    fontSize: 14,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6c757d',
  },
  content: {
    flex: 1,
  },
  section: {
    backgroundColor: '#ffffff',
    marginTop: 16,
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#212529',
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    color: '#6c757d',
    marginBottom: 20,
    lineHeight: 20,
  },
  settingsGroup: {
    gap: 16,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 8,
  },
  settingInfo: {
    flex: 1,
    marginRight: 16,
  },
  settingHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  settingIcon: {
    marginRight: 12,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#212529',
  },
  settingDescription: {
    fontSize: 14,
    color: '#6c757d',
    lineHeight: 18,
    marginLeft: 36,
  },
  permissionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  permissionInfo: {
    flex: 1,
    marginLeft: 12,
  },
  permissionTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#212529',
    marginBottom: 4,
  },
  permissionDescription: {
    fontSize: 14,
    color: '#6c757d',
  },
  testButtonsContainer: {
    gap: 12,
  },
  testButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 14,
    paddingHorizontal: 16,
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  testButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#007bff',
    marginLeft: 12,
  },
  infoBox: {
    flexDirection: 'row',
    padding: 16,
    backgroundColor: '#e3f2fd',
    borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#17a2b8',
  },
  infoContent: {
    flex: 1,
    marginLeft: 12,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#17a2b8',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    color: '#17a2b8',
    lineHeight: 20,
  },
  bottomSpacing: {
    height: 32,
  },
  warningBanner: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: 16,
    marginHorizontal: 16,
    marginTop: 16,
    backgroundColor: '#fff3cd',
    borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#ffc107',
  },
  warningContent: {
    flex: 1,
    marginLeft: 12,
  },
  warningTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#856404',
    marginBottom: 4,
  },
  warningText: {
    fontSize: 14,
    color: '#856404',
    lineHeight: 18,
  },
  disabledSetting: {
    opacity: 0.6,
  },
  disabledText: {
    color: '#6c757d',
  },
});

export default NotificationSettingsScreen;