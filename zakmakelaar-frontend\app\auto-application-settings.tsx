import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  Switch,
  TextInput,
  Alert,
  ActivityIndicator,
  Modal,
} from 'react-native';
import { useRouter } from 'expo-router';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import Animated, { FadeInUp, FadeInDown } from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';

import { useAuthStore } from '../store/authStore';
import { autoApplicationService, AutoApplicationSettings } from '../services/autoApplicationService';
import { LocationSelector } from '../components/LocationSelector';

// Theme colors
const THEME = {
  primary: '#4361ee',
  secondary: '#7209b7',
  accent: '#f72585',
  dark: '#0a0a18',
  light: '#ffffff',
  gray: '#6b7280',
  lightGray: '#f3f4f6',
  success: '#10b981',
  warning: '#f59e0b',
  danger: '#ef4444',
};

// Header Component
const Header = () => {
  const router = useRouter();
  const insets = useSafeAreaInsets();

  return (
    <LinearGradient
      colors={[THEME.primary, THEME.secondary]}
      style={[styles.header, { paddingTop: Math.max(insets.top, 16) }]}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
    >
      <Animated.View
        style={styles.headerContent}
        entering={FadeInUp.duration(600)}
      >
        <TouchableOpacity
          onPress={() => {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            router.back();
          }}
          style={styles.backButton}
          activeOpacity={0.8}
        >
          <View style={styles.backButtonInner}>
            <Ionicons name="chevron-back" size={24} color={THEME.primary} />
          </View>
        </TouchableOpacity>

        <View style={styles.headerCenter}>
          <View style={styles.logoContainer}>
            <Text style={styles.logoText}>ZM</Text>
          </View>
          <Text style={styles.headerTitle}>Auto-Application Settings</Text>
        </View>

        <View style={styles.headerSpacer} />
      </Animated.View>
    </LinearGradient>
  );
};

// Settings Section Component
const SettingsSection = ({
  title,
  children,
  icon
}: {
  title: string;
  children: React.ReactNode;
  icon: string;
}) => (
  <Animated.View
    style={styles.settingsSection}
    entering={FadeInUp.duration(600)}
  >
    <View style={styles.sectionHeader}>
      <Ionicons name={icon as any} size={20} color={THEME.primary} />
      <Text style={styles.sectionTitle}>{title}</Text>
    </View>
    {children}
  </Animated.View>
);

// Setting Item Component
const SettingItem = ({
  label,
  description,
  children,
  warning,
}: {
  label: string;
  description?: string;
  children: React.ReactNode;
  warning?: string;
}) => (
  <View style={styles.settingItem}>
    <View style={styles.settingContent}>
      <View style={styles.settingText}>
        <Text style={styles.settingLabel} numberOfLines={2}>{label}</Text>
        {description && <Text style={styles.settingDescription} numberOfLines={3}>{description}</Text>}
        {warning && (
          <View style={styles.warningContainer}>
            <Ionicons name="warning" size={14} color={THEME.warning} />
            <Text style={styles.warningText} numberOfLines={2}>{warning}</Text>
          </View>
        )}
      </View>
      <View style={styles.settingControl}>
        {children}
      </View>
    </View>
  </View>
);

export default function AutoApplicationSettingsScreen() {
  const router = useRouter();
  const { user } = useAuthStore();

  // State
  const [settings, setSettings] = useState<AutoApplicationSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [showAdvanced, setShowAdvanced] = useState(false);

  // Load settings on mount
  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    if (!user || !user.id) return;

    try {
      setLoading(true);
      const response = await autoApplicationService.getSettings(user.id);

      if (response.success && response.data) {
        setSettings(response.data);
      } else {
        // Create default settings if none exist
        const defaultSettings: Partial<AutoApplicationSettings> = {
          userId: user.id!,
          enabled: false,
          settings: {
            maxApplicationsPerDay: 3,
            applicationTemplate: 'professional',
            autoSubmit: false,
            requireManualReview: true,
            notificationPreferences: {
              immediate: true,
              daily: true,
              weekly: false,
            },
            language: 'english',
          },
          criteria: {
            maxPrice: 2000,
            minRooms: 1,
            maxRooms: 5,
            propertyTypes: ['apartment', 'house'],
            locations: [],
            excludeKeywords: ['student', 'shared'],
            includeKeywords: [],
            minSize: 30,
            maxSize: 150,
          },
          personalInfo: {
            fullName: user.firstName + ' ' + user.lastName,
            email: user.email,
            phone: '',
          },
          documents: [],
          statistics: {
            totalApplications: 0,
            successfulApplications: 0,
            pendingApplications: 0,
            rejectedApplications: 0,
            averageResponseTime: 0,
          },
          status: {
            isActive: false,
            currentQueue: 0,
            dailyApplicationsUsed: 0,
            weeklyApplicationsUsed: 0,
            monthlyApplicationsUsed: 0,
            lastResetDate: new Date(),
          },
        };

        setSettings(defaultSettings as AutoApplicationSettings);
      }
    } catch (error) {
      console.error('Error loading settings:', error);
      Alert.alert('Error', 'Failed to load auto-application settings');
    } finally {
      setLoading(false);
    }
  };

  const validateSettings = (): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];

    if (!settings) {
      errors.push('Settings not loaded');
      return { isValid: false, errors };
    }

    // Validate price range
    if (settings.criteria.maxPrice && settings.criteria.maxPrice < 100) {
      errors.push('Maximum price should be at least €100');
    }

    // Validate room range
    if (settings.criteria.minRooms && settings.criteria.maxRooms) {
      if (settings.criteria.minRooms > settings.criteria.maxRooms) {
        errors.push('Minimum rooms cannot be greater than maximum rooms');
      }
    }

    // Validate size range
    if (settings.criteria.minSize && settings.criteria.maxSize) {
      if (settings.criteria.minSize > settings.criteria.maxSize) {
        errors.push('Minimum size cannot be greater than maximum size');
      }
    }

    // Validate property types
    if (settings.criteria.propertyTypes.length === 0) {
      errors.push('Please select at least one property type');
    }

    // Validate locations
    if (settings.criteria.locations.length === 0) {
      errors.push('Please select at least one preferred location');
    }

    // Validate daily limit
    if (settings.settings.maxApplicationsPerDay < 1 || settings.settings.maxApplicationsPerDay > 20) {
      errors.push('Daily application limit must be between 1 and 20');
    }

    return { isValid: errors.length === 0, errors };
  };

  const saveSettings = async () => {
    if (!user || !user.id || !settings) return;

    // Validate settings before saving
    const validation = validateSettings();
    if (!validation.isValid) {
      Alert.alert(
        'Invalid Settings',
        validation.errors.join('\n\n'),
        [{ text: 'OK', style: 'default' }]
      );
      return;
    }

    try {
      setSaving(true);
      const response = await autoApplicationService.updateSettings(user.id, settings);

      if (response.success) {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        Alert.alert('Success', 'Settings saved successfully');
      } else {
        throw new Error(response.message || 'Failed to save settings');
      }
    } catch (error: any) {
      console.error('Error saving settings:', error);
      Alert.alert('Error', error.message || 'Failed to save settings');
    } finally {
      setSaving(false);
    }
  };

  const toggleAutoApplication = async () => {
    if (!user || !user.id || !settings) return;

    try {
      setSaving(true);

      if (settings.enabled) {
        // Disable auto-application
        const response = await autoApplicationService.disableAutoApplication(user.id);
        if (response.success) {
          setSettings({ ...settings, enabled: false });
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        }
      } else {
        // Enable auto-application
        const response = await autoApplicationService.enableAutoApplication(user.id, settings);
        if (response.success && response.data) {
          setSettings(response.data);
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        }
      }
    } catch (error: any) {
      console.error('Error toggling auto-application:', error);
      Alert.alert('Error', error.message || 'Failed to update auto-application status');
    } finally {
      setSaving(false);
    }
  };

  const updateSetting = (path: string, value: any) => {
    if (!settings) return;

    const pathArray = path.split('.');
    const newSettings = { ...settings };
    let current: any = newSettings;

    for (let i = 0; i < pathArray.length - 1; i++) {
      current = current[pathArray[i]];
    }

    current[pathArray[pathArray.length - 1]] = value;
    setSettings(newSettings);
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <Header />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={THEME.primary} />
          <Text style={styles.loadingText}>Loading settings...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!settings) {
    return (
      <SafeAreaView style={styles.container}>
        <Header />
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={48} color={THEME.danger} />
          <Text style={styles.errorText}>Failed to load settings</Text>
          <TouchableOpacity style={styles.retryButton} onPress={loadSettings}>
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <Header />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Main Toggle */}
        <Animated.View
          style={styles.mainToggleContainer}
          entering={FadeInUp.duration(600).delay(200)}
        >
          <LinearGradient
            colors={settings.enabled ? [THEME.success, '#059669'] : [THEME.gray, '#4b5563']}
            style={styles.mainToggleGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <View style={styles.mainToggleContent}>
              <View style={styles.mainToggleText}>
                <Text style={styles.mainToggleTitle}>
                  Auto-Application {settings.enabled ? 'Enabled' : 'Disabled'}
                </Text>
                <Text style={styles.mainToggleDescription}>
                  {settings.enabled
                    ? 'Automatically applying to matching properties'
                    : 'Enable to start automatic applications'
                  }
                </Text>
              </View>
              <Switch
                value={settings.enabled}
                onValueChange={toggleAutoApplication}
                disabled={saving}
                trackColor={{ false: '#e5e7eb', true: '#ffffff' }}
                thumbColor={settings.enabled ? THEME.success : '#f4f3f4'}
              />
            </View>
          </LinearGradient>
        </Animated.View>

        {/* Status Overview */}
        {settings.enabled && (
          <Animated.View
            style={styles.statusContainer}
            entering={FadeInUp.duration(600).delay(400)}
          >
            <Text style={styles.statusTitle}>Current Status</Text>
            <View style={styles.statusGrid}>
              <View style={styles.statusItem}>
                <Text style={styles.statusValue}>{settings.status.dailyApplicationsUsed}</Text>
                <Text style={styles.statusLabel}>Today</Text>
              </View>
              <View style={styles.statusItem}>
                <Text style={styles.statusValue}>{settings.status.currentQueue}</Text>
                <Text style={styles.statusLabel}>In Queue</Text>
              </View>
              <View style={styles.statusItem}>
                <Text style={styles.statusValue}>
                  {settings.statistics.totalApplications > 0
                    ? Math.round((settings.statistics.successfulApplications / settings.statistics.totalApplications) * 100)
                    : 0
                  }%
                </Text>
                <Text style={styles.statusLabel}>Success Rate</Text>
              </View>
            </View>
          </Animated.View>
        )}

        {/* Basic Settings */}
        <SettingsSection title="Application Settings" icon="settings-outline">
          <SettingItem
            label="Daily Application Limit"
            description="Maximum applications per day to avoid spam"
          >
            <View style={styles.numberInputContainer}>
              <TouchableOpacity
                style={styles.numberButton}
                onPress={() => updateSetting('settings.maxApplicationsPerDay', Math.max(1, settings.settings.maxApplicationsPerDay - 1))}
              >
                <Ionicons name="remove" size={16} color={THEME.primary} />
              </TouchableOpacity>
              <Text style={styles.numberValue}>{settings.settings.maxApplicationsPerDay}</Text>
              <TouchableOpacity
                style={styles.numberButton}
                onPress={() => updateSetting('settings.maxApplicationsPerDay', Math.min(20, settings.settings.maxApplicationsPerDay + 1))}
              >
                <Ionicons name="add" size={16} color={THEME.primary} />
              </TouchableOpacity>
            </View>
          </SettingItem>

          <SettingItem
            label="Application Template"
            description="Style of application letters"
          >
            <View style={styles.templateSelector}>
              {['professional', 'casual', 'student', 'expat'].map((template) => (
                <TouchableOpacity
                  key={template}
                  style={[
                    styles.templateOption,
                    settings.settings.applicationTemplate === template && styles.templateOptionSelected
                  ]}
                  onPress={() => updateSetting('settings.applicationTemplate', template)}
                >
                  <Text style={[
                    styles.templateOptionText,
                    settings.settings.applicationTemplate === template && styles.templateOptionTextSelected
                  ]}>
                    {template.charAt(0).toUpperCase() + template.slice(1)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </SettingItem>

          <SettingItem
            label="Auto Submit"
            description="Automatically submit applications without manual review"
            warning={settings.settings.autoSubmit ? "Applications will be submitted immediately" : undefined}
          >
            <Switch
              value={settings.settings.autoSubmit}
              onValueChange={(value) => updateSetting('settings.autoSubmit', value)}
              trackColor={{ false: '#e5e7eb', true: THEME.accent }}
              thumbColor={settings.settings.autoSubmit ? '#ffffff' : '#f4f3f4'}
            />
          </SettingItem>
        </SettingsSection>

        {/* Criteria Settings */}
        <SettingsSection title="Application Criteria" icon="filter-outline">
          <View style={styles.sectionNote}>
            <Ionicons name="information-circle" size={16} color={THEME.primary} />
            <Text style={styles.sectionNoteText}>
              Configure your preferences to automatically apply to matching properties
            </Text>
          </View>
          <SettingItem
            label="Maximum Price"
            description="Maximum monthly rent in euros"
          >
            <View style={styles.priceInputContainer}>
              <Text style={styles.currencySymbol}>€</Text>
              <TextInput
                style={styles.priceInput}
                value={settings.criteria.maxPrice?.toString() || ''}
                onChangeText={(text) => {
                  const numericValue = text.replace(/[^0-9]/g, '');
                  updateSetting('criteria.maxPrice', parseInt(numericValue) || 0);
                }}
                placeholder="2000"
                keyboardType="numeric"
                selectTextOnFocus
                maxLength={5}
              />
            </View>
          </SettingItem>

          <SettingItem
            label="Room Range"
            description="Minimum and maximum number of rooms"
          >
            <View style={styles.rangeContainer}>
              <View style={styles.rangeInput}>
                <Text style={styles.rangeLabel}>Min</Text>
                <View style={styles.numberInputContainer}>
                  <TouchableOpacity
                    style={styles.numberButton}
                    onPress={() => updateSetting('criteria.minRooms', Math.max(1, (settings.criteria.minRooms || 1) - 1))}
                  >
                    <Ionicons name="remove" size={14} color={THEME.primary} />
                  </TouchableOpacity>
                  <TextInput
                    style={styles.rangeNumberInput}
                    value={settings.criteria.minRooms?.toString() || '1'}
                    onChangeText={(text) => {
                      const value = parseInt(text) || 1;
                      const maxValue = Math.min(value, settings.criteria.maxRooms || 10);
                      updateSetting('criteria.minRooms', Math.max(1, maxValue));
                    }}
                    keyboardType="numeric"
                    selectTextOnFocus
                    maxLength={2}
                  />
                  <TouchableOpacity
                    style={styles.numberButton}
                    onPress={() => {
                      const newMin = Math.min((settings.criteria.minRooms || 1) + 1, settings.criteria.maxRooms || 10);
                      updateSetting('criteria.minRooms', newMin);
                    }}
                  >
                    <Ionicons name="add" size={14} color={THEME.primary} />
                  </TouchableOpacity>
                </View>
              </View>
              <Text style={styles.rangeSeparator}>-</Text>
              <View style={styles.rangeInput}>
                <Text style={styles.rangeLabel}>Max</Text>
                <View style={styles.numberInputContainer}>
                  <TouchableOpacity
                    style={styles.numberButton}
                    onPress={() => {
                      const newMax = Math.max((settings.criteria.maxRooms || 5) - 1, settings.criteria.minRooms || 1);
                      updateSetting('criteria.maxRooms', newMax);
                    }}
                  >
                    <Ionicons name="remove" size={14} color={THEME.primary} />
                  </TouchableOpacity>
                  <TextInput
                    style={styles.rangeNumberInput}
                    value={settings.criteria.maxRooms?.toString() || '5'}
                    onChangeText={(text) => {
                      const value = parseInt(text) || 5;
                      const minValue = Math.max(value, settings.criteria.minRooms || 1);
                      updateSetting('criteria.maxRooms', Math.max(minValue, value));
                    }}
                    keyboardType="numeric"
                    selectTextOnFocus
                    maxLength={2}
                  />
                  <TouchableOpacity
                    style={styles.numberButton}
                    onPress={() => updateSetting('criteria.maxRooms', Math.min((settings.criteria.maxRooms || 5) + 1, 20))}
                  >
                    <Ionicons name="add" size={14} color={THEME.primary} />
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </SettingItem>

          <SettingItem
            label="Preferred Locations"
            description="Select cities where you want to apply for properties"
          >
            <LocationSelector
              selectedLocations={settings.criteria.locations}
              onSelectionChange={(locations) => updateSetting('criteria.locations', locations)}
              placeholder="Select cities..."
              maxSelections={10}
              displayMode="chips"
              style={styles.locationSelector}
            />
          </SettingItem>

          <SettingItem
            label="Property Types"
            description="Types of properties to apply for"
          >
            <View style={styles.checkboxContainer}>
              {['apartment', 'house', 'studio', 'room'].map((type) => (
                <TouchableOpacity
                  key={type}
                  style={styles.checkboxItem}
                  onPress={() => {
                    const currentTypes = settings.criteria.propertyTypes;
                    const newTypes = currentTypes.includes(type)
                      ? currentTypes.filter(t => t !== type)
                      : [...currentTypes, type];
                    updateSetting('criteria.propertyTypes', newTypes);
                  }}
                >
                  <Ionicons
                    name={settings.criteria.propertyTypes.includes(type) ? "checkbox" : "square-outline"}
                    size={20}
                    color={settings.criteria.propertyTypes.includes(type) ? THEME.primary : THEME.gray}
                  />
                  <Text style={styles.checkboxLabel}>
                    {type.charAt(0).toUpperCase() + type.slice(1)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </SettingItem>

          <SettingItem
            label="Furnished Properties"
            description="Only apply to furnished properties"
          >
            <Switch
              value={settings.criteria.furnished || false}
              onValueChange={(value) => updateSetting('criteria.furnished', value)}
              trackColor={{ false: '#e5e7eb', true: THEME.accent }}
              thumbColor={settings.criteria.furnished ? '#ffffff' : '#f4f3f4'}
            />
          </SettingItem>

          <SettingItem
            label="Pets Allowed"
            description="Only apply to pet-friendly properties"
          >
            <Switch
              value={settings.criteria.petsAllowed || false}
              onValueChange={(value) => updateSetting('criteria.petsAllowed', value)}
              trackColor={{ false: '#e5e7eb', true: THEME.accent }}
              thumbColor={settings.criteria.petsAllowed ? '#ffffff' : '#f4f3f4'}
            />
          </SettingItem>
        </SettingsSection>

        {/* Notification Settings */}
        <SettingsSection title="Notifications" icon="notifications-outline">
          <SettingItem
            label="Immediate Notifications"
            description="Get notified when applications are submitted"
          >
            <Switch
              value={settings.settings.notificationPreferences.immediate}
              onValueChange={(value) => updateSetting('settings.notificationPreferences.immediate', value)}
              trackColor={{ false: '#e5e7eb', true: THEME.accent }}
              thumbColor={settings.settings.notificationPreferences.immediate ? '#ffffff' : '#f4f3f4'}
            />
          </SettingItem>

          <SettingItem
            label="Daily Summary"
            description="Daily summary of auto-application activity"
          >
            <Switch
              value={settings.settings.notificationPreferences.daily}
              onValueChange={(value) => updateSetting('settings.notificationPreferences.daily', value)}
              trackColor={{ false: '#e5e7eb', true: THEME.accent }}
              thumbColor={settings.settings.notificationPreferences.daily ? '#ffffff' : '#f4f3f4'}
            />
          </SettingItem>
        </SettingsSection>

        {/* Advanced Settings */}
        <TouchableOpacity
          style={styles.advancedToggle}
          onPress={() => setShowAdvanced(!showAdvanced)}
        >
          <Text style={styles.advancedToggleText}>Advanced Settings</Text>
          <Ionicons
            name={showAdvanced ? "chevron-up" : "chevron-down"}
            size={20}
            color={THEME.primary}
          />
        </TouchableOpacity>

        {showAdvanced && (
          <Animated.View entering={FadeInDown.duration(400)}>
            <SettingsSection title="Advanced Options" icon="construct-outline">
              <SettingItem
                label="Exclude Keywords"
                description="Skip properties containing these words (comma separated)"
              >
                <TextInput
                  style={[styles.textInput, styles.multilineInput, styles.keywordInput]}
                  value={settings.criteria.excludeKeywords.join(', ')}
                  onChangeText={(text) => updateSetting('criteria.excludeKeywords', text.split(',').map(s => s.trim()).filter(s => s))}
                  placeholder="student, shared, temporary"
                  multiline
                  numberOfLines={3}
                />
              </SettingItem>

              <SettingItem
                label="Include Keywords"
                description="Prioritize properties containing these words (comma separated)"
              >
                <TextInput
                  style={[styles.textInput, styles.multilineInput, styles.keywordInput]}
                  value={settings.criteria.includeKeywords.join(', ')}
                  onChangeText={(text) => updateSetting('criteria.includeKeywords', text.split(',').map(s => s.trim()).filter(s => s))}
                  placeholder="balcony, parking, elevator"
                  multiline
                  numberOfLines={3}
                />
              </SettingItem>

              <SettingItem
                label="Size Range (m²)"
                description="Minimum and maximum property size"
              >
                <View style={styles.rangeContainer}>
                  <View style={styles.rangeInput}>
                    <Text style={styles.rangeLabel}>Min</Text>
                    <View style={styles.sizeInputContainer}>
                      <TextInput
                        style={styles.sizeInput}
                        value={settings.criteria.minSize?.toString() || ''}
                        onChangeText={(text) => {
                          const value = parseInt(text) || 0;
                          const maxValue = Math.min(value, settings.criteria.maxSize || 500);
                          updateSetting('criteria.minSize', Math.max(0, maxValue));
                        }}
                        placeholder="30"
                        keyboardType="numeric"
                        selectTextOnFocus
                        maxLength={3}
                      />
                      <Text style={styles.unitLabel}>m²</Text>
                    </View>
                  </View>
                  <Text style={styles.rangeSeparator}>-</Text>
                  <View style={styles.rangeInput}>
                    <Text style={styles.rangeLabel}>Max</Text>
                    <View style={styles.sizeInputContainer}>
                      <TextInput
                        style={styles.sizeInput}
                        value={settings.criteria.maxSize?.toString() || ''}
                        onChangeText={(text) => {
                          const value = parseInt(text) || 200;
                          const minValue = Math.max(value, settings.criteria.minSize || 0);
                          updateSetting('criteria.maxSize', Math.max(minValue, value));
                        }}
                        placeholder="150"
                        keyboardType="numeric"
                        selectTextOnFocus
                        maxLength={3}
                      />
                      <Text style={styles.unitLabel}>m²</Text>
                    </View>
                  </View>
                </View>
              </SettingItem>
            </SettingsSection>
          </Animated.View>
        )}

        {/* Reset Button */}
        <Animated.View
          style={styles.resetContainer}
          entering={FadeInUp.duration(600).delay(700)}
        >
          <TouchableOpacity
            style={styles.resetButton}
            onPress={() => {
              Alert.alert(
                'Reset Settings',
                'Are you sure you want to reset all settings to default values?',
                [
                  { text: 'Cancel', style: 'cancel' },
                  {
                    text: 'Reset',
                    style: 'destructive',
                    onPress: () => {
                      loadSettings();
                      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
                    }
                  }
                ]
              );
            }}
          >
            <Ionicons name="refresh-outline" size={16} color={THEME.danger} />
            <Text style={styles.resetButtonText}>Reset to Defaults</Text>
          </TouchableOpacity>
        </Animated.View>

        {/* Action Buttons */}
        <Animated.View
          style={styles.actionButtons}
          entering={FadeInUp.duration(600).delay(800)}
        >
          <TouchableOpacity
            style={[styles.actionButton, styles.secondaryButton]}
            onPress={() => router.push('/auto-application-dashboard')}
          >
            <Ionicons name="analytics-outline" size={20} color={THEME.primary} />
            <Text style={styles.secondaryButtonText}>View Dashboard</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, styles.primaryButton]}
            onPress={saveSettings}
            disabled={saving}
          >
            {saving ? (
              <ActivityIndicator size="small" color="#ffffff" />
            ) : (
              <Ionicons name="save-outline" size={20} color="#ffffff" />
            )}
            <Text style={styles.primaryButtonText}>
              {saving ? 'Saving...' : 'Save Settings'}
            </Text>
          </TouchableOpacity>
        </Animated.View>

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: THEME.lightGray,
  },
  header: {
    paddingBottom: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  backButton: {
    marginRight: 16,
  },
  backButtonInner: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerCenter: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  logoContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  logoText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#ffffff',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#ffffff',
  },
  headerSpacer: {
    width: 40,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: THEME.gray,
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 40,
  },
  errorText: {
    marginTop: 16,
    fontSize: 16,
    color: THEME.danger,
    textAlign: 'center',
  },
  retryButton: {
    marginTop: 20,
    paddingHorizontal: 24,
    paddingVertical: 12,
    backgroundColor: THEME.primary,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#ffffff',
    fontWeight: '600',
  },
  mainToggleContainer: {
    marginTop: 20,
    marginBottom: 20,
  },
  mainToggleGradient: {
    borderRadius: 16,
    padding: 20,
  },
  mainToggleContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  mainToggleText: {
    flex: 1,
    marginRight: 16,
  },
  mainToggleTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#ffffff',
    marginBottom: 4,
  },
  mainToggleDescription: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  statusContainer: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
  },
  statusTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: THEME.dark,
    marginBottom: 16,
  },
  statusGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statusItem: {
    alignItems: 'center',
  },
  statusValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: THEME.primary,
    marginBottom: 4,
  },
  statusLabel: {
    fontSize: 12,
    color: THEME.gray,
  },
  settingsSection: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    marginBottom: 16,
    overflow: 'hidden',
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: THEME.lightGray,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: THEME.dark,
    marginLeft: 8,
  },
  sectionNote: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingHorizontal: 20,
    paddingVertical: 12,
    backgroundColor: '#f0f9ff',
    borderBottomWidth: 1,
    borderBottomColor: THEME.lightGray,
  },
  sectionNoteText: {
    fontSize: 13,
    color: THEME.primary,
    marginLeft: 8,
    flex: 1,
    lineHeight: 18,
  },
  settingItem: {
    borderBottomWidth: 1,
    borderBottomColor: THEME.lightGray,
  },
  settingContent: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingHorizontal: 20,
    paddingVertical: 16,
    minHeight: 60,
  },
  settingText: {
    flex: 1,
    marginRight: 16,
    paddingTop: 4,
  },
  settingLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: THEME.dark,
    marginBottom: 4,
    flexWrap: 'wrap',
  },
  settingDescription: {
    fontSize: 14,
    color: THEME.gray,
    lineHeight: 18,
    flexWrap: 'wrap',
  },
  warningContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginTop: 4,
    flexWrap: 'wrap',
  },
  warningText: {
    fontSize: 12,
    color: THEME.warning,
    marginLeft: 4,
    flex: 1,
    lineHeight: 16,
  },
  settingControl: {
    alignItems: 'flex-end',
    justifyContent: 'center',
    minHeight: 40,
    flexShrink: 0,
  },
  numberInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: THEME.lightGray,
    borderRadius: 8,
    padding: 4,
  },
  numberButton: {
    width: 32,
    height: 32,
    borderRadius: 6,
    backgroundColor: '#ffffff',
    alignItems: 'center',
    justifyContent: 'center',
  },
  numberValue: {
    fontSize: 16,
    fontWeight: '600',
    color: THEME.dark,
    marginHorizontal: 16,
    minWidth: 24,
    textAlign: 'center',
  },
  templateSelector: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'flex-end',
    alignItems: 'center',
    gap: 8,
    maxWidth: 200,
  },
  templateOption: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    backgroundColor: THEME.lightGray,
    borderWidth: 1,
    borderColor: 'transparent',
    minWidth: 80,
    alignItems: 'center',
  },
  templateOptionSelected: {
    backgroundColor: THEME.primary,
    borderColor: THEME.primary,
  },
  templateOptionText: {
    fontSize: 12,
    fontWeight: '500',
    color: THEME.gray,
    textAlign: 'center',
  },
  templateOptionTextSelected: {
    color: '#ffffff',
    fontWeight: '600',
  },
  textInput: {
    minWidth: 120,
    maxWidth: 200,
    paddingHorizontal: 12,
    paddingVertical: 8,
    backgroundColor: THEME.lightGray,
    borderRadius: 8,
    fontSize: 14,
    color: THEME.dark,
    textAlign: 'right',
    borderWidth: 1,
    borderColor: 'transparent',
  },
  multilineInput: {
    textAlign: 'left',
    minHeight: 60,
    textAlignVertical: 'top',
  },
  keywordInput: {
    minWidth: 180,
    maxWidth: 250,
    minHeight: 80,
  },
  locationSelector: {
    minWidth: 180,
    maxWidth: 250,
  },
  rangeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  rangeInput: {
    alignItems: 'center',
  },
  rangeLabel: {
    fontSize: 12,
    color: THEME.gray,
    marginBottom: 4,
    textAlign: 'center',
  },
  rangeTextInput: {
    width: 60,
    paddingHorizontal: 8,
    paddingVertical: 6,
    backgroundColor: THEME.lightGray,
    borderRadius: 6,
    fontSize: 14,
    color: THEME.dark,
    textAlign: 'center',
    borderWidth: 1,
    borderColor: 'transparent',
  },
  rangeNumberInput: {
    width: 40,
    paddingHorizontal: 4,
    paddingVertical: 4,
    fontSize: 14,
    fontWeight: '600',
    color: THEME.dark,
    textAlign: 'center',
    backgroundColor: 'transparent',
  },
  priceInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: THEME.lightGray,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    minWidth: 120,
    maxWidth: 200,
  },
  currencySymbol: {
    fontSize: 16,
    fontWeight: '600',
    color: THEME.primary,
    marginRight: 4,
  },
  priceInput: {
    flex: 1,
    fontSize: 14,
    color: THEME.dark,
    textAlign: 'right',
    paddingVertical: 0,
  },
  sizeInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: THEME.lightGray,
    borderRadius: 6,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  sizeInput: {
    width: 40,
    fontSize: 14,
    color: THEME.dark,
    textAlign: 'center',
    paddingVertical: 0,
  },
  unitLabel: {
    fontSize: 12,
    color: THEME.gray,
    marginLeft: 2,
  },
  rangeSeparator: {
    fontSize: 16,
    color: THEME.gray,
    marginHorizontal: 8,
  },
  checkboxContainer: {
    flexDirection: 'column',
    alignItems: 'flex-end',
    gap: 8,
    maxWidth: 120,
  },
  checkboxItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    minWidth: 100,
  },
  checkboxLabel: {
    fontSize: 14,
    color: THEME.dark,
    marginLeft: 6,
    textAlign: 'right',
  },
  advancedToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    marginBottom: 16,
  },
  advancedToggleText: {
    fontSize: 16,
    fontWeight: '500',
    color: THEME.primary,
    marginRight: 8,
  },
  resetContainer: {
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 10,
  },
  resetButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    backgroundColor: '#fef2f2',
    borderWidth: 1,
    borderColor: '#fecaca',
  },
  resetButtonText: {
    fontSize: 14,
    color: THEME.danger,
    marginLeft: 6,
    fontWeight: '500',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 20,
    marginBottom: 20,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
    gap: 8,
  },
  primaryButton: {
    backgroundColor: THEME.primary,
  },
  secondaryButton: {
    backgroundColor: '#ffffff',
    borderWidth: 2,
    borderColor: THEME.primary,
  },
  primaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  secondaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: THEME.primary,
  },
  bottomSpacing: {
    height: 40,
  },
});