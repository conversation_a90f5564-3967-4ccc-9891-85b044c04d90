// Debug update property functionality with detailed logging
async function debugUpdateProperty() {
  console.log('=== DEBUGGING UPDATE PROPERTY ===\n');
  
  const baseUrl = 'http://localhost:3000/api/property-owner';
  const propertyId = '507f1f77bcf86cd799439011'; // Mock property ID
  
  // Test data similar to what frontend sends
  const updateData = {
    title: 'Updated Test Property',
    description: 'Updated description for debugging',
    address: {
      street: 'Updated Street',
      houseNumber: '456',
      postalCode: '1234CD',
      city: 'Amsterdam',
      province: 'Noord-Holland'
    },
    propertyType: 'apartment',
    size: 85,
    rooms: 4,
    bedrooms: 3,
    bathrooms: 2,
    rent: {
      amount: 1600,
      currency: 'EUR',
      deposit: 3200,
      additionalCosts: {
        utilities: 175,
        serviceCharges: 75,
        parking: 0,
        other: 0
      }
    },
    features: {
      furnished: true,
      interior: 'gemeubileerd',
      parking: true,
      balcony: true,
      garden: false,
      elevator: true,
      energyLabel: 'A'
    },
    policies: {
      petsAllowed: false,
      smokingAllowed: false,
      studentsAllowed: true,
      expatFriendly: true,
      minimumIncome: 4800,
      maximumOccupants: 3
    },
    status: 'draft',
    images: [],
    availabilityDate: '2024-03-01'
  };
  
  console.log('1. Testing update property endpoint...');
  console.log(`URL: ${baseUrl}/properties/${propertyId}`);
  console.log('Method: PUT');
  console.log('Data:', JSON.stringify(updateData, null, 2));
  
  try {
    const response = await fetch(`${baseUrl}/properties/${propertyId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer mock-token-for-testing'
      },
      body: JSON.stringify(updateData)
    });
    
    console.log('\n2. Response Details:');
    console.log('Status:', response.status);
    console.log('Status Text:', response.statusText);
    console.log('Headers:', Object.fromEntries(response.headers.entries()));
    
    const responseText = await response.text();
    console.log('Raw Response Body:', responseText);
    
    let parsedResponse;
    try {
      parsedResponse = JSON.parse(responseText);
      console.log('Parsed Response:', JSON.stringify(parsedResponse, null, 2));
    } catch (parseError) {
      console.log('Failed to parse response as JSON');
    }
    
    console.log('\n3. Analysis:');
    
    if (response.status === 401) {
      console.log('❌ AUTHENTICATION ERROR');
      console.log('- The request is not authenticated');
      console.log('- Need valid JWT token');
      console.log('- This is expected with mock token');
    } else if (response.status === 403) {
      console.log('❌ AUTHORIZATION ERROR');
      console.log('- User is authenticated but not authorized');
      console.log('- Property might not belong to this user');
      console.log('- User might not be a property owner');
    } else if (response.status === 404) {
      console.log('❌ PROPERTY NOT FOUND');
      console.log('- Property with this ID does not exist');
      console.log('- Check if property ID is correct');
    } else if (response.status === 400) {
      console.log('❌ VALIDATION ERROR');
      console.log('- Request data failed validation');
      console.log('- Check required fields and data formats');
      if (parsedResponse && parsedResponse.errors) {
        console.log('- Validation errors:', parsedResponse.errors);
      }
    } else if (response.status === 500) {
      console.log('❌ SERVER ERROR');
      console.log('- Internal server error');
      console.log('- Check server logs for details');
    } else if (response.status >= 200 && response.status < 300) {
      console.log('✅ SUCCESS');
      console.log('- Update request was successful');
    } else {
      console.log(`⚠️ UNEXPECTED STATUS: ${response.status}`);
    }
    
    console.log('\n4. Common Issues and Solutions:');
    console.log('If you see this error in the frontend:');
    console.log('- Check authentication token is valid');
    console.log('- Verify property ID exists and belongs to user');
    console.log('- Check all required fields are provided');
    console.log('- Ensure user has property owner role');
    
  } catch (error) {
    console.log('\n❌ NETWORK ERROR:', error.message);
    console.log('This suggests a connection problem between frontend and backend');
    
    if (error.code === 'ECONNREFUSED') {
      console.log('💡 Backend server is not running');
    } else if (error.code === 'ENOTFOUND') {
      console.log('💡 DNS resolution failed - check URL');
    } else if (error.code === 'ETIMEDOUT') {
      console.log('💡 Request timed out');
    }
  }
  
  console.log('\n=== DEBUGGING COMPLETE ===');
}

debugUpdateProperty().catch(console.error);