/**
 * Debug Script: Check Applications Collection Directly
 * 
 * This script helps debug why applications aren't showing up by:
 * 1. Checking if applications exist in the database
 * 2. Verifying the property ownership is correct
 * 3. Testing the query logic
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3000/api';

class ApplicationsDebugTest {
  constructor() {
    this.token = null;
    this.userId = null;
  }

  async apiRequest(method, endpoint, data = null) {
    try {
      const config = {
        method,
        url: `${API_BASE_URL}${endpoint}`,
        headers: {
          'Content-Type': 'application/json',
          ...(this.token && { 'Authorization': `Bearer ${this.token}` })
        },
        ...(data && { data })
      };

      const response = await axios(config);
      return response.data;
    } catch (error) {
      console.error(`❌ ${method.toUpperCase()} ${endpoint} failed:`, error.response?.data?.message || error.message);
      if (error.response?.data) {
        console.error('   Response data:', JSON.stringify(error.response.data, null, 2));
      }
      throw error;
    }
  }

  async login() {
    console.log('🔐 Logging in as property owner...');
    const response = await this.apiRequest('POST', '/auth/login', {
      email: '<EMAIL>',
      password: 'TestPassword123!'
    });
    this.token = response.token || response.data?.token;
    this.userId = response.user?.id || response.data?.user?.id;
    console.log('✅ Logged in successfully');
    console.log(`👤 User ID: ${this.userId}`);
  }

  async checkPropertyOwnership() {
    console.log('\n🏠 === VERIFYING PROPERTY OWNERSHIP ===');
    
    try {
      const response = await this.apiRequest('GET', '/property-owner/properties');
      const properties = response.data || [];
      
      console.log(`🏠 Properties owned by user: ${properties.length}`);
      
      const targetPropertyId = '68a9b3262e90c2ad7de52303';
      const targetProperty = properties.find(prop => prop._id === targetPropertyId);
      
      if (targetProperty) {
        console.log('✅ TARGET PROPERTY IS NOW OWNED BY USER!');
        console.log(`📝 Title: ${targetProperty.title}`);
        console.log(`📍 Address: ${targetProperty.address?.street} ${targetProperty.address?.houseNumber}, ${targetProperty.address?.city}`);
        console.log(`👤 Owner ID: ${targetProperty.owner?.userId}`);
        console.log(`📊 Status: ${targetProperty.status}`);
        return true;
      } else {
        console.log('❌ Target property not found in owned properties');
        console.log('🔍 Available properties:');
        properties.forEach((prop, index) => {
          console.log(`   ${index + 1}. ${prop._id} - ${prop.title}`);
        });
        return false;
      }
    } catch (error) {
      console.log('❌ Failed to check property ownership:', error.message);
      return false;
    }
  }

  async createDebugEndpoint() {
    console.log('\n🔧 === CREATING DEBUG ENDPOINT ===');
    
    console.log('💡 We need to create a debug endpoint to check applications directly');
    console.log('💡 This would help us see:');
    console.log('   1. All applications in the database');
    console.log('   2. Their property IDs');
    console.log('   3. The exact query being executed');
    
    // Let's try to get some debug info through existing endpoints
    console.log('\n📊 Checking applications with different parameters...');
    
    try {
      // Try with different status filters
      const statuses = ['submitted', 'pending', 'under_review', 'approved', 'rejected'];
      
      for (const status of statuses) {
        console.log(`\n🔍 Checking applications with status: ${status}`);
        const response = await this.apiRequest('GET', `/property-owner/applications?status=${status}`);
        const apps = response.data || [];
        console.log(`   Found: ${apps.length} applications`);
        
        if (apps.length > 0) {
          apps.forEach(app => {
            console.log(`   - ${app.applicantName} (${app.applicantEmail}) - ${app.propertyId}`);
          });
        }
      }
    } catch (error) {
      console.log('❌ Failed to check applications with status filters');
    }
  }

  async testDirectQuery() {
    console.log('\n🗄️  === TESTING DIRECT QUERY LOGIC ===');
    
    console.log('💡 The backend query should be:');
    console.log('1. Find all properties owned by user:', this.userId);
    console.log('2. Get property IDs from those properties');
    console.log('3. Find applications where property.propertyId is in those IDs');
    
    console.log('\n🔍 Expected application details:');
    console.log('   - Application ID: 68a9d0b27cc6c69855b7b593');
    console.log('   - Applicant Email: <EMAIL>');
    console.log('   - Property ID: 68a9b3262e90c2ad7de52303');
    console.log('   - Status: submitted');
    
    console.log('\n💡 If the application still doesn\'t show up, possible issues:');
    console.log('   1. Application.property.propertyId doesn\'t match Property._id');
    console.log('   2. Application status is not being handled correctly');
    console.log('   3. Query logic has a bug');
    console.log('   4. Database connection issue');
  }

  async suggestSolutions() {
    console.log('\n💡 === SUGGESTED SOLUTIONS ===');
    
    console.log('🔧 Option 1: Check Application Document Structure');
    console.log('   - Open MongoDB Compass');
    console.log('   - Go to applications collection');
    console.log('   - Find application: 68a9d0b27cc6c69855b7b593');
    console.log('   - Verify property.propertyId matches: 68a9b3262e90c2ad7de52303');
    
    console.log('\n🔧 Option 2: Add Debug Logging to Backend');
    console.log('   - Add console.log to getAllApplications method');
    console.log('   - Log the propertyIds array');
    console.log('   - Log the query object');
    console.log('   - Log the raw applications from database');
    
    console.log('\n🔧 Option 3: Create Test Application');
    console.log('   - Use the tenant mobile app');
    console.log('   - Apply to the property we just linked');
    console.log('   - This will create a fresh application with correct structure');
    
    console.log('\n🔧 Option 4: Manual Database Fix');
    console.log('   - If application.property.propertyId is wrong, update it:');
    console.log('   db.applications.updateOne(');
    console.log('     { _id: ObjectId("68a9d0b27cc6c69855b7b593") },');
    console.log('     { $set: { "property.propertyId": ObjectId("68a9b3262e90c2ad7de52303") } }');
    console.log('   )');
  }

  async runDebug() {
    console.log('🔍 === APPLICATIONS DEBUG TEST ===');
    console.log(`📅 Started at: ${new Date().toISOString()}`);
    
    try {
      // Step 1: Login
      await this.login();
      
      // Step 2: Verify property ownership is fixed
      const ownsProperty = await this.checkPropertyOwnership();
      
      // Step 3: Try debug approaches
      await this.createDebugEndpoint();
      
      // Step 4: Analyze query logic
      await this.testDirectQuery();
      
      // Step 5: Suggest solutions
      await this.suggestSolutions();
      
      // Summary
      console.log('\n🎉 === DEBUG SUMMARY ===');
      console.log(`✅ Login: Success`);
      console.log(`${ownsProperty ? '✅' : '❌'} Property Ownership: ${ownsProperty ? 'Fixed' : 'Still Issue'}`);
      
      if (ownsProperty) {
        console.log('\n✅ Property ownership is correct!');
        console.log('🔍 The issue is likely in the application query or data structure');
        console.log('📝 Follow the suggested solutions above to debug further');
      } else {
        console.log('\n❌ Property ownership is still not correct');
        console.log('🔄 The MongoDB update may not have worked properly');
      }
      
    } catch (error) {
      console.error('\n💥 DEBUG FAILED:', error.message);
    }
    
    console.log(`\n📅 Completed at: ${new Date().toISOString()}`);
  }
}

// Run the debug
if (require.main === module) {
  const test = new ApplicationsDebugTest();
  test.runDebug().catch(console.error);
}

module.exports = ApplicationsDebugTest;