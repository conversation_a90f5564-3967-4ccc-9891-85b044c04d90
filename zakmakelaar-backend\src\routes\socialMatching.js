const express = require('express');
const router = express.Router();
const socialMatchingService = require('../services/socialMatchingService');
const { auth } = require('../middleware/auth');
const { body, param, query, validationResult } = require('express-validator');
const { loggers } = require('../services/logger');

// Validation middleware
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      status: 'error',
      message: 'Validation failed',
      errors: errors.array()
    });
  }
  next();
};

/**
 * GET /api/social-matching/potential-roommates
 * Find potential roommates for the authenticated user
 * Requirement 9.1, 9.2, 9.3
 */
router.get('/potential-roommates', 
  auth,
  [
    query('userType').optional().isIn(['student', 'expat', 'young_professional', 'property_owner']),
    query('minAge').optional().isInt({ min: 18, max: 100 }),
    query('maxAge').optional().isInt({ min: 18, max: 100 }),
    query('location').optional().isString().trim(),
    query('minCompatibility').optional().isInt({ min: 0, max: 100 }),
    query('limit').optional().isInt({ min: 1, max: 50 }),
    query('page').optional().isInt({ min: 1 })
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const userId = req.user._id;
      const filters = {};

      // Build filters from query parameters
      if (req.query.userType) {
        filters.userType = req.query.userType;
      }

      if (req.query.minAge && req.query.maxAge) {
        if (parseInt(req.query.minAge) > parseInt(req.query.maxAge)) {
          return res.status(400).json({
            status: 'error',
            message: 'Minimum age cannot be greater than maximum age'
          });
        }
        filters.ageRange = {
          min: parseInt(req.query.minAge),
          max: parseInt(req.query.maxAge)
        };
      }

      if (req.query.location) {
        filters.location = req.query.location;
      }

      if (req.query.minCompatibility) {
        filters.minCompatibility = parseInt(req.query.minCompatibility);
      }

      if (req.query.limit) {
        filters.limit = parseInt(req.query.limit);
      }

      const potentialMatches = await socialMatchingService.findPotentialRoommates(userId, filters);

      res.json({
        status: 'success',
        data: {
          potentialMatches,
          count: potentialMatches.length
        }
      });

    } catch (error) {
      loggers.app.error('Error finding potential roommates:', error);
      res.status(500).json({
        status: 'error',
        message: error.message || 'Failed to find potential roommates'
      });
    }
  }
);

/**
 * POST /api/social-matching/matches
 * Create a match with another user
 * Requirement 9.3, 9.4
 */
router.post('/matches',
  auth,
  [
    body('targetUserId').isMongoId().withMessage('Valid target user ID is required')
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const initiatorId = req.user._id;
      const { targetUserId } = req.body;

      if (initiatorId.toString() === targetUserId) {
        return res.status(400).json({
          status: 'error',
          message: 'Cannot create match with yourself'
        });
      }

      const match = await socialMatchingService.createMatch(initiatorId, targetUserId);

      res.status(201).json({
        status: 'success',
        data: { match },
        message: 'Match created successfully'
      });

    } catch (error) {
      loggers.app.error('Error creating match:', error);
      res.status(400).json({
        status: 'error',
        message: error.message || 'Failed to create match'
      });
    }
  }
);

/**
 * PUT /api/social-matching/matches/:matchId/respond
 * Respond to a match (accept/reject)
 * Requirement 9.4
 */
router.put('/matches/:matchId/respond',
  auth,
  [
    param('matchId').isMongoId().withMessage('Valid match ID is required'),
    body('response').isIn(['accept', 'reject']).withMessage('Response must be accept or reject')
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const userId = req.user._id;
      const { matchId } = req.params;
      const { response } = req.body;

      const match = await socialMatchingService.respondToMatch(matchId, userId, response);

      res.json({
        status: 'success',
        data: { match },
        message: `Match ${response}ed successfully`
      });

    } catch (error) {
      loggers.app.error('Error responding to match:', error);
      res.status(400).json({
        status: 'error',
        message: error.message || 'Failed to respond to match'
      });
    }
  }
);

/**
 * GET /api/social-matching/matches
 * Get matches for the authenticated user
 * Requirement 9.3
 */
router.get('/matches',
  auth,
  [
    query('status').optional().isIn(['pending', 'accepted', 'rejected', 'blocked']),
    query('page').optional().isInt({ min: 1 }),
    query('limit').optional().isInt({ min: 1, max: 50 })
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const userId = req.user._id;
      const status = req.query.status || null;
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 20;

      const result = await socialMatchingService.getMatches(userId, status, page, limit);

      res.json({
        status: 'success',
        data: result
      });

    } catch (error) {
      loggers.app.error('Error getting matches:', error);
      res.status(500).json({
        status: 'error',
        message: error.message || 'Failed to get matches'
      });
    }
  }
);

/**
 * PUT /api/social-matching/preferences
 * Update social preferences
 * Requirement 9.1, 9.2
 */
router.put('/preferences',
  auth,
  [
    body('lookingForRoommate').optional().isBoolean(),
    body('isVisible').optional().isBoolean(),
    body('roommateCriteria.ageRange.min').optional().isInt({ min: 18, max: 100 }),
    body('roommateCriteria.ageRange.max').optional().isInt({ min: 18, max: 100 }),
    body('roommateCriteria.gender').optional().isIn(['male', 'female', 'any']),
    body('roommateCriteria.occupation').optional().isArray(),
    body('roommateCriteria.lifestyle.cleanliness').optional().isIn(['very_clean', 'clean', 'moderate', 'relaxed']),
    body('roommateCriteria.lifestyle.noiseLevel').optional().isIn(['very_quiet', 'quiet', 'moderate', 'lively']),
    body('roommateCriteria.lifestyle.socialLevel').optional().isIn(['very_social', 'social', 'moderate', 'private']),
    body('roommateCriteria.lifestyle.smokingTolerance').optional().isBoolean(),
    body('roommateCriteria.lifestyle.petTolerance').optional().isBoolean(),
    body('roommateCriteria.lifestyle.guestPolicy').optional().isIn(['strict', 'moderate', 'flexible'])
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const userId = req.user._id;
      const preferences = req.body;

      const updatedPreferences = await socialMatchingService.updateSocialPreferences(userId, preferences);

      res.json({
        status: 'success',
        data: { preferences: updatedPreferences },
        message: 'Social preferences updated successfully'
      });

    } catch (error) {
      loggers.app.error('Error updating social preferences:', error);
      res.status(400).json({
        status: 'error',
        message: error.message || 'Failed to update social preferences'
      });
    }
  }
);

/**
 * PUT /api/social-matching/visibility
 * Toggle visibility for social matching
 * Requirement 9.2
 */
router.put('/visibility',
  auth,
  [
    body('isVisible').isBoolean().withMessage('isVisible must be a boolean')
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const userId = req.user._id;
      const { isVisible } = req.body;

      const preferences = await socialMatchingService.toggleVisibility(userId, isVisible);

      res.json({
        status: 'success',
        data: { preferences },
        message: `Visibility ${isVisible ? 'enabled' : 'disabled'} successfully`
      });

    } catch (error) {
      loggers.app.error('Error toggling visibility:', error);
      res.status(400).json({
        status: 'error',
        message: error.message || 'Failed to toggle visibility'
      });
    }
  }
);

/**
 * POST /api/social-matching/report
 * Report a user for inappropriate behavior
 * Requirement 9.5, 9.6
 */
router.post('/report',
  auth,
  [
    body('reportedUserId').isMongoId().withMessage('Valid reported user ID is required'),
    body('reason').isIn(['inappropriate_behavior', 'fake_profile', 'harassment', 'spam', 'other']).withMessage('Valid reason is required'),
    body('description').optional().isString().trim().isLength({ max: 500 })
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const reporterId = req.user._id;
      const { reportedUserId, reason, description } = req.body;

      if (reporterId.toString() === reportedUserId) {
        return res.status(400).json({
          status: 'error',
          message: 'Cannot report yourself'
        });
      }

      const result = await socialMatchingService.reportUser(reporterId, reportedUserId, reason, description);

      res.json({
        status: 'success',
        data: result,
        message: 'User reported successfully'
      });

    } catch (error) {
      loggers.app.error('Error reporting user:', error);
      res.status(400).json({
        status: 'error',
        message: error.message || 'Failed to report user'
      });
    }
  }
);

/**
 * POST /api/social-matching/block
 * Block a user
 * Requirement 9.5, 9.6
 */
router.post('/block',
  auth,
  [
    body('blockedUserId').isMongoId().withMessage('Valid blocked user ID is required')
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const userId = req.user._id;
      const { blockedUserId } = req.body;

      if (userId.toString() === blockedUserId) {
        return res.status(400).json({
          status: 'error',
          message: 'Cannot block yourself'
        });
      }

      const result = await socialMatchingService.blockUser(userId, blockedUserId);

      res.json({
        status: 'success',
        data: result,
        message: 'User blocked successfully'
      });

    } catch (error) {
      loggers.app.error('Error blocking user:', error);
      res.status(400).json({
        status: 'error',
        message: error.message || 'Failed to block user'
      });
    }
  }
);

/**
 * POST /api/social-matching/messages
 * Send a message to a matched user
 * Requirement 9.5
 */
router.post('/messages',
  auth,
  [
    body('matchId').isMongoId().withMessage('Valid match ID is required'),
    body('content').isString().trim().isLength({ min: 1, max: 1000 }).withMessage('Message content must be between 1 and 1000 characters')
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const senderId = req.user._id;
      const { matchId, content } = req.body;

      const message = await socialMatchingService.sendMessage(matchId, senderId, content);

      res.status(201).json({
        status: 'success',
        data: { message },
        message: 'Message sent successfully'
      });

    } catch (error) {
      loggers.app.error('Error sending message:', error);
      res.status(400).json({
        status: 'error',
        message: error.message || 'Failed to send message'
      });
    }
  }
);

/**
 * GET /api/social-matching/conversations/:matchId
 * Get conversation messages for a match
 * Requirement 9.5
 */
router.get('/conversations/:matchId',
  auth,
  [
    param('matchId').isMongoId().withMessage('Valid match ID is required'),
    query('page').optional().isInt({ min: 1 }),
    query('limit').optional().isInt({ min: 1, max: 100 })
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const userId = req.user._id;
      const { matchId } = req.params;
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 50;

      const conversation = await socialMatchingService.getConversation(matchId, userId, page, limit);

      res.json({
        status: 'success',
        data: conversation
      });

    } catch (error) {
      loggers.app.error('Error getting conversation:', error);
      res.status(400).json({
        status: 'error',
        message: error.message || 'Failed to get conversation'
      });
    }
  }
);

/**
 * GET /api/social-matching/conversations
 * Get all conversations for the authenticated user
 * Requirement 9.5
 */
router.get('/conversations',
  auth,
  [
    query('page').optional().isInt({ min: 1 }),
    query('limit').optional().isInt({ min: 1, max: 50 })
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const userId = req.user._id;
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 20;

      const conversations = await socialMatchingService.getUserConversations(userId, page, limit);

      res.json({
        status: 'success',
        data: conversations
      });

    } catch (error) {
      loggers.app.error('Error getting conversations:', error);
      res.status(500).json({
        status: 'error',
        message: error.message || 'Failed to get conversations'
      });
    }
  }
);

/**
 * GET /api/social-matching/messages/unread-count
 * Get unread message count for the authenticated user
 * Requirement 9.5
 */
router.get('/messages/unread-count',
  auth,
  async (req, res) => {
    try {
      const userId = req.user._id;
      const result = await socialMatchingService.getUnreadMessageCount(userId);

      res.json({
        status: 'success',
        data: result
      });

    } catch (error) {
      loggers.app.error('Error getting unread message count:', error);
      res.status(500).json({
        status: 'error',
        message: error.message || 'Failed to get unread message count'
      });
    }
  }
);

/**
 * DELETE /api/social-matching/messages/:messageId
 * Delete a message
 * Requirement 9.5
 */
router.delete('/messages/:messageId',
  auth,
  [
    param('messageId').isMongoId().withMessage('Valid message ID is required')
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const userId = req.user._id;
      const { messageId } = req.params;

      const result = await socialMatchingService.deleteMessage(messageId, userId);

      res.json({
        status: 'success',
        data: result,
        message: 'Message deleted successfully'
      });

    } catch (error) {
      loggers.app.error('Error deleting message:', error);
      res.status(400).json({
        status: 'error',
        message: error.message || 'Failed to delete message'
      });
    }
  }
);

module.exports = router;