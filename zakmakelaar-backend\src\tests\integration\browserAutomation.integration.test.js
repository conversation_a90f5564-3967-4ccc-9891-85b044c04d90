const FormAutomationEngine = require('../../services/formAutomationEngine');
const { browserPool, setupPageStealth } = require('../../services/scraperUtils');
const path = require('path');
const fs = require('fs');

// Mock dependencies for integration tests
jest.mock('../../services/logger', () => ({
  loggers: {
    formAutomation: {
      info: jest.fn(),
      warn: jest.fn(),
      error: jest.fn()
    }
  }
}));

describe('Browser Automation Integration Tests', () => {
  let engine;
  let browser;
  let testServer;
  
  // Test HTML templates for different form types
  const testForms = {
    singleStep: `
      <!DOCTYPE html>
      <html>
      <head><title>Single Step Form</title></head>
      <body>
        <form id="application-form" action="/submit" method="POST">
          <input type="text" name="firstName" placeholder="First Name" required />
          <input type="text" name="lastName" placeholder="Last Name" required />
          <input type="email" name="email" placeholder="Email" required />
          <input type="tel" name="phone" placeholder="Phone" />
          <textarea name="message" placeholder="Application Letter"></textarea>
          <input type="file" name="documents" multiple />
          <button type="submit">Submit Application</button>
        </form>
      </body>
      </html>
    `,
    
    multiStep: `
      <!DOCTYPE html>
      <html>
      <head><title>Multi Step Form</title></head>
      <body>
        <div class="step-indicator">Step 1 of 3</div>
        <form id="step-1-form" action="/step2" method="POST">
          <h2>Personal Information</h2>
          <input type="text" name="firstName" placeholder="First Name" required />
          <input type="text" name="lastName" placeholder="Last Name" required />
          <input type="email" name="email" placeholder="Email" required />
          <button type="button" class="next-button">Next</button>
        </form>
        
        <form id="step-2-form" action="/step3" method="POST" style="display:none;">
          <h2>Contact Information</h2>
          <input type="tel" name="phone" placeholder="Phone" required />
          <input type="date" name="moveInDate" placeholder="Move-in Date" />
          <button type="button" class="next-button">Next</button>
        </form>
        
        <form id="step-3-form" action="/submit" method="POST" style="display:none;">
          <h2>Application Details</h2>
          <textarea name="message" placeholder="Application Letter" required></textarea>
          <input type="file" name="documents" multiple />
          <button type="submit">Submit Application</button>
        </form>
        
        <script>
          let currentStep = 1;
          document.querySelectorAll('.next-button').forEach(btn => {
            btn.addEventListener('click', () => {
              document.getElementById('step-' + currentStep + '-form').style.display = 'none';
              currentStep++;
              document.getElementById('step-' + currentStep + '-form').style.display = 'block';
              document.querySelector('.step-indicator').textContent = 'Step ' + currentStep + ' of 3';
            });
          });
        </script>
      </body>
      </html>
    `,
    
    external: `
      <!DOCTYPE html>
      <html>
      <head><title>External Form</title></head>
      <body>
        <form action="https://external-site.com/apply" method="POST" class="external-form">
          <input type="text" name="applicantName" placeholder="Full Name" required />
          <input type="email" name="applicantEmail" placeholder="Email Address" required />
          <select name="propertyType" required>
            <option value="">Select Property Type</option>
            <option value="apartment">Apartment</option>
            <option value="house">House</option>
            <option value="studio">Studio</option>
          </select>
          <input type="number" name="monthlyIncome" placeholder="Monthly Income" required />
          <textarea name="applicationLetter" placeholder="Why do you want this property?" required></textarea>
          <input type="file" name="idDocument" accept=".pdf,.jpg,.png" />
          <input type="file" name="incomeProof" accept=".pdf" />
          <button type="submit">Apply Now</button>
        </form>
      </body>
      </html>
    `,
    
    withErrors: `
      <!DOCTYPE html>
      <html>
      <head><title>Form with Validation</title></head>
      <body>
        <form id="validation-form" action="/submit" method="POST">
          <input type="text" name="firstName" placeholder="First Name" required />
          <input type="email" name="email" placeholder="Email" required />
          <div class="error" style="display:none; color:red;">Please fill all required fields</div>
          <button type="submit">Submit</button>
        </form>
        
        <script>
          document.getElementById('validation-form').addEventListener('submit', (e) => {
            const firstName = document.querySelector('[name="firstName"]').value;
            const email = document.querySelector('[name="email"]').value;
            
            if (!firstName || !email || !email.includes('@')) {
              e.preventDefault();
              document.querySelector('.error').style.display = 'block';
              document.querySelector('.error').textContent = 'Please fill all required fields correctly';
            }
          });
        </script>
      </body>
      </html>
    `
  };

  beforeAll(async () => {
    // Set up test server to serve test forms
    const express = require('express');
    const app = express();
    
    app.use(express.static('public'));
    app.use(express.urlencoded({ extended: true }));
    
    // Serve different test forms
    Object.keys(testForms).forEach(formType => {
      app.get(`/test-${formType}`, (req, res) => {
        res.send(testForms[formType]);
      });
    });
    
    // Handle form submissions
    app.post('/submit', (req, res) => {
      res.send(`
        <html>
          <body>
            <h1>Application Submitted Successfully!</h1>
            <p>Confirmation Number: APP-${Date.now()}</p>
            <p>Thank you for your application.</p>
          </body>
        </html>
      `);
    });
    
    app.post('/step2', (req, res) => {
      res.redirect('/test-multiStep#step2');
    });
    
    app.post('/step3', (req, res) => {
      res.redirect('/test-multiStep#step3');
    });
    
    testServer = app.listen(3001);
    
    // Initialize engine
    engine = new FormAutomationEngine({
      timeout: 10000,
      screenshotOnError: true,
      delayMin: 100,
      delayMax: 300
    });
    
    // Get browser from pool
    browser = await browserPool.getBrowser();
  });

  afterAll(async () => {
    if (testServer) {
      testServer.close();
    }
    if (browser) {
      await browser.close();
    }
  });

  beforeEach(() => {
    // Create screenshots directory if it doesn't exist
    if (!fs.existsSync('screenshots')) {
      fs.mkdirSync('screenshots', { recursive: true });
    }
  });

  describe('Form Detection Integration', () => {
    it('should detect single step form correctly', async () => {
      const page = await browser.newPage();
      await setupPageStealth(page);
      
      try {
        await page.goto('http://localhost:3001/test-singleStep', { waitUntil: 'networkidle0' });
        
        const formInfo = await engine.detectFormType(page);
        
        expect(formInfo.type).toBe('native');
        expect(formInfo.confidence).toBeGreaterThan(0);
        expect(formInfo.forms).toHaveLength(1);
        expect(formInfo.forms[0].fieldCount).toBeGreaterThan(0);
        
      } finally {
        await page.close();
      }
    });

    it('should detect multi-step form correctly', async () => {
      const page = await browser.newPage();
      await setupPageStealth(page);
      
      try {
        await page.goto('http://localhost:3001/test-multiStep', { waitUntil: 'networkidle0' });
        
        const formInfo = await engine.detectFormType(page);
        const stepInfo = await engine.detectSteps(page);
        
        expect(formInfo.forms.length).toBeGreaterThan(0);
        expect(stepInfo.totalSteps).toBe(3);
        
      } finally {
        await page.close();
      }
    });

    it('should detect external form correctly', async () => {
      const page = await browser.newPage();
      await setupPageStealth(page);
      
      try {
        await page.goto('http://localhost:3001/test-external', { waitUntil: 'networkidle0' });
        
        const formInfo = await engine.detectFormType(page);
        
        expect(formInfo.type).toBe('external');
        expect(formInfo.forms).toHaveLength(1);
        
      } finally {
        await page.close();
      }
    });
  });

  describe('Field Analysis Integration', () => {
    it('should analyze form fields and create proper mappings', async () => {
      const page = await browser.newPage();
      await setupPageStealth(page);
      
      try {
        await page.goto('http://localhost:3001/test-singleStep', { waitUntil: 'networkidle0' });
        
        const fieldAnalysis = await engine.analyzeFormFields(page);
        
        expect(fieldAnalysis.formType).toBeDefined();
        expect(fieldAnalysis.mappedFields).toBeDefined();
        expect(fieldAnalysis.mappedFields.personal).toBeDefined();
        expect(fieldAnalysis.requiredFields.length).toBeGreaterThan(0);
        
        // Check that required fields are properly identified
        const requiredFieldNames = fieldAnalysis.requiredFields.map(f => f.name);
        expect(requiredFieldNames).toContain('firstName');
        expect(requiredFieldNames).toContain('lastName');
        expect(requiredFieldNames).toContain('email');
        
      } finally {
        await page.close();
      }
    });

    it('should handle complex external form field mapping', async () => {
      const page = await browser.newPage();
      await setupPageStealth(page);
      
      try {
        await page.goto('http://localhost:3001/test-external', { waitUntil: 'networkidle0' });
        
        const fieldAnalysis = await engine.analyzeFormFields(page);
        
        expect(fieldAnalysis.mappedFields.personal).toBeDefined();
        expect(fieldAnalysis.mappedFields.financial).toBeDefined();
        expect(fieldAnalysis.mappedFields.documents).toBeDefined();
        
        // Check for select field handling
        const selectFields = fieldAnalysis.mappedFields.housing || fieldAnalysis.mappedFields.personal;
        expect(selectFields).toBeDefined();
        
      } finally {
        await page.close();
      }
    });
  });

  describe('Form Filling Integration', () => {
    const testApplicationData = {
      personalInfo: {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+31612345678',
        monthlyIncome: 3500,
        moveInDate: new Date('2024-07-01')
      },
      generatedContent: {
        message: 'I am very interested in this property and would like to apply. I am a reliable tenant with stable income.'
      },
      documents: [
        {
          type: 'ID',
          path: path.join(__dirname, '../fixtures/test-document.pdf')
        }
      ]
    };

    beforeEach(() => {
      // Create test document if it doesn't exist
      const testDocPath = path.join(__dirname, '../fixtures/test-document.pdf');
      if (!fs.existsSync(path.dirname(testDocPath))) {
        fs.mkdirSync(path.dirname(testDocPath), { recursive: true });
      }
      if (!fs.existsSync(testDocPath)) {
        fs.writeFileSync(testDocPath, 'Mock PDF content for testing');
      }
    });

    it('should fill single step form completely', async () => {
      const page = await browser.newPage();
      await setupPageStealth(page);
      
      try {
        await page.goto('http://localhost:3001/test-singleStep', { waitUntil: 'networkidle0' });
        
        const fieldAnalysis = await engine.analyzeFormFields(page);
        const fillResult = await engine.fillApplicationForm(page, testApplicationData, fieldAnalysis);
        
        expect(fillResult.success).toBe(true);
        expect(fillResult.filledFields.length).toBeGreaterThan(0);
        expect(fillResult.errors).toHaveLength(0);
        
        // Verify fields were actually filled
        const firstName = await page.$eval('[name="firstName"]', el => el.value);
        const email = await page.$eval('[name="email"]', el => el.value);
        const message = await page.$eval('[name="message"]', el => el.value);
        
        expect(firstName).toBe('John');
        expect(email).toBe('<EMAIL>');
        expect(message).toContain('interested in this property');
        
      } finally {
        await page.close();
      }
    });

    it('should handle multi-step form filling', async () => {
      const page = await browser.newPage();
      await setupPageStealth(page);
      
      try {
        await page.goto('http://localhost:3001/test-multiStep', { waitUntil: 'networkidle0' });
        
        const processResult = await engine.handleMultiStepProcess(page, testApplicationData, {
          autoSubmit: false
        });
        
        expect(processResult.success).toBe(true);
        expect(processResult.totalSteps).toBe(3);
        expect(processResult.completedSteps.length).toBe(3);
        expect(processResult.errors).toHaveLength(0);
        
      } finally {
        await page.close();
      }
    });

    it('should handle form validation errors gracefully', async () => {
      const page = await browser.newPage();
      await setupPageStealth(page);
      
      try {
        await page.goto('http://localhost:3001/test-withErrors', { waitUntil: 'networkidle0' });
        
        // Try to submit empty form to trigger validation
        const submitButton = await page.$('button[type="submit"]');
        await submitButton.click();
        
        await page.waitForTimeout(1000);
        
        // Check if error handling works
        const errors = await engine.extractErrorMessages(page);
        expect(errors.length).toBeGreaterThan(0);
        
        // Try error recovery
        const recoveryResult = await engine.handleFormErrors(page, errors);
        expect(recoveryResult.actions.length).toBeGreaterThan(0);
        
      } finally {
        await page.close();
      }
    });
  });

  describe('Document Upload Integration', () => {
    it('should upload documents successfully', async () => {
      const page = await browser.newPage();
      await setupPageStealth(page);
      
      try {
        await page.goto('http://localhost:3001/test-singleStep', { waitUntil: 'networkidle0' });
        
        const testDocuments = [
          {
            type: 'ID',
            path: path.join(__dirname, '../fixtures/test-document.pdf')
          }
        ];
        
        const uploadResult = await engine.uploadDocuments(page, testDocuments);
        
        expect(uploadResult.success).toBe(true);
        expect(uploadResult.uploadedDocuments).toHaveLength(1);
        expect(uploadResult.errors).toHaveLength(0);
        
      } finally {
        await page.close();
      }
    });

    it('should handle missing documents gracefully', async () => {
      const page = await browser.newPage();
      await setupPageStealth(page);
      
      try {
        await page.goto('http://localhost:3001/test-singleStep', { waitUntil: 'networkidle0' });
        
        const testDocuments = [
          {
            type: 'ID',
            path: '/nonexistent/path/document.pdf'
          }
        ];
        
        const uploadResult = await engine.uploadDocuments(page, testDocuments);
        
        expect(uploadResult.success).toBe(false);
        expect(uploadResult.errors.length).toBeGreaterThan(0);
        expect(uploadResult.uploadedDocuments).toHaveLength(0);
        
      } finally {
        await page.close();
      }
    });
  });

  describe('Form Submission Integration', () => {
    it('should submit form and extract confirmation data', async () => {
      const page = await browser.newPage();
      await setupPageStealth(page);
      
      try {
        await page.goto('http://localhost:3001/test-singleStep', { waitUntil: 'networkidle0' });
        
        // Fill form first
        const testData = {
          personalInfo: {
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>'
          },
          generatedContent: {
            message: 'Test application message'
          }
        };
        
        await engine.fillApplicationForm(page, testData);
        
        // Submit form
        const submitResult = await engine.submitForm(page);
        
        expect(submitResult.success).toBe(true);
        expect(submitResult.confirmationData).toBeDefined();
        expect(submitResult.confirmationData.message).toContain('Successfully');
        expect(submitResult.confirmationData.confirmationNumber).toMatch(/APP-\d+/);
        
      } finally {
        await page.close();
      }
    });

    it('should handle submission errors', async () => {
      const page = await browser.newPage();
      await setupPageStealth(page);
      
      try {
        await page.goto('http://localhost:3001/test-withErrors', { waitUntil: 'networkidle0' });
        
        // Try to submit without filling required fields
        const submitResult = await engine.submitForm(page, { validateBeforeSubmit: true });
        
        expect(submitResult.success).toBe(false);
        expect(submitResult.errors.length).toBeGreaterThan(0);
        
      } finally {
        await page.close();
      }
    });
  });

  describe('Complete Browser Automation Workflow', () => {
    it('should complete full automation workflow for single step form', async () => {
      const testApplicationData = {
        personalInfo: {
          firstName: 'Jane',
          lastName: 'Smith',
          email: '<EMAIL>',
          phone: '+31687654321'
        },
        generatedContent: {
          message: 'I am interested in applying for this rental property. I have stable employment and excellent references.'
        },
        documents: [
          {
            type: 'ID',
            path: path.join(__dirname, '../fixtures/test-document.pdf')
          }
        ]
      };

      const result = await engine.automateFormFilling(
        'http://localhost:3001/test-singleStep',
        testApplicationData,
        { autoSubmit: true }
      );

      expect(result.success).toBe(true);
      expect(result.formType).toBe('native');
      expect(result.processResult).toBeDefined();
      expect(result.processResult.success).toBe(true);
      expect(result.screenshots.length).toBeGreaterThan(0);
      expect(result.metrics.duration).toBeGreaterThan(0);
      expect(result.metrics.fieldsFilledCount).toBeGreaterThan(0);
    });

    it('should complete full automation workflow for multi-step form', async () => {
      const testApplicationData = {
        personalInfo: {
          firstName: 'Bob',
          lastName: 'Johnson',
          email: '<EMAIL>',
          phone: '+31612345678',
          moveInDate: new Date('2024-08-01')
        },
        generatedContent: {
          message: 'I would like to rent this property for my family. We are looking for a long-term rental.'
        }
      };

      const result = await engine.automateFormFilling(
        'http://localhost:3001/test-multiStep',
        testApplicationData,
        { autoSubmit: false }
      );

      expect(result.success).toBe(true);
      expect(result.processResult.totalSteps).toBe(3);
      expect(result.processResult.completedSteps.length).toBe(3);
      expect(result.metrics.stepsCompleted).toBe(3);
    });

    it('should handle automation failures gracefully', async () => {
      const testApplicationData = {
        personalInfo: {
          firstName: 'Test',
          lastName: 'User'
        }
      };

      await expect(
        engine.automateFormFilling(
          'http://localhost:3001/nonexistent-page',
          testApplicationData
        )
      ).rejects.toThrow('Browser automation failed');
    });
  });

  describe('Screenshot and Debugging Integration', () => {
    it('should capture screenshots during automation process', async () => {
      const testApplicationData = {
        personalInfo: {
          firstName: 'Screenshot',
          lastName: 'Test',
          email: '<EMAIL>'
        },
        generatedContent: {
          message: 'Testing screenshot functionality'
        }
      };

      const result = await engine.automateFormFilling(
        'http://localhost:3001/test-singleStep',
        testApplicationData,
        { autoSubmit: false }
      );

      expect(result.screenshots.length).toBeGreaterThan(0);
      
      // Verify screenshots exist
      result.screenshots.forEach(screenshotPath => {
        expect(fs.existsSync(screenshotPath)).toBe(true);
      });
    });

    it('should capture error screenshots on failures', async () => {
      const engine = new FormAutomationEngine({
        screenshotOnError: true,
        timeout: 1000 // Very short timeout to force error
      });

      try {
        await engine.automateFormFilling(
          'http://localhost:3001/test-singleStep',
          { personalInfo: {} }
        );
      } catch (error) {
        // Error is expected, check if screenshot was captured
        expect(error.message).toContain('Browser automation failed');
      }
    });
  });

  describe('Browser Pool Integration', () => {
    it('should properly integrate with browser pool', async () => {
      const page = await browser.newPage();
      const stealthPage = await engine.createStealthPage(browser);
      
      expect(stealthPage).toBeDefined();
      expect(typeof stealthPage.goto).toBe('function');
      expect(typeof stealthPage.evaluate).toBe('function');
      
      await stealthPage.close();
      await page.close();
    });

    it('should handle browser pool resource management', async () => {
      // Test multiple concurrent automations
      const testData = {
        personalInfo: {
          firstName: 'Concurrent',
          lastName: 'Test',
          email: '<EMAIL>'
        },
        generatedContent: {
          message: 'Testing concurrent automation'
        }
      };

      const promises = Array(3).fill().map((_, index) => 
        engine.automateFormFilling(
          'http://localhost:3001/test-singleStep',
          { 
            ...testData, 
            personalInfo: { 
              ...testData.personalInfo, 
              firstName: `Concurrent${index}` 
            } 
          },
          { autoSubmit: false }
        )
      );

      const results = await Promise.all(promises);
      
      results.forEach(result => {
        expect(result.success).toBe(true);
      });
    });
  });
});