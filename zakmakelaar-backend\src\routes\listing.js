const express = require("express");
const listingController = require("../controllers/listingController");
const {
  validateListingQuery,
  validateObjectId,
} = require("../middleware/validation");
const { cacheConfigs } = require("../middleware/cache");
const { performanceMonitor } = require("../utils/performanceMonitor");

const router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     QuickStatsResponse:
 *       type: object
 *       properties:
 *         status:
 *           type: string
 *           enum: [success, partial, fallback, error]
 *           description: Response status indicating data quality and error handling state
 *           example: success
 *         data:
 *           type: object
 *           properties:
 *             stats:
 *               $ref: '#/components/schemas/QuickStats'
 *         cached:
 *           type: boolean
 *           description: Whether the response was served from cache
 *           example: false
 *         timestamp:
 *           type: string
 *           format: date-time
 *           description: Response generation timestamp
 *           example: "2025-01-23T10:30:00.000Z"
 *         performance:
 *           $ref: '#/components/schemas/PerformanceMetrics'
 *         degraded:
 *           type: boolean
 *           description: Indicates if service is running in degraded mode
 *           example: false
 *         fallback:
 *           type: boolean
 *           description: Indicates if fallback data is being served
 *           example: false
 *         error:
 *           type: string
 *           description: Error message if primary calculation failed
 *           example: "Database timeout, serving cached data"
 *         warnings:
 *           type: array
 *           items:
 *             type: string
 *           description: List of warnings encountered during processing
 *           example: ["Cache set failed: Redis connection timeout"]
 *       example:
 *         status: success
 *         data:
 *           stats:
 *             totalListings: 1250
 *             averagePrice: 2850
 *             newToday: 15
 *         cached: false
 *         timestamp: "2025-01-23T10:30:00.000Z"
 *         performance:
 *           duration: "150ms"
 *           timestamp: "2025-01-23T10:30:00.000Z"
 *     
 *     QuickStats:
 *       type: object
 *       properties:
 *         totalListings:
 *           type: number
 *           description: Total number of active listings
 *           example: 1250
 *         averagePrice:
 *           type: number
 *           description: Average price of all listings in euros
 *           example: 2850
 *         newToday:
 *           type: number
 *           description: Number of listings added today
 *           example: 15
 *     
 *     PerformanceMetrics:
 *       type: object
 *       properties:
 *         duration:
 *           type: string
 *           description: Request processing time
 *           example: "150ms"
 *         timestamp:
 *           type: string
 *           format: date-time
 *           description: Performance measurement timestamp
 *     
 *     HealthStatus:
 *       type: object
 *       properties:
 *         status:
 *           type: string
 *           enum: [healthy, degraded, unhealthy]
 *           description: Overall health status
 *         healthy:
 *           type: boolean
 *           description: Boolean indicator of health status
 *         performance:
 *           type: object
 *           properties:
 *             responseTime:
 *               type: string
 *               example: "150ms"
 *             withinThreshold:
 *               type: boolean
 *             threshold:
 *               type: string
 *               example: "1000ms"
 *         data:
 *           $ref: '#/components/schemas/QuickStats'
 *         issues:
 *           type: array
 *           items:
 *             type: string
 *           description: List of issues detected
 *         timestamp:
 *           type: string
 *           format: date-time
 *     
 *     MonitoringData:
 *       type: object
 *       properties:
 *         status:
 *           type: string
 *           example: success
 *         data:
 *           type: object
 *           properties:
 *             health:
 *               type: object
 *               properties:
 *                 overall:
 *                   type: string
 *                   enum: [healthy, degraded, unhealthy]
 *                 database:
 *                   type: string
 *                   enum: [healthy, unhealthy]
 *                 cache:
 *                   type: string
 *                   enum: [healthy, unhealthy]
 *             performance:
 *               type: object
 *               properties:
 *                 lastTestDuration:
 *                   type: string
 *                   example: "150ms"
 *                 monitoringDuration:
 *                   type: string
 *                   example: "200ms"
 *                 threshold:
 *                   type: string
 *                   example: "500ms"
 *                 withinThreshold:
 *                   type: boolean
 *             cache:
 *               type: object
 *               properties:
 *                 exists:
 *                   type: boolean
 *                 hasData:
 *                   type: boolean
 *                 connected:
 *                   type: boolean
 *             database:
 *               type: object
 *               properties:
 *                 connected:
 *                   type: boolean
 *                 sampleCount:
 *                   type: number
 *                 error:
 *                   type: string
 *         timestamp:
 *           type: string
 *           format: date-time
 *     
 *     Listing:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *           description: Unique identifier for the listing
 *         title:
 *           type: string
 *           description: Title of the property listing
 *         price:
 *           type: string
 *           description: Price of the property (formatted as '€ X.XXX per maand' with proper handling of European number formats)
 *         location:
 *           type: string
 *           description: Location of the property
 *         url:
 *           type: string
 *           description: URL to the original listing
 *         propertyType:
 *           type: string
 *           description: Type of property (apartment, house, etc.)
 *         rooms:
 *           type: number
 *           description: Number of rooms
 *         dateAdded:
 *           type: string
 *           format: date-time
 *           description: Date when listing was added
 *         description:
 *           type: string
 *           description: Property description
 *         timestamp:
 *           type: string
 *           format: date-time
 *           description: Timestamp of last update
 *
 *     PaginationInfo:
 *       type: object
 *       properties:
 *         currentPage:
 *           type: number
 *           description: Current page number
 *         totalPages:
 *           type: number
 *           description: Total number of pages
 *         totalCount:
 *           type: number
 *           description: Total number of items
 *         hasNextPage:
 *           type: boolean
 *           description: Whether there is a next page
 *         hasPrevPage:
 *           type: boolean
 *           description: Whether there is a previous page
 *         limit:
 *           type: number
 *           description: Number of items per page
 *
 * /api/listings:
 *   get:
 *     summary: Get listings with advanced search and filtering
 *     tags: [Listings]
 *     parameters:
 *       - in: query
 *         name: q
 *         schema:
 *           type: string
 *         description: Text search query (searches title, description, location)
 *       - in: query
 *         name: location
 *         schema:
 *           type: string
 *         description: Filter by location (partial match, case-insensitive)
 *       - in: query
 *         name: minPrice
 *         schema:
 *           type: number
 *         description: Minimum price filter
 *       - in: query
 *         name: maxPrice
 *         schema:
 *           type: number
 *         description: Maximum price filter
 *       - in: query
 *         name: propertyType
 *         schema:
 *           type: string
 *         description: Filter by property type
 *       - in: query
 *         name: minRooms
 *         schema:
 *           type: number
 *         description: Minimum number of rooms
 *       - in: query
 *         name: maxRooms
 *         schema:
 *           type: number
 *         description: Maximum number of rooms
 *       - in: query
 *         name: dateFrom
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter listings added from this date
 *       - in: query
 *         name: dateTo
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter listings added until this date
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: [dateAdded, price, title, location, timestamp]
 *           default: dateAdded
 *         description: Field to sort by
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: desc
 *         description: Sort order
 *       - in: query
 *         name: page
 *         schema:
 *           type: number
 *           minimum: 1
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: number
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: Successfully retrieved listings
 *         headers:
 *           X-Cache:
 *             description: Cache status (HIT or MISS)
 *             schema:
 *               type: string
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 results:
 *                   type: number
 *                   description: Number of listings returned
 *                 pagination:
 *                   $ref: '#/components/schemas/PaginationInfo'
 *                 data:
 *                   type: object
 *                   properties:
 *                     listings:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Listing'
 *       400:
 *         description: Invalid query parameters
 *       429:
 *         description: Too many requests
 *       500:
 *         description: Internal server error
 */
router.get(
  "/listings",
  validateListingQuery,
  cacheConfigs.listings,
  listingController.getListings
);

/**
 * @swagger
 * /api/listings/cities:
 *   get:
 *     summary: Get available cities from listings
 *     tags: [Listings]
 *     responses:
 *       200:
 *         description: List of available cities
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 results:
 *                   type: number
 *                   example: 25
 *                 data:
 *                   type: array
 *                   items:
 *                     type: string
 *                   example: ["Amsterdam", "Rotterdam", "Den Haag"]
 *       500:
 *         description: Server error
 */
router.get(
  "/listings/cities",
  cacheConfigs.listings,
  listingController.getAvailableCities
);

/**
 * @swagger
 * /api/listings/quick-stats:
 *   get:
 *     summary: Get quick dashboard statistics with enhanced error handling
 *     tags: [Listings]
 *     description: |
 *       Returns quick statistics for dashboard including total listings count, average price, and new listings added today.
 *       Features comprehensive error handling with graceful degradation, fallback mechanisms, and detailed performance monitoring.
 *       
 *       **Error Handling Features:**
 *       - Multi-layer fallback system (cache → partial data → defaults)
 *       - Circuit breaker pattern for database protection
 *       - Graceful degradation with partial data availability
 *       - Automatic retry with exponential backoff
 *       - Performance monitoring and alerting
 *       
 *       **Response Status Types:**
 *       - `success`: All data calculated successfully
 *       - `partial`: Some calculations failed, partial data available
 *       - `fallback`: Primary calculation failed, serving cached/default data
 *       - `error`: Critical failure, minimal data available
 *     responses:
 *       200:
 *         description: Successfully retrieved quick statistics (may include partial data or fallback values)
 *         headers:
 *           X-Cache:
 *             description: Cache status (HIT or MISS)
 *             schema:
 *               type: string
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/QuickStatsResponse'
 *       429:
 *         description: Too many requests
 *       503:
 *         description: Service temporarily unavailable (circuit breaker open)
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: error
 *                 message:
 *                   type: string
 *                   example: Service temporarily unavailable
 *                 data:
 *                   type: object
 *                   properties:
 *                     stats:
 *                       type: object
 *                       properties:
 *                         totalListings:
 *                           type: number
 *                           example: 0
 *                         averagePrice:
 *                           type: number
 *                           example: 0
 *                         newToday:
 *                           type: number
 *                           example: 0
 */
router.get(
  "/listings/quick-stats",
  performanceMonitor.middleware("quick-stats"),
  cacheConfigs.short,
  listingController.getQuickStats
);

/**
 * @swagger
 * /api/listings/{id}:
 *   get:
 *     summary: Get a specific listing by ID
 *     tags: [Listings]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: MongoDB ObjectId of the listing
 *     responses:
 *       200:
 *         description: Successfully retrieved listing
 *         headers:
 *           X-Cache:
 *             description: Cache status (HIT or MISS)
 *             schema:
 *               type: string
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     listing:
 *                       $ref: '#/components/schemas/Listing'
 *       400:
 *         description: Invalid listing ID format
 *       404:
 *         description: Listing not found
 *       500:
 *         description: Internal server error
 */
router.get(
  "/listings/:id",
  validateObjectId,
  cacheConfigs.listings,
  listingController.getListingById
);

/**
 * @swagger
 * /api/search/suggestions:
 *   get:
 *     summary: Get search suggestions for auto-complete
 *     tags: [Search]
 *     parameters:
 *       - in: query
 *         name: q
 *         required: true
 *         schema:
 *           type: string
 *         description: Query string to get suggestions for
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [location, propertyType]
 *           default: location
 *         description: Type of suggestions to return
 *     responses:
 *       200:
 *         description: Successfully retrieved suggestions
 *         headers:
 *           X-Cache:
 *             description: Cache status (HIT or MISS)
 *             schema:
 *               type: string
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 results:
 *                   type: number
 *                   description: Number of suggestions returned
 *                 data:
 *                   type: object
 *                   properties:
 *                     suggestions:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           suggestion:
 *                             type: string
 *                             description: The suggested text
 *                           count:
 *                             type: number
 *                             description: Number of listings matching this suggestion
 *       400:
 *         description: Missing or invalid query parameter
 *       500:
 *         description: Internal server error
 */
router.get(
  "/search/suggestions",
  cacheConfigs.short,
  listingController.getSearchSuggestions
);

/**
 * @swagger
 * /api/search/stats:
 *   get:
 *     summary: Get search statistics and database analytics
 *     tags: [Search]
 *     responses:
 *       200:
 *         description: Successfully retrieved search statistics
 *         headers:
 *           X-Cache:
 *             description: Cache status (HIT or MISS)
 *             schema:
 *               type: string
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     stats:
 *                       type: object
 *                       properties:
 *                         totalListings:
 *                           type: number
 *                           description: Total number of listings in database
 *                         avgPrice:
 *                           type: number
 *                           description: Average price of all listings
 *                         minPrice:
 *                           type: number
 *                           description: Minimum price found
 *                         maxPrice:
 *                           type: number
 *                           description: Maximum price found
 *                         uniqueLocations:
 *                           type: number
 *                           description: Number of unique locations
 *                         uniquePropertyTypes:
 *                           type: number
 *                           description: Number of unique property types
 *                         locations:
 *                           type: array
 *                           items:
 *                             type: string
 *                           description: List of available locations (top 20)
 *                         propertyTypes:
 *                           type: array
 *                           items:
 *                             type: string
 *                           description: List of available property types (top 20)
 *       500:
 *         description: Internal server error
 */
router.get(
  "/search/stats",
  cacheConfigs.general,
  listingController.getSearchStats
);

module.exports = router;
