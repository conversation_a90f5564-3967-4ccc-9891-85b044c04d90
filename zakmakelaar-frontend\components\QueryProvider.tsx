import React, { useEffect } from 'react';
import { QueryClientProvider } from '@tanstack/react-query';
import { queryClient, backgroundSync, offlineUtils } from '../services/queryClient';
import { AppState, AppStateStatus } from 'react-native';
import NetInfo from '@react-native-community/netinfo';

interface QueryProviderProps {
  children: React.ReactNode;
}

export const QueryProvider: React.FC<QueryProviderProps> = ({ children }) => {
  useEffect(() => {
    // Setup background sync
    backgroundSync.setupBackgroundRefetch();
    
    // Handle app state changes
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (nextAppState === 'active') {
        // App became active, refetch stale queries
        queryClient.refetchQueries({
          type: 'active',
          stale: true,
        });
      }
    };
    
    // Handle network state changes
    const unsubscribeNetInfo = NetInfo.addEventListener(state => {
      if (state.isConnected && state.isInternetReachable) {
        // Back online, sync queued mutations and refetch stale data
        backgroundSync.syncOnReconnect();
      }
    });
    
    // Subscribe to app state changes
    const appStateSubscription = AppState.addEventListener('change', handleAppStateChange);
    
    // Cleanup
    return () => {
      appStateSubscription?.remove();
      unsubscribeNetInfo();
    };
  }, []);
  
  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};