const mongoose = require('mongoose');
const bcrypt = require('bcrypt');
const config = require('./src/config/config');
const User = require('./src/models/User');
const Property = require('./src/models/Property');

// Mock property owner data
const mockPropertyOwner = {
  email: '<EMAIL>',
  password: 'password123',
  role: 'owner',
  emailVerified: true,
  profile: {
    firstName: 'John',
    lastName: 'Doe',
    phoneNumber: '+***********',
    userType: ['property_owner']
  },
  propertyOwner: {
    isPropertyOwner: true,
    verificationStatus: 'verified',
    businessRegistration: 'NL123456789B01',
    taxNumber: 'NL123456789',
    bankAccount: '******************'
  }
};

// Mock properties data (converted from frontend format to backend schema)
const mockProperties = [
  {
    title: 'Modern Apartment in Utrecht',
    description: 'Beautiful modern apartment in the heart of Utrecht with excellent public transport connections.',
    address: {
      street: 'Main Street',
      houseNumber: '123',
      postalCode: '3511AB',
      city: 'Utrecht',
      province: 'Utrecht',
      country: 'Netherlands'
    },
    propertyType: 'apartment',
    size: 75,
    rooms: 3,
    bedrooms: 2,
    bathrooms: 1,
    rent: {
      amount: 1200,
      currency: 'EUR',
      period: 'monthly',
      includesUtilities: false,
      deposit: 2400,
      additionalCosts: {
        utilities: 150,
        serviceCharges: 50,
        parking: 0,
        other: 0
      }
    },
    features: {
      furnished: false,
      interior: 'gestoffeerd',
      parking: false,
      balcony: true,
      garden: false,
      terrace: false,
      elevator: true,
      airConditioning: false,
      heating: 'central',
      energyLabel: 'B'
    },
    policies: {
      petsAllowed: false,
      smokingAllowed: false,
      studentsAllowed: true,
      expatFriendly: true,
      registrationAllowed: true,
      minimumIncome: 3600,
      maximumOccupants: 2,
      minimumRentalPeriod: 12,
      maximumRentalPeriod: 24
    },
    status: 'active',
    availability: {
      availableFrom: new Date(),
      viewingSchedule: 'by_appointment'
    },
    images: [{
      url: 'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60',
      caption: 'Living room',
      isPrimary: true,
      uploadedAt: new Date()
    }],
    applicationSettings: {
      requiresApplication: true,
      autoScreening: false,
      minimumTenantScore: 60,
      requiredDocuments: ['income_proof', 'employment_contract', 'id_document'],
      maxApplications: 10
    },
    metrics: {
      views: 45,
      applications: 3,
      viewings: 2,
      daysOnMarket: 5
    }
  },
  {
    title: 'Spacious House in Amsterdam',
    description: 'Large family house in Amsterdam with garden and parking space. Perfect for families or professionals.',
    address: {
      street: 'Oak Avenue',
      houseNumber: '456',
      postalCode: '1012AB',
      city: 'Amsterdam',
      province: 'Noord-Holland',
      country: 'Netherlands'
    },
    propertyType: 'house',
    size: 120,
    rooms: 5,
    bedrooms: 3,
    bathrooms: 2,
    floors: 2,
    rent: {
      amount: 1800,
      currency: 'EUR',
      period: 'monthly',
      includesUtilities: false,
      deposit: 3600,
      additionalCosts: {
        utilities: 200,
        serviceCharges: 75,
        parking: 50,
        other: 25
      }
    },
    features: {
      furnished: false,
      interior: 'kaal',
      parking: true,
      parkingType: 'private',
      balcony: false,
      garden: true,
      terrace: true,
      elevator: false,
      airConditioning: false,
      heating: 'central',
      energyLabel: 'A'
    },
    policies: {
      petsAllowed: true,
      smokingAllowed: false,
      studentsAllowed: false,
      expatFriendly: true,
      registrationAllowed: true,
      minimumIncome: 5400,
      maximumOccupants: 4,
      minimumRentalPeriod: 12,
      maximumRentalPeriod: 36
    },
    status: 'rented',
    availability: {
      availableFrom: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
      viewingSchedule: 'by_appointment'
    },
    images: [{
      url: 'https://images.unsplash.com/photo-*************-78b9dba3b914?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60',
      caption: 'Front view',
      isPrimary: true,
      uploadedAt: new Date()
    }],
    applicationSettings: {
      requiresApplication: true,
      autoScreening: true,
      minimumTenantScore: 70,
      requiredDocuments: ['income_proof', 'employment_contract', 'bank_statement', 'rental_reference'],
      maxApplications: 15
    },
    metrics: {
      views: 89,
      applications: 12,
      viewings: 8,
      daysOnMarket: 15
    },
    rentedAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000) // Rented 15 days ago
  },
  {
    title: 'Cozy Studio in Rotterdam',
    description: 'Compact but well-designed studio apartment in Rotterdam city center. Perfect for students or young professionals.',
    address: {
      street: 'Pine Road',
      houseNumber: '789',
      postalCode: '3011AB',
      city: 'Rotterdam',
      province: 'Zuid-Holland',
      country: 'Netherlands'
    },
    propertyType: 'studio',
    size: 45,
    rooms: 1,
    bedrooms: 1,
    bathrooms: 1,
    rent: {
      amount: 950,
      currency: 'EUR',
      period: 'monthly',
      includesUtilities: true,
      deposit: 1900,
      additionalCosts: {
        utilities: 0, // Included in rent
        serviceCharges: 30,
        parking: 0,
        other: 0
      }
    },
    features: {
      furnished: true,
      interior: 'gemeubileerd',
      parking: false,
      balcony: true,
      garden: false,
      terrace: false,
      elevator: true,
      airConditioning: false,
      heating: 'individual',
      energyLabel: 'C'
    },
    policies: {
      petsAllowed: false,
      smokingAllowed: false,
      studentsAllowed: true,
      expatFriendly: true,
      registrationAllowed: true,
      minimumIncome: 2850,
      maximumOccupants: 1,
      minimumRentalPeriod: 6,
      maximumRentalPeriod: 12
    },
    status: 'maintenance',
    availability: {
      availableFrom: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // Available in 7 days
      viewingSchedule: 'flexible'
    },
    images: [{
      url: 'https://images.unsplash.com/photo-1493809842364-78817add7ffb?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60',
      caption: 'Studio overview',
      isPrimary: true,
      uploadedAt: new Date()
    }],
    applicationSettings: {
      requiresApplication: true,
      autoScreening: false,
      minimumTenantScore: 50,
      requiredDocuments: ['income_proof', 'id_document'],
      maxApplications: 20
    },
    metrics: {
      views: 23,
      applications: 0,
      viewings: 0,
      daysOnMarket: 0
    }
  }
];

async function addMockData() {
  try {
    console.log('🔄 Connecting to MongoDB...');
    await mongoose.connect(config.mongoURI);
    console.log('✅ Connected to MongoDB');

    // Check if property owner already exists
    let propertyOwner = await User.findOne({ email: mockPropertyOwner.email });
    
    if (!propertyOwner) {
      console.log('🔄 Creating property owner...');
      propertyOwner = new User(mockPropertyOwner);
      await propertyOwner.save();
      console.log('✅ Property owner created');
    } else {
      console.log('ℹ️ Property owner already exists');
    }

    // Clear existing properties for this owner
    console.log('🔄 Clearing existing properties...');
    await Property.deleteMany({ 'owner.userId': propertyOwner._id });
    console.log('✅ Existing properties cleared');

    // Add properties
    console.log('🔄 Adding mock properties...');
    const propertyIds = [];
    
    for (const propertyData of mockProperties) {
      // Add owner information to property
      propertyData.owner = {
        userId: propertyOwner._id,
        contactPreference: 'email',
        responseTime: 'within 24 hours'
      };
      
      // Set published date for active properties
      if (propertyData.status === 'active') {
        propertyData.publishedAt = new Date();
      }
      
      const property = new Property(propertyData);
      await property.save();
      propertyIds.push(property._id.toString());
      
      console.log(`✅ Added property: ${property.title}`);
    }

    // Update property owner with property references
    propertyOwner.propertyOwner.properties = propertyIds;
    await propertyOwner.save();
    
    console.log('✅ Updated property owner with property references');
    console.log(`📊 Summary:`);
    console.log(`   - Property owner: ${propertyOwner.email}`);
    console.log(`   - Properties added: ${propertyIds.length}`);
    console.log(`   - Active properties: ${mockProperties.filter(p => p.status === 'active').length}`);
    console.log(`   - Rented properties: ${mockProperties.filter(p => p.status === 'rented').length}`);
    console.log(`   - Maintenance properties: ${mockProperties.filter(p => p.status === 'maintenance').length}`);

  } catch (error) {
    console.error('❌ Error adding mock data:', error);
  } finally {
    await mongoose.connection.close();
    console.log('✅ Database connection closed');
  }
}

// Run the script
if (require.main === module) {
  addMockData();
}

module.exports = { addMockData, mockPropertyOwner, mockProperties };