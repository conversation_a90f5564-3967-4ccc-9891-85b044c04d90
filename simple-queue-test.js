const axios = require('axios');

const BASE_URL = 'http://localhost:3000';
const TEST_USER = {
  email: '<EMAIL>',
  password: 'Password123'
};

let authToken = null;

async function login() {
  try {
    console.log('🔐 Logging in...');
    const response = await axios.post(`${BASE_URL}/api/auth/login`, TEST_USER);
    authToken = response.data.token;
    console.log('✅ Login successful');
    return response.data;
  } catch (error) {
    console.error('❌ Login failed:', error.response?.data || error.message);
    throw error;
  }
}

async function testSimpleQueue() {
  try {
    console.log('📋 Testing simple queue addition...');
    
    // Very minimal queue item
    const queueItem = {
      listingId: 'test-123',
      listingUrl: 'https://example.com/test',
      priority: 1
    };
    
    console.log('Sending:', JSON.stringify(queueItem, null, 2));
    
    const response = await axios.post(`${BASE_URL}/api/auto-application/queue`, queueItem, {
      headers: { 
        Authorization: `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Queue item added successfully');
    console.log('Response:', JSON.stringify(response.data, null, 2));
    return response.data;
  } catch (error) {
    console.error('❌ Queue addition failed');
    console.error('Status:', error.response?.status);
    console.error('Status Text:', error.response?.statusText);
    console.error('Data:', JSON.stringify(error.response?.data, null, 2));
    console.error('Headers:', error.response?.headers);
    throw error;
  }
}

async function run() {
  try {
    await login();
    await testSimpleQueue();
  } catch (error) {
    console.error('Test failed:', error.message);
    process.exit(1);
  }
}

run();