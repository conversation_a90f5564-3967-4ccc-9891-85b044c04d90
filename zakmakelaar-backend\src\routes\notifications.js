const express = require('express');
const router = express.Router();
const autoApplicationNotificationService = require('../services/autoApplicationNotificationService');
const notificationScheduler = require('../services/notificationScheduler');
const AutoApplicationSettings = require('../models/AutoApplicationSettings');
const { auth } = require('../middleware/auth');
const { loggers } = require('../services/logger');

/**
 * @route GET /api/notifications/preferences
 * @desc Get user notification preferences
 * @access Private
 */
router.get('/preferences', auth, async (req, res) => {
  try {
    const userId = req.user._id;
    
    const settings = await AutoApplicationSettings.findByUserId(userId);
    
    if (!settings) {
      return res.json({
        success: true,
        data: {
          immediate: true,
          daily: true,
          weekly: false,
          email: true,
          sms: false,
          push: true
        }
      });
    }

    const preferences = settings.settings?.notificationPreferences || {};
    
    res.json({
      success: true,
      data: {
        immediate: preferences.immediate !== false,
        daily: preferences.daily !== false,
        weekly: preferences.weekly || false,
        email: preferences.email !== false,
        sms: preferences.sms || false,
        push: preferences.push !== false
      }
    });

  } catch (error) {
    loggers.app.error('Error getting notification preferences:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get notification preferences',
      error: error.message
    });
  }
});

/**
 * @route PUT /api/notifications/preferences
 * @desc Update user notification preferences
 * @access Private
 */
router.put('/preferences', auth, async (req, res) => {
  try {
    const userId = req.user._id;
    const { immediate, daily, weekly, email, sms, push } = req.body;

    // Validate preferences
    const preferences = {
      immediate: immediate !== undefined ? Boolean(immediate) : true,
      daily: daily !== undefined ? Boolean(daily) : true,
      weekly: weekly !== undefined ? Boolean(weekly) : false,
      email: email !== undefined ? Boolean(email) : true,
      sms: sms !== undefined ? Boolean(sms) : false,
      push: push !== undefined ? Boolean(push) : true
    };

    // Find or create auto-application settings
    let settings = await AutoApplicationSettings.findByUserId(userId);
    
    if (!settings) {
      settings = new AutoApplicationSettings({
        userId,
        enabled: false,
        settings: {
          notificationPreferences: preferences
        },
        criteria: {},
        personalInfo: {},
        documents: []
      });
    } else {
      if (!settings.settings) {
        settings.settings = {};
      }
      settings.settings.notificationPreferences = preferences;
    }

    await settings.save();

    loggers.app.info(`Notification preferences updated for user ${userId}`, preferences);

    res.json({
      success: true,
      message: 'Notification preferences updated successfully',
      data: preferences
    });

  } catch (error) {
    loggers.app.error('Error updating notification preferences:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update notification preferences',
      error: error.message
    });
  }
});

/**
 * @route POST /api/notifications/test-daily-summary
 * @desc Send test daily summary notification
 * @access Private
 */
router.post('/test-daily-summary', auth, async (req, res) => {
  try {
    const userId = req.user._id;

    await notificationScheduler.triggerDailySummary(userId);

    res.json({
      success: true,
      message: 'Test daily summary sent successfully'
    });

  } catch (error) {
    loggers.app.error('Error sending test daily summary:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send test daily summary',
      error: error.message
    });
  }
});

/**
 * @route POST /api/notifications/test-weekly-summary
 * @desc Send test weekly summary notification
 * @access Private
 */
router.post('/test-weekly-summary', auth, async (req, res) => {
  try {
    const userId = req.user._id;

    await notificationScheduler.triggerWeeklySummary(userId);

    res.json({
      success: true,
      message: 'Test weekly summary sent successfully'
    });

  } catch (error) {
    loggers.app.error('Error sending test weekly summary:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send test weekly summary',
      error: error.message
    });
  }
});

/**
 * @route POST /api/notifications/test-urgent-alert
 * @desc Send test urgent alert notification
 * @access Private
 */
router.post('/test-urgent-alert', auth, async (req, res) => {
  try {
    const userId = req.user._id;
    const { alertType = 'system_error', message = 'This is a test alert' } = req.body;

    await autoApplicationNotificationService.sendUrgentAlert(userId, alertType, {
      error: message,
      timestamp: new Date()
    });

    res.json({
      success: true,
      message: 'Test urgent alert sent successfully'
    });

  } catch (error) {
    loggers.app.error('Error sending test urgent alert:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send test urgent alert',
      error: error.message
    });
  }
});

/**
 * @route GET /api/notifications/history
 * @desc Get notification history for user
 * @access Private
 */
router.get('/history', auth, async (req, res) => {
  try {
    const userId = req.user._id;
    const { limit = 20, offset = 0, type } = req.query;

    // This would typically query a notifications table
    // For now, return mock data based on application history
    const mockHistory = [
      {
        id: '1',
        type: 'application_status_update',
        title: 'Application Status Update',
        message: 'Your application for Apartment in Amsterdam has been submitted',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
        read: true,
        channels: ['email', 'push']
      },
      {
        id: '2',
        type: 'daily_summary',
        title: 'Daily Summary',
        message: '3 applications submitted today with 67% success rate',
        timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
        read: true,
        channels: ['email']
      },
      {
        id: '3',
        type: 'urgent_alert',
        title: 'Daily Limit Reached',
        message: 'You have reached your daily application limit of 5',
        timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
        read: false,
        channels: ['email', 'sms', 'push']
      }
    ];

    // Filter by type if specified
    let filteredHistory = mockHistory;
    if (type) {
      filteredHistory = mockHistory.filter(n => n.type === type);
    }

    // Apply pagination
    const paginatedHistory = filteredHistory.slice(
      parseInt(offset),
      parseInt(offset) + parseInt(limit)
    );

    res.json({
      success: true,
      data: {
        notifications: paginatedHistory,
        total: filteredHistory.length,
        limit: parseInt(limit),
        offset: parseInt(offset)
      }
    });

  } catch (error) {
    loggers.app.error('Error getting notification history:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get notification history',
      error: error.message
    });
  }
});

/**
 * @route PUT /api/notifications/:id/read
 * @desc Mark notification as read
 * @access Private
 */
router.put('/:id/read', auth, async (req, res) => {
  try {
    const userId = req.user._id;
    const notificationId = req.params.id;

    // This would typically update a notifications table
    // For now, just return success
    loggers.app.info(`Notification ${notificationId} marked as read for user ${userId}`);

    res.json({
      success: true,
      message: 'Notification marked as read'
    });

  } catch (error) {
    loggers.app.error('Error marking notification as read:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to mark notification as read',
      error: error.message
    });
  }
});

/**
 * @route DELETE /api/notifications/:id
 * @desc Delete notification
 * @access Private
 */
router.delete('/:id', auth, async (req, res) => {
  try {
    const userId = req.user._id;
    const notificationId = req.params.id;

    // This would typically delete from a notifications table
    // For now, just return success
    loggers.app.info(`Notification ${notificationId} deleted for user ${userId}`);

    res.json({
      success: true,
      message: 'Notification deleted successfully'
    });

  } catch (error) {
    loggers.app.error('Error deleting notification:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete notification',
      error: error.message
    });
  }
});

/**
 * @route GET /api/notifications/stats
 * @desc Get notification statistics
 * @access Private
 */
router.get('/stats', auth, async (req, res) => {
  try {
    const userId = req.user._id;

    // This would typically query notification delivery stats
    // For now, return mock stats
    const stats = {
      totalSent: 45,
      delivered: 43,
      failed: 2,
      deliveryRate: 95.6,
      channelStats: {
        email: { sent: 45, delivered: 43, rate: 95.6 },
        sms: { sent: 12, delivered: 12, rate: 100 },
        push: { sent: 38, delivered: 36, rate: 94.7 }
      },
      typeStats: {
        application_status_update: 28,
        daily_summary: 7,
        weekly_summary: 4,
        urgent_alert: 6
      },
      last30Days: {
        dates: Array.from({ length: 30 }, (_, i) => {
          const date = new Date();
          date.setDate(date.getDate() - (29 - i));
          return date.toISOString().split('T')[0];
        }),
        counts: Array.from({ length: 30 }, () => Math.floor(Math.random() * 5))
      }
    };

    res.json({
      success: true,
      data: stats
    });

  } catch (error) {
    loggers.app.error('Error getting notification stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get notification statistics',
      error: error.message
    });
  }
});

/**
 * @route GET /api/notifications/scheduler/status
 * @desc Get notification scheduler status (admin only)
 * @access Private
 */
router.get('/scheduler/status', auth, async (req, res) => {
  try {
    // Check if user is admin (you might want to add admin middleware)
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Admin access required'
      });
    }

    const status = notificationScheduler.getStatus();

    res.json({
      success: true,
      data: status
    });

  } catch (error) {
    loggers.app.error('Error getting scheduler status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get scheduler status',
      error: error.message
    });
  }
});

/**
 * @route POST /api/notifications/maintenance
 * @desc Send maintenance notification to all users (admin only)
 * @access Private
 */
router.post('/maintenance', auth, async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Admin access required'
      });
    }

    const {
      scheduledStart,
      estimatedDuration,
      affectedServices = ['Auto-Application'],
      impact = 'Auto-applications will be temporarily paused',
      alternativeActions = [],
      contactInfo = '<EMAIL>'
    } = req.body;

    if (!scheduledStart || !estimatedDuration) {
      return res.status(400).json({
        success: false,
        message: 'scheduledStart and estimatedDuration are required'
      });
    }

    await autoApplicationNotificationService.sendMaintenanceNotification(null, {
      scheduledStart: new Date(scheduledStart),
      estimatedDuration,
      affectedServices,
      impact,
      alternativeActions,
      contactInfo
    });

    res.json({
      success: true,
      message: 'Maintenance notification sent to all users'
    });

  } catch (error) {
    loggers.app.error('Error sending maintenance notification:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send maintenance notification',
      error: error.message
    });
  }
});

/**
 * @route POST /api/notifications/register-push-token
 * @desc Register push notification token for user
 * @access Private
 */
router.post('/register-push-token', auth, async (req, res) => {
  try {
    const userId = req.user._id;
    const { pushToken } = req.body;

    if (!pushToken) {
      return res.status(400).json({
        success: false,
        message: 'Push token is required'
      });
    }

    // Get or create user settings
    let settings = await AutoApplicationSettings.findByUserId(userId);
    
    if (!settings) {
      settings = await AutoApplicationSettings.create({
        userId,
        settings: {
          notificationPreferences: {
            immediate: true,
            daily: true,
            weekly: false,
            email: true,
            sms: false,
            push: true
          }
        }
      });
    }

    // Update push token in settings
    if (!settings.settings) {
      settings.settings = {};
    }
    if (!settings.settings.notificationPreferences) {
      settings.settings.notificationPreferences = {};
    }

    settings.settings.pushToken = pushToken;
    settings.settings.pushTokenUpdatedAt = new Date();
    
    await settings.save();

    loggers.app.info(`Push token registered for user ${userId}`, {
      userId,
      tokenPreview: pushToken.substring(0, 20) + '...'
    });

    res.json({
      success: true,
      message: 'Push token registered successfully'
    });

  } catch (error) {
    loggers.app.error('Error registering push token:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to register push token',
      error: error.message
    });
  }
});

module.exports = router;