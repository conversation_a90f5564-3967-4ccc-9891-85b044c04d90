// Test connection to the backend
// Using built-in fetch (Node.js 18+)

async function testConnection() {
  console.log('Testing connection to backend...');
  
  const baseUrl = 'http://localhost:3000';
  
  try {
    // Test health endpoint
    console.log('1. Testing health endpoint...');
    const healthResponse = await fetch(`${baseUrl}/health`);
    const healthData = await healthResponse.json();
    
    console.log('Health check response:', healthData);
    
    if (healthResponse.ok) {
      console.log('✅ Backend is running and healthy!');
    } else {
      console.log('❌ Backend health check failed');
      return;
    }
    
    // Test property owner endpoint
    console.log('\n2. Testing property owner endpoint...');
    const propertyResponse = await fetch(`${baseUrl}/api/property-owner/properties`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token'
      }
    });
    
    console.log('Property endpoint status:', propertyResponse.status);
    
    if (propertyResponse.status === 401) {
      console.log('✅ Property endpoint is accessible (401 = auth required, which is expected)');
    } else if (propertyResponse.ok) {
      console.log('✅ Property endpoint is accessible');
    } else {
      console.log('❌ Property endpoint failed with status:', propertyResponse.status);
    }
    
    // Test update endpoint specifically
    console.log('\n3. Testing update property endpoint...');
    const updateResponse = await fetch(`${baseUrl}/api/property-owner/properties/test-id`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token'
      },
      body: JSON.stringify({ title: 'Test Property' })
    });
    
    console.log('Update endpoint status:', updateResponse.status);
    
    if (updateResponse.status === 401) {
      console.log('✅ Update endpoint is accessible (401 = auth required, which is expected)');
    } else if (updateResponse.status === 400) {
      console.log('✅ Update endpoint is accessible (400 = validation error, which is expected)');
    } else {
      console.log('Update endpoint response status:', updateResponse.status);
      const updateText = await updateResponse.text();
      console.log('Update endpoint response:', updateText);
    }
    
  } catch (error) {
    console.error('❌ Connection failed:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('💡 Make sure the backend server is running with: npm run dev');
    }
  }
}

testConnection();