# Setting Up Development Build for Push Notifications

Since Expo Go no longer supports push notifications in SDK 53+, you need to create a development build.

## Steps to Create Development Build

### 1. Install EAS CLI
```bash
npm install -g @expo/eas-cli
```

### 2. Configure EAS Build
```bash
eas build:configure
```

### 3. Create Development Build
For Android:
```bash
eas build --platform android --profile development
```

For iOS:
```bash
eas build --platform ios --profile development
```

### 4. Install the Development Build
- Download the APK/IPA from the EAS build dashboard
- Install it on your device
- Use `expo start --dev-client` to run your app

## Alternative: Local Development Build

If you prefer to build locally:

### For Android:
```bash
npx expo run:android
```

### For iOS:
```bash
npx expo run:ios
```

This will create a development build locally and install it on your device/simulator.

## Benefits of Development Build
- Full access to all native APIs including push notifications
- Better performance than Expo Go
- Ability to add custom native modules
- More similar to production builds