const scraperAutoApplicationIntegration = require('../../services/scraperAutoApplicationIntegration');
const AutoApplicationSettings = require('../../models/AutoApplicationSettings');
const ApplicationQueue = require('../../models/ApplicationQueue');
const Listing = require('../../models/Listing');
const autoApplicationService = require('../../services/autoApplicationService');

// Mock dependencies
jest.mock('../../models/AutoApplicationSettings');
jest.mock('../../models/ApplicationQueue');
jest.mock('../../models/Listing');
jest.mock('../../services/autoApplicationService');
jest.mock('../../services/logger', () => ({
  loggers: {
    app: {
      info: jest.fn(),
      debug: jest.fn(),
      error: jest.fn(),
      warn: jest.fn()
    }
  }
}));

describe('ScraperAutoApplicationIntegration Unit Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    scraperAutoApplicationIntegration.clearCaches();
  });

  describe('processNewListings', () => {
    test('should return early if no users have auto-application enabled', async () => {
      AutoApplicationSettings.find.mockResolvedValue([]);

      const listings = [
        {
          title: 'Test Apartment',
          url: 'https://example.com/test',
          source: 'funda.nl'
        }
      ];

      const result = await scraperAutoApplicationIntegration.processNewListings(listings, 'funda.nl');

      expect(result.processed).toBe(0);
      expect(result.autoApplicationTriggered).toBe(0);
      expect(AutoApplicationSettings.find).toHaveBeenCalledWith({ enabled: true });
    });

    test('should process listings in batches', async () => {
      const mockUser = { userId: 'user1', criteria: {}, settings: { maxApplicationsPerDay: 5 } };
      AutoApplicationSettings.find.mockResolvedValue([mockUser]);
      Listing.findOne.mockResolvedValue(null); // No duplicates
      ApplicationQueue.findOne.mockResolvedValue(null); // No existing applications
      ApplicationQueue.countDocuments.mockResolvedValue(0); // No daily limit reached
      autoApplicationService.processNewListing.mockResolvedValue();

      const listings = Array.from({ length: 10 }, (_, i) => ({
        title: `Apartment ${i}`,
        url: `https://example.com/apartment-${i}`,
        price: '€ 1.500 per maand',
        location: 'Utrecht',
        propertyType: 'appartement',
        rooms: '3',
        source: 'funda.nl',
        dateAdded: new Date()
      }));

      const result = await scraperAutoApplicationIntegration.processNewListings(listings, 'funda.nl');

      expect(result.processed).toBe(10);
      expect(AutoApplicationSettings.find).toHaveBeenCalledWith({ enabled: true });
    });
  });

  describe('quality scoring', () => {
    test('should calculate higher scores for complete listings', async () => {
      const integration = scraperAutoApplicationIntegration;
      
      const highQualityListing = {
        title: 'Premium Apartment',
        price: '€ 1.800 per maand',
        location: 'Utrecht Center',
        url: 'https://example.com/premium',
        size: '85',
        bedrooms: '2',
        rooms: '3',
        year: '2020',
        propertyType: 'appartement',
        description: 'This is a very detailed description of a beautiful apartment with all amenities included.',
        images: ['https://example.com/image1.jpg', 'https://example.com/image2.jpg'],
        dateAdded: new Date()
      };

      const lowQualityListing = {
        title: 'Basic Room',
        price: 'Prijs op aanvraag',
        location: 'Unknown',
        url: 'https://example.com/basic',
        dateAdded: new Date()
      };

      // Use reflection to access private method for testing
      const calculateQualityScore = integration._calculateQualityScore.bind(integration);
      
      const highScore = await calculateQualityScore(highQualityListing);
      const lowScore = await calculateQualityScore(lowQualityListing);

      expect(highScore).toBeGreaterThan(lowScore);
      expect(highScore).toBeGreaterThan(0.8);
      expect(lowScore).toBeLessThan(0.7);
    });

    test('should give recency bonus to new listings', async () => {
      const integration = scraperAutoApplicationIntegration;
      const calculateQualityScore = integration._calculateQualityScore.bind(integration);

      const newListing = {
        title: 'New Apartment',
        price: '€ 1.500 per maand',
        url: 'https://example.com/new',
        dateAdded: new Date() // Very recent
      };

      const oldListing = {
        title: 'Old Apartment',
        price: '€ 1.500 per maand',
        url: 'https://example.com/old',
        dateAdded: new Date(Date.now() - 24 * 60 * 60 * 1000) // 24 hours ago
      };

      const newScore = await calculateQualityScore(newListing);
      const oldScore = await calculateQualityScore(oldListing);

      expect(newScore).toBeGreaterThan(oldScore);
    });

    test('should cache quality scores', async () => {
      const integration = scraperAutoApplicationIntegration;
      const calculateQualityScore = integration._calculateQualityScore.bind(integration);

      const listing = {
        title: 'Test Apartment',
        price: '€ 1.500 per maand',
        url: 'https://example.com/test',
        dateAdded: new Date()
      };

      // Calculate score twice
      const score1 = await calculateQualityScore(listing);
      const score2 = await calculateQualityScore(listing);

      expect(score1).toBe(score2);
      
      const stats = integration.getStatistics();
      expect(stats.qualityScoreCacheSize).toBe(1);
    });
  });

  describe('criteria matching', () => {
    test('should match price criteria correctly', async () => {
      const integration = scraperAutoApplicationIntegration;
      const matchesUserCriteria = integration._matchesUserCriteria.bind(integration);

      const userSettings = {
        criteria: {
          maxPrice: 2000
        }
      };

      const affordableListing = {
        price: '€ 1.500 per maand'
      };

      const expensiveListing = {
        price: '€ 2.500 per maand'
      };

      const affordableMatch = await matchesUserCriteria(affordableListing, userSettings);
      const expensiveMatch = await matchesUserCriteria(expensiveListing, userSettings);

      expect(affordableMatch).toBe(true);
      expect(expensiveMatch).toBe(false);
    });

    test('should match room count criteria correctly', async () => {
      const integration = scraperAutoApplicationIntegration;
      const matchesUserCriteria = integration._matchesUserCriteria.bind(integration);

      const userSettings = {
        criteria: {
          minRooms: 2,
          maxRooms: 4
        }
      };

      const validListing = { rooms: '3' };
      const tooSmallListing = { rooms: '1' };
      const tooLargeListing = { rooms: '5' };

      expect(await matchesUserCriteria(validListing, userSettings)).toBe(true);
      expect(await matchesUserCriteria(tooSmallListing, userSettings)).toBe(false);
      expect(await matchesUserCriteria(tooLargeListing, userSettings)).toBe(false);
    });

    test('should match property type criteria correctly', async () => {
      const integration = scraperAutoApplicationIntegration;
      const matchesUserCriteria = integration._matchesUserCriteria.bind(integration);

      const userSettings = {
        criteria: {
          propertyTypes: ['appartement', 'huis']
        }
      };

      const apartmentListing = { propertyType: 'appartement' };
      const houseListing = { propertyType: 'huis' };
      const roomListing = { propertyType: 'kamer' };

      expect(await matchesUserCriteria(apartmentListing, userSettings)).toBe(true);
      expect(await matchesUserCriteria(houseListing, userSettings)).toBe(true);
      expect(await matchesUserCriteria(roomListing, userSettings)).toBe(false);
    });

    test('should match location criteria correctly', async () => {
      const integration = scraperAutoApplicationIntegration;
      const matchesUserCriteria = integration._matchesUserCriteria.bind(integration);

      const userSettings = {
        criteria: {
          locations: ['utrecht', 'amsterdam']
        }
      };

      const utrechtListing = { location: 'Utrecht' };
      const amsterdamListing = { location: 'Amsterdam' };
      const rotterdamListing = { location: 'Rotterdam' };

      expect(await matchesUserCriteria(utrechtListing, userSettings)).toBe(true);
      expect(await matchesUserCriteria(amsterdamListing, userSettings)).toBe(true);
      expect(await matchesUserCriteria(rotterdamListing, userSettings)).toBe(false);
    });

    test('should exclude listings with excluded keywords', async () => {
      const integration = scraperAutoApplicationIntegration;
      const matchesUserCriteria = integration._matchesUserCriteria.bind(integration);

      const userSettings = {
        criteria: {
          excludeKeywords: ['student', 'shared']
        }
      };

      const regularListing = {
        title: 'Beautiful apartment',
        description: 'Great for professionals'
      };

      const studentListing = {
        title: 'Student apartment',
        description: 'Perfect for students'
      };

      const sharedListing = {
        title: 'Shared house',
        description: 'Shared living space'
      };

      expect(await matchesUserCriteria(regularListing, userSettings)).toBe(true);
      expect(await matchesUserCriteria(studentListing, userSettings)).toBe(false);
      expect(await matchesUserCriteria(sharedListing, userSettings)).toBe(false);
    });

    test('should require included keywords when specified', async () => {
      const integration = scraperAutoApplicationIntegration;
      const matchesUserCriteria = integration._matchesUserCriteria.bind(integration);

      const userSettings = {
        criteria: {
          includeKeywords: ['balcony', 'garden']
        }
      };

      const balconyListing = {
        title: 'Apartment with balcony',
        description: 'Nice balcony with city view'
      };

      const gardenListing = {
        title: 'House with garden',
        description: 'Beautiful garden space'
      };

      const regularListing = {
        title: 'Regular apartment',
        description: 'Standard apartment'
      };

      expect(await matchesUserCriteria(balconyListing, userSettings)).toBe(true);
      expect(await matchesUserCriteria(gardenListing, userSettings)).toBe(true);
      expect(await matchesUserCriteria(regularListing, userSettings)).toBe(false);
    });

    test('should match furnished preference correctly', async () => {
      const integration = scraperAutoApplicationIntegration;
      const matchesUserCriteria = integration._matchesUserCriteria.bind(integration);

      const furnishedUserSettings = {
        criteria: { furnished: true }
      };

      const unfurnishedUserSettings = {
        criteria: { furnished: false }
      };

      const furnishedListing = { interior: 'Gemeubileerd' };
      const unfurnishedListing = { interior: 'Kaal' };

      expect(await matchesUserCriteria(furnishedListing, furnishedUserSettings)).toBe(true);
      expect(await matchesUserCriteria(unfurnishedListing, furnishedUserSettings)).toBe(false);
      expect(await matchesUserCriteria(furnishedListing, unfurnishedUserSettings)).toBe(false);
      expect(await matchesUserCriteria(unfurnishedListing, unfurnishedUserSettings)).toBe(true);
    });
  });

  describe('duplicate detection', () => {
    test('should detect duplicates by URL', async () => {
      const integration = scraperAutoApplicationIntegration;
      const checkForDuplicate = integration._checkForDuplicate.bind(integration);

      const existingListing = { _id: 'existing123' };
      Listing.findOne.mockResolvedValue(existingListing);

      const listing = {
        url: 'https://example.com/test',
        title: 'Test Apartment',
        location: 'Utrecht'
      };

      const duplicate = await checkForDuplicate(listing);

      expect(duplicate).toBe(existingListing);
      expect(Listing.findOne).toHaveBeenCalledWith({ url: listing.url });
    });

    test('should detect duplicates by title and location as fallback', async () => {
      const integration = scraperAutoApplicationIntegration;
      const checkForDuplicate = integration._checkForDuplicate.bind(integration);

      const existingListing = { _id: 'existing123' };
      Listing.findOne
        .mockResolvedValueOnce(null) // No URL match
        .mockResolvedValueOnce(existingListing); // Title/location match

      const listing = {
        url: 'https://example.com/test',
        title: 'Test Apartment',
        location: 'Utrecht'
      };

      const duplicate = await checkForDuplicate(listing);

      expect(duplicate).toBe(existingListing);
      expect(Listing.findOne).toHaveBeenCalledTimes(2);
    });

    test('should return null if no duplicates found', async () => {
      const integration = scraperAutoApplicationIntegration;
      const checkForDuplicate = integration._checkForDuplicate.bind(integration);

      Listing.findOne.mockResolvedValue(null);

      const listing = {
        url: 'https://example.com/test',
        title: 'Test Apartment',
        location: 'Utrecht'
      };

      const duplicate = await checkForDuplicate(listing);

      expect(duplicate).toBeNull();
    });
  });

  describe('price extraction', () => {
    test('should extract numeric prices correctly', () => {
      const integration = scraperAutoApplicationIntegration;
      const extractPriceNumber = integration._extractPriceNumber.bind(integration);

      expect(extractPriceNumber('€ 1500 per maand')).toBe(1500);
      expect(extractPriceNumber('€1,500')).toBe(1500);
      expect(extractPriceNumber('€ 2.350,50 per maand')).toBe(2350.50);
      expect(extractPriceNumber('€1500')).toBe(1500);
      expect(extractPriceNumber('1500')).toBe(1500);
      expect(extractPriceNumber('Prijs op aanvraag')).toBeNull();
      expect(extractPriceNumber('')).toBeNull();
      expect(extractPriceNumber(null)).toBeNull();
    });

    test('should handle European number formats', () => {
      const integration = scraperAutoApplicationIntegration;
      const extractPriceNumber = integration._extractPriceNumber.bind(integration);

      // European format with dot as thousands separator and comma as decimal
      expect(extractPriceNumber('€ 1.500,50')).toBe(1500.50);
      
      // US format with comma as thousands separator and dot as decimal
      expect(extractPriceNumber('€ 1,500.50')).toBe(1500.50);
      
      // Only comma (could be thousands or decimal)
      expect(extractPriceNumber('€ 1,500')).toBe(1500); // Thousands separator
      expect(extractPriceNumber('€ 15,50')).toBe(15.50); // Decimal separator
    });
  });

  describe('listing ID generation', () => {
    test('should use URL as primary ID', () => {
      const integration = scraperAutoApplicationIntegration;
      const generateListingId = integration._generateListingId.bind(integration);

      const listing = {
        url: 'https://example.com/test',
        title: 'Test Apartment',
        location: 'Utrecht',
        price: '€ 1.500'
      };

      expect(generateListingId(listing)).toBe('https://example.com/test');
    });

    test('should fallback to title-location-price format', () => {
      const integration = scraperAutoApplicationIntegration;
      const generateListingId = integration._generateListingId.bind(integration);

      const listing = {
        title: 'Test Apartment',
        location: 'Utrecht',
        price: '€ 1.500'
      };

      expect(generateListingId(listing)).toBe('test-apartment-utrecht-€-1.500');
    });
  });

  describe('statistics and caching', () => {
    test('should provide accurate statistics', () => {
      const stats = scraperAutoApplicationIntegration.getStatistics();

      expect(stats).toHaveProperty('processedListingsCount');
      expect(stats).toHaveProperty('qualityScoreCacheSize');
      expect(stats).toHaveProperty('currentlyProcessingCount');
      expect(stats).toHaveProperty('cacheExpiryMs');
      expect(stats).toHaveProperty('minQualityScore');

      expect(typeof stats.processedListingsCount).toBe('number');
      expect(typeof stats.qualityScoreCacheSize).toBe('number');
      expect(typeof stats.currentlyProcessingCount).toBe('number');
      expect(typeof stats.cacheExpiryMs).toBe('number');
      expect(typeof stats.minQualityScore).toBe('number');
    });

    test('should clear caches correctly', () => {
      scraperAutoApplicationIntegration.clearCaches();
      
      const stats = scraperAutoApplicationIntegration.getStatistics();
      expect(stats.processedListingsCount).toBe(0);
      expect(stats.qualityScoreCacheSize).toBe(0);
    });
  });

  describe('error handling', () => {
    test('should handle errors in quality score calculation gracefully', async () => {
      const integration = scraperAutoApplicationIntegration;
      const calculateQualityScore = integration._calculateQualityScore.bind(integration);

      // Test with invalid listing data
      const invalidListing = null;
      const score = await calculateQualityScore(invalidListing);

      expect(score).toBe(0.5); // Should return neutral score
    });

    test('should handle errors in criteria matching gracefully', async () => {
      const integration = scraperAutoApplicationIntegration;
      const matchesUserCriteria = integration._matchesUserCriteria.bind(integration);

      const invalidUserSettings = null;
      const listing = { title: 'Test' };

      const matches = await matchesUserCriteria(listing, invalidUserSettings);
      expect(matches).toBe(false);
    });

    test('should handle errors in duplicate checking gracefully', async () => {
      const integration = scraperAutoApplicationIntegration;
      const checkForDuplicate = integration._checkForDuplicate.bind(integration);

      Listing.findOne.mockRejectedValue(new Error('Database error'));

      const listing = { url: 'https://example.com/test' };
      const result = await checkForDuplicate(listing);

      expect(result).toBeNull();
    });
  });
});