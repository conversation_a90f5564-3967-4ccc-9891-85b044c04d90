const { logHelpers } = require('../services/logger');

/**
 * Performance monitoring utility for tracking endpoint performance
 */
class PerformanceMonitor {
  constructor() {
    this.metrics = new Map();
    this.thresholds = {
      'quick-stats': 500, // 500ms threshold for quick stats
      'search': 2000,     // 2s threshold for search
      'default': 1000     // 1s default threshold
    };
  }

  /**
   * Start monitoring an operation
   * @param {string} operation - Operation name
   * @param {Object} metadata - Additional metadata
   * @returns {Object} Monitor instance
   */
  start(operation, metadata = {}) {
    const startTime = Date.now();
    const monitorId = `${operation}-${startTime}-${Math.random().toString(36).substr(2, 9)}`;
    
    const monitor = {
      operation,
      startTime,
      metadata,
      monitorId,
      
      // End monitoring and log results
      end: (additionalMetadata = {}) => {
        const duration = Date.now() - startTime;
        const threshold = this.thresholds[operation] || this.thresholds.default;
        
        const logData = {
          ...metadata,
          ...additionalMetadata,
          duration: `${duration}ms`,
          threshold: `${threshold}ms`,
          withinThreshold: duration <= threshold,
          monitorId
        };

        // Log performance metric
        logHelpers.logPerformance(operation, duration, logData);

        // Log warning if threshold exceeded
        if (duration > threshold) {
          logHelpers.logPerformance(`${operation}-slow`, duration, {
            ...logData,
            warning: `Operation exceeded threshold of ${threshold}ms`
          });
        }

        // Update metrics
        this.updateMetrics(operation, duration, threshold);

        return {
          duration,
          withinThreshold: duration <= threshold,
          threshold,
          metadata: logData
        };
      },

      // Add error information
      error: (error, additionalMetadata = {}) => {
        const duration = Date.now() - startTime;
        
        const logData = {
          ...metadata,
          ...additionalMetadata,
          duration: `${duration}ms`,
          error: error.message,
          stack: error.stack,
          monitorId
        };

        logHelpers.logPerformance(`${operation}-error`, duration, logData);
        
        this.updateMetrics(operation, duration, this.thresholds[operation] || this.thresholds.default, true);

        return {
          duration,
          error: error.message,
          metadata: logData
        };
      }
    };

    return monitor;
  }

  /**
   * Update internal metrics
   * @private
   */
  updateMetrics(operation, duration, threshold, isError = false) {
    if (!this.metrics.has(operation)) {
      this.metrics.set(operation, {
        count: 0,
        totalDuration: 0,
        errors: 0,
        slowRequests: 0,
        threshold
      });
    }

    const metric = this.metrics.get(operation);
    metric.count++;
    metric.totalDuration += duration;
    
    if (isError) {
      metric.errors++;
    }
    
    if (duration > threshold) {
      metric.slowRequests++;
    }

    metric.averageDuration = Math.round(metric.totalDuration / metric.count);
    metric.errorRate = (metric.errors / metric.count * 100).toFixed(2);
    metric.slowRequestRate = (metric.slowRequests / metric.count * 100).toFixed(2);
  }

  /**
   * Get metrics for an operation
   * @param {string} operation - Operation name
   * @returns {Object} Metrics data
   */
  getMetrics(operation) {
    return this.metrics.get(operation) || null;
  }

  /**
   * Get all metrics
   * @returns {Object} All metrics data
   */
  getAllMetrics() {
    const result = {};
    for (const [operation, metrics] of this.metrics.entries()) {
      result[operation] = { ...metrics };
    }
    return result;
  }

  /**
   * Reset metrics for an operation
   * @param {string} operation - Operation name
   */
  resetMetrics(operation) {
    if (operation) {
      this.metrics.delete(operation);
    } else {
      this.metrics.clear();
    }
  }

  /**
   * Set threshold for an operation
   * @param {string} operation - Operation name
   * @param {number} threshold - Threshold in milliseconds
   */
  setThreshold(operation, threshold) {
    this.thresholds[operation] = threshold;
  }

  /**
   * Express middleware for automatic performance monitoring
   * @param {string} operation - Operation name
   * @returns {Function} Express middleware
   */
  middleware(operation) {
    return (req, res, next) => {
      const monitor = this.start(operation, {
        method: req.method,
        url: req.originalUrl,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });

      // Store monitor in request for later use
      req.performanceMonitor = monitor;

      // Override res.json to capture response
      const originalJson = res.json;
      res.json = function(data) {
        const responseMetadata = {
          statusCode: res.statusCode,
          hasError: res.statusCode >= 400,
          responseSize: JSON.stringify(data).length
        };

        if (res.statusCode >= 400) {
          monitor.error(new Error(`HTTP ${res.statusCode}`), responseMetadata);
        } else {
          monitor.end(responseMetadata);
        }

        return originalJson.call(this, data);
      };

      next();
    };
  }
}

// Create singleton instance
const performanceMonitor = new PerformanceMonitor();

module.exports = {
  PerformanceMonitor,
  performanceMonitor
};