import { renderHook, act } from '@testing-library/react-native';
import { useNotificationStore, AppNotification, NotificationSettings } from '../../store/notificationStore';
import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';

// Mock expo-notifications
jest.mock('expo-notifications', () => ({
  setNotificationHandler: jest.fn(),
  getPermissionsAsync: jest.fn(),
  requestPermissionsAsync: jest.fn(),
  getExpoPushTokenAsync: jest.fn(),
  setNotificationChannelAsync: jest.fn(),
  scheduleNotificationAsync: jest.fn(),
  cancelScheduledNotificationAsync: jest.fn(),
  cancelAllScheduledNotificationsAsync: jest.fn(),
  getAllScheduledNotificationsAsync: jest.fn(),
  setBadgeCountAsync: jest.fn(),
  addNotificationReceivedListener: jest.fn(),
  addNotificationResponseReceivedListener: jest.fn(),
  DEFAULT_ACTION_IDENTIFIER: 'default',
  AndroidImportance: {
    MAX: 5,
    HIGH: 4,
  },
}));

// Mock expo-device
jest.mock('expo-device', () => ({
  isDevice: true,
}));

// Mock expo-constants
jest.mock('expo-constants', () => ({
  expoConfig: {
    extra: {
      eas: {
        projectId: 'test-project-id',
      },
    },
  },
}));

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
}));

// Mock Platform
jest.mock('react-native', () => ({
  Platform: {
    OS: 'ios',
  },
}));

const mockNotifications = Notifications as jest.Mocked<typeof Notifications>;
const mockDevice = Device as jest.Mocked<typeof Device>;

describe('Notification Store', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset store state
    useNotificationStore.getState().clearAllNotifications();
    useNotificationStore.getState().clearError();
  });

  describe('Initialization', () => {
    it('should initialize notifications successfully', async () => {
      mockDevice.isDevice = true;
      mockNotifications.requestPermissionsAsync.mockResolvedValue({
        status: 'granted',
        granted: true,
        canAskAgain: false,
        ios: {
          allowsAlert: true,
          allowsBadge: true,
          allowsSound: true,
        },
      } as any);
      mockNotifications.getExpoPushTokenAsync.mockResolvedValue({
        data: 'test-push-token',
      } as any);
      mockNotifications.addNotificationReceivedListener.mockReturnValue({
        remove: jest.fn(),
      } as any);
      mockNotifications.addNotificationResponseReceivedListener.mockReturnValue({
        remove: jest.fn(),
      } as any);

      const { result } = renderHook(() => useNotificationStore());

      await act(async () => {
        const success = await result.current.initializeNotifications();
        expect(success).toBe(true);
      });

      expect(result.current.pushToken).toBe('test-push-token');
      expect(result.current.permissionStatus?.granted).toBe(true);
      expect(result.current.isRegistering).toBe(false);
      expect(result.current.error).toBeNull();
    });

    it('should handle initialization failure on simulator', async () => {
      mockDevice.isDevice = false;

      const { result } = renderHook(() => useNotificationStore());

      await act(async () => {
        const success = await result.current.initializeNotifications();
        expect(success).toBe(false);
      });

      expect(result.current.isRegistering).toBe(false);
    });

    it('should handle permission denial', async () => {
      mockDevice.isDevice = true;
      mockNotifications.requestPermissionsAsync.mockResolvedValue({
        status: 'denied',
        granted: false,
        canAskAgain: true,
      } as any);

      const { result } = renderHook(() => useNotificationStore());

      await act(async () => {
        const success = await result.current.initializeNotifications();
        expect(success).toBe(false);
      });

      expect(result.current.error).toBe('Notification permissions not granted');
      expect(result.current.permissionStatus?.granted).toBe(false);
    });
  });

  describe('Permission Management', () => {
    it('should request permissions successfully', async () => {
      mockNotifications.requestPermissionsAsync.mockResolvedValue({
        status: 'granted',
        granted: true,
        canAskAgain: false,
        ios: {
          allowsAlert: true,
          allowsBadge: true,
          allowsSound: true,
        },
      } as any);

      const { result } = renderHook(() => useNotificationStore());

      await act(async () => {
        const permissions = await result.current.requestPermissions();
        expect(permissions.granted).toBe(true);
        expect(permissions.status).toBe('granted');
      });

      expect(result.current.permissionStatus?.granted).toBe(true);
    });

    it('should check permission status', async () => {
      mockNotifications.getPermissionsAsync.mockResolvedValue({
        status: 'granted',
        granted: true,
        canAskAgain: false,
      } as any);

      const { result } = renderHook(() => useNotificationStore());

      await act(async () => {
        const status = await result.current.checkPermissionStatus();
        expect(status.granted).toBe(true);
      });
    });
  });

  describe('Push Token Registration', () => {
    it('should register for push notifications successfully', async () => {
      mockDevice.isDevice = true;
      mockNotifications.getPermissionsAsync.mockResolvedValue({
        status: 'granted',
      } as any);
      mockNotifications.getExpoPushTokenAsync.mockResolvedValue({
        data: 'test-push-token',
      } as any);

      const { result } = renderHook(() => useNotificationStore());

      await act(async () => {
        const token = await result.current.registerForPushNotifications();
        expect(token).toBe('test-push-token');
      });

      expect(result.current.pushToken).toBe('test-push-token');
    });

    it('should handle push token registration failure', async () => {
      mockDevice.isDevice = false;

      const { result } = renderHook(() => useNotificationStore());

      await act(async () => {
        const token = await result.current.registerForPushNotifications();
        expect(token).toBeNull();
      });

      expect(result.current.error).toContain('Must use physical device');
    });
  });

  describe('Notification Handling', () => {
    it('should handle incoming notifications', () => {
      const { result } = renderHook(() => useNotificationStore());

      const testNotification: AppNotification = {
        id: 'test-notification-1',
        title: 'New Property Match',
        body: 'Found a great apartment for you!',
        category: 'property_match',
        priority: 'high',
        read: false,
        timestamp: new Date(),
      };

      act(() => {
        result.current.handleNotification(testNotification);
      });

      expect(result.current.notifications).toHaveLength(1);
      expect(result.current.notifications[0].title).toBe('New Property Match');
      expect(result.current.unreadCount).toBe(1);
    });

    it('should mark notifications as read', () => {
      const { result } = renderHook(() => useNotificationStore());

      const testNotification: AppNotification = {
        id: 'test-notification-1',
        title: 'Test Notification',
        body: 'Test body',
        category: 'system',
        priority: 'normal',
        read: false,
        timestamp: new Date(),
      };

      act(() => {
        result.current.handleNotification(testNotification);
      });

      expect(result.current.unreadCount).toBe(1);

      act(() => {
        result.current.markAsRead('test-notification-1');
      });

      expect(result.current.unreadCount).toBe(0);
      expect(result.current.notifications[0].read).toBe(true);
    });

    it('should mark all notifications as read', () => {
      const { result } = renderHook(() => useNotificationStore());

      const notifications: AppNotification[] = [
        {
          id: 'test-1',
          title: 'Test 1',
          body: 'Body 1',
          category: 'system',
          priority: 'normal',
          read: false,
          timestamp: new Date(),
        },
        {
          id: 'test-2',
          title: 'Test 2',
          body: 'Body 2',
          category: 'property_match',
          priority: 'high',
          read: false,
          timestamp: new Date(),
        },
      ];

      act(() => {
        notifications.forEach((notification) => {
          result.current.handleNotification(notification);
        });
      });

      expect(result.current.unreadCount).toBe(2);

      act(() => {
        result.current.markAllAsRead();
      });

      expect(result.current.unreadCount).toBe(0);
      expect(result.current.notifications.every((n) => n.read)).toBe(true);
    });

    it('should delete notifications', () => {
      const { result } = renderHook(() => useNotificationStore());

      const testNotification: AppNotification = {
        id: 'test-notification-1',
        title: 'Test Notification',
        body: 'Test body',
        category: 'system',
        priority: 'normal',
        read: false,
        timestamp: new Date(),
      };

      act(() => {
        result.current.handleNotification(testNotification);
      });

      expect(result.current.notifications).toHaveLength(1);

      act(() => {
        result.current.deleteNotification('test-notification-1');
      });

      expect(result.current.notifications).toHaveLength(0);
      expect(result.current.unreadCount).toBe(0);
    });
  });

  describe('Local Notifications', () => {
    it('should schedule local notifications', async () => {
      mockNotifications.scheduleNotificationAsync.mockResolvedValue('scheduled-notification-id');

      const { result } = renderHook(() => useNotificationStore());

      await act(async () => {
        const notificationId = await result.current.scheduleLocalNotification({
          id: 'local-1',
          title: 'Reminder',
          body: 'Check new listings',
          trigger: {
            type: 'time',
            date: new Date(Date.now() + 60000), // 1 minute from now
          },
          category: 'reminder',
          priority: 'normal',
        });

        expect(notificationId).toBe('scheduled-notification-id');
      });

      expect(result.current.scheduledNotifications).toHaveLength(1);
      expect(mockNotifications.scheduleNotificationAsync).toHaveBeenCalled();
    });

    it('should cancel local notifications', async () => {
      mockNotifications.scheduleNotificationAsync.mockResolvedValue('scheduled-notification-id');
      mockNotifications.cancelScheduledNotificationAsync.mockResolvedValue(undefined);

      const { result } = renderHook(() => useNotificationStore());

      await act(async () => {
        await result.current.scheduleLocalNotification({
          id: 'local-1',
          title: 'Reminder',
          body: 'Check new listings',
          trigger: {
            type: 'time',
            date: new Date(Date.now() + 60000),
          },
        });
      });

      expect(result.current.scheduledNotifications).toHaveLength(1);

      await act(async () => {
        const success = await result.current.cancelLocalNotification('scheduled-notification-id');
        expect(success).toBe(true);
      });

      expect(result.current.scheduledNotifications).toHaveLength(0);
      expect(mockNotifications.cancelScheduledNotificationAsync).toHaveBeenCalledWith('scheduled-notification-id');
    });
  });

  describe('Settings Management', () => {
    it('should update notification settings', async () => {
      const { result } = renderHook(() => useNotificationStore());

      const newSettings: Partial<NotificationSettings> = {
        enabled: false,
        batchNotifications: true,
        maxNotificationsPerHour: 5,
      };

      await act(async () => {
        const success = await result.current.updateSettings(newSettings);
        expect(success).toBe(true);
      });

      expect(result.current.settings.enabled).toBe(false);
      expect(result.current.settings.batchNotifications).toBe(true);
      expect(result.current.settings.maxNotificationsPerHour).toBe(5);
    });

    it('should update category settings', async () => {
      const { result } = renderHook(() => useNotificationStore());

      await act(async () => {
        const success = await result.current.updateCategorySettings('property_match', {
          enabled: false,
          sound: false,
          priority: 'low',
        });
        expect(success).toBe(true);
      });

      expect(result.current.settings.categories.property_match.enabled).toBe(false);
      expect(result.current.settings.categories.property_match.sound).toBe(false);
      expect(result.current.settings.categories.property_match.priority).toBe('low');
    });
  });

  describe('Quiet Hours', () => {
    it('should detect quiet hours correctly', () => {
      const { result } = renderHook(() => useNotificationStore());

      // Mock current time to be 23:00 (11 PM)
      const mockDate = new Date();
      mockDate.setHours(23, 0, 0, 0);
      jest.spyOn(global, 'Date').mockImplementation(() => mockDate as any);

      act(() => {
        result.current.updateSettings({
          quietHours: {
            enabled: true,
            start: '22:00',
            end: '08:00',
          },
        });
      });

      const isQuietTime = result.current.isInQuietHours();
      expect(isQuietTime).toBe(true);

      // Restore Date
      (global.Date as any).mockRestore();
    });

    it('should allow critical notifications during quiet hours', () => {
      const { result } = renderHook(() => useNotificationStore());

      // Mock current time to be in quiet hours
      const mockDate = new Date();
      mockDate.setHours(23, 0, 0, 0);
      jest.spyOn(global, 'Date').mockImplementation(() => mockDate as any);

      act(() => {
        result.current.updateSettings({
          quietHours: {
            enabled: true,
            start: '22:00',
            end: '08:00',
          },
          allowCriticalAlerts: true,
        });
      });

      const canSendCritical = result.current.canSendNotification('landlord_response', 'critical');
      const canSendNormal = result.current.canSendNotification('system', 'normal');

      expect(canSendCritical).toBe(true);
      expect(canSendNormal).toBe(false);

      // Restore Date
      (global.Date as any).mockRestore();
    });
  });

  describe('Notification Batching', () => {
    it('should batch notifications when enabled', () => {
      const { result } = renderHook(() => useNotificationStore());

      act(() => {
        result.current.updateSettings({
          batchNotifications: true,
        });
      });

      const testNotification: AppNotification = {
        id: 'test-batch-1',
        title: 'Test Notification',
        body: 'Test body',
        category: 'system',
        priority: 'normal',
        read: false,
        timestamp: new Date(),
      };

      // Mock shouldBatchNotification to return true
      jest.spyOn(result.current, 'shouldBatchNotification').mockReturnValue(true);

      act(() => {
        result.current.handleNotification(testNotification);
      });

      // Notification should be queued, not added directly
      expect(result.current.notificationQueue).toHaveLength(1);
      expect(result.current.notifications).toHaveLength(0);
    });
  });

  describe('Utility Functions', () => {
    it('should get notifications by category', () => {
      const { result } = renderHook(() => useNotificationStore());

      const notifications: AppNotification[] = [
        {
          id: 'test-1',
          title: 'Property Match',
          body: 'New match found',
          category: 'property_match',
          priority: 'high',
          read: false,
          timestamp: new Date(),
        },
        {
          id: 'test-2',
          title: 'System Update',
          body: 'App updated',
          category: 'system',
          priority: 'normal',
          read: false,
          timestamp: new Date(),
        },
      ];

      act(() => {
        notifications.forEach((notification) => {
          result.current.handleNotification(notification);
        });
      });

      const propertyNotifications = result.current.getNotificationsByCategory('property_match');
      const systemNotifications = result.current.getNotificationsByCategory('system');

      expect(propertyNotifications).toHaveLength(1);
      expect(systemNotifications).toHaveLength(1);
      expect(propertyNotifications[0].title).toBe('Property Match');
    });

    it('should get unread notifications', () => {
      const { result } = renderHook(() => useNotificationStore());

      const notifications: AppNotification[] = [
        {
          id: 'test-1',
          title: 'Unread 1',
          body: 'Body 1',
          category: 'system',
          priority: 'normal',
          read: false,
          timestamp: new Date(),
        },
        {
          id: 'test-2',
          title: 'Read 1',
          body: 'Body 2',
          category: 'system',
          priority: 'normal',
          read: true,
          timestamp: new Date(),
        },
      ];

      act(() => {
        notifications.forEach((notification) => {
          result.current.handleNotification(notification);
        });
      });

      const unreadNotifications = result.current.getUnreadNotifications();
      expect(unreadNotifications).toHaveLength(1);
      expect(unreadNotifications[0].title).toBe('Unread 1');
    });
  });
});