import { renderHook, act } from "@testing-library/react-native";
import { useAIStore } from "../../store/aiStore";
import { useAuthStore } from "../../store/authStore";
import { aiService } from "../../services/aiService";
import { autoApplicationService } from "../../services/autoApplicationService";
import { Listing } from "../../services/listingsService";
import { User, UserPreferences } from "../../services/authService";

// Mocks
jest.mock("../../services/aiService");
jest.mock("../../services/autoApplicationService");
jest.mock("../../services/autonomousRunner", () => ({
  autonomousRunner: {
    start: jest.fn(),
    stop: jest.fn(),
    runOnce: jest.fn(),
  },
}));

const mockAIService = aiService as jest.Mocked<typeof aiService>;
const mockAutoAppService = autoApplicationService as jest.Mocked<
  typeof autoApplicationService
>;

// Mock AsyncStorage
jest.mock("@react-native-async-storage/async-storage", () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
}));

// Mock data
const mockUser: User = {
  _id: "user123",
  email: "<EMAIL>",
  firstName: "John",
  lastName: "Doe",
  preferences: {
    minPrice: 800,
    maxPrice: 1500,
    minRooms: 2,
    propertyTypes: ["apartment"],
    preferredCities: ["Amsterdam"],
    matchThreshold: 70,
    prioritizeNewListings: true,
    includeSlightlyOverBudget: false,
    alertFrequency: "daily",
    quietHours: { start: "22:00", end: "08:00" },
  },
};

const mockPreferences: UserPreferences = {
  minPrice: 800,
  maxPrice: 1500,
  minRooms: 2,
  propertyTypes: ["apartment"],
  preferredCities: ["Amsterdam"],
  matchThreshold: 70,
  prioritizeNewListings: true,
  includeSlightlyOverBudget: false,
  alertFrequency: "daily",
  quietHours: { start: "22:00", end: "08:00" },
};

const mockListing: Listing = {
  _id: "listing123",
  title: "Beautiful Apartment in Amsterdam",
  price: "€1,200",
  location: "Amsterdam",
  size: "75m²",
  rooms: "2",
  propertyType: "apartment",
  images: ["image1.jpg"],
  url: "https://example.com/listing",
  source: "funda",
  description: "A beautiful apartment in the heart of Amsterdam",
  dateAdded: new Date(),
  lastUpdated: new Date(),
};

const mockMatchingResponse = {
  success: true,
  data: {
    matches: [
      {
        listing: mockListing,
        score: 0.85,
        reasons: ["Great location", "Within budget"],
        pros: ["Close to public transport", "Recently renovated"],
        cons: ["No parking"],
      },
    ],
    totalAnalyzed: 10,
    averageScore: 0.75,
    recommendations: ["Consider expanding search radius"],
  },
};

const mockApplicationResponse = {
  success: true,
  data: {
    message: "Dear landlord, I am very interested in your property...",
    subject: "Application for Beautiful Apartment in Amsterdam",
    template: "professional",
    personalizedElements: ["Mentioned user's profession"],
    tips: ["Follow up within 24 hours"],
    generatedAt: new Date().toISOString(),
  },
};

describe("AI Store", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Ensure auth store has a user for userId lookups
    useAuthStore.setState({ user: mockUser, isAuthenticated: true });
    // Reset AI store state
    useAIStore.getState().clearAllData();
  });

  describe("Property Matching", () => {
    it("should request property matching successfully", async () => {
      mockAIService.getPropertyMatches.mockResolvedValue(mockMatchingResponse);

      const { result } = renderHook(() => useAIStore());

      await act(async () => {
        const matches = await result.current.requestPropertyMatching(
          mockPreferences,
          mockUser
        );
        expect(matches).toHaveLength(1);
        expect(matches[0].score).toBe(0.85);
        expect(matches[0].listing.title).toBe(
          "Beautiful Apartment in Amsterdam"
        );
      });

      expect(result.current.matches).toHaveLength(1);
      expect(result.current.matchingInProgress).toBe(false);
      expect(result.current.matchingError).toBeNull();
      expect(result.current.lastMatchUpdate).toBeTruthy();
    });

    it("should handle property matching errors", async () => {
      const errorResponse = {
        success: false,
        message: "API Error",
      };
      mockAIService.getPropertyMatches.mockResolvedValue(errorResponse);

      const { result } = renderHook(() => useAIStore());

      await act(async () => {
        try {
          await result.current.requestPropertyMatching(
            mockPreferences,
            mockUser
          );
        } catch (error) {
          expect(error.message).toBe("API Error");
        }
      });

      expect(result.current.matchingError).toBe("API Error");
      expect(result.current.matchingInProgress).toBe(false);
      expect(result.current.matches).toHaveLength(0);
    });

    it("should mark match as viewed", async () => {
      mockAIService.getPropertyMatches.mockResolvedValue(mockMatchingResponse);

      const { result } = renderHook(() => useAIStore());

      await act(async () => {
        await result.current.requestPropertyMatching(mockPreferences, mockUser);
      });

      const matchId = result.current.matches[0].id;

      act(() => {
        result.current.markMatchViewed(matchId);
      });

      expect(result.current.matches[0].viewed).toBe(true);
    });

    it("should save and unsave matches", async () => {
      mockAIService.getPropertyMatches.mockResolvedValue(mockMatchingResponse);

      const { result } = renderHook(() => useAIStore());

      await act(async () => {
        await result.current.requestPropertyMatching(mockPreferences, mockUser);
      });

      const matchId = result.current.matches[0].id;

      act(() => {
        result.current.saveMatch(matchId);
      });

      expect(result.current.matches[0].saved).toBe(true);

      act(() => {
        result.current.unsaveMatch(matchId);
      });

      expect(result.current.matches[0].saved).toBe(false);
    });
  });

  describe("Application Generation", () => {
    it("should generate application successfully", async () => {
      mockAIService.generateApplication.mockResolvedValue(
        mockApplicationResponse
      );

      const { result } = renderHook(() => useAIStore());

      await act(async () => {
        const application = await result.current.generateApplication(
          mockListing,
          mockUser,
          "professional"
        );
        expect(application.content.message).toContain("Dear landlord");
        expect(application.status).toBe("draft");
        expect(application.submissionMethod).toBe("manual");
      });

      expect(result.current.applications).toHaveLength(1);
      expect(result.current.applicationInProgress).toBe(false);
      expect(result.current.applicationError).toBeNull();
    });

    it("should approve and submit applications", async () => {
      mockAIService.generateApplication.mockResolvedValue(
        mockApplicationResponse
      );

      const { result } = renderHook(() => useAIStore());

      await act(async () => {
        await result.current.generateApplication(mockListing, mockUser);
      });

      const applicationId = result.current.applications[0].id;

      await act(async () => {
        await result.current.approveApplication(applicationId);
      });

      expect(result.current.applications[0].status).toBe("approved");

      await act(async () => {
        await result.current.submitApplication(applicationId, "manual");
      });

      expect(result.current.applications[0].status).toBe("submitted");
      expect(result.current.applications[0].submittedAt).toBeTruthy();
    });

    it("should delete applications", async () => {
      mockAIService.generateApplication.mockResolvedValue(
        mockApplicationResponse
      );

      const { result } = renderHook(() => useAIStore());

      await act(async () => {
        await result.current.generateApplication(mockListing, mockUser);
      });

      const applicationId = result.current.applications[0].id;

      act(() => {
        result.current.deleteApplication(applicationId);
      });

      expect(result.current.applications).toHaveLength(0);
    });
  });

  describe("Autonomous Mode", () => {
    it("should update autonomous settings", async () => {
      const { result } = renderHook(() => useAIStore());

      await act(async () => {
        const success = await result.current.updateAutonomousSettings({
          enabled: true,
          autoApplyThreshold: 90,
          maxApplicationsPerDay: 3,
        });
        expect(success).toBe(true);
      });

      expect(result.current.autonomousSettings.enabled).toBe(true);
      expect(result.current.autonomousSettings.autoApplyThreshold).toBe(90);
      expect(result.current.autonomousSettings.maxApplicationsPerDay).toBe(3);
    });

    it("should call backend when updating autonomous settings and update local state", async () => {
      const { result } = renderHook(() => useAIStore());
      (mockAutoAppService.updateSettings as any).mockResolvedValue({
        success: true,
        data: {},
      });

      await act(async () => {
        const ok = await result.current.updateAutonomousSettings({
          enabled: true,
          autoApplyThreshold: 90,
          maxApplicationsPerDay: 3,
          autoApplyMaxPrice: 1800,
          autoApplyPropertyTypes: ["apartment"],
          defaultApplicationStyle: "professional",
        });
        expect(ok).toBe(true);
      });

      // Local state
      expect(result.current.autonomousSettings.enabled).toBe(true);
      expect(result.current.autonomousSettings.maxApplicationsPerDay).toBe(3);

      // Backend call shape
      expect(mockAutoAppService.updateSettings).toHaveBeenCalledTimes(1);
      const [uid, payload] = (mockAutoAppService.updateSettings as jest.Mock)
        .mock.calls[0];
      expect(uid).toBe("user123");
      expect(payload.settings?.maxApplicationsPerDay).toBe(3);
      expect(payload.criteria?.maxPrice).toBe(1800);
      expect(payload.criteria?.propertyTypes).toEqual(["apartment"]);
    });

    it("should call backend enable on start and disable on stop", async () => {
      const { result } = renderHook(() => useAIStore());

      (mockAutoAppService.enableAutoApplication as any).mockResolvedValue({
        success: true,
        data: {},
      });
      (mockAutoAppService.disableAutoApplication as any).mockResolvedValue({
        success: true,
      });

      await act(async () => {
        await result.current.startAutonomousMode();
      });

      expect(result.current.autonomousSettings.enabled).toBe(true);
      expect(mockAutoAppService.enableAutoApplication).toHaveBeenCalledTimes(1);
      const [uidEnable, payloadEnable] = (
        mockAutoAppService.enableAutoApplication as jest.Mock
      ).mock.calls[0];
      expect(uidEnable).toBe("user123");
      expect(payloadEnable?.enabled).toBe(true);

      await act(async () => {
        await result.current.stopAutonomousMode();
      });

      expect(result.current.autonomousSettings.enabled).toBe(false);
      expect(mockAutoAppService.disableAutoApplication).toHaveBeenCalledTimes(
        1
      );
      const [uidDisable] = (
        mockAutoAppService.disableAutoApplication as jest.Mock
      ).mock.calls[0];
      expect(uidDisable).toBe("user123");
    });

    it("should start and stop autonomous mode", async () => {
      const { result } = renderHook(() => useAIStore());

      await act(async () => {
        await result.current.startAutonomousMode();
      });

      expect(result.current.autonomousSettings.enabled).toBe(true);
      expect(result.current.autonomousStatus.isActive).toBe(true);
      expect(result.current.autonomousActivities).toHaveLength(1);
      expect(result.current.autonomousActivities[0].type).toBe("resumed");

      await act(async () => {
        await result.current.stopAutonomousMode();
      });

      expect(result.current.autonomousSettings.enabled).toBe(false);
      expect(result.current.autonomousStatus.isActive).toBe(false);
      expect(result.current.autonomousStatus.pausedReason).toBe(
        "Manually stopped"
      );
    });

    it("should pause and resume autonomous mode", async () => {
      const { result } = renderHook(() => useAIStore());

      await act(async () => {
        await result.current.startAutonomousMode();
      });

      await act(async () => {
        await result.current.pauseAutonomousMode("Testing pause");
      });

      expect(result.current.autonomousStatus.isActive).toBe(false);
      expect(result.current.autonomousStatus.pausedReason).toBe(
        "Testing pause"
      );

      await act(async () => {
        await result.current.resumeAutonomousMode();
      });

      expect(result.current.autonomousStatus.isActive).toBe(true);
      expect(result.current.autonomousStatus.pausedReason).toBeUndefined();
    });

    it("should track autonomous activities", () => {
      const { result } = renderHook(() => useAIStore());

      act(() => {
        result.current.addAutonomousActivity({
          type: "application_generated",
          listingId: "listing123",
          propertyTitle: "Test Property",
          message: "Application generated for Test Property",
          success: true,
        });
      });

      expect(result.current.autonomousActivities).toHaveLength(1);
      expect(result.current.autonomousActivities[0].type).toBe(
        "application_generated"
      );
      expect(result.current.autonomousActivities[0].message).toBe(
        "Application generated for Test Property"
      );

      act(() => {
        result.current.clearAutonomousActivities();
      });

      expect(result.current.autonomousActivities).toHaveLength(0);
    });
  });

  describe("Contract Analysis", () => {
    it("should analyze contract successfully", async () => {
      const mockAnalysisResponse = {
        success: true,
        data: {
          summary: "Standard rental contract",
          keyTerms: {
            rentAmount: "€1,200",
            deposit: "€2,400",
            duration: "12 months",
            noticePeriod: "1 month",
            utilities: "Included",
          },
          risks: {
            level: "low" as const,
            issues: [],
            recommendations: ["Review termination clause"],
          },
          legalCompliance: {
            compliant: true,
            violations: [],
            suggestions: [],
          },
          score: 85,
          confidence: 0.9,
        },
      };

      mockAIService.analyzeContract.mockResolvedValue(mockAnalysisResponse);

      const { result } = renderHook(() => useAIStore());

      await act(async () => {
        const analysis = await result.current.analyzeContract(
          { contractText: "Sample contract text" },
          "Test Contract",
          "listing123"
        );
        expect(analysis.analysis.summary).toBe("Standard rental contract");
        expect(analysis.analysis.score).toBe(85);
      });

      expect(result.current.contractAnalyses).toHaveLength(1);
      expect(result.current.analysisInProgress).toBe(false);
    });
  });

  describe("Market Insights", () => {
    it("should get market insights successfully", async () => {
      const mockInsightsResponse = {
        success: true,
        data: {
          location: "Amsterdam",
          averagePrice: 1400,
          priceRange: { min: 800, max: 2500 },
          marketTrend: "rising" as const,
          pricePrediction: "Prices expected to rise 5% next quarter",
          demandLevel: "high" as const,
          keyInsights: ["High demand in city center"],
          recommendations: ["Consider expanding search area"],
          confidenceScore: 0.85,
          dataPoints: 150,
        },
      };

      mockAIService.getMarketAnalysis.mockResolvedValue(mockInsightsResponse);

      const { result } = renderHook(() => useAIStore());

      await act(async () => {
        const insights = await result.current.getMarketInsights(
          "Amsterdam",
          mockPreferences
        );
        expect(insights.location).toBe("Amsterdam");
        expect(insights.analysis.averagePrice).toBe(1400);
        expect(insights.userSpecific).toBe(true);
      });

      expect(result.current.marketInsights).toHaveLength(1);
      expect(result.current.insightsInProgress).toBe(false);
    });
  });

  describe("Translation", () => {
    it("should translate text and cache results", async () => {
      const mockTranslationResponse = {
        success: true,
        data: {
          translatedText: "Hello world",
          sourceLanguage: "nl",
          targetLanguage: "en",
          confidence: 0.95,
        },
      };

      mockAIService.translateText.mockResolvedValue(mockTranslationResponse);

      const { result } = renderHook(() => useAIStore());

      await act(async () => {
        const translation = await result.current.translateText(
          "Hallo wereld",
          "en"
        );
        expect(translation.translatedText).toBe("Hello world");
        expect(translation.confidence).toBe(0.95);
      });

      // Test caching - second call should not hit the API
      mockAIService.translateText.mockClear();

      await act(async () => {
        const cachedTranslation = await result.current.translateText(
          "Hallo wereld",
          "en"
        );
        expect(cachedTranslation.translatedText).toBe("Hello world");
      });

      expect(mockAIService.translateText).not.toHaveBeenCalled();
    });
  });

  describe("Utility Functions", () => {
    it("should find items by ID", async () => {
      mockAIService.getPropertyMatches.mockResolvedValue(mockMatchingResponse);
      mockAIService.generateApplication.mockResolvedValue(
        mockApplicationResponse
      );

      const { result } = renderHook(() => useAIStore());

      await act(async () => {
        await result.current.requestPropertyMatching(mockPreferences, mockUser);
        await result.current.generateApplication(mockListing, mockUser);
      });

      const matchId = result.current.matches[0].id;
      const applicationId = result.current.applications[0].id;

      const foundMatch = result.current.getMatchById(matchId);
      const foundApplication = result.current.getApplicationById(applicationId);

      expect(foundMatch).toBeTruthy();
      expect(foundMatch?.id).toBe(matchId);
      expect(foundApplication).toBeTruthy();
      expect(foundApplication?.id).toBe(applicationId);
    });

    it("should clear errors", () => {
      const { result } = renderHook(() => useAIStore());

      // Set some errors manually
      act(() => {
        result.current.clearError();
      });

      expect(result.current.error).toBeNull();
      expect(result.current.matchingError).toBeNull();
      expect(result.current.applicationError).toBeNull();
      expect(result.current.analysisError).toBeNull();
      expect(result.current.insightsError).toBeNull();
    });

    it("should clear all data", () => {
      const { result } = renderHook(() => useAIStore());

      act(() => {
        result.current.clearAllData();
      });

      expect(result.current.matches).toHaveLength(0);
      expect(result.current.applications).toHaveLength(0);
      expect(result.current.contractAnalyses).toHaveLength(0);
      expect(result.current.marketInsights).toHaveLength(0);
      expect(result.current.autonomousActivities).toHaveLength(0);
      expect(result.current.lastMatchUpdate).toBeNull();
    });
  });
});
