# Requirements Document

## Introduction

The backend needs to provide quick statistics for the dashboard including total listings count, average price, and new listings added today. This feature will enhance the user experience by providing immediate insights into the property market data available in the system.

## Requirements

### Requirement 1

**User Story:** As a dashboard user, I want to see the total number of listings available, so that I can understand the scope of properties in the system.

#### Acceptance Criteria

1. WHEN the dashboard loads THEN the system SHALL display the total count of all active listings
2. WHEN listings are added or removed THEN the total count SHALL be updated in real-time
3. IF no listings exist THEN the system SHALL display 0 as the total count

### Requirement 2

**User Story:** As a dashboard user, I want to see the average price of all listings, so that I can understand the general price range in the market.

#### Acceptance Criteria

1. WHEN the dashboard loads THEN the system SHALL calculate and display the average price of all active listings
2. WHEN calculating average price THEN the system SHALL exclude listings with invalid or missing price data
3. IF no valid prices exist THEN the system SHALL display 0 or "-" as the average price
4. WHEN displaying the average price THEN the system SHALL format it as a currency value in euros

### Requirement 3

**User Story:** As a dashboard user, I want to see how many new listings were added today, so that I can stay informed about fresh market opportunities.

#### Acceptance Criteria

1. WHEN the dashboard loads THEN the system SHALL count and display listings added within the current day
2. WHEN determining "today" THEN the system SHALL use the server's local timezone
3. WHEN counting new listings THEN the system SHALL use the listing's dateAdded field
4. IF no listings were added today THEN the system SHALL display 0

### Requirement 4

**User Story:** As a frontend developer, I want a dedicated API endpoint for quick stats, so that I can efficiently fetch dashboard statistics without loading all listing data.

#### Acceptance Criteria

1. WHEN the frontend requests quick stats THEN the system SHALL provide a dedicated API endpoint
2. WHEN the API is called THEN the system SHALL return all three statistics in a single response
3. WHEN the API responds THEN the system SHALL use a consistent JSON structure
4. WHEN the API encounters errors THEN the system SHALL return appropriate HTTP status codes and error messages

### Requirement 5

**User Story:** As a system administrator, I want the stats calculation to be performant, so that dashboard loading remains fast even with large datasets.

#### Acceptance Criteria

1. WHEN calculating stats THEN the system SHALL use efficient database queries
2. WHEN possible THEN the system SHALL use database aggregation functions rather than loading all records
3. WHEN the stats endpoint is called THEN the system SHALL respond within 500ms under normal load
4. IF the calculation takes longer than expected THEN the system SHALL implement appropriate caching strategies