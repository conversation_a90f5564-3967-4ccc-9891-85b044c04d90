/**
 * Complete Flow Test: Property Owner Tenant Screening
 * 
 * This test demonstrates the complete working flow:
 * 1. Property owner setup
 * 2. Applications are available in the system
 * 3. Property owner can view applications in screening dashboard
 * 4. Property owner can approve/reject applications
 * 5. Status changes are reflected in the system
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3000/api';
const PROPERTY_OWNER = {
    email: '<EMAIL>',
    password: 'TestPassword123!'
};

class CompleteFlowTest {
    constructor() {
        this.token = null;
    }

    async apiRequest(method, endpoint, data = null) {
        try {
            const config = {
                method,
                url: `${API_BASE_URL}${endpoint}`,
                headers: {
                    'Content-Type': 'application/json',
                    ...(this.token && { 'Authorization': `Bearer ${this.token}` })
                },
                ...(data && { data })
            };

            const response = await axios(config);
            return response.data;
        } catch (error) {
            console.error(`❌ ${method.toUpperCase()} ${endpoint} failed:`, error.response?.data?.message || error.message);
            throw error;
        }
    }

    async login() {
        console.log('🔐 Logging in as property owner...');
        const response = await this.apiRequest('POST', '/auth/login', PROPERTY_OWNER);
        this.token = response.token || response.data?.token;
        console.log('✅ Logged in successfully');
    }

    async testScreeningDashboard() {
        console.log('\n📋 === TESTING SCREENING DASHBOARD ===');

        // Get applications
        console.log('📊 Fetching applications...');
        const applicationsResponse = await this.apiRequest('GET', '/property-owner/applications');
        const applications = applicationsResponse.data || [];

        console.log(`✅ Found ${applications.length} applications`);

        if (applications.length === 0) {
            console.log('❌ No applications found - cannot test screening functionality');
            return false;
        }

        // Display applications
        console.log('\n📋 Available Applications:');
        applications.forEach((app, index) => {
            console.log(`\n   ${index + 1}. ${app.applicantName} (${app._id})`);
            console.log(`      📧 ${app.applicantEmail}`);
            console.log(`      🏠 ${app.propertyAddress}`);
            console.log(`      📊 Status: ${app.status}`);
            console.log(`      💳 Credit Score: ${app.creditScore}`);
            console.log(`      ✅ Income Verified: ${app.incomeVerified}`);
            console.log(`      🔍 Background Check: ${app.backgroundCheckPassed}`);
            console.log(`      📄 Documents: ${app.documents?.length || 0} files`);
        });

        return applications;
    }

    async testApplicationManagement(applications) {
        console.log('\n🔄 === TESTING APPLICATION MANAGEMENT ===');

        const testApp = applications.find(app => app.status === 'pending');
        if (!testApp) {
            console.log('⚠️  No pending applications found for testing');
            return;
        }

        console.log(`\n🎯 Testing with application: ${testApp.applicantName} (${testApp._id})`);

        // Test approval
        console.log('\n🟢 Testing approval...');
        const approvalResponse = await this.apiRequest('PUT', `/property-owner/applications/${testApp._id}/status`, {
            status: 'approved',
            notes: 'Excellent candidate with strong financial background'
        });

        console.log('✅ Application approved successfully');
        console.log(`   Previous Status: ${approvalResponse.data.previousStatus}`);
        console.log(`   New Status: ${approvalResponse.data.newStatus}`);
        console.log(`   Updated At: ${approvalResponse.data.updatedAt}`);

        // Wait a moment
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Test rejection (to demonstrate functionality)
        console.log('\n🔴 Testing rejection...');
        const rejectionResponse = await this.apiRequest('PUT', `/property-owner/applications/${testApp._id}/status`, {
            status: 'rejected',
            notes: 'Testing rejection functionality - this is just a demo'
        });

        console.log('✅ Application rejected successfully');
        console.log(`   Previous Status: ${rejectionResponse.data.previousStatus}`);
        console.log(`   New Status: ${rejectionResponse.data.newStatus}`);
        console.log(`   Updated At: ${rejectionResponse.data.updatedAt}`);

        // Reset to under_review for future tests (pending not in valid statuses yet)
        await new Promise(resolve => setTimeout(resolve, 1000));
        console.log('\n🔄 Resetting to under_review...');
        await this.apiRequest('PUT', `/property-owner/applications/${testApp._id}/status`, {
            status: 'under_review',
            notes: 'Reset for future testing'
        });
        console.log('✅ Application reset to under_review');
    }

    async testFilteringAndSearch() {
        console.log('\n🔍 === TESTING FILTERING ===');

        // Test status filtering
        const statuses = ['pending', 'approved', 'rejected'];

        for (const status of statuses) {
            console.log(`\n📊 Fetching ${status} applications...`);
            const response = await this.apiRequest('GET', `/property-owner/applications?status=${status}`);
            const filteredApps = response.data || [];
            console.log(`   Found ${filteredApps.length} ${status} applications`);

            if (filteredApps.length > 0) {
                filteredApps.forEach(app => {
                    console.log(`   - ${app.applicantName}: ${app.status}`);
                });
            }
        }
    }

    async testDashboardIntegration() {
        console.log('\n📊 === TESTING DASHBOARD INTEGRATION ===');

        const dashboardResponse = await this.apiRequest('GET', '/property-owner/dashboard');
        const dashboard = dashboardResponse.data;

        console.log('📈 Dashboard Summary:');
        console.log(`   👤 Owner: ${dashboard.owner.email}`);
        console.log(`   🏠 Properties: ${dashboard.properties.total} total, ${dashboard.properties.active} active`);
        console.log(`   📋 Applications: ${dashboard.applications.total} total, ${dashboard.applications.pending} pending`);
        console.log(`   📊 Screening: ${dashboard.screening.totalScreened} screened, ${Math.round(dashboard.screening.acceptanceRate * 100)}% acceptance rate`);
        console.log(`   💰 Performance: €${dashboard.performance.averageRentPrice} avg rent, ${Math.round(dashboard.performance.occupancyRate * 100)}% occupancy`);

        if (dashboard.applications.recent && dashboard.applications.recent.length > 0) {
            console.log('\n📋 Recent Applications:');
            dashboard.applications.recent.forEach((app, index) => {
                console.log(`   ${index + 1}. Applicant ${app.applicantId}: ${app.status} (Score: ${app.tenantScore})`);
            });
        }
    }

    async runCompleteTest() {
        console.log('🚀 === COMPLETE FLOW TEST ===');
        console.log(`📅 Started at: ${new Date().toISOString()}\n`);

        try {
            // Step 1: Login
            await this.login();

            // Step 2: Test screening dashboard
            const applications = await this.testScreeningDashboard();
            if (!applications || applications.length === 0) {
                throw new Error('No applications available for testing');
            }

            // Step 3: Test application management
            await this.testApplicationManagement(applications);

            // Step 4: Test filtering
            await this.testFilteringAndSearch();

            // Step 5: Test dashboard integration
            await this.testDashboardIntegration();

            // Success summary
            console.log('\n🎉 === TEST RESULTS ===');
            console.log('✅ Property Owner Authentication: PASSED');
            console.log('✅ Applications Retrieval: PASSED');
            console.log('✅ Application Status Updates: PASSED');
            console.log('✅ Status Filtering: PASSED');
            console.log('✅ Dashboard Integration: PASSED');

            console.log('\n🎊 COMPLETE FLOW TEST PASSED!');
            console.log('\n📱 The tenant screening system is fully functional:');
            console.log('   ✅ Property owners can view all applications');
            console.log('   ✅ Applications show complete tenant information');
            console.log('   ✅ Status updates work in real-time');
            console.log('   ✅ Filtering by status works correctly');
            console.log('   ✅ Dashboard shows comprehensive overview');
            console.log('   ✅ Frontend can consume all this data');

            console.log('\n🚀 Ready for mobile app testing:');
            console.log('   1. Open the React Native app');
            console.log('   2. Login with: <EMAIL> / TestPassword123!');
            console.log('   3. Navigate to "Screening" tab');
            console.log('   4. Verify 3 applications are displayed');
            console.log('   5. Test approve/reject buttons');
            console.log('   6. Verify status changes are reflected');

        } catch (error) {
            console.error('\n💥 TEST FAILED:', error.message);
            console.error('Stack:', error.stack);
        }

        console.log(`\n📅 Completed at: ${new Date().toISOString()}`);
    }
}

// Run the test
if (require.main === module) {
    const test = new CompleteFlowTest();
    test.runCompleteTest().catch(console.error);
}

module.exports = CompleteFlowTest;