const fs = require('fs');
const path = require('path');
const { browserPool, setupPageStealth } = require('./src/services/scraperUtils');

async function delay(ms) { return new Promise(r => setTimeout(r, ms)); }

(async () => {
  const propertyUrl = process.env.PROPERTY_URL || 'https://www.funda.nl/detail/huur/utrecht/appartement-eendrachtlaan-46-d/43728488/';
  const firstName = process.env.FIRST_NAME || 'Wellis';
  const lastName = process.env.LAST_NAME || 'Hant';
  const email = process.env.EMAIL || '<EMAIL>';
  const phone = process.env.PHONE || '030 686 62 00';

  const message = (process.env.MESSAGE && process.env.MESSAGE.trim().length > 0)
    ? process.env.MESSAGE
    : `<PERSON><PERSON> verhuurder,\n\nIk ben zeer geïnteresseerd in deze woning en zou graag meer informatie ontvangen over de bezichtigingsmogelijkheden en beschikbaarheid.\n\nAlvast hartelijk dank.\n\nMet vriendelijke groet,\n${firstName} ${lastName}`;

  const artifactsDir = path.join(__dirname, 'artifacts');
  try { fs.mkdirSync(artifactsDir, { recursive: true }); } catch {}

  let browser, page;
  try {
    browser = await browserPool.getBrowser();
    page = await browser.newPage();

    await setupPageStealth(page);
    await page.setExtraHTTPHeaders({
      'Accept-Language': 'en-US,en;q=0.9,nl;q=0.8',
      'Accept-Encoding': 'gzip, deflate, br',
      'Upgrade-Insecure-Requests': '1'
    });

    console.log('Navigating to listing...');
    await page.goto(propertyUrl, { waitUntil: 'networkidle2', timeout: 60000 });
    await delay(1500);
    await page.screenshot({ path: path.join(artifactsDir, '1_listing.png'), fullPage: true });

    // Accept cookies if present
    try {
      await page.waitForSelector('[data-testid="accept-all-cookies-button"], .cookie-accept-all, #accept-cookies', { timeout: 5000 });
      await page.click('[data-testid="accept-all-cookies-button"], .cookie-accept-all, #accept-cookies');
      console.log('Accepted cookie banner');
      await delay(800);
    } catch {}

    // Find contact button
    const contactSelectors = [
      '[data-optimizely="contact-email"]',
      'a[href*="makelaar-contact"]',
      '[data-testid="contact-button"]',
      '[data-testid="contact-form-button"]',
      '[data-testid="broker-contact-button"]',
      '.makelaar-contact-button', '.broker-contact', '.fd-button--contact',
      '.contact-button', 'button[data-testid*="contact"]'
    ];

    let contactButton = null;
    for (const sel of contactSelectors) {
      try {
        await page.waitForSelector(sel, { timeout: 2000 });
        contactButton = await page.$(sel);
        if (contactButton) { console.log('Found contact button:', sel); break; }
      } catch {}
    }

    if (!contactButton) {
      // Fallback by text/href
      const handle = await page.evaluateHandle(() => {
        const links = Array.from(document.querySelectorAll('a'));
        return links.find(link =>
          (link.textContent && (link.textContent.includes('Neem contact op') || link.textContent.includes('Contact')))
          || ((link.getAttribute('href') || '').includes('makelaar-contact'))
        );
      });
      if (handle && handle.asElement()) {
        contactButton = handle.asElement();
        console.log('Found contact button via text/href fallback');
      }
    }

    if (!contactButton) throw new Error('Contact button not found');

    await contactButton.click();
    await delay(2500);
    await page.screenshot({ path: path.join(artifactsDir, '2_form_open.png'), fullPage: true });

    // Fill fields helper
    async function fill(selector, value) {
      try {
        await page.waitForSelector(selector, { timeout: 4000 });
        const el = await page.$(selector);
        if (!el) return false;
        await page.evaluate(node => { try { node.value = ''; } catch(e) {} }, el);
        await el.type(value, { delay: 50 });
        return true;
      } catch { return false; }
    }

    // Message
    await (async () => {
      const sels = ['#questionInput','textarea[aria-labelledby="questionInput-label"]','textarea[data-testid*="message"]','textarea'];
      for (const s of sels) { if (await fill(s, message)) { console.log('Filled message'); break; } }
    })();

    // Email
    await (async () => {
      const sels = ['#emailAddress','input[type="email"]','input[name="email"]','input[data-testid*="email"]'];
      for (const s of sels) { if (await fill(s, email)) { console.log('Filled email'); break; } }
    })();

    // First/Last name
    await fill('#firstName', firstName);
    await fill('#lastName', lastName);

    // Phone
    await (async () => {
      const sels = ['#phoneNumber','input[type="tel"]','input[name="phone"]','input[name="telephone"]'];
      for (const s of sels) { if (await fill(s, phone)) { console.log('Filled phone'); break; } }
    })();

    // Viewing request checkbox
    try {
      const cb = await page.$('#checkbox-viewingRequest');
      if (cb) {
        const isChecked = await page.evaluate(el => el.checked, cb);
        if (!isChecked) { await cb.click(); console.log('Checked viewing request'); }
      }
    } catch {}

    await delay(800);
    await page.screenshot({ path: path.join(artifactsDir, '3_filled.png'), fullPage: true });

    // Submit
    let submitted = false;
    try {
      const submitButtonHandle = await page.evaluateHandle(() => {
        const texts = ['Verstuur', 'Verzenden', 'Send message', 'Versturen'];
        const buttons = Array.from(document.querySelectorAll('button'));
        return buttons.find(btn => texts.some(t => (btn.textContent || '').trim().includes(t)) || btn.type === 'submit') || null;
      });
      if (submitButtonHandle && submitButtonHandle.asElement()) {
        const btn = submitButtonHandle.asElement();
        await btn.click();
        submitted = true;
        console.log('Clicked submit');
      }
    } catch {}

    if (!submitted) {
      // Fallback: any form button
      try {
        const btn = await page.$('form button');
        if (btn) { await btn.click(); submitted = true; console.log('Clicked generic form button'); }
      } catch {}
    }

    await delay(3000);
    await page.screenshot({ path: path.join(artifactsDir, '4_submitted.png'), fullPage: true });
    console.log('Done. Submitted:', submitted);
  } catch (e) {
    console.error('Error in docker runner:', e);
  } finally {
    try { if (page) await page.close(); } catch {}
    try { if (browser) await browser.close(); } catch {}
  }
})();

