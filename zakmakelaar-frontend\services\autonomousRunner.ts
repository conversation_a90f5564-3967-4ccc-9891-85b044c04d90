import { useAuthStore } from "../store/authStore";
import { Listing } from "./listingsService";

// Lightweight orchestrator that periodically fetches matches and (optionally)
// generates/submits applications under autonomous mode constraints.
// It does NOT import the AI store directly to avoid circular dependencies.
// The store passes the required closures when starting the runner.

export type AutonomousRunnerDeps = {
  getState: () => {
    autonomousSettings: {
      enabled: boolean;
      autoApplyMinMatchScore: number; // 0-100
      autoApplyMaxPrice: number;
      defaultApplicationStyle:
        | "professional"
        | "personal"
        | "creative"
        | "student"
        | "expat";
      autoApplyPropertyTypes: string[];
    };
    autonomousStatus: {
      isActive: boolean;
    };
    applications: Array<{
      id: string;
      listingId: string;
      status: "draft" | "approved" | "submitted" | "responded";
    }>;
  };
  actions: {
    requestPropertyMatching: (preferences: any, user: any) => Promise<any[]>;
    generateApplication: (
      listing: Listing,
      user: any,
      style?: "professional" | "personal" | "creative" | "student" | "expat",
      customMessage?: string
    ) => Promise<{ id: string; listingId: string }>;
    submitApplication: (
      applicationId: string,
      method: "manual" | "autonomous"
    ) => Promise<boolean>;
    addAutonomousActivity: (activity: {
      type:
        | "application_generated"
        | "application_submitted"
        | "match_found"
        | "error"
        | "paused"
        | "resumed";
      listingId?: string;
      propertyTitle?: string;
      message: string;
      success: boolean;
      details?: any;
    }) => void;
    enforceApplicationLimits: () => Promise<boolean>;
    checkBudgetOverride: (
      propertyPrice: number,
      userMaxBudget: number
    ) => boolean;
    shouldRequireConfirmation: (
      propertyPrice: number,
      userMaxBudget: number
    ) => boolean;
  };
  pollMs?: number; // default 120000 (2 min)
};

class AutonomousRunner {
  private intervalId: NodeJS.Timeout | null = null;
  private running = false;
  private deps: AutonomousRunnerDeps | null = null;

  start(deps: AutonomousRunnerDeps) {
    if (this.running) return;
    this.deps = deps;
    this.running = true;

    // Kick an immediate run, then schedule periodic runs
    this.runOnce().catch((e) =>
      console.error("AutonomousRunner initial run error:", e)
    );

    const period = deps.pollMs ?? 120000; // 2 minutes by default
    this.intervalId = setInterval(() => {
      this.runOnce().catch((e) =>
        console.error("AutonomousRunner run error:", e)
      );
    }, period);
  }

  stop() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    this.running = false;
    this.deps = null;
  }

  // Expose a single-run method for background tasks to call
  async runOnce() {
    if (!this.deps) return;

    const { getState, actions } = this.deps;
    const state = getState();

    // Only run when autonomous is active and enabled
    if (!state.autonomousStatus.isActive || !state.autonomousSettings.enabled)
      return;

    const { user } = useAuthStore.getState();
    if (!user || !user.preferences) return;

    // Fetch/refresh matches
    let matches: any[] = [];
    try {
      matches = await actions.requestPropertyMatching(user.preferences, user);
      if (Array.isArray(matches) && matches.length > 0) {
        actions.addAutonomousActivity({
          type: "match_found",
          message: `Found ${matches.length} matches in background`,
          success: true,
        });
      }
    } catch (error: any) {
      actions.addAutonomousActivity({
        type: "error",
        message: `Failed to fetch matches: ${
          error?.message || "Unknown error"
        }`,
        success: false,
        details: { error },
      });
      return; // Don't attempt apply on error
    }

    const settings = state.autonomousSettings;

    // Normalize threshold to 0..1
    const minScore =
      Math.max(0, Math.min(100, settings.autoApplyMinMatchScore)) / 100;

    // Helper: parse price to number if possible
    const parsePrice = (p: any): number | null => {
      if (p == null) return null;
      if (typeof p === "number") return p;
      if (typeof p === "string") {
        const digits = p.replace(/[^0-9.]/g, "");
        const n = Number(digits);
        return isNaN(n) ? null : n;
      }
      return null;
    };

    // Pick first eligible match to apply (avoid bursts)
    const eligible = matches.find((m: any) => {
      const scoreOk = typeof m.score === "number" ? m.score >= minScore : false;
      const listing = m.listing as Listing | undefined;
      if (!listing) return false;

      const rawPrice = (listing as any).price;
      const price = parsePrice(rawPrice);
      const priceOk = price == null || price <= settings.autoApplyMaxPrice;

      const typeOk =
        settings.autoApplyPropertyTypes.length === 0 ||
        (listing.propertyType &&
          settings.autoApplyPropertyTypes.some((t) =>
            listing.propertyType!.toLowerCase().includes(t.toLowerCase())
          ));

      // Skip if already submitted for this listing
      const already = state.applications.some(
        (a) =>
          a.listingId === (listing as any)._id &&
          (a.status === "submitted" || a.status === "responded")
      );

      return scoreOk && priceOk && typeOk && !already;
    });

    if (!eligible) return; // Nothing to do this run

    // Safety: enforce limits before applying
    const okToProceed = await actions.enforceApplicationLimits();
    if (!okToProceed) return;

    const listing: Listing = eligible.listing;
    const rawPrice = (listing as any).price;
    const price = parsePrice(rawPrice) ?? 0;
    const userBudget = useAuthStore.getState().user?.preferences?.maxPrice;

    // Budget override check
    if (typeof userBudget === "number") {
      const withinOverride = actions.checkBudgetOverride(price, userBudget);
      if (!withinOverride) {
        actions.addAutonomousActivity({
          type: "match_found",
          listingId: (listing as any)._id,
          propertyTitle: (listing as any).title,
          message: `Skipped (over budget override): ${(listing as any).title}`,
          success: true,
        });
        return;
      }

      // Require confirmation for expensive cases
      const needsConfirmation = actions.shouldRequireConfirmation(
        price,
        userBudget
      );
      if (needsConfirmation) {
        actions.addAutonomousActivity({
          type: "match_found",
          listingId: (listing as any)._id,
          propertyTitle: (listing as any).title,
          message: `Requires manual confirmation: ${(listing as any).title}`,
          success: true,
        });
        return;
      }
    }

    try {
      // Generate
      const app = await actions.generateApplication(
        listing,
        user,
        settings.defaultApplicationStyle
      );

      actions.addAutonomousActivity({
        type: "application_generated",
        listingId: (listing as any)._id,
        propertyTitle: (listing as any).title,
        message: `Generated application for ${(listing as any).title}`,
        success: true,
      });

      // Submit autonomously
      const submitted = await actions.submitApplication(app.id, "autonomous");
      actions.addAutonomousActivity({
        type: "application_submitted",
        listingId: (listing as any)._id,
        propertyTitle: (listing as any).title,
        message: submitted
          ? `Submitted application for ${(listing as any).title}`
          : `Failed to submit application for ${(listing as any).title}`,
        success: submitted,
      });
    } catch (error: any) {
      actions.addAutonomousActivity({
        type: "error",
        listingId: (listing as any)._id,
        propertyTitle: (listing as any).title,
        message: `Autonomous application failed: ${
          error?.message || "Unknown error"
        }`,
        success: false,
        details: { error },
      });
    }
  }
}

export const autonomousRunner = new AutonomousRunner();
export default autonomousRunner;
