/**
 * Auto Application Workflow Integration Test
 * Run this script from the backend folder
 */

// Load environment variables
require("dotenv").config();

// Import workflow and related models
const applicationWorkflow = require("./src/services/applicationSubmissionWorkflow");
const ApplicationQueue = require("./src/models/ApplicationQueue");
const AutoApplicationSettings = require("./src/models/AutoApplicationSettings");
const ApplicationResult = require("./src/models/ApplicationResult");
const User = require("./src/models/User");
const path = require("path");
const fs = require("fs");
const { v4: uuidv4 } = require("uuid");

// Configure logging
const logger = console;
const LOG_FILE = path.join(__dirname, "workflow-test.log");
const writeLog = (message) => {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}\n`;
  fs.appendFileSync(LOG_FILE, logMessage);
  logger.log(message);
};

// Test configuration
const TEST_CONFIG = {
  // Test listing URL - replace with a real rental listing URL
  testListingUrl:
    "https://www.pararius.com/apartment-for-rent/amsterdam/example-listing",

  // Test user ID - generate a random one if needed
  testUserId: "60a2b3c4d5e6f7g8h9i0j1k2",

  // Test timeout in milliseconds (5 minutes)
  testTimeout: 5 * 60 * 1000,

  // Screenshot directory
  screenshotDir: path.join(__dirname, "test-screenshots"),
};

// Ensure screenshot directory exists
if (!fs.existsSync(TEST_CONFIG.screenshotDir)) {
  fs.mkdirSync(TEST_CONFIG.screenshotDir, { recursive: true });
}

/**
 * Create or update test user settings
 */
async function setupTestUserSettings() {
  writeLog("Setting up test user settings...");

  // Create or find test user
  let user = await User.findById(TEST_CONFIG.testUserId);
  if (!user) {
    user = new User({
      _id: TEST_CONFIG.testUserId,
      email: "<EMAIL>",
      name: "Test Auto Application User",
      role: "tester",
    });
    await user.save();
    writeLog(`Created test user with ID: ${user._id}`);
  }

  // Create or update auto application settings
  let settings = await AutoApplicationSettings.findOne({
    userId: TEST_CONFIG.testUserId,
  });
  if (!settings) {
    settings = new AutoApplicationSettings({
      userId: TEST_CONFIG.testUserId,
      enabled: true,
      personalInfo: {
        fullName: "Test User",
        email: "<EMAIL>",
        phone: "+31612345678",
        dateOfBirth: "1990-01-01",
        nationality: "Dutch",
        occupation: "Software Engineer",
        monthlyIncome: 4500,
        employmentType: "permanent",
        currentAddress: "Test Street 123, Amsterdam",
        movingReason: "Looking for a larger apartment",
      },
      documents: [
        {
          type: "id",
          name: "ID Card",
          filename: "test-id-card.pdf",
          path: path.join(__dirname, "test-data", "test-id-card.pdf"),
          uploaded: true,
          required: true,
        },
        {
          type: "income",
          name: "Income Statement",
          filename: "test-income-statement.pdf",
          path: path.join(__dirname, "test-data", "test-income-statement.pdf"),
          uploaded: true,
          required: true,
        },
        {
          type: "employment",
          name: "Employment Contract",
          filename: "test-employment-contract.pdf",
          path: path.join(
            __dirname,
            "test-data",
            "test-employment-contract.pdf"
          ),
          uploaded: true,
          required: true,
        },
      ],
      settings: {
        maxApplicationsPerDay: 10,
        autoApplyEnabled: true,
        preferredLocations: ["Amsterdam", "Utrecht"],
        maxRent: 2000,
        minRooms: 2,
      },
    });
  } else {
    // Update existing settings to ensure they're valid for testing
    settings.enabled = true;
    settings.personalInfo = {
      ...settings.personalInfo,
      fullName: settings.personalInfo?.fullName || "Test User",
      email:
        settings.personalInfo?.email || "<EMAIL>",
      phone: settings.personalInfo?.phone || "+31612345678",
      dateOfBirth: settings.personalInfo?.dateOfBirth || "1990-01-01",
      nationality: settings.personalInfo?.nationality || "Dutch",
      occupation: settings.personalInfo?.occupation || "Software Engineer",
      monthlyIncome: settings.personalInfo?.monthlyIncome || 4500,
    };

    // Ensure required documents are marked as uploaded
    if (settings.documents && Array.isArray(settings.documents)) {
      settings.documents.forEach((doc) => {
        if (doc.required) {
          doc.uploaded = true;
        }
      });
    }

    settings.settings = {
      ...settings.settings,
      maxApplicationsPerDay: 10,
      autoApplyEnabled: true,
    };
  }

  await settings.save();
  writeLog(`User settings configured for user ID: ${TEST_CONFIG.testUserId}`);
  return settings;
}

/**
 * Create a test queue item for processing
 */
async function createTestQueueItem(settings) {
  writeLog("Creating test queue item...");

  // Generate test application data
  const applicationData = {
    personalInfo: settings.personalInfo,
    documents: settings.documents,
    additionalInfo: {
      movingDate: "2025-09-01",
      numberOfOccupants: 2,
      hasPets: false,
      isSmoker: false,
    },
  };

  // Generate test content (application letter)
  const generatedContent = {
    message: `Dear landlord,

I am very interested in renting this property. I am a ${settings.personalInfo.occupation} with a stable income of €${settings.personalInfo.monthlyIncome} per month. I am looking for a new home because ${settings.personalInfo.movingReason}.

I am a non-smoker, have no pets, and will be living with my partner. We are both quiet, responsible tenants who take good care of our home.

I have all required documents ready and can provide them immediately. I am available for viewings at your convenience.

Thank you for considering my application.

Best regards,
${settings.personalInfo.fullName}`,
    language: "en",
    tone: "professional",
    generatedAt: new Date(),
  };

  // Create the queue item
  const queueItem = new ApplicationQueue({
    userId: TEST_CONFIG.testUserId,
    listingId: `test-listing-${uuidv4().substring(0, 8)}`,
    listingUrl: TEST_CONFIG.testListingUrl,
    priority: 10,
    status: "pending",
    createdAt: new Date(),
    scheduledAt: new Date(),
    attempts: 0,
    maxAttempts: 3,
    applicationData,
    generatedContent,
    errors: [],
  });

  await queueItem.save();
  writeLog(`Created test queue item with ID: ${queueItem._id}`);
  return queueItem;
}

/**
 * Run the workflow test
 */
async function runWorkflowTest() {
  writeLog("Starting auto application workflow test...");
  writeLog(`Test configuration: ${JSON.stringify(TEST_CONFIG, null, 2)}`);

  try {
    // Connect to MongoDB
    const mongoose = require("mongoose");
    const mongoUri = process.env.MONGODB_URI || TEST_CONFIG.mongoUri;
    writeLog(`Connecting to MongoDB: ${mongoUri}`);
    await mongoose.connect(mongoUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    writeLog("Connected to MongoDB");

    // Setup test data
    const userSettings = await setupTestUserSettings();
    const queueItem = await createTestQueueItem(userSettings);

    // Get workflow status before submission
    const beforeStatus = applicationWorkflow.getStatus();
    writeLog(
      `Workflow status before submission: ${JSON.stringify(beforeStatus)}`
    );

    // Run the workflow
    writeLog(`Submitting application for queue item: ${queueItem._id}`);
    const startTime = Date.now();

    const result = await applicationWorkflow.submitApplication(queueItem._id);

    const endTime = Date.now();
    const processingTime = endTime - startTime;

    writeLog(`Application submission completed in ${processingTime}ms`);
    writeLog(`Result: ${JSON.stringify(result, null, 2)}`);

    // Get workflow status after submission
    const afterStatus = applicationWorkflow.getStatus();
    writeLog(
      `Workflow status after submission: ${JSON.stringify(afterStatus)}`
    );

    // Check if result was successful
    if (result.success) {
      writeLog("✅ TEST PASSED: Application submitted successfully");

      // Fetch and log the application result
      if (result.applicationResultId) {
        const applicationResult = await ApplicationResult.findById(
          result.applicationResultId
        );
        writeLog(
          `Application result details: ${JSON.stringify(
            applicationResult,
            null,
            2
          )}`
        );
      }
    } else {
      writeLog(`❌ TEST FAILED: ${result.error || "Unknown error"}`);

      if (result.errorType) {
        writeLog(`Error type: ${result.errorType}`);
      }

      if (result.shouldRetry) {
        writeLog(`Retry scheduled for: ${result.nextAttempt}`);
      }
    }

    // Update queue item status for cleanup
    await ApplicationQueue.findByIdAndUpdate(queueItem._id, {
      $set: { status: "test_completed" },
    });
  } catch (error) {
    writeLog(`❌ TEST ERROR: ${error.message}`);
    writeLog(error.stack);
  } finally {
    // Close MongoDB connection
    const mongoose = require("mongoose");
    if (mongoose.connection.readyState !== 0) {
      await mongoose.connection.close();
      writeLog("Closed MongoDB connection");
    }
  }
}

// Run the test
writeLog("=== AUTO APPLICATION WORKFLOW INTEGRATION TEST ===");
writeLog(`Test started at: ${new Date().toISOString()}`);

runWorkflowTest()
  .then(() => {
    writeLog(`Test completed at: ${new Date().toISOString()}`);
    process.exit(0);
  })
  .catch((error) => {
    writeLog(`Test failed with error: ${error.message}`);
    writeLog(error.stack);
    process.exit(1);
  });
