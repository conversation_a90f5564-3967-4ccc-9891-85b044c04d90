const ApplicationResult = require('../models/ApplicationResult');
const ApplicationQueue = require('../models/ApplicationQueue');
const AutoApplicationSettings = require('../models/AutoApplicationSettings');
const User = require('../models/User');
const { sendAlerts } = require('./alertService');
const { logger } = require('./logger');

/**
 * ApplicationMonitor - Real-time monitoring and analytics for auto-application system
 * 
 * This service provides:
 * - Real-time status tracking
 * - Success rate analytics and pattern detection
 * - Performance metrics collection and reporting
 * - User notification system for application events
 * - Dashboard data aggregation
 */
class ApplicationMonitor {
  constructor() {
    this.eventListeners = new Map();
    this.metricsCache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
    this.notificationQueue = [];
    this.isProcessingNotifications = false;
  }

  /**
   * Track application status change
   * @param {string} applicationId - Application result ID
   * @param {string} status - New status
   * @param {Object} metadata - Additional metadata
   */
  async trackApplication(applicationId, status, metadata = {}) {
    try {
      const application = await ApplicationResult.findById(applicationId);
      if (!application) {
        throw new Error(`Application ${applicationId} not found`);
      }

      // Update application with new status
      application.status = status;
      if (metadata.submittedAt) application.submittedAt = metadata.submittedAt;
      if (metadata.response) application.response = { ...application.response, ...metadata.response };
      if (metadata.metrics) application.metrics = { ...application.metrics, ...metadata.metrics };
      if (metadata.error) application.error = metadata.error;

      await application.save();

      // Emit tracking event
      this.emit('applicationStatusChanged', {
        applicationId,
        userId: application.userId,
        listingId: application.listingId,
        oldStatus: application.status,
        newStatus: status,
        metadata,
        timestamp: new Date()
      });

      // Queue notification
      await this.queueNotification(application.userId, 'status_change', {
        applicationId,
        status,
        listingTitle: application.listingSnapshot?.title,
        metadata
      });

      // Clear relevant caches
      this.clearUserCache(application.userId);

      logger.info(`Application ${applicationId} status updated to ${status}`, {
        userId: application.userId,
        listingId: application.listingId,
        metadata
      });

      return application;
    } catch (error) {
      logger.error('Error tracking application:', error);
      throw error;
    }
  }

  /**
   * Get success rates for user or system-wide
   * @param {string} userId - User ID (optional, null for system-wide)
   * @param {number} timeframe - Days to look back (default: 30)
   * @returns {Object} Success rate analytics
   */
  async getSuccessRates(userId = null, timeframe = 30) {
    try {
      const cacheKey = `success_rates_${userId || 'system'}_${timeframe}`;
      const cached = this.getFromCache(cacheKey);
      if (cached) return cached;

      const startDate = new Date();
      startDate.setDate(startDate.getDate() - timeframe);

      const matchStage = {
        submittedAt: { $gte: startDate }
      };
      if (userId) matchStage.userId = userId;

      const results = await ApplicationResult.aggregate([
        { $match: matchStage },
        {
          $group: {
            _id: null,
            total: { $sum: 1 },
            submitted: {
              $sum: { $cond: [{ $eq: ['$status', 'submitted'] }, 1, 0] }
            },
            successful: {
              $sum: {
                $cond: [
                  { $and: [{ $eq: ['$status', 'submitted'] }, { $eq: ['$response.success', true] }] },
                  1, 0
                ]
              }
            },
            failed: {
              $sum: { $cond: [{ $eq: ['$status', 'failed'] }, 1, 0] }
            },
            blocked: {
              $sum: { $cond: [{ $eq: ['$status', 'blocked'] }, 1, 0] }
            },
            captchaRequired: {
              $sum: { $cond: [{ $eq: ['$status', 'captcha_required'] }, 1, 0] }
            },
            landlordResponses: {
              $sum: { $cond: ['$landlordResponse.responseReceived', 1, 0] }
            },
            acceptances: {
              $sum: { $cond: [{ $eq: ['$landlordResponse.finalDecision', 'accepted'] }, 1, 0] }
            },
            viewingInvites: {
              $sum: { $cond: ['$landlordResponse.viewingInvite.invited', 1, 0] }
            },
            avgProcessingTime: { $avg: '$metrics.processingTime' },
            avgSuccessProbability: { $avg: '$metrics.successProbability' }
          }
        }
      ]);

      const data = results[0] || {
        total: 0, submitted: 0, successful: 0, failed: 0, blocked: 0,
        captchaRequired: 0, landlordResponses: 0, acceptances: 0, viewingInvites: 0,
        avgProcessingTime: 0, avgSuccessProbability: 0
      };

      const analytics = {
        timeframe,
        total: data.total,
        submitted: data.submitted,
        successful: data.successful,
        failed: data.failed,
        blocked: data.blocked,
        captchaRequired: data.captchaRequired,
        landlordResponses: data.landlordResponses,
        acceptances: data.acceptances,
        viewingInvites: data.viewingInvites,
        
        // Calculated rates
        submissionRate: data.total > 0 ? (data.submitted / data.total * 100) : 0,
        successRate: data.submitted > 0 ? (data.successful / data.submitted * 100) : 0,
        responseRate: data.successful > 0 ? (data.landlordResponses / data.successful * 100) : 0,
        acceptanceRate: data.landlordResponses > 0 ? (data.acceptances / data.landlordResponses * 100) : 0,
        viewingRate: data.successful > 0 ? (data.viewingInvites / data.successful * 100) : 0,
        
        // Performance metrics
        avgProcessingTime: Math.round(data.avgProcessingTime || 0),
        avgSuccessProbability: Math.round(data.avgSuccessProbability || 0),
        
        // Error analysis
        errorRate: data.total > 0 ? (data.failed / data.total * 100) : 0,
        blockingRate: data.total > 0 ? (data.blocked / data.total * 100) : 0,
        captchaRate: data.total > 0 ? (data.captchaRequired / data.total * 100) : 0
      };

      this.setCache(cacheKey, analytics);
      return analytics;
    } catch (error) {
      logger.error('Error getting success rates:', error);
      throw error;
    }
  }

  /**
   * Detect patterns in application data
   * @param {string} userId - User ID (optional)
   * @param {number} timeframe - Days to analyze (default: 30)
   * @returns {Object} Pattern analysis results
   */
  async detectPatterns(userId = null, timeframe = 30) {
    try {
      const cacheKey = `patterns_${userId || 'system'}_${timeframe}`;
      const cached = this.getFromCache(cacheKey);
      if (cached) return cached;

      const startDate = new Date();
      startDate.setDate(startDate.getDate() - timeframe);

      const matchStage = {
        submittedAt: { $gte: startDate },
        status: 'submitted',
        'response.success': true
      };
      if (userId) matchStage.userId = userId;

      // Analyze success patterns by various factors
      const patterns = await Promise.all([
        this.analyzeTimePatterns(matchStage),
        this.analyzeLocationPatterns(matchStage),
        this.analyzePropertyTypePatterns(matchStage),
        this.analyzePricePatterns(matchStage),
        this.analyzeTemplatePatterns(matchStage),
        this.analyzeMarketConditionPatterns(matchStage)
      ]);

      const analysis = {
        timeframe,
        timePatterns: patterns[0],
        locationPatterns: patterns[1],
        propertyTypePatterns: patterns[2],
        pricePatterns: patterns[3],
        templatePatterns: patterns[4],
        marketConditionPatterns: patterns[5],
        recommendations: this.generateRecommendations(patterns),
        confidence: this.calculatePatternConfidence(patterns)
      };

      this.setCache(cacheKey, analysis);
      return analysis;
    } catch (error) {
      logger.error('Error detecting patterns:', error);
      throw error;
    }
  }

  /**
   * Generate comprehensive reports
   * @param {string} userId - User ID (optional)
   * @param {string} reportType - Type of report ('daily', 'weekly', 'monthly')
   * @returns {Object} Generated report
   */
  async generateReports(userId = null, reportType = 'weekly') {
    try {
      const timeframes = {
        daily: 1,
        weekly: 7,
        monthly: 30
      };

      const timeframe = timeframes[reportType] || 7;
      const [successRates, patterns, performance, errors] = await Promise.all([
        this.getSuccessRates(userId, timeframe),
        this.detectPatterns(userId, timeframe),
        this.getPerformanceMetrics(userId, timeframe),
        this.getErrorAnalysis(userId, timeframe)
      ]);

      const report = {
        reportType,
        timeframe,
        generatedAt: new Date(),
        userId,
        
        summary: {
          totalApplications: successRates.total,
          successRate: successRates.successRate,
          acceptanceRate: successRates.acceptanceRate,
          avgProcessingTime: successRates.avgProcessingTime,
          topPerformingLocation: patterns.locationPatterns[0]?.location || 'N/A',
          mostCommonError: errors.commonErrors[0]?.category || 'N/A'
        },
        
        successRates,
        patterns,
        performance,
        errors,
        
        insights: this.generateInsights(successRates, patterns, performance, errors),
        actionItems: this.generateActionItems(successRates, patterns, performance, errors)
      };

      // Store report for future reference
      await this.storeReport(report);

      return report;
    } catch (error) {
      logger.error('Error generating reports:', error);
      throw error;
    }
  }

  /**
   * Send notifications to users
   * @param {string} userId - User ID
   * @param {string} eventType - Type of event
   * @param {Object} data - Event data
   */
  async sendNotifications(userId, eventType, data) {
    try {
      const user = await User.findById(userId);
      if (!user) return;

      const settings = await AutoApplicationSettings.findOne({ userId });
      if (!settings || !settings.settings?.notificationPreferences) return;

      const { notificationPreferences } = settings.settings;

      // Check if user wants this type of notification
      const shouldNotify = this.shouldSendNotification(eventType, notificationPreferences);
      if (!shouldNotify) return;

      const notification = {
        userId,
        eventType,
        data,
        timestamp: new Date(),
        channels: this.getNotificationChannels(user, notificationPreferences)
      };

      // Send via configured channels
      await this.deliverNotification(notification);

      logger.info(`Notification sent to user ${userId}`, {
        eventType,
        channels: notification.channels
      });
    } catch (error) {
      logger.error('Error sending notifications:', error);
    }
  }

  /**
   * Get performance metrics
   * @param {string} userId - User ID (optional)
   * @param {number} timeframe - Days to analyze
   * @returns {Object} Performance metrics
   */
  async getPerformanceMetrics(userId = null, timeframe = 30) {
    try {
      const cacheKey = `performance_${userId || 'system'}_${timeframe}`;
      const cached = this.getFromCache(cacheKey);
      if (cached) return cached;

      const startDate = new Date();
      startDate.setDate(startDate.getDate() - timeframe);

      const matchStage = {
        submittedAt: { $gte: startDate }
      };
      if (userId) matchStage.userId = userId;

      const metrics = await ApplicationResult.aggregate([
        { $match: matchStage },
        {
          $group: {
            _id: null,
            avgProcessingTime: { $avg: '$metrics.processingTime' },
            minProcessingTime: { $min: '$metrics.processingTime' },
            maxProcessingTime: { $max: '$metrics.processingTime' },
            avgFormDetectionTime: { $avg: '$metrics.formDetectionTime' },
            avgFormFillingTime: { $avg: '$metrics.formFillingTime' },
            avgSubmissionTime: { $avg: '$metrics.submissionTime' },
            avgNetworkLatency: { $avg: '$metrics.networkLatency' },
            avgPageLoadTime: { $avg: '$metrics.pageLoadTime' },
            totalApplications: { $sum: 1 },
            complexForms: {
              $sum: { $cond: [{ $in: ['$metrics.formComplexity', ['complex', 'very_complex']] }, 1, 0] }
            },
            captchaEncounters: {
              $sum: { $cond: [{ $eq: ['$status', 'captcha_required'] }, 1, 0] }
            },
            blockingIncidents: {
              $sum: { $cond: [{ $eq: ['$status', 'blocked'] }, 1, 0] }
            }
          }
        }
      ]);

      const data = metrics[0] || {};
      
      const performance = {
        timeframe,
        totalApplications: data.totalApplications || 0,
        
        // Processing times (in milliseconds)
        avgProcessingTime: Math.round(data.avgProcessingTime || 0),
        minProcessingTime: Math.round(data.minProcessingTime || 0),
        maxProcessingTime: Math.round(data.maxProcessingTime || 0),
        avgFormDetectionTime: Math.round(data.avgFormDetectionTime || 0),
        avgFormFillingTime: Math.round(data.avgFormFillingTime || 0),
        avgSubmissionTime: Math.round(data.avgSubmissionTime || 0),
        
        // Network performance
        avgNetworkLatency: Math.round(data.avgNetworkLatency || 0),
        avgPageLoadTime: Math.round(data.avgPageLoadTime || 0),
        
        // Complexity and challenges
        complexFormRate: data.totalApplications > 0 ? (data.complexForms / data.totalApplications * 100) : 0,
        captchaRate: data.totalApplications > 0 ? (data.captchaEncounters / data.totalApplications * 100) : 0,
        blockingRate: data.totalApplications > 0 ? (data.blockingIncidents / data.totalApplications * 100) : 0,
        
        // Performance grades
        processingGrade: this.calculatePerformanceGrade(data.avgProcessingTime),
        reliabilityGrade: this.calculateReliabilityGrade(data.blockingIncidents, data.totalApplications),
        efficiencyGrade: this.calculateEfficiencyGrade(data.avgFormFillingTime, data.avgFormDetectionTime)
      };

      this.setCache(cacheKey, performance);
      return performance;
    } catch (error) {
      logger.error('Error getting performance metrics:', error);
      throw error;
    }
  }

  /**
   * Get error analysis
   * @param {string} userId - User ID (optional)
   * @param {number} timeframe - Days to analyze
   * @returns {Object} Error analysis
   */
  async getErrorAnalysis(userId = null, timeframe = 7) {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - timeframe);

      const matchStage = {
        submittedAt: { $gte: startDate },
        status: 'failed'
      };
      if (userId) matchStage.userId = userId;

      const errorAnalysis = await ApplicationResult.aggregate([
        { $match: matchStage },
        {
          $group: {
            _id: '$error.category',
            count: { $sum: 1 },
            messages: { $push: '$error.message' },
            retryableCount: {
              $sum: { $cond: ['$error.retryable', 1, 0] }
            }
          }
        },
        { $sort: { count: -1 } }
      ]);

      const commonErrors = errorAnalysis.map(error => ({
        category: error._id || 'unknown',
        count: error.count,
        percentage: 0, // Will be calculated below
        retryablePercentage: error.count > 0 ? (error.retryableCount / error.count * 100) : 0,
        commonMessages: [...new Set(error.messages)].slice(0, 3)
      }));

      const totalErrors = commonErrors.reduce((sum, error) => sum + error.count, 0);
      commonErrors.forEach(error => {
        error.percentage = totalErrors > 0 ? (error.count / totalErrors * 100) : 0;
      });

      return {
        timeframe,
        totalErrors,
        commonErrors,
        recommendations: this.generateErrorRecommendations(commonErrors)
      };
    } catch (error) {
      logger.error('Error getting error analysis:', error);
      throw error;
    }
  }

  // Helper methods for pattern analysis
  async analyzeTimePatterns(matchStage) {
    const timeAnalysis = await ApplicationResult.aggregate([
      { $match: matchStage },
      {
        $group: {
          _id: {
            hour: { $hour: '$submittedAt' },
            dayOfWeek: { $dayOfWeek: '$submittedAt' }
          },
          count: { $sum: 1 },
          successRate: {
            $avg: { $cond: ['$response.success', 100, 0] }
          }
        }
      },
      { $sort: { count: -1 } }
    ]);

    return {
      bestHours: timeAnalysis.filter(t => t._id.hour).slice(0, 3),
      bestDays: timeAnalysis.filter(t => t._id.dayOfWeek).slice(0, 3),
      patterns: timeAnalysis
    };
  }

  async analyzeLocationPatterns(matchStage) {
    const locationAnalysis = await ApplicationResult.aggregate([
      { $match: matchStage },
      {
        $group: {
          _id: '$listingSnapshot.address',
          count: { $sum: 1 },
          successRate: {
            $avg: { $cond: ['$response.success', 100, 0] }
          },
          avgPrice: { $avg: '$listingSnapshot.price' }
        }
      },
      { $sort: { successRate: -1, count: -1 } },
      { $limit: 10 }
    ]);

    return locationAnalysis.map(loc => ({
      location: loc._id,
      applications: loc.count,
      successRate: Math.round(loc.successRate),
      avgPrice: Math.round(loc.avgPrice || 0)
    }));
  }

  async analyzePropertyTypePatterns(matchStage) {
    const typeAnalysis = await ApplicationResult.aggregate([
      { $match: matchStage },
      {
        $group: {
          _id: '$listingSnapshot.propertyType',
          count: { $sum: 1 },
          successRate: {
            $avg: { $cond: ['$response.success', 100, 0] }
          }
        }
      },
      { $sort: { successRate: -1 } }
    ]);

    return typeAnalysis.map(type => ({
      propertyType: type._id,
      applications: type.count,
      successRate: Math.round(type.successRate)
    }));
  }

  async analyzePricePatterns(matchStage) {
    const priceAnalysis = await ApplicationResult.aggregate([
      { $match: matchStage },
      {
        $bucket: {
          groupBy: '$listingSnapshot.price',
          boundaries: [0, 1000, 1500, 2000, 2500, 3000, 5000, 10000],
          default: 'other',
          output: {
            count: { $sum: 1 },
            successRate: {
              $avg: { $cond: ['$response.success', 100, 0] }
            }
          }
        }
      }
    ]);

    return priceAnalysis.map(price => ({
      priceRange: price._id,
      applications: price.count,
      successRate: Math.round(price.successRate)
    }));
  }

  async analyzeTemplatePatterns(matchStage) {
    const templateAnalysis = await ApplicationResult.aggregate([
      { $match: matchStage },
      {
        $group: {
          _id: '$aiContent.template',
          count: { $sum: 1 },
          successRate: {
            $avg: { $cond: ['$response.success', 100, 0] }
          },
          avgConfidence: { $avg: '$aiContent.confidence' }
        }
      },
      { $sort: { successRate: -1 } }
    ]);

    return templateAnalysis.map(template => ({
      template: template._id,
      applications: template.count,
      successRate: Math.round(template.successRate),
      avgConfidence: Math.round(template.avgConfidence || 0)
    }));
  }

  async analyzeMarketConditionPatterns(matchStage) {
    const marketAnalysis = await ApplicationResult.aggregate([
      { $match: matchStage },
      {
        $group: {
          _id: '$analytics.marketCondition',
          count: { $sum: 1 },
          successRate: {
            $avg: { $cond: ['$response.success', 100, 0] }
          }
        }
      },
      { $sort: { successRate: -1 } }
    ]);

    return marketAnalysis.map(market => ({
      condition: market._id,
      applications: market.count,
      successRate: Math.round(market.successRate)
    }));
  }

  // Helper methods for recommendations and insights
  generateRecommendations(patterns) {
    const recommendations = [];
    
    // Time-based recommendations
    if (patterns[0].bestHours.length > 0) {
      const bestHour = patterns[0].bestHours[0];
      recommendations.push({
        type: 'timing',
        priority: 'high',
        message: `Best application time is ${bestHour._id.hour}:00 with ${Math.round(bestHour.successRate)}% success rate`
      });
    }

    // Location-based recommendations
    if (patterns[1].length > 0) {
      const bestLocation = patterns[1][0];
      recommendations.push({
        type: 'location',
        priority: 'medium',
        message: `Focus on ${bestLocation.location} - ${bestLocation.successRate}% success rate`
      });
    }

    // Template recommendations
    if (patterns[4].length > 0) {
      const bestTemplate = patterns[4][0];
      recommendations.push({
        type: 'template',
        priority: 'medium',
        message: `Use ${bestTemplate.template} template - ${bestTemplate.successRate}% success rate`
      });
    }

    return recommendations;
  }

  generateInsights(successRates, patterns, performance, errors) {
    const insights = [];

    // Success rate insights
    if (successRates.successRate > 80) {
      insights.push({
        type: 'positive',
        message: 'Excellent success rate! Your applications are performing very well.'
      });
    } else if (successRates.successRate < 50) {
      insights.push({
        type: 'warning',
        message: 'Success rate is below average. Consider optimizing your application strategy.'
      });
    }

    // Performance insights
    if (performance.avgProcessingTime > 10 * 60 * 1000) { // 10 minutes
      insights.push({
        type: 'warning',
        message: 'Processing time is high. This may affect your application speed.'
      });
    }

    // Error insights
    if (errors.commonErrors.length > 0) {
      const topError = errors.commonErrors[0];
      insights.push({
        type: 'info',
        message: `Most common error: ${topError.category} (${topError.percentage.toFixed(1)}% of failures)`
      });
    }

    return insights;
  }

  generateActionItems(successRates, patterns, performance, errors) {
    const actions = [];

    if (successRates.successRate < 70) {
      actions.push({
        priority: 'high',
        action: 'Review and optimize application templates',
        reason: 'Low success rate detected'
      });
    }

    if (performance.blockingRate > 10) {
      actions.push({
        priority: 'high',
        action: 'Review anti-detection measures',
        reason: 'High blocking rate detected'
      });
    }

    if (errors.commonErrors.length > 0 && errors.commonErrors[0].percentage > 30) {
      actions.push({
        priority: 'medium',
        action: `Address ${errors.commonErrors[0].category} errors`,
        reason: 'High error rate in specific category'
      });
    }

    return actions;
  }

  generateErrorRecommendations(commonErrors) {
    const recommendations = [];

    commonErrors.forEach(error => {
      switch (error.category) {
        case 'network':
          recommendations.push({
            category: error.category,
            message: 'Consider implementing better retry logic and network timeout handling'
          });
          break;
        case 'form':
          recommendations.push({
            category: error.category,
            message: 'Update form detection patterns and field mapping strategies'
          });
          break;
        case 'detection':
          recommendations.push({
            category: error.category,
            message: 'Enhance anti-detection measures and randomization techniques'
          });
          break;
        case 'data':
          recommendations.push({
            category: error.category,
            message: 'Validate user data completeness and format requirements'
          });
          break;
        default:
          recommendations.push({
            category: error.category,
            message: 'Review error handling and logging for this category'
          });
      }
    });

    return recommendations;
  }

  // Performance grading helpers
  calculatePerformanceGrade(avgProcessingTime) {
    if (avgProcessingTime < 3 * 60 * 1000) return 'A'; // < 3 minutes
    if (avgProcessingTime < 5 * 60 * 1000) return 'B'; // < 5 minutes
    if (avgProcessingTime < 10 * 60 * 1000) return 'C'; // < 10 minutes
    return 'D';
  }

  calculateReliabilityGrade(blockingIncidents, totalApplications) {
    const blockingRate = totalApplications > 0 ? (blockingIncidents / totalApplications * 100) : 0;
    if (blockingRate < 5) return 'A';
    if (blockingRate < 10) return 'B';
    if (blockingRate < 20) return 'C';
    return 'D';
  }

  calculateEfficiencyGrade(avgFormFillingTime, avgFormDetectionTime) {
    const totalTime = (avgFormFillingTime || 0) + (avgFormDetectionTime || 0);
    if (totalTime < 30 * 1000) return 'A'; // < 30 seconds
    if (totalTime < 60 * 1000) return 'B'; // < 1 minute
    if (totalTime < 120 * 1000) return 'C'; // < 2 minutes
    return 'D';
  }

  calculatePatternConfidence(patterns) {
    // Calculate confidence based on data volume and consistency
    const totalDataPoints = patterns.reduce((sum, pattern) => {
      if (Array.isArray(pattern)) return sum + pattern.length;
      if (pattern.patterns) return sum + pattern.patterns.length;
      return sum + 1;
    }, 0);

    if (totalDataPoints > 100) return 'high';
    if (totalDataPoints > 50) return 'medium';
    return 'low';
  }

  // Notification helpers
  shouldSendNotification(eventType, preferences) {
    switch (eventType) {
      case 'status_change':
        return preferences.immediate || false;
      case 'daily_summary':
        return preferences.daily || false;
      case 'weekly_summary':
        return preferences.weekly || false;
      case 'error_alert':
        return preferences.immediate || false;
      default:
        return false;
    }
  }

  getNotificationChannels(user, preferences) {
    const channels = [];
    
    if (user.email && preferences.email !== false) {
      channels.push('email');
    }
    
    if (user.profile?.phone && preferences.sms) {
      channels.push('sms');
    }
    
    if (preferences.push !== false) {
      channels.push('push');
    }

    return channels;
  }

  async deliverNotification(notification) {
    const { userId, eventType, data, channels } = notification;

    // Email notifications
    if (channels.includes('email')) {
      await this.sendEmailNotification(userId, eventType, data);
    }

    // SMS notifications
    if (channels.includes('sms')) {
      await this.sendSMSNotification(userId, eventType, data);
    }

    // Push notifications
    if (channels.includes('push')) {
      await this.sendPushNotification(userId, eventType, data);
    }
  }

  async sendEmailNotification(userId, eventType, data) {
    try {
      const user = await User.findById(userId);
      if (!user?.email) return;

      const emailData = this.formatEmailNotification(eventType, data);
      
      // Use existing alert service for email delivery
      // This would integrate with the existing sendAlerts function
      logger.info(`Email notification sent to ${user.email}`, { eventType, userId });
    } catch (error) {
      logger.error('Error sending email notification:', error);
    }
  }

  async sendSMSNotification(userId, eventType, data) {
    try {
      const user = await User.findById(userId);
      if (!user?.profile?.phone) return;

      const smsData = this.formatSMSNotification(eventType, data);
      
      // SMS implementation would go here
      logger.info(`SMS notification sent to ${user.profile.phone}`, { eventType, userId });
    } catch (error) {
      logger.error('Error sending SMS notification:', error);
    }
  }

  async sendPushNotification(userId, eventType, data) {
    try {
      const pushData = this.formatPushNotification(eventType, data);
      
      // Push notification implementation would go here
      logger.info(`Push notification sent to user ${userId}`, { eventType });
    } catch (error) {
      logger.error('Error sending push notification:', error);
    }
  }

  formatEmailNotification(eventType, data) {
    switch (eventType) {
      case 'status_change':
        return {
          subject: `Application Status Update: ${data.listingTitle}`,
          body: `Your application status has changed to: ${data.status}`
        };
      case 'daily_summary':
        return {
          subject: 'Daily Auto-Application Summary',
          body: `Today's summary: ${data.applicationsToday} applications submitted`
        };
      default:
        return {
          subject: 'Auto-Application Notification',
          body: 'You have a new notification from the auto-application system'
        };
    }
  }

  formatSMSNotification(eventType, data) {
    switch (eventType) {
      case 'status_change':
        return `Application update: ${data.status} for ${data.listingTitle}`;
      case 'error_alert':
        return `Auto-application error: ${data.error}`;
      default:
        return 'Auto-application notification';
    }
  }

  formatPushNotification(eventType, data) {
    switch (eventType) {
      case 'status_change':
        return {
          title: 'Application Status Update',
          body: `${data.listingTitle}: ${data.status}`,
          data: { applicationId: data.applicationId }
        };
      default:
        return {
          title: 'Auto-Application',
          body: 'New notification available',
          data: {}
        };
    }
  }

  // Queue notification for batch processing
  async queueNotification(userId, eventType, data) {
    this.notificationQueue.push({
      userId,
      eventType,
      data,
      timestamp: new Date()
    });

    // Process queue if not already processing
    if (!this.isProcessingNotifications) {
      this.processNotificationQueue();
    }
  }

  async processNotificationQueue() {
    if (this.isProcessingNotifications || this.notificationQueue.length === 0) {
      return;
    }

    this.isProcessingNotifications = true;

    try {
      while (this.notificationQueue.length > 0) {
        const notification = this.notificationQueue.shift();
        await this.sendNotifications(
          notification.userId,
          notification.eventType,
          notification.data
        );

        // Small delay to avoid overwhelming notification services
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    } catch (error) {
      logger.error('Error processing notification queue:', error);
    } finally {
      this.isProcessingNotifications = false;
    }
  }

  // Cache management
  getFromCache(key) {
    const cached = this.metricsCache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }
    return null;
  }

  setCache(key, data) {
    this.metricsCache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  clearUserCache(userId) {
    const keysToDelete = [];
    for (const [key] of this.metricsCache) {
      if (key.includes(userId)) {
        keysToDelete.push(key);
      }
    }
    keysToDelete.forEach(key => this.metricsCache.delete(key));
  }

  clearCache() {
    this.metricsCache.clear();
  }

  // Event system
  emit(eventName, data) {
    const listeners = this.eventListeners.get(eventName) || [];
    listeners.forEach(listener => {
      try {
        listener(data);
      } catch (error) {
        logger.error(`Error in event listener for ${eventName}:`, error);
      }
    });
  }

  on(eventName, listener) {
    if (!this.eventListeners.has(eventName)) {
      this.eventListeners.set(eventName, []);
    }
    this.eventListeners.get(eventName).push(listener);
  }

  off(eventName, listener) {
    const listeners = this.eventListeners.get(eventName) || [];
    const index = listeners.indexOf(listener);
    if (index > -1) {
      listeners.splice(index, 1);
    }
  }

  // Store report for historical tracking
  async storeReport(report) {
    try {
      // This could be stored in a separate Reports collection
      // For now, we'll just log it
      logger.info('Report generated', {
        reportType: report.reportType,
        userId: report.userId,
        summary: report.summary
      });
    } catch (error) {
      logger.error('Error storing report:', error);
    }
  }

  // Cleanup old data
  async cleanup() {
    try {
      // Clear old cache entries
      const now = Date.now();
      for (const [key, value] of this.metricsCache) {
        if (now - value.timestamp > this.cacheTimeout) {
          this.metricsCache.delete(key);
        }
      }

      logger.info('ApplicationMonitor cleanup completed');
    } catch (error) {
      logger.error('Error during cleanup:', error);
    }
  }
}

module.exports = ApplicationMonitor;