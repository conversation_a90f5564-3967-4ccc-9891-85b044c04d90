/**
 * Tests for the Enhanced Property Model with Unified Schema Support
 */

const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const EnhancedProperty = require('../models/EnhancedProperty');
const { createMinimalProperty } = require('../schemas/unifiedPropertySchema');

let mongoServer;

// Setup in-memory MongoDB server
beforeAll(async () => {
  mongoServer = await MongoMemoryServer.create();
  const uri = mongoServer.getUri();
  await mongoose.connect(uri);
});

// Clean up after tests
afterAll(async () => {
  await mongoose.disconnect();
  await mongoServer.stop();
});

// Clear database between tests
afterEach(async () => {
  await EnhancedProperty.deleteMany({});
});

describe('Enhanced Property Model', () => {
  test('should create a property with unified schema fields', async () => {
    // Create a property with unified schema data
    const unifiedData = createMinimalProperty({
      title: 'Test Property',
      description: 'A test property',
      price: '€1,500 per month',
      location: 'Amsterdam, Netherlands',
      source: 'funda.nl',
      url: 'https://www.funda.nl/test',
      propertyType: 'apartment',
      size: '85 m²',
      rooms: '3',
      bedrooms: '2'
    });
    
    const property = await EnhancedProperty.createFromUnifiedSchema(unifiedData);
    await property.save();
    
    // Retrieve the property
    const savedProperty = await EnhancedProperty.findById(property._id);
    
    // Check unified schema fields
    expect(savedProperty.unifiedData).toBeDefined();
    expect(savedProperty.sourceMetadata).toBeDefined();
    expect(savedProperty.sourceMetadata.website).toBe('funda.nl');
    expect(savedProperty.dataQuality).toBeDefined();
    expect(savedProperty.processingMetadata).toBeDefined();
    expect(savedProperty.rawData).toBeDefined();
    
    // Check mapped fields
    expect(savedProperty.title).toBe('Test Property');
    expect(savedProperty.description).toBe('A test property');
    expect(savedProperty.propertyType).toBe('apartment');
  });
  
  test('should update a property from unified schema data', async () => {
    // Create a basic property
    const property = new EnhancedProperty({
      title: 'Original Title',
      description: 'Original description',
      address: {
        street: 'Test Street',
        houseNumber: '123',
        postalCode: '1234AB',
        city: 'Amsterdam',
        country: 'Netherlands'
      },
      propertyType: 'house',
      rent: {
        amount: 1000,
        currency: 'EUR',
        period: 'monthly'
      },
      owner: {
        userId: mongoose.Types.ObjectId('000000000000000000000000')
      },
      status: 'draft'
    });
    
    await property.save();
    
    // Create unified data for update
    const unifiedData = createMinimalProperty({
      title: 'Updated Title',
      description: 'Updated description',
      price: '€1,800 per month',
      location: {
        _unified: {
          address: {
            street: 'New Street',
            houseNumber: '456',
            postalCode: '5678CD',
            city: 'Rotterdam',
            country: 'Netherlands'
          }
        }
      },
      source: 'huurwoningen.nl',
      url: 'https://www.huurwoningen.nl/test',
      propertyType: 'apartment',
      size: '100 m²',
      rooms: '4',
      bedrooms: '3',
      bathrooms: '2',
      year: '2010',
      interior: 'Gemeubileerd',
      furnished: true,
      garden: true,
      balcony: true,
      images: ['https://example.com/image1.jpg', 'https://example.com/image2.jpg']
    });
    
    // Update the property
    property.updateFromUnifiedSchema(unifiedData);
    await property.save();
    
    // Retrieve the updated property
    const updatedProperty = await EnhancedProperty.findById(property._id);
    
    // Check updated fields
    expect(updatedProperty.title).toBe('Updated Title');
    expect(updatedProperty.description).toBe('Updated description');
    expect(updatedProperty.propertyType).toBe('apartment');
    expect(updatedProperty.address.street).toBe('New Street');
    expect(updatedProperty.address.city).toBe('Rotterdam');
    expect(updatedProperty.size).toBe(100); // Numeric size
    expect(updatedProperty.rooms).toBe(4);
    expect(updatedProperty.bedrooms).toBe(3);
    expect(updatedProperty.bathrooms).toBe(2);
    expect(updatedProperty.features.interior).toBe('gemeubileerd');
    expect(updatedProperty.features.furnished).toBe(true);
    expect(updatedProperty.features.garden).toBe(true);
    expect(updatedProperty.features.balcony).toBe(true);
    expect(updatedProperty.images).toHaveLength(2);
    expect(updatedProperty.images[0].url).toBe('https://example.com/image1.jpg');
    expect(updatedProperty.images[0].isPrimary).toBe(true);
    
    // Check source metadata
    expect(updatedProperty.sourceMetadata.website).toBe('huurwoningen.nl');
    expect(updatedProperty.unifiedData).toEqual(unifiedData);
  });
  
  test('should find properties by source metadata', async () => {
    // Create properties from different sources
    const fundaData = createMinimalProperty({
      title: 'Funda Property',
      source: 'funda.nl',
      url: 'https://www.funda.nl/test',
      externalId: 'funda-123'
    });
    
    const huurwoningenData = createMinimalProperty({
      title: 'Huurwoningen Property',
      source: 'huurwoningen.nl',
      url: 'https://www.huurwoningen.nl/test',
      externalId: 'huurwoningen-456'
    });
    
    const fundaProperty = await EnhancedProperty.createFromUnifiedSchema(fundaData);
    fundaProperty.sourceMetadata.externalId = 'funda-123';
    await fundaProperty.save();
    
    const huurwoningenProperty = await EnhancedProperty.createFromUnifiedSchema(huurwoningenData);
    huurwoningenProperty.sourceMetadata.externalId = 'huurwoningen-456';
    await huurwoningenProperty.save();
    
    // Find by source website
    const fundaProperties = await EnhancedProperty.find({
      'sourceMetadata.website': 'funda.nl'
    });
    
    expect(fundaProperties).toHaveLength(1);
    expect(fundaProperties[0].title).toBe('Funda Property');
    
    // Find by source metadata
    const property = await EnhancedProperty.findBySourceMetadata('funda.nl', 'funda-123');
    expect(property).toBeDefined();
    expect(property.title).toBe('Funda Property');
  });
  
  test('should find properties with quality issues', async () => {
    // Create properties with different quality scores
    const property1 = new EnhancedProperty({
      title: 'High Quality Property',
      description: 'Complete description',
      address: {
        street: 'Test Street',
        houseNumber: '123',
        postalCode: '1234AB',
        city: 'Amsterdam',
        country: 'Netherlands'
      },
      propertyType: 'house',
      rent: {
        amount: 1000,
        currency: 'EUR',
        period: 'monthly'
      },
      owner: {
        userId: mongoose.Types.ObjectId('000000000000000000000000')
      },
      status: 'active',
      dataQuality: {
        completeness: 95,
        accuracy: 90,
        validationErrors: []
      }
    });
    
    const property2 = new EnhancedProperty({
      title: 'Low Quality Property',
      description: 'Incomplete description',
      address: {
        street: 'Test Street',
        houseNumber: '123',
        postalCode: '1234AB',
        city: 'Amsterdam',
        country: 'Netherlands'
      },
      propertyType: 'house',
      rent: {
        amount: 1000,
        currency: 'EUR',
        period: 'monthly'
      },
      owner: {
        userId: mongoose.Types.ObjectId('000000000000000000000000')
      },
      status: 'active',
      dataQuality: {
        completeness: 40,
        accuracy: 30,
        validationErrors: ['Missing required fields']
      }
    });
    
    await property1.save();
    await property2.save();
    
    // Find properties with quality issues
    const propertiesWithIssues = await EnhancedProperty.findWithQualityIssues(50, 50);
    
    expect(propertiesWithIssues).toHaveLength(1);
    expect(propertiesWithIssues[0].title).toBe('Low Quality Property');
    expect(propertiesWithIssues[0].dataQuality.completeness).toBe(40);
  });
});