const aiService = require('../../services/aiService');
const autoApplicationService = require('../../services/autoApplicationService');

// Mock the OpenAI service
jest.mock('openai');

// Mock the logger
jest.mock('../../services/logger', () => ({
  logHelpers: {
    logAiOperation: jest.fn()
  },
  loggers: {
    app: {
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn()
    }
  }
}));

// Mock config
jest.mock('../../config/config', () => ({
  openRouter: {
    apiKey: 'test-api-key',
    baseURL: 'https://test-api.com',
    models: {
      analysis: 'gpt-3.5-turbo',
      matching: 'gpt-3.5-turbo',
      summarization: 'gpt-3.5-turbo',
      translation: 'gpt-3.5-turbo'
    },
    defaultModel: 'gpt-3.5-turbo',
    maxTokens: 1000,
    temperature: 0.7
  }
}));

// Mock database models
jest.mock('../../models/AutoApplicationSettings');
jest.mock('../../models/ApplicationQueue');
jest.mock('../../models/User');

const OpenAI = require('openai');
const AutoApplicationSettings = require('../../models/AutoApplicationSettings');
const ApplicationQueue = require('../../models/ApplicationQueue');
const User = require('../../models/User');

describe('AI Service - Auto Application Integration', () => {
  let mockOpenAI;

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock OpenAI instance
    mockOpenAI = {
      chat: {
        completions: {
          create: jest.fn().mockResolvedValue({
            choices: [{
              message: {
                content: 'Geachte verhuurder, ik ben zeer geïnteresseerd in uw prachtige appartement in Amsterdam. Als software engineer met een stabiel inkomen van €4000 per maand, kan ik de huur probleemloos betalen. Ik ben op zoek naar een moderne woning waar ik me thuis kan voelen en productief kan werken.'
              }
            }]
          })
        }
      }
    };

    OpenAI.mockImplementation(() => mockOpenAI);
    aiService.openai = mockOpenAI;
  });

  describe('Integration with AutoApplicationService', () => {
    it('should generate application content using new AI methods', async () => {
      const mockListing = {
        id: 'listing123',
        title: 'Modern Apartment Amsterdam',
        location: 'Amsterdam Centrum',
        price: '€1,800',
        size: '85m²',
        rooms: '3 rooms',
        propertyType: 'Apartment',
        description: 'Beautiful modern apartment with great city views'
      };

      const mockUserProfile = {
        name: 'John Doe',
        personalInfo: {
          fullName: 'John Doe',
          occupation: 'Software Engineer',
          monthlyIncome: 4000,
          nationality: 'Dutch',
          dateOfBirth: '1990-01-15'
        }
      };

      const mockSettings = {
        applicationTemplate: 'professional'
      };

      // Test the AI service method directly
      const result = await aiService.generateAutoApplicationLetter(
        mockListing,
        mockUserProfile,
        mockSettings,
        'dutch'
      );

      // Verify AI service was called with correct parameters
      expect(mockOpenAI.chat.completions.create).toHaveBeenCalledWith({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: expect.stringContaining('expert rental application writer')
          },
          {
            role: 'user',
            content: expect.stringContaining('Modern Apartment Amsterdam')
          }
        ],
        max_tokens: 1200,
        temperature: 0.7
      });

      // Verify the result structure
      expect(result).toEqual({
        subject: 'Rental Application - Modern Apartment Amsterdam',
        message: expect.any(String),
        template: 'professional',
        language: 'dutch',
        personalizedElements: expect.any(Array),
        generatedAt: expect.any(String),
        wordCount: expect.any(Number),
        estimatedReadTime: expect.any(Number)
      });
    });

    it('should handle different template types correctly', async () => {
      const testCases = [
        { template: 'professional', expectedContent: 'professional' },
        { template: 'casual', expectedContent: 'casual' },
        { template: 'student', expectedContent: 'student' },
        { template: 'expat', expectedContent: 'expat' }
      ];

      for (const testCase of testCases) {
        const mockListing = {
          title: 'Test Property',
          location: 'Amsterdam'
        };

        const mockUserProfile = {
          name: 'Test User',
          personalInfo: {
            fullName: 'Test User',
            occupation: 'Professional'
          }
        };

        const mockSettings = {
          applicationTemplate: testCase.template
        };

        // Reset mock
        mockOpenAI.chat.completions.create.mockClear();

        await aiService.generateAutoApplicationLetter(
          mockListing,
          mockUserProfile,
          mockSettings,
          'dutch'
        );

        const systemPrompt = mockOpenAI.chat.completions.create.mock.calls[0][0].messages[0].content;
        expect(systemPrompt.toLowerCase()).toContain(testCase.template);
      }
    });

    it('should support multi-language generation', async () => {
      const mockListing = {
        title: 'Test Property',
        location: 'Amsterdam'
      };

      const mockUserProfile = {
        name: 'Test User',
        personalInfo: {
          fullName: 'Test User'
        }
      };

      // Test Dutch
      await aiService.generateAutoApplicationLetter(
        mockListing,
        mockUserProfile,
        { applicationTemplate: 'professional' },
        'dutch'
      );

      let systemPrompt = mockOpenAI.chat.completions.create.mock.calls[0][0].messages[0].content;
      expect(systemPrompt).toContain('fluent Dutch');

      // Reset and test English
      mockOpenAI.chat.completions.create.mockClear();

      await aiService.generateAutoApplicationLetter(
        mockListing,
        mockUserProfile,
        { applicationTemplate: 'professional' },
        'english'
      );

      systemPrompt = mockOpenAI.chat.completions.create.mock.calls[0][0].messages[0].content;
      expect(systemPrompt).toContain('fluent English');
    });

    it('should generate property-specific content', async () => {
      const mockListing = {
        title: 'Garden Apartment',
        location: 'Utrecht',
        description: 'Spacious apartment with private garden and parking'
      };

      const mockUserProfile = {
        name: 'Nature Lover',
        personalInfo: {
          fullName: 'Nature Lover',
          occupation: 'Landscape Architect'
        }
      };

      // Mock JSON response for property-specific content
      mockOpenAI.chat.completions.create.mockResolvedValue({
        choices: [{
          message: {
            content: JSON.stringify({
              propertyConnection: 'The private garden perfectly suits my work as a landscape architect',
              locationBenefits: 'Utrecht offers great work opportunities in my field',
              featureHighlights: ['Private garden', 'Parking space'],
              personalizedReasons: ['Perfect for my gardening hobby'],
              futureVision: 'I can create a beautiful garden space'
            })
          }
        }]
      });

      const result = await aiService.generatePropertySpecificContent(
        mockListing,
        mockUserProfile,
        ['garden', 'parking'],
        'english'
      );

      expect(result.propertyConnection).toContain('landscape architect');
      expect(result.featureHighlights).toContain('Private garden');
      expect(result.language).toBe('english');
    });

    it('should enhance basic content with property details', async () => {
      const baseContent = 'I am interested in your property.';
      const mockListing = {
        title: 'Luxury Penthouse',
        location: 'Amsterdam Zuid',
        description: 'Penthouse with roof terrace and canal views'
      };

      const mockUserProfile = {
        name: 'Professional',
        personalInfo: {
          fullName: 'Professional',
          occupation: 'Consultant'
        }
      };

      const enhancedContent = 'I am very interested in your Luxury Penthouse in Amsterdam Zuid. The roof terrace and canal views would be perfect for my lifestyle as a consultant who works from home.';

      mockOpenAI.chat.completions.create.mockResolvedValue({
        choices: [{
          message: {
            content: enhancedContent
          }
        }]
      });

      const result = await aiService.enhanceWithPropertyDetails(
        baseContent,
        mockListing,
        mockUserProfile,
        'english'
      );

      expect(result.originalContent).toBe(baseContent);
      expect(result.enhancedContent).toBe(enhancedContent);
      expect(result.enhancements).toContain('Content expanded with additional details');
    });
  });

  describe('Error Handling Integration', () => {
    it('should handle AI service failures gracefully', async () => {
      const mockListing = {
        title: 'Test Property'
      };

      const mockUserProfile = {
        name: 'John Doe',
        personalInfo: { fullName: 'John Doe' }
      };

      const mockSettings = {
        applicationTemplate: 'professional'
      };

      // Mock AI service failure
      mockOpenAI.chat.completions.create.mockRejectedValue(new Error('API Error'));

      // Should throw an error with proper message
      await expect(
        aiService.generateAutoApplicationLetter(
          mockListing,
          mockUserProfile,
          mockSettings,
          'dutch'
        )
      ).rejects.toThrow('Auto-application generation failed: API Error');
    });
  });

  describe('Performance Integration', () => {
    it('should handle multiple concurrent application generations', async () => {
      const mockListing = {
        title: 'Test Property',
        location: 'Amsterdam'
      };

      const mockUserProfile = {
        name: 'Test User',
        personalInfo: { fullName: 'Test User' }
      };

      const mockSettings = {
        applicationTemplate: 'professional'
      };

      // Generate multiple applications concurrently
      const promises = Array(5).fill().map(() =>
        aiService.generateAutoApplicationLetter(
          mockListing,
          mockUserProfile,
          mockSettings,
          'dutch'
        )
      );

      const results = await Promise.all(promises);

      expect(results).toHaveLength(5);
      results.forEach(result => {
        expect(result.subject).toContain('Test Property');
        expect(result.template).toBe('professional');
        expect(result.language).toBe('dutch');
      });

      // Verify AI service was called for each generation
      expect(mockOpenAI.chat.completions.create).toHaveBeenCalledTimes(5);
    });
  });
});