/**
 * Frontend Compatibility Integration Tests
 * 
 * Tests the integration of the frontend compatibility layer with the API endpoints
 * to ensure that property data is returned in a format compatible with frontend expectations.
 */

const request = require('supertest');
const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const express = require('express');
const Listing = require('../models/Listing');
const listingRoutes = require('../routes/listing');
const { convertToFrontendFormat } = require('../services/frontendCompatibilityLayer');

// Create a test app
const app = express();
app.use(express.json());
app.use('/api', listingRoutes);

// Global error handler for tests
app.use((err, req, res, next) => {
  res.status(err.statusCode || 500).json({
    status: 'error',
    message: err.message
  });
});

describe('Frontend Compatibility Integration', function() {
  let mongoServer;
  
  // Set up MongoDB memory server before tests
  before(async function() {
    this.timeout(10000); // Increase timeout for MongoDB setup
    
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    
    await mongoose.connect(mongoUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
  });
  
  // Clean up after tests
  after(async function() {
    await mongoose.disconnect();
    await mongoServer.stop();
  });
  
  // Clear database between tests
  beforeEach(async function() {
    await Listing.deleteMany({});
  });
  
  describe('API Endpoints with Frontend Compatibility', function() {
    it('should return listings in frontend-compatible format', async function() {
      // Create test listings with unified data
      const testListing = new Listing({
        title: 'Test Apartment',
        description: 'A nice apartment',
        price: '€ 1.500 per maand',
        location: 'Amsterdam',
        url: 'https://example.com',
        propertyType: 'apartment',
        rooms: '2',
        bedrooms: '1',
        size: '75 m²',
        year: '2015',
        interior: 'Gemeubileerd',
        source: 'funda.nl',
        images: ['image1.jpg', 'image2.jpg'],
        dateAdded: new Date(),
        // Add unified data
        unifiedData: {
          title: 'Test Apartment',
          description: 'A nice apartment',
          price: 1500,
          location: {
            _unified: {
              address: {
                street: 'Test Street',
                houseNumber: '123',
                city: 'Amsterdam'
              }
            },
            _legacy: 'Amsterdam'
          },
          propertyType: 'apartment',
          rooms: 2,
          bedrooms: 1,
          bathrooms: 1,
          area: 75,
          size: '75 m²',
          year: '2015',
          interior: 'Gemeubileerd',
          furnished: true,
          source: 'funda.nl',
          url: 'https://example.com',
          images: ['image1.jpg', 'image2.jpg'],
          dateAdded: new Date(),
          isActive: true
        }
      });
      
      await testListing.save();
      
      // Test getListings endpoint
      const response = await request(app)
        .get('/api/listings')
        .expect(200);
      
      expect(response.body.status).toBe('success');
      expect(Array.isArray(response.body.data.listings)).toBe(true);
      expect(response.body.data.listings.length).toBe(1);
      
      const listing = response.body.data.listings[0];
      
      // Verify frontend-compatible format
      expect(listing.title).toBe('Test Apartment');
      expect(listing.price).toBe('€ 1.500 per maand');
      expect(listing.location).toBe('Amsterdam');
      expect(listing.propertyType).toBe('apartment');
      expect(listing.rooms).toBe(2);
      expect(listing.bedrooms).toBe(1);
      expect(listing.bathrooms).toBe(1);
      expect(listing.area).toBe(75);
      expect(listing.size).toBe('75 m²');
      expect(listing.furnished).toBe(true);
      expect(listing.images).toEqual(['image1.jpg', 'image2.jpg']);
    });
    
    it('should return a single listing in frontend-compatible format', async function() {
      // Create a test listing with unified data
      const testListing = new Listing({
        title: 'Single Test Apartment',
        description: 'A nice apartment',
        price: '€ 1.200 per maand',
        location: 'Rotterdam',
        url: 'https://example.com',
        propertyType: 'apartment',
        rooms: '3',
        bedrooms: '2',
        size: '85 m²',
        year: '2018',
        interior: 'Gemeubileerd',
        source: 'funda.nl',
        images: ['image1.jpg'],
        dateAdded: new Date(),
        // Add unified data
        unifiedData: {
          title: 'Single Test Apartment',
          description: 'A nice apartment',
          price: 1200,
          location: {
            _unified: {
              address: {
                street: 'Test Avenue',
                houseNumber: '456',
                city: 'Rotterdam'
              }
            },
            _legacy: 'Rotterdam'
          },
          propertyType: 'apartment',
          rooms: 3,
          bedrooms: 2,
          bathrooms: 1,
          area: 85,
          size: '85 m²',
          year: '2018',
          interior: 'Gemeubileerd',
          furnished: true,
          source: 'funda.nl',
          url: 'https://example.com',
          images: ['image1.jpg'],
          dateAdded: new Date(),
          isActive: true
        }
      });
      
      await testListing.save();
      
      // Test getListingById endpoint
      const response = await request(app)
        .get(`/api/listings/${testListing._id}`)
        .expect(200);
      
      expect(response.body.status).toBe('success');
      expect(response.body.data.listing).toBeInstanceOf(Object);
      
      const listing = response.body.data.listing;
      
      // Verify frontend-compatible format
      expect(listing.title).toBe('Single Test Apartment');
      expect(listing.price).toBe('€ 1.200 per maand');
      expect(listing.location).toBe('Rotterdam');
      expect(listing.propertyType).toBe('apartment');
      expect(listing.rooms).toBe(3);
      expect(listing.bedrooms).toBe(2);
      expect(listing.bathrooms).toBe(1);
      expect(listing.area).toBe(85);
      expect(listing.size).toBe('85 m²');
      expect(listing.furnished).toBe(true);
      expect(listing.images).toEqual(['image1.jpg']);
    });
    
    it('should handle legacy listings without unified data', async function() {
      // Create a legacy listing without unified data
      const legacyListing = new Listing({
        title: 'Legacy Listing',
        description: 'An old listing',
        price: '€ 950 per maand',
        location: 'Utrecht',
        url: 'https://example.com',
        propertyType: 'woning',
        rooms: '1',
        size: '45 m²',
        year: '2000',
        interior: 'Kaal',
        source: 'huurwoningen.nl',
        images: [],
        dateAdded: new Date()
      });
      
      await legacyListing.save();
      
      // Test getListings endpoint
      const response = await request(app)
        .get('/api/listings')
        .expect(200);
      
      expect(response.body.status).toBe('success');
      expect(Array.isArray(response.body.data.listings)).toBe(true);
      expect(response.body.data.listings.length).toBe(1);
      
      const listing = response.body.data.listings[0];
      
      // Verify original format is preserved
      expect(listing.title).toBe('Legacy Listing');
      expect(listing.price).toBe('€ 950 per maand');
      expect(listing.location).toBe('Utrecht');
      expect(listing.propertyType).toBe('woning');
      expect(listing.rooms).toBe('1');
      expect(listing.size).toBe('45 m²');
      expect(listing.year).toBe('2000');
      expect(listing.interior).toBe('Kaal');
    });
  });
});