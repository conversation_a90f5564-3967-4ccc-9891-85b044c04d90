# Implementation Plan

- [x] 1. Implement price extraction utility function





  - Create a utility function to extract numeric values from price strings in various European formats
  - Handle edge cases like null values, malformed strings, and different separator conventions
  - Add comprehensive unit tests for price extraction logic
  - _Requirements: 2.2, 2.3, 2.4_

- [x] 2. Create quick stats service method





  - Add `getQuickStats()` method to the existing searchService class
  - Implement MongoDB aggregation pipeline using `$facet` to calculate all three statistics in one query
  - Add price extraction logic within the aggregation pipeline for average price calculation
  - Implement date filtering for "newToday" calculation using current day boundaries
  - _Requirements: 1.1, 1.2, 2.1, 3.1, 3.2, 3.3_

- [x] 3. Add caching layer for performance optimization





  - Implement Redis caching for quick stats with 5-minute expiration
  - Add cache key management and cache hit/miss logic
  - Implement fallback to database calculation when cache fails
  - Add cache status indicator in response
  - _Requirements: 5.1, 5.2, 5.3_

- [x] 4. Create controller method for quick stats endpoint





  - Add `getQuickStats` method to listingController.js
  - Implement proper error handling with fallback to default values
  - Format response according to API specification
  - Add timestamp and cache status to response
  - _Requirements: 4.1, 4.2, 4.3, 4.4_

- [x] 5. Register new API route





  - Add `/api/listings/quick-stats` route to listing.js router
  - Apply appropriate middleware (caching, rate limiting)
  - Add Swagger documentation for the new endpoint
  - _Requirements: 4.1, 4.2_

- [ ] 6. Write comprehensive unit tests
  - Create unit tests for price extraction utility function
  - Write tests for the quick stats service method with various data scenarios
  - Test caching functionality including cache hits, misses, and failures
  - Test controller method with success and error scenarios
  - _Requirements: 1.1, 1.2, 2.1, 2.2, 2.3, 3.1, 3.2, 3.3, 4.3, 4.4_

- [ ] 7. Create integration tests for API endpoint
  - Write integration tests for the complete API endpoint
  - Test response format and status codes
  - Test performance requirements (sub-500ms response time)
  - Test concurrent request handling
  - _Requirements: 4.1, 4.2, 4.3, 4.4, 5.3_

- [x] 8. Add error handling and logging





  - Implement comprehensive error handling for database failures
  - Add logging for performance metrics and error tracking
  - Implement graceful degradation when partial data is available
  - Add monitoring hooks for the new endpoint
  - _Requirements: 4.4, 5.1, 5.2, 5.3_