const {
  scrapeFunda,
  scrape<PERSON><PERSON><PERSON>,
  scrapeHuurwoningen,
  getScrapingMetrics,
  enableScraper,
  disableScraper,
  getScraperStatus,
  getAgentStatus,
  stopAgent,
  forceStopAll,
} = require("../services/scraper");
const { catchAsync, AppError } = require("../middleware/errorHandler");

exports.scrape = catchAsync(async (req, res, next) => {
  console.log("Manual scraping triggered by user");

  // Run all three scrapers in parallel
  const [fundaResult, pariusResult, huurwoningenResult] =
    await Promise.allSettled([
      scrapeFunda(),
      scrapePararius(),
      scrapeHuurwoningen(),
    ]);

  let allListings = [];
  let errors = [];

  if (fundaResult.status === "fulfilled") {
    allListings = allListings.concat(fundaResult.value || []);
  } else {
    errors.push({ source: "Funda", error: fundaResult.reason.message });
  }

  if (pariusResult.status === "fulfilled") {
    allListings = allListings.concat(pariusResult.value || []);
  } else {
    errors.push({ source: "Pararius", error: pariusResult.reason.message });
  }

  if (huurwoningenResult.status === "fulfilled") {
    allListings = allListings.concat(huurwoningenResult.value || []);
  } else {
    errors.push({
      source: "Huurwoningen",
      error: huurwoningenResult.reason.message,
    });
  }

  if (allListings.length === 0) {
    return next(new AppError("No listings were found during scraping", 404));
  }

  res.status(200).json({
    status: "success",
    message: "Scraping completed successfully",
    results: allListings.length,
    data: {
      listings: allListings,
      errors: errors.length > 0 ? errors : undefined,
    },
    metrics: getScrapingMetrics(),
  });
});

exports.getMetrics = catchAsync(async (req, res, next) => {
  const metrics = getScrapingMetrics();

  res.status(200).json({
    status: "success",
    data: {
      metrics,
    },
  });
});

exports.enableScraper = catchAsync(async (req, res, next) => {
  const { scraperName } = req.params;
  
  if (!scraperName) {
    return next(new AppError("Scraper name is required", 400));
  }

  const result = enableScraper(scraperName.toLowerCase());
  
  if (!result.success) {
    return next(new AppError(result.message, 400));
  }

  res.status(200).json({
    status: "success",
    message: result.message,
    data: {
      activeScrapers: result.activeScrapers
    }
  });
});

exports.disableScraper = catchAsync(async (req, res, next) => {
  const { scraperName } = req.params;
  
  if (!scraperName) {
    return next(new AppError("Scraper name is required", 400));
  }

  const result = disableScraper(scraperName.toLowerCase());
  
  if (!result.success) {
    return next(new AppError(result.message, 400));
  }

  res.status(200).json({
    status: "success",
    message: result.message,
    data: {
      activeScrapers: result.activeScrapers
    }
  });
});

exports.getScraperStatus = catchAsync(async (req, res, next) => {
  const status = getScraperStatus();

  res.status(200).json({
    status: "success",
    data: status
  });
});

exports.getAgentStatus = catchAsync(async (req, res, next) => {
  const agentStatus = getAgentStatus();

  res.status(200).json({
    status: "success",
    data: agentStatus
  });
});

exports.stopAgent = catchAsync(async (req, res, next) => {
  const result = stopAgent();

  if (!result.success) {
    return next(new AppError(result.message, 400));
  }

  res.status(200).json({
    status: "success",
    message: result.message
  });
});

exports.forceStopAll = catchAsync(async (req, res, next) => {
  const result = await forceStopAll();

  res.status(200).json({
    status: "success",
    message: result.message,
    data: result
  });
});
