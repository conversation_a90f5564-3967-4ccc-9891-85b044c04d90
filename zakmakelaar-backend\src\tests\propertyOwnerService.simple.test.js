const mongoose = require('mongoose');
const propertyOwnerService = require('../services/propertyOwnerService');
const User = require('../models/User');
const tenantScoringService = require('../services/tenantScoringService');
const aiService = require('../services/aiService');
const cacheService = require('../services/cacheService');

// Mock dependencies
jest.mock('../services/tenantScoringService');
jest.mock('../services/aiService');
jest.mock('../services/cacheService');
jest.mock('../services/logger', () => ({
  loggers: {
    app: {
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn()
    }
  }
}));

describe('PropertyOwnerService - Core Functionality', () => {
  let testUser;
  let testPropertyOwner;

  beforeAll(async () => {
    // Connect to test database
    const mongoUri = process.env.MONGODB_TEST_URI || 'mongodb://localhost:27017/zakmakelaar_test';
    if (mongoose.connection.readyState === 0) {
      await mongoose.connect(mongoUri);
    }
  });

  beforeEach(async () => {
    // Clear test data
    await User.deleteMany({});

    // Create test user with unique email
    const timestamp = Date.now();
    testUser = new User({
      email: `test${timestamp}@example.com`,
      password: 'password123',
      profile: {
        firstName: 'John',
        lastName: 'Doe',
        userType: ['property_owner']
      }
    });
    await testUser.save();

    // Create test property owner with unique email
    testPropertyOwner = new User({
      email: `owner${timestamp}@example.com`,
      password: 'password123',
      profile: {
        firstName: 'Jane',
        lastName: 'Smith',
        userType: ['property_owner']
      },
      propertyOwner: {
        isPropertyOwner: true,
        verificationStatus: 'verified',
        businessRegistration: '********',
        taxNumber: 'NL********9B12',
        bankAccount: '******************',
        properties: []
      }
    });
    await testPropertyOwner.save();

    // Reset mocks
    jest.clearAllMocks();
  });

  afterAll(async () => {
    await User.deleteMany({});
    if (mongoose.connection.readyState !== 0) {
      await mongoose.connection.close();
    }
  });

  describe('Property Owner Registration', () => {
    it('should successfully register a user as property owner', async () => {
      // Requirements: 10.1, 10.2
      const registrationData = {
        businessRegistration: '********',
        taxNumber: 'NL9********B12',
        bankAccount: 'NL98WXYZ9********0',
        businessName: 'Test Property Management B.V.'
      };

      const result = await propertyOwnerService.registerAsPropertyOwner(testUser._id, registrationData);

      expect(result.success).toBe(true);
      expect(result.message).toBe('Property owner registration completed');
      expect(result.verificationStatus).toBe('pending');

      // Verify user was updated
      const updatedUser = await User.findById(testUser._id);
      expect(updatedUser.propertyOwner.isPropertyOwner).toBe(true);
      expect(updatedUser.propertyOwner.businessRegistration).toBe('********');
      expect(updatedUser.profile.userType).toContain('property_owner');
    });

    it('should prevent duplicate property owner registration', async () => {
      // Requirements: 10.1
      const registrationData = {
        businessRegistration: '********'
      };

      await expect(
        propertyOwnerService.registerAsPropertyOwner(testPropertyOwner._id, registrationData)
      ).rejects.toThrow('User is already registered as a property owner');
    });

    it('should handle user not found error', async () => {
      // Requirements: 10.1
      const nonExistentUserId = new mongoose.Types.ObjectId();
      const registrationData = {
        businessRegistration: '********'
      };

      await expect(
        propertyOwnerService.registerAsPropertyOwner(nonExistentUserId, registrationData)
      ).rejects.toThrow('User not found');
    });
  });

  describe('Property Owner Verification', () => {
    it('should successfully verify property owner', async () => {
      // Requirements: 10.2, 10.3
      const verificationDocuments = [
        { documentId: 'doc1', type: 'business_registration' },
        { documentId: 'doc2', type: 'tax_certificate' }
      ];

      const result = await propertyOwnerService.verifyPropertyOwner(testPropertyOwner._id, verificationDocuments);

      expect(result.overallStatus).toBeDefined();
      expect(result.businessRegistration).toBeDefined();
      expect(result.taxNumber).toBeDefined();
      expect(result.bankAccount).toBeDefined();
      expect(result.verificationScore).toBeGreaterThanOrEqual(0);
      expect(result.verificationScore).toBeLessThanOrEqual(100);
    });

    it('should reject verification for non-property owner', async () => {
      // Requirements: 10.2
      await expect(
        propertyOwnerService.verifyPropertyOwner(testUser._id)
      ).rejects.toThrow('User is not registered as a property owner');
    });

    it('should return cached verification results', async () => {
      // Requirements: 10.2
      const cachedResult = {
        overallStatus: 'verified',
        verificationScore: 85,
        businessRegistration: { valid: true }
      };

      cacheService.get.mockResolvedValue(cachedResult);

      const result = await propertyOwnerService.verifyPropertyOwner(testPropertyOwner._id);

      expect(result).toEqual(cachedResult);
      expect(cacheService.get).toHaveBeenCalledWith(`property_owner_verification:${testPropertyOwner._id}`);
    });
  });

  describe('Property Owner Dashboard', () => {
    it('should return comprehensive dashboard data', async () => {
      // Requirements: 10.4, 10.5
      const result = await propertyOwnerService.getPropertyOwnerDashboard(testPropertyOwner._id);

      expect(result.owner).toBeDefined();
      expect(result.owner.id.toString()).toBe(testPropertyOwner._id.toString());
      expect(result.owner.verificationStatus).toBe('verified');

      expect(result.properties).toBeDefined();
      expect(result.properties.total).toBeGreaterThanOrEqual(0);

      expect(result.applications).toBeDefined();
      expect(result.screening).toBeDefined();
      expect(result.performance).toBeDefined();
      expect(result.lastUpdated).toBeDefined();
    });

    it('should reject dashboard access for non-property owner', async () => {
      // Requirements: 10.4
      await expect(
        propertyOwnerService.getPropertyOwnerDashboard(testUser._id)
      ).rejects.toThrow('User is not a property owner');
    });
  });

  describe('Property Management', () => {
    it('should list properties for owner', async () => {
      // Requirements: 10.3, 10.4
      const result = await propertyOwnerService.manageProperties(testPropertyOwner._id, 'list');

      expect(Array.isArray(result)).toBe(true);
    });

    it('should add new property', async () => {
      // Requirements: 10.3
      const propertyData = {
        title: 'Test Property',
        address: 'Test Street 123',
        rent: 2500,
        propertyType: 'apartment'
      };

      const result = await propertyOwnerService.manageProperties(testPropertyOwner._id, 'add', propertyData);

      expect(result.success).toBe(true);
      expect(result.propertyId).toBeDefined();
      expect(result.message).toBe('Property added successfully');
    });

    it('should reject invalid action', async () => {
      // Requirements: 10.3
      await expect(
        propertyOwnerService.manageProperties(testPropertyOwner._id, 'invalid_action')
      ).rejects.toThrow('Invalid property management action: invalid_action');
    });
  });

  describe('Tenant Screening', () => {
    beforeEach(() => {
      // Mock tenant scoring service
      tenantScoringService.calculateTenantScore.mockResolvedValue({
        overallScore: 85,
        components: {
          incomeStability: 90,
          rentalHistory: 80,
          creditworthiness: 85,
          employment: 90,
          references: 75
        },
        verificationLevel: 'verified'
      });

      // Mock AI service
      aiService.openai = {
        chat: {
          completions: {
            create: jest.fn().mockResolvedValue({
              choices: [{
                message: {
                  content: 'Strong candidate with excellent tenant score. Recommend approval.'
                }
              }]
            })
          }
        }
      };
    });

    it('should handle empty application list', async () => {
      // Requirements: 10.4
      const propertyId = 'prop123';
      const applicationIds = [];

      // Mock the internal method to return empty applications
      const originalMethod = propertyOwnerService._getApplicationsForScreening;
      propertyOwnerService._getApplicationsForScreening = jest.fn().mockResolvedValue([]);

      const result = await propertyOwnerService.screenTenants(propertyId, applicationIds);

      expect(result.screenedApplications).toHaveLength(0);
      expect(result.message).toBe('No applications found for screening');

      // Restore original method
      propertyOwnerService._getApplicationsForScreening = originalMethod;
    });
  });

  describe('Report Generation', () => {
    it('should generate comprehensive report', async () => {
      // Requirements: 10.6
      const propertyId = 'prop123';
      const reportType = 'comprehensive';

      const result = await propertyOwnerService.generatePropertyReport(propertyId, reportType);

      expect(result.propertyId).toBe(propertyId);
      expect(result.reportType).toBe(reportType);
      expect(result.property).toBeDefined();
      expect(result.generatedAt).toBeDefined();
    });

    it('should reject invalid report type', async () => {
      // Requirements: 10.6
      const propertyId = 'prop123';
      const invalidReportType = 'invalid_type';

      await expect(
        propertyOwnerService.generatePropertyReport(propertyId, invalidReportType)
      ).rejects.toThrow('Invalid report type: invalid_type');
    });
  });

  describe('AI Integration', () => {
    it('should integrate with AI service for tenant evaluation', async () => {
      // Requirements: 10.4, 10.5
      const mockAIResponse = {
        choices: [{
          message: {
            content: 'Based on the tenant score of 85/100, this applicant shows strong financial stability and rental history. Recommend approval with standard lease terms.'
          }
        }]
      };

      aiService.openai = {
        chat: {
          completions: {
            create: jest.fn().mockResolvedValue(mockAIResponse)
          }
        }
      };

      const application = { applicantId: testUser._id };
      const property = { title: 'Test Property', location: 'Amsterdam', price: '€2,500' };
      const screeningResult = { overallScore: 85 };

      const result = await propertyOwnerService._getAIRecommendation(application, property, screeningResult);

      expect(result).toContain('Recommend approval');
      expect(aiService.openai.chat.completions.create).toHaveBeenCalled();
    });

    it('should handle AI service errors gracefully', async () => {
      // Requirements: 10.4, 10.5
      aiService.openai = {
        chat: {
          completions: {
            create: jest.fn().mockRejectedValue(new Error('AI service unavailable'))
          }
        }
      };

      const application = { applicantId: testUser._id };
      const property = { title: 'Test Property' };
      const screeningResult = { overallScore: 85 };

      const result = await propertyOwnerService._getAIRecommendation(application, property, screeningResult);

      expect(result).toBe('Unable to generate AI recommendation at this time.');
    });
  });

  describe('Caching', () => {
    it('should cache verification results', async () => {
      // Requirements: 10.2
      const verificationData = {
        overallStatus: 'verified',
        verificationScore: 85
      };

      await propertyOwnerService._cacheVerification(testPropertyOwner._id, verificationData);

      expect(cacheService.set).toHaveBeenCalledWith(
        `property_owner_verification:${testPropertyOwner._id}`,
        verificationData,
        86400 // 24 hours
      );
    });

    it('should retrieve cached verification results', async () => {
      // Requirements: 10.2
      const cachedData = {
        overallStatus: 'verified',
        verificationScore: 85
      };

      cacheService.get.mockResolvedValue(cachedData);

      const result = await propertyOwnerService._getCachedVerification(testPropertyOwner._id);

      expect(result).toEqual(cachedData);
      expect(cacheService.get).toHaveBeenCalledWith(`property_owner_verification:${testPropertyOwner._id}`);
    });
  });

  describe('Integration Test', () => {
    it('should complete basic property owner workflow', async () => {
      // Requirements: 10.1, 10.2, 10.3, 10.4, 10.6
      
      // Step 1: Register as property owner
      const registrationData = {
        businessRegistration: '********',
        taxNumber: 'NL9********B12',
        bankAccount: 'NL98WXYZ9********0',
        businessName: 'Integration Test Properties B.V.'
      };

      const registrationResult = await propertyOwnerService.registerAsPropertyOwner(testUser._id, registrationData);
      expect(registrationResult.success).toBe(true);

      // Step 2: Verify property owner
      const verificationResult = await propertyOwnerService.verifyPropertyOwner(testUser._id);
      expect(verificationResult.overallStatus).toBeDefined();

      // Step 3: Add property
      const propertyData = {
        title: 'Integration Test Property',
        address: 'Test Street 123',
        rent: 2500,
        propertyType: 'apartment'
      };

      const addPropertyResult = await propertyOwnerService.manageProperties(testUser._id, 'add', propertyData);
      expect(addPropertyResult.success).toBe(true);

      // Step 4: Get dashboard
      const dashboardResult = await propertyOwnerService.getPropertyOwnerDashboard(testUser._id);
      expect(dashboardResult.owner.id.toString()).toBe(testUser._id.toString());

      // Step 5: Generate report
      const reportResult = await propertyOwnerService.generatePropertyReport(addPropertyResult.propertyId, 'comprehensive');
      expect(reportResult.propertyId).toBe(addPropertyResult.propertyId);
      expect(reportResult.reportType).toBe('comprehensive');
    });
  });
});