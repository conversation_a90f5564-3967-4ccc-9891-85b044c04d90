const ApplicationQueueManager = require('../../services/applicationQueueManager');
const ApplicationQueue = require('../../models/ApplicationQueue');
const AutoApplicationSettings = require('../../models/AutoApplicationSettings');
const logger = require('../../services/logger');

// Mock dependencies
jest.mock('../../models/ApplicationQueue');
jest.mock('../../models/AutoApplicationSettings');
jest.mock('../../services/logger', () => ({
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  debug: jest.fn()
}));

describe('ApplicationQueueManager', () => {
  let queueManager;
  let mockQueueItem;
  let mockUserSettings;

  beforeEach(() => {
    queueManager = new ApplicationQueueManager();
    
    // Mock queue item
    mockQueueItem = {
      _id: 'queue123',
      userId: 'user123',
      listingId: 'listing123',
      listingUrl: 'https://funda.nl/listing123',
      priority: 10,
      status: 'pending',
      attempts: 0,
      maxAttempts: 3,
      scheduledAt: new Date(),
      applicationData: {
        personalInfo: { fullName: 'John Doe', email: '<EMAIL>' },
        preferences: { template: 'professional' }
      },
      listingSnapshot: {
        title: 'Test Property',
        price: 1500,
        rooms: 2,
        availableFrom: new Date()
      },
      save: jest.fn().mockResolvedValue(true),
      updateStatus: jest.fn().mockResolvedValue(true),
      incrementAttempt: jest.fn().mockResolvedValue(true),
      scheduleRetry: jest.fn().mockResolvedValue(true),
      updateMetadata: jest.fn().mockResolvedValue(true),
      canRetry: true
    };

    // Mock user settings
    mockUserSettings = {
      userId: 'user123',
      settings: {
        requireManualReview: false,
        maxApplicationsPerDay: 10
      }
    };

    // Reset mocks
    jest.clearAllMocks();
  });

  afterEach(() => {
    if (queueManager.isProcessing) {
      queueManager.stopProcessing();
    }
  });

  describe('initialization', () => {
    it('should initialize successfully', async () => {
      ApplicationQueue.updateMany = jest.fn().mockResolvedValue({ modifiedCount: 0 });
      
      await queueManager.initialize();
      
      expect(queueManager.isProcessing).toBe(true);
      expect(ApplicationQueue.updateMany).toHaveBeenCalledWith(
        expect.objectContaining({ status: 'processing' }),
        expect.objectContaining({ status: 'pending' })
      );
    });

    it('should handle initialization errors', async () => {
      // Mock resetStuckItems to throw an error
      jest.spyOn(queueManager, 'resetStuckItems').mockRejectedValue(new Error('DB Error'));
      
      await expect(queueManager.initialize()).rejects.toThrow('DB Error');
    });
  });

  describe('addToQueue', () => {
    beforeEach(() => {
      ApplicationQueue.findOne = jest.fn().mockResolvedValue(null);
      ApplicationQueue.prototype.save = jest.fn().mockResolvedValue(mockQueueItem);
      AutoApplicationSettings.findOne = jest.fn().mockResolvedValue(mockUserSettings);
    });

    it('should add application to queue successfully', async () => {
      const applicationData = {
        userId: 'user123',
        listingId: 'listing123',
        listingUrl: 'https://funda.nl/listing123',
        personalInfo: { fullName: 'John Doe' },
        preferences: { template: 'professional' },
        documents: [],
        listingSnapshot: { title: 'Test Property', price: 1500 }
      };

      const result = await queueManager.addToQueue(applicationData);

      expect(ApplicationQueue.findOne).toHaveBeenCalledWith({
        userId: 'user123',
        listingId: 'listing123'
      });
      expect(result).toBeDefined();
    });

    it('should reject duplicate applications', async () => {
      ApplicationQueue.findOne = jest.fn().mockResolvedValue(mockQueueItem);

      const applicationData = {
        userId: 'user123',
        listingId: 'listing123',
        listingUrl: 'https://funda.nl/listing123'
      };

      const result = await queueManager.addToQueue(applicationData);
      
      expect(result).toBe(mockQueueItem);
      expect(logger.warn).toHaveBeenCalledWith(
        expect.stringContaining('Application already exists')
      );
    });

    it('should respect rate limits', async () => {
      // Set rate limits to exceeded
      queueManager.rateLimits.perUser.maxPerHour = 1;
      queueManager.userRateLimits.set('user123', {
        currentHour: 1,
        currentDay: 1,
        hourReset: new Date(Date.now() + 3600000),
        dayReset: new Date(Date.now() + 86400000)
      });

      const applicationData = {
        userId: 'user123',
        listingId: 'listing123',
        listingUrl: 'https://funda.nl/listing123'
      };

      await expect(queueManager.addToQueue(applicationData))
        .rejects.toThrow('Rate limit exceeded');
    });

    it('should calculate priority correctly', async () => {
      const applicationData = {
        userId: 'user123',
        listingId: 'listing123',
        listingUrl: 'https://funda.nl/listing123',
        listingSnapshot: {
          price: 1200, // Low price should increase priority
          availableFrom: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000), // 5 days
          propertyType: 'apartment',
          scrapedAt: new Date(Date.now() - 30 * 60 * 1000) // 30 minutes ago
        }
      };

      const priority = await queueManager.calculatePriority(applicationData);
      
      expect(priority).toBeGreaterThan(0);
      // Should get points for low price (20), soon available (15), apartment (5), recent (15)
      expect(priority).toBeGreaterThanOrEqual(55);
    });
  });

  describe('queue processing', () => {
    beforeEach(() => {
      ApplicationQueue.findReadyItems = jest.fn().mockResolvedValue([mockQueueItem]);
      ApplicationQueue.getQueueStats = jest.fn().mockResolvedValue([]);
      ApplicationQueue.countDocuments = jest.fn().mockResolvedValue(1);
    });

    it('should start and stop processing', () => {
      queueManager.startProcessing();
      expect(queueManager.isProcessing).toBe(true);
      expect(queueManager.processingInterval).toBeDefined();

      queueManager.stopProcessing();
      expect(queueManager.isProcessing).toBe(false);
      expect(queueManager.processingInterval).toBeNull();
    });

    it('should process queue items', async () => {
      jest.spyOn(queueManager, 'processQueueItem').mockResolvedValue();
      jest.spyOn(queueManager, 'updateQueueStats').mockResolvedValue();

      await queueManager.processQueue();

      expect(queueManager.processQueueItem).toHaveBeenCalledWith(mockQueueItem);
      expect(queueManager.updateQueueStats).toHaveBeenCalled();
    });

    it('should handle processing errors gracefully', async () => {
      jest.spyOn(queueManager, 'processQueueItem').mockRejectedValue(new Error('Processing error'));
      jest.spyOn(queueManager, 'updateQueueStats').mockResolvedValue();

      await queueManager.processQueue();

      expect(logger.error).not.toHaveBeenCalled(); // Errors are handled in processQueueItem
    });

    it('should respect rate limits when getting ready items', async () => {
      // Set global rate limit to exceeded
      queueManager.rateLimits.global.currentHour = queueManager.rateLimits.global.maxPerHour;
      queueManager.rateLimits.global.hourReset = new Date(Date.now() + 3600000); // Future time
      
      // Mock resetRateLimitCounters to not reset the counters
      jest.spyOn(queueManager, 'resetRateLimitCounters').mockImplementation(() => {});

      const readyItems = await queueManager.getReadyItems();

      expect(readyItems).toEqual([]);
      expect(logger.info).toHaveBeenCalledWith(
        expect.stringContaining('Global rate limit reached')
      );
    });

    it('should filter items by user rate limits', async () => {
      queueManager.userRateLimits.set('user123', {
        currentHour: queueManager.rateLimits.perUser.maxPerHour,
        currentDay: 1,
        hourReset: new Date(Date.now() + 3600000),
        dayReset: new Date(Date.now() + 86400000)
      });

      const readyItems = await queueManager.getReadyItems();

      expect(readyItems).toEqual([]);
    });
  });

  describe('processQueueItem', () => {
    beforeEach(() => {
      jest.spyOn(queueManager, 'simulateApplicationProcessing')
        .mockResolvedValue({ success: true });
      jest.spyOn(queueManager, 'sleep').mockResolvedValue();
    });

    it('should process successful application', async () => {
      await queueManager.processQueueItem(mockQueueItem);

      expect(mockQueueItem.updateStatus).toHaveBeenCalledWith('processing');
      expect(mockQueueItem.updateStatus).toHaveBeenCalledWith('completed');
      expect(queueManager.queueStats.successful).toBe(1);
      expect(queueManager.queueStats.processed).toBe(1);
    });

    it('should handle processing failure with retry', async () => {
      const error = new Error('Processing failed');
      jest.spyOn(queueManager, 'simulateApplicationProcessing')
        .mockResolvedValue({ success: false, error });
      jest.spyOn(queueManager, 'handleProcessingFailure').mockResolvedValue();

      await queueManager.processQueueItem(mockQueueItem);

      expect(queueManager.handleProcessingFailure).toHaveBeenCalledWith(mockQueueItem, error);
    });

    it('should apply random delays', async () => {
      await queueManager.processQueueItem(mockQueueItem);

      expect(queueManager.sleep).toHaveBeenCalledWith(expect.any(Number));
    });
  });

  describe('retry mechanism', () => {
    it('should handle processing failure with retry', async () => {
      const error = new Error('Test error');
      mockQueueItem.canRetry = true;

      await queueManager.handleProcessingFailure(mockQueueItem, error);

      expect(mockQueueItem.incrementAttempt).toHaveBeenCalled();
      expect(mockQueueItem.scheduleRetry).toHaveBeenCalled();
      expect(queueManager.queueStats.retries).toBe(1);
    });

    it('should mark as failed when max attempts reached', async () => {
      const error = new Error('Test error');
      mockQueueItem.canRetry = false;

      await queueManager.handleProcessingFailure(mockQueueItem, error);

      expect(mockQueueItem.updateStatus).toHaveBeenCalledWith('failed', error);
      expect(queueManager.queueStats.failed).toBe(1);
    });

    it('should calculate exponential backoff correctly', () => {
      const delay1 = queueManager.calculateExponentialBackoff(1);
      const delay2 = queueManager.calculateExponentialBackoff(2);
      const delay3 = queueManager.calculateExponentialBackoff(3);

      expect(delay1).toBeGreaterThanOrEqual(4); // ~5 minutes with jitter
      expect(delay1).toBeLessThanOrEqual(6);
      
      expect(delay2).toBeGreaterThanOrEqual(8); // ~10 minutes with jitter
      expect(delay2).toBeLessThanOrEqual(12);
      
      expect(delay3).toBeGreaterThanOrEqual(16); // ~20 minutes with jitter
      expect(delay3).toBeLessThanOrEqual(24);
    });
  });

  describe('rate limiting', () => {
    it('should check rate limits correctly', async () => {
      const result = await queueManager.checkRateLimits('user123');
      expect(result.allowed).toBe(true);
    });

    it('should reject when global hourly limit exceeded', async () => {
      // Set the current hour to exceed the limit
      queueManager.rateLimits.global.currentHour = queueManager.rateLimits.global.maxPerHour;
      // Set the reset time to future to prevent automatic reset
      queueManager.rateLimits.global.hourReset = new Date(Date.now() + 3600000);

      const result = await queueManager.checkRateLimits('user123');
      
      expect(result.allowed).toBe(false);
      expect(result.reason).toContain('Global hourly limit');
    });

    it('should reject when user daily limit exceeded', async () => {
      queueManager.userRateLimits.set('user123', {
        currentHour: 0,
        currentDay: queueManager.rateLimits.perUser.maxPerDay,
        hourReset: new Date(Date.now() + 3600000),
        dayReset: new Date(Date.now() + 86400000)
      });

      const result = await queueManager.checkRateLimits('user123');
      
      expect(result.allowed).toBe(false);
      expect(result.reason).toContain('User daily limit');
    });

    it('should update rate limit counters', async () => {
      await queueManager.updateRateLimitCounters('user123');

      expect(queueManager.rateLimits.global.currentHour).toBe(1);
      expect(queueManager.rateLimits.global.currentDay).toBe(1);
      
      const userLimits = queueManager.getUserRateLimits('user123');
      expect(userLimits.currentHour).toBe(1);
      expect(userLimits.currentDay).toBe(1);
    });

    it('should reset rate limit counters when time windows expire', () => {
      // Set counters to non-zero values
      queueManager.rateLimits.global.currentHour = 10;
      queueManager.rateLimits.global.hourReset = new Date(Date.now() - 1000); // Past time

      queueManager.resetRateLimitCounters();

      expect(queueManager.rateLimits.global.currentHour).toBe(0);
      expect(queueManager.rateLimits.global.hourReset).toBeInstanceOf(Date);
    });

    it('should update rate limits configuration', () => {
      const newLimits = {
        global: { maxPerHour: 200 },
        perUser: { maxPerDay: 100 }
      };

      queueManager.updateRateLimits(newLimits);

      expect(queueManager.rateLimits.global.maxPerHour).toBe(200);
      expect(queueManager.rateLimits.perUser.maxPerDay).toBe(100);
    });
  });

  describe('queue control', () => {
    beforeEach(() => {
      ApplicationQueue.updateMany = jest.fn().mockResolvedValue({ modifiedCount: 1 });
    });

    it('should pause queue for specific user', async () => {
      await queueManager.pauseQueue('user123', 'Test pause');

      expect(ApplicationQueue.updateMany).toHaveBeenCalledWith(
        { userId: 'user123', status: 'pending' },
        { status: 'paused', 'metadata.pauseReason': 'Test pause' }
      );
    });

    it('should pause entire queue', async () => {
      queueManager.startProcessing();
      
      await queueManager.pauseQueue(null, 'Global pause');

      expect(queueManager.isProcessing).toBe(false);
      expect(ApplicationQueue.updateMany).toHaveBeenCalledWith(
        { status: 'pending' },
        { status: 'paused', 'metadata.pauseReason': 'Global pause' }
      );
    });

    it('should resume queue for specific user', async () => {
      await queueManager.resumeQueue('user123');

      expect(ApplicationQueue.updateMany).toHaveBeenCalledWith(
        { userId: 'user123', status: 'paused' },
        { status: 'pending', $unset: { 'metadata.pauseReason': 1 } }
      );
    });

    it('should resume entire queue', async () => {
      await queueManager.resumeQueue();

      expect(queueManager.isProcessing).toBe(true);
      expect(ApplicationQueue.updateMany).toHaveBeenCalledWith(
        { status: 'paused' },
        { status: 'pending', $unset: { 'metadata.pauseReason': 1 } }
      );
    });
  });

  describe('queue monitoring', () => {
    beforeEach(() => {
      ApplicationQueue.getQueueStats = jest.fn().mockResolvedValue([
        { _id: 'pending', count: 5 },
        { _id: 'completed', count: 10 }
      ]);
      ApplicationQueue.countDocuments = jest.fn().mockResolvedValue(3);
    });

    it('should get queue status', async () => {
      const status = await queueManager.getQueueStatus();

      expect(status).toHaveProperty('isProcessing');
      expect(status).toHaveProperty('queueStats');
      expect(status).toHaveProperty('statusBreakdown');
      expect(status).toHaveProperty('readyToProcess');
      expect(status).toHaveProperty('rateLimits');
      
      expect(status.readyToProcess).toBe(3);
    });

    it('should update queue statistics', async () => {
      ApplicationQueue.aggregate = jest.fn().mockResolvedValue([{
        totalProcessed: 20,
        successful: 15,
        failed: 5,
        avgProcessingTime: 45000
      }]);

      await queueManager.updateQueueStats();

      expect(queueManager.queueStats.processed).toBe(20);
      expect(queueManager.queueStats.successful).toBe(15);
      expect(queueManager.queueStats.failed).toBe(5);
      expect(queueManager.queueStats.averageProcessingTime).toBe(45000);
    });
  });

  describe('cleanup and maintenance', () => {
    it('should reset stuck items', async () => {
      ApplicationQueue.updateMany = jest.fn().mockResolvedValue({ modifiedCount: 2 });

      await queueManager.resetStuckItems();

      expect(ApplicationQueue.updateMany).toHaveBeenCalledWith(
        expect.objectContaining({ status: 'processing' }),
        expect.objectContaining({ status: 'pending' })
      );
    });

    it('should cleanup expired items', async () => {
      const expiredItem = { ...mockQueueItem, updateStatus: jest.fn().mockResolvedValue() };
      ApplicationQueue.findExpiredItems = jest.fn().mockResolvedValue([expiredItem]);

      await queueManager.cleanupExpiredItems();

      expect(expiredItem.updateStatus).toHaveBeenCalledWith('cancelled', expect.any(Error));
    });
  });

  describe('utility methods', () => {
    it('should generate random delay within range', () => {
      const delay = queueManager.generateRandomDelay();
      
      expect(delay).toBeGreaterThanOrEqual(2 * 60 * 1000); // 2 minutes
      expect(delay).toBeLessThanOrEqual(10 * 60 * 1000); // 10 minutes
    });

    it('should sleep for specified duration', async () => {
      const start = Date.now();
      await queueManager.sleep(100);
      const end = Date.now();
      
      expect(end - start).toBeGreaterThanOrEqual(100);
    });

    it('should shutdown gracefully', async () => {
      queueManager.startProcessing();
      
      await queueManager.shutdown();
      
      expect(queueManager.isProcessing).toBe(false);
      expect(queueManager.processingInterval).toBeNull();
    });
  });

  describe('priority calculation', () => {
    it('should calculate higher priority for low-priced properties', async () => {
      const lowPriceData = {
        listingSnapshot: { price: 1200 },
        userId: 'user123'
      };
      
      const highPriceData = {
        listingSnapshot: { price: 2500 },
        userId: 'user123'
      };

      AutoApplicationSettings.findOne = jest.fn().mockResolvedValue(mockUserSettings);

      const lowPricePriority = await queueManager.calculatePriority(lowPriceData);
      const highPricePriority = await queueManager.calculatePriority(highPriceData);

      expect(lowPricePriority).toBeGreaterThan(highPricePriority);
    });

    it('should calculate higher priority for soon-available properties', async () => {
      const soonAvailable = {
        listingSnapshot: { 
          availableFrom: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000) // 5 days
        },
        userId: 'user123'
      };
      
      const laterAvailable = {
        listingSnapshot: { 
          availableFrom: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000) // 60 days
        },
        userId: 'user123'
      };

      AutoApplicationSettings.findOne = jest.fn().mockResolvedValue(mockUserSettings);

      const soonPriority = await queueManager.calculatePriority(soonAvailable);
      const laterPriority = await queueManager.calculatePriority(laterAvailable);

      expect(soonPriority).toBeGreaterThan(laterPriority);
    });

    it('should calculate higher priority for recently scraped listings', async () => {
      const recentListing = {
        listingSnapshot: { 
          scrapedAt: new Date(Date.now() - 30 * 60 * 1000) // 30 minutes ago
        },
        userId: 'user123'
      };
      
      const oldListing = {
        listingSnapshot: { 
          scrapedAt: new Date(Date.now() - 25 * 60 * 60 * 1000) // 25 hours ago
        },
        userId: 'user123'
      };

      AutoApplicationSettings.findOne = jest.fn().mockResolvedValue(mockUserSettings);

      const recentPriority = await queueManager.calculatePriority(recentListing);
      const oldPriority = await queueManager.calculatePriority(oldListing);

      expect(recentPriority).toBeGreaterThan(oldPriority);
    });

    it('should handle missing listing data gracefully', async () => {
      const emptyData = { userId: 'user123' };
      
      AutoApplicationSettings.findOne = jest.fn().mockResolvedValue(null);

      const priority = await queueManager.calculatePriority(emptyData);

      expect(priority).toBe(0);
    });
  });
});