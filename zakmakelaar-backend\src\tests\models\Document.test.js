const mongoose = require('mongoose');
const Document = require('../../models/Document');

describe('Document Model', () => {
  const mockUserId = new mongoose.Types.ObjectId();
  const mockAdminId = new mongoose.Types.ObjectId();

  const validDocumentData = {
    userId: mockUserId,
    filename: 'test-document.pdf',
    originalName: 'salary_slip.pdf',
    type: 'income_proof',
    size: 1024000,
    mimeType: 'application/pdf',
    encryptedPath: '/uploads/documents/encrypted/test-document.pdf.enc'
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Schema Validation', () => {
    it('should create a valid document', () => {
      const document = new Document(validDocumentData);
      
      expect(document.userId).toEqual(mockUserId);
      expect(document.filename).toBe('test-document.pdf');
      expect(document.originalName).toBe('salary_slip.pdf');
      expect(document.type).toBe('income_proof');
      expect(document.size).toBe(1024000);
      expect(document.mimeType).toBe('application/pdf');
      expect(document.encryptedPath).toBe('/uploads/documents/encrypted/test-document.pdf.enc');
      expect(document.verified).toBe(false); // Default value
      expect(document.createdAt).toBeInstanceOf(Date);
    });

    it('should require userId', () => {
      const document = new Document({
        ...validDocumentData,
        userId: undefined
      });

      const error = document.validateSync();
      expect(error.errors.userId).toBeDefined();
      expect(error.errors.userId.message).toContain('required');
    });

    it('should require filename', () => {
      const document = new Document({
        ...validDocumentData,
        filename: undefined
      });

      const error = document.validateSync();
      expect(error.errors.filename).toBeDefined();
    });

    it('should require originalName', () => {
      const document = new Document({
        ...validDocumentData,
        originalName: undefined
      });

      const error = document.validateSync();
      expect(error.errors.originalName).toBeDefined();
    });

    it('should require type', () => {
      const document = new Document({
        ...validDocumentData,
        type: undefined
      });

      const error = document.validateSync();
      expect(error.errors.type).toBeDefined();
    });

    it('should validate document type enum', () => {
      const document = new Document({
        ...validDocumentData,
        type: 'invalid_type'
      });

      const error = document.validateSync();
      expect(error.errors.type).toBeDefined();
      expect(error.errors.type.message).toContain('enum');
    });

    it('should accept valid document types', () => {
      const validTypes = [
        'income_proof',
        'employment_contract',
        'bank_statement',
        'id_document',
        'rental_reference',
        'other'
      ];

      validTypes.forEach(type => {
        const document = new Document({
          ...validDocumentData,
          type
        });

        const error = document.validateSync();
        expect(error?.errors?.type).toBeUndefined();
      });
    });

    it('should require size', () => {
      const document = new Document({
        ...validDocumentData,
        size: undefined
      });

      const error = document.validateSync();
      expect(error.errors.size).toBeDefined();
    });

    it('should require mimeType', () => {
      const document = new Document({
        ...validDocumentData,
        mimeType: undefined
      });

      const error = document.validateSync();
      expect(error.errors.mimeType).toBeDefined();
    });

    it('should require encryptedPath', () => {
      const document = new Document({
        ...validDocumentData,
        encryptedPath: undefined
      });

      const error = document.validateSync();
      expect(error.errors.encryptedPath).toBeDefined();
    });
  });

  describe('Virtual Fields', () => {
    it('should calculate isExpired virtual field correctly', () => {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 30);

      const pastDate = new Date();
      pastDate.setDate(pastDate.getDate() - 30);

      const documentNotExpired = new Document({
        ...validDocumentData,
        expiryDate: futureDate
      });

      const documentExpired = new Document({
        ...validDocumentData,
        expiryDate: pastDate
      });

      const documentNoExpiry = new Document(validDocumentData);

      expect(documentNotExpired.isExpired).toBe(false);
      expect(documentExpired.isExpired).toBe(true);
      expect(documentNoExpiry.isExpired).toBeFalsy(); // undefined or false
    });

    it('should calculate fileExtension virtual field correctly', () => {
      const pdfDocument = new Document({
        ...validDocumentData,
        originalName: 'document.pdf'
      });

      const jpgDocument = new Document({
        ...validDocumentData,
        originalName: 'image.JPG'
      });

      const docxDocument = new Document({
        ...validDocumentData,
        originalName: 'contract.docx'
      });

      expect(pdfDocument.fileExtension).toBe('pdf');
      expect(jpgDocument.fileExtension).toBe('jpg');
      expect(docxDocument.fileExtension).toBe('docx');
    });

    it('should calculate humanReadableSize virtual field correctly', () => {
      const bytesDocument = new Document({
        ...validDocumentData,
        size: 512
      });

      const kbDocument = new Document({
        ...validDocumentData,
        size: 1536 // 1.5 KB
      });

      const mbDocument = new Document({
        ...validDocumentData,
        size: 2097152 // 2 MB
      });

      const gbDocument = new Document({
        ...validDocumentData,
        size: 1073741824 // 1 GB
      });

      expect(bytesDocument.humanReadableSize).toBe('512 Bytes');
      expect(kbDocument.humanReadableSize).toBe('1.5 KB');
      expect(mbDocument.humanReadableSize).toBe('2 MB');
      expect(gbDocument.humanReadableSize).toBe('1 GB');
    });

    it('should handle zero size correctly', () => {
      const zeroSizeDocument = new Document({
        ...validDocumentData,
        size: 0
      });

      expect(zeroSizeDocument.humanReadableSize).toBe('0 Bytes');
    });
  });

  describe('Instance Methods', () => {
    describe('logAccess', () => {
      it('should add access log entry', async () => {
        const document = new Document(validDocumentData);
        document.save = jest.fn().mockResolvedValue(document);

        await document.logAccess(mockUserId, 'view', '127.0.0.1');

        expect(document.accessLog).toHaveLength(1);
        expect(document.accessLog[0]).toMatchObject({
          accessedBy: mockUserId,
          action: 'view',
          ipAddress: '127.0.0.1',
          accessedAt: expect.any(Date)
        });
        expect(document.save).toHaveBeenCalled();
      });

      it('should handle multiple access log entries', async () => {
        const document = new Document(validDocumentData);
        document.save = jest.fn().mockResolvedValue(document);

        await document.logAccess(mockUserId, 'view', '127.0.0.1');
        await document.logAccess(mockUserId, 'download', '127.0.0.1');

        expect(document.accessLog).toHaveLength(2);
        expect(document.accessLog[0].action).toBe('view');
        expect(document.accessLog[1].action).toBe('download');
      });
    });

    describe('canAccess', () => {
      it('should allow document owner to access', () => {
        const document = new Document({
          ...validDocumentData,
          userId: mockUserId
        });

        expect(document.canAccess(mockUserId, 'user')).toBe(true);
      });

      it('should allow admin to access any document', () => {
        const document = new Document({
          ...validDocumentData,
          userId: mockUserId
        });

        expect(document.canAccess(mockAdminId, 'admin')).toBe(true);
      });

      it('should deny access to unauthorized users', () => {
        const document = new Document({
          ...validDocumentData,
          userId: mockUserId
        });

        const unauthorizedUserId = new mongoose.Types.ObjectId();
        expect(document.canAccess(unauthorizedUserId, 'user')).toBe(false);
      });
    });
  });

  describe('Static Methods', () => {
    describe('findByUserAndType', () => {
      it('should build correct query for user and type', () => {
        const mockFind = jest.fn().mockReturnValue({
          sort: jest.fn().mockResolvedValue([])
        });
        Document.find = mockFind;

        Document.findByUserAndType(mockUserId, 'income_proof');

        expect(mockFind).toHaveBeenCalledWith({
          userId: mockUserId,
          type: 'income_proof'
        });
      });

      it('should build correct query for user only', () => {
        const mockFind = jest.fn().mockReturnValue({
          sort: jest.fn().mockResolvedValue([])
        });
        Document.find = mockFind;

        Document.findByUserAndType(mockUserId, null);

        expect(mockFind).toHaveBeenCalledWith({
          userId: mockUserId
        });
      });
    });

    describe('findExpired', () => {
      it('should find documents with expiry date in the past', () => {
        const mockFind = jest.fn().mockResolvedValue([]);
        Document.find = mockFind;

        Document.findExpired();

        expect(mockFind).toHaveBeenCalledWith({
          expiryDate: { $lt: expect.any(Date) }
        });
      });
    });

    describe('findUnverified', () => {
      it('should find unverified documents with user population', () => {
        const mockFind = jest.fn().mockReturnValue({
          populate: jest.fn().mockResolvedValue([])
        });
        Document.find = mockFind;

        Document.findUnverified();

        expect(mockFind).toHaveBeenCalledWith({ verified: false });
      });
    });
  });

  describe('Pre-save Middleware', () => {
    it('should update updatedAt on save', async () => {
      const document = new Document(validDocumentData);
      const originalUpdatedAt = document.updatedAt;

      // Wait a bit to ensure different timestamp
      await new Promise(resolve => setTimeout(resolve, 1));

      // Simulate pre-save middleware
      document.updatedAt = new Date();

      expect(document.updatedAt.getTime()).toBeGreaterThan(originalUpdatedAt.getTime());
    });

    it('should generate encryption key if not provided', () => {
      const document = new Document(validDocumentData);

      // Simulate pre-save middleware
      if (!document.encryptionKey) {
        document.encryptionKey = require('crypto').randomBytes(32).toString('hex');
      }

      expect(document.encryptionKey).toBeDefined();
      expect(document.encryptionKey).toHaveLength(64); // 32 bytes = 64 hex chars
    });

    it('should not overwrite existing encryption key', () => {
      const existingKey = 'existing-encryption-key';
      const document = new Document({
        ...validDocumentData,
        encryptionKey: existingKey
      });

      // Simulate pre-save middleware
      if (!document.encryptionKey) {
        document.encryptionKey = require('crypto').randomBytes(32).toString('hex');
      }

      expect(document.encryptionKey).toBe(existingKey);
    });
  });

  describe('Metadata Handling', () => {
    it('should accept metadata with all fields', () => {
      const document = new Document({
        ...validDocumentData,
        metadata: {
          extractedText: 'Sample text content',
          pageCount: 3,
          language: 'en',
          containsPII: true,
          securityLevel: 'high'
        }
      });

      expect(document.metadata.extractedText).toBe('Sample text content');
      expect(document.metadata.pageCount).toBe(3);
      expect(document.metadata.language).toBe('en');
      expect(document.metadata.containsPII).toBe(true);
      expect(document.metadata.securityLevel).toBe('high');
    });

    it('should validate security level enum', () => {
      const document = new Document({
        ...validDocumentData,
        metadata: {
          securityLevel: 'invalid_level'
        }
      });

      const error = document.validateSync();
      expect(error.errors['metadata.securityLevel']).toBeDefined();
    });

    it('should accept valid security levels', () => {
      const validLevels = ['low', 'medium', 'high'];

      validLevels.forEach(level => {
        const document = new Document({
          ...validDocumentData,
          metadata: {
            securityLevel: level
          }
        });

        const error = document.validateSync();
        expect(error?.errors?.['metadata.securityLevel']).toBeUndefined();
      });
    });
  });

  describe('Access Log', () => {
    it('should accept access log entries', () => {
      const document = new Document({
        ...validDocumentData,
        accessLog: [{
          accessedBy: mockUserId,
          action: 'view',
          ipAddress: '127.0.0.1'
        }]
      });

      expect(document.accessLog).toHaveLength(1);
      expect(document.accessLog[0].accessedBy).toEqual(mockUserId);
      expect(document.accessLog[0].action).toBe('view');
      expect(document.accessLog[0].ipAddress).toBe('127.0.0.1');
      expect(document.accessLog[0].accessedAt).toBeInstanceOf(Date);
    });

    it('should validate access log action enum', () => {
      const document = new Document({
        ...validDocumentData,
        accessLog: [{
          accessedBy: mockUserId,
          action: 'invalid_action',
          ipAddress: '127.0.0.1'
        }]
      });

      const error = document.validateSync();
      expect(error.errors['accessLog.0.action']).toBeDefined();
    });

    it('should accept valid access log actions', () => {
      const validActions = ['view', 'download', 'verify', 'delete'];

      validActions.forEach(action => {
        const document = new Document({
          ...validDocumentData,
          accessLog: [{
            accessedBy: mockUserId,
            action: action,
            ipAddress: '127.0.0.1'
          }]
        });

        const error = document.validateSync();
        expect(error?.errors?.['accessLog.0.action']).toBeUndefined();
      });
    });
  });

  describe('Indexes', () => {
    it('should have proper indexes defined', () => {
      const indexes = Document.schema.indexes();
      
      // Check for userId index
      expect(indexes.some(index => 
        index[0].userId === 1
      )).toBe(true);

      // Check for type index
      expect(indexes.some(index => 
        index[0].type === 1
      )).toBe(true);

      // Check for verified index
      expect(indexes.some(index => 
        index[0].verified === 1
      )).toBe(true);

      // Check for createdAt index
      expect(indexes.some(index => 
        index[0].createdAt === -1
      )).toBe(true);

      // Check for compound index (userId + type)
      expect(indexes.some(index => 
        index[0].userId === 1 && index[0].type === 1
      )).toBe(true);

      // Check for compound index (userId + verified)
      expect(indexes.some(index => 
        index[0].userId === 1 && index[0].verified === 1
      )).toBe(true);
    });
  });

  describe('JSON Serialization', () => {
    it('should include virtual fields in JSON output', () => {
      const document = new Document({
        ...validDocumentData,
        size: 1024000,
        originalName: 'document.pdf',
        expiryDate: new Date('2023-01-01') // Past date
      });

      const json = document.toJSON();

      expect(json.humanReadableSize).toBeDefined();
      expect(json.fileExtension).toBeDefined();
      expect(json.isExpired).toBeDefined();
      expect(json.id).toBeDefined(); // MongoDB _id virtual
    });
  });
});