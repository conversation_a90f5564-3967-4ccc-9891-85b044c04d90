// Test to verify the photo grid fix works without VirtualizedList error
import React from 'react';
import { View, Text, ScrollView, Image, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface Photo {
  uri: string;
  name: string;
  type: string;
  isPrimary: boolean;
}

const TestPhotoGridFix = () => {
  const photos: Photo[] = [
    { uri: 'test1.jpg', name: 'photo1', type: 'image/jpeg', isPrimary: true },
    { uri: 'test2.jpg', name: 'photo2', type: 'image/jpeg', isPrimary: false },
    { uri: 'test3.jpg', name: 'photo3', type: 'image/jpeg', isPrimary: false },
  ];

  return (
    <ScrollView style={{ flex: 1 }}>
      <View style={{ padding: 20 }}>
        <Text style={{ fontSize: 18, fontWeight: 'bold' }}>Property Photos</Text>
        
        {/* Photo Grid - No FlatList, using map instead */}
        {photos.length > 0 && (
          <View style={{ marginTop: 20 }}>
            <Text>Selected Photos ({photos.length})</Text>
            <View style={{
              flexDirection: 'row',
              flexWrap: 'wrap',
              paddingTop: 12,
              justifyContent: 'space-between',
            }}>
              {photos.map((item, index) => (
                <View key={index} style={{
                  width: '48%',
                  marginBottom: 12,
                  borderRadius: 12,
                  overflow: 'hidden',
                  backgroundColor: '#F0F0F0',
                  position: 'relative',
                }}>
                  <View style={{ width: '100%', height: 120, backgroundColor: '#DDD' }} />
                  
                  {/* Primary Photo Badge */}
                  {item.isPrimary && (
                    <View style={{
                      position: 'absolute',
                      top: 8,
                      left: 8,
                      backgroundColor: '#007AFF',
                      paddingHorizontal: 8,
                      paddingVertical: 4,
                      borderRadius: 12,
                    }}>
                      <Text style={{ color: '#FFFFFF', fontSize: 10, fontWeight: '600' }}>
                        Primary
                      </Text>
                    </View>
                  )}
                  
                  {/* Photo Actions */}
                  <View style={{
                    position: 'absolute',
                    top: 8,
                    right: 8,
                    flexDirection: 'row',
                    gap: 8,
                  }}>
                    {!item.isPrimary && (
                      <TouchableOpacity style={{
                        backgroundColor: 'rgba(0, 0, 0, 0.6)',
                        borderRadius: 16,
                        width: 32,
                        height: 32,
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}>
                        <Ionicons name="star-outline" size={20} color="#FFF" />
                      </TouchableOpacity>
                    )}
                    
                    <TouchableOpacity style={{
                      backgroundColor: 'rgba(255, 59, 48, 0.8)',
                      borderRadius: 16,
                      width: 32,
                      height: 32,
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                      <Ionicons name="trash-outline" size={20} color="#FFF" />
                    </TouchableOpacity>
                  </View>
                </View>
              ))}
            </View>
          </View>
        )}
        
        <Text style={{ marginTop: 20 }}>
          ✅ This layout uses map() instead of FlatList to avoid VirtualizedList nesting error
        </Text>
      </View>
    </ScrollView>
  );
};

export default TestPhotoGridFix;