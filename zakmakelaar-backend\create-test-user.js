/**
 * <PERSON><PERSON><PERSON> to create a test user for auto application testing
 */

const axios = require('axios');

const API_BASE_URL = process.env.API_URL || 'http://localhost:3000';

const TEST_USER = {
  email: '<EMAIL>',
  password: 'Password123',
  firstName: 'Wellishant',
  lastName: 'Test',
  isPropertyOwner: false
};

async function createTestUser() {
  try {
    console.log('🔧 Creating test user...');

    const response = await axios.post(`${API_BASE_URL}/api/auth/register`, TEST_USER);

    if (response.data.success) {
      console.log('✅ Test user created successfully');
      console.log(`📧 Email: ${TEST_USER.email}`);
      console.log(`🔑 Password: ${TEST_USER.password}`);
      console.log(`👤 User ID: ${response.data.user.id}`);

      // Set up basic preferences
      const token = response.data.token;
      const preferences = {
        minPrice: 1000,
        maxPrice: 2500,
        preferredLocations: ['Amsterdam', 'Utrecht'],
        propertyTypes: ['apartment', 'house'],
        minRooms: 2,
        maxRooms: 4,
        amenities: ['parking', 'balcony'],
        notifications: {
          email: true,
          push: false,
          sms: false
        }
      };

      try {
        const prefResponse = await axios.put(`${API_BASE_URL}/api/users/preferences`, preferences, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (prefResponse.data.success) {
          console.log('✅ Basic preferences set');
        }
      } catch (prefError) {
        console.log('⚠️  Could not set preferences:', prefError.response?.data?.message || prefError.message);
      }

      return true;
    } else {
      console.log('❌ Failed to create user:', response.data.message || 'Unknown error');
      console.log('📋 Full response:', JSON.stringify(response.data, null, 2));
      return false;
    }
  } catch (error) {
    if (error.response?.status === 400 && error.response?.data?.message?.includes('already exists')) {
      console.log('ℹ️  Test user already exists');
      console.log(`📧 Email: ${TEST_USER.email}`);
      console.log(`🔑 Password: ${TEST_USER.password}`);
      return true;
    } else if (error.code === 'ECONNREFUSED') {
      console.log('❌ Cannot connect to backend server');
      console.log('💡 Make sure the backend server is running on', API_BASE_URL);
      console.log('💡 Start the server with: npm run dev');
      return false;
    } else {
      console.log('❌ Error creating user:', error.response?.data?.message || error.message);
      console.log('📋 Error details:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data
      });
      return false;
    }
  }
}

// Run if this file is executed directly
if (require.main === module) {
  createTestUser().catch(console.error);
}

module.exports = createTestUser;