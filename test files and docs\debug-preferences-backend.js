// Debug script to check preferences and listings in the backend
const mongoose = require('mongoose');
require('dotenv').config({ path: './zakmakelaar-backend/.env' });

// Import models
const User = require('./zakmakelaar-backend/src/models/User');
const Listing = require('./zakmakelaar-backend/src/models/Listing');

async function debugPreferencesBackend() {
  console.log('🔍 Debugging preferences fallback issue (Backend)...\n');

  try {
    // Connect to MongoDB
    console.log('Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/zakmakelaar');
    console.log('✅ Connected to MongoDB\n');

    // 1. Check if there are any users with preferences
    console.log('1. Checking users with preferences...');
    const usersWithPreferences = await User.find({ 
      preferences: { $exists: true, $ne: null } 
    }).select('email firstName preferences').limit(5);

    console.log(`Found ${usersWithPreferences.length} users with preferences:`);
    usersWithPreferences.forEach((user, index) => {
      console.log(`User ${index + 1}:`, {
        email: user.email,
        firstName: user.firstName,
        preferences: user.preferences
      });
    });

    if (usersWithPreferences.length === 0) {
      console.log('❌ No users found with preferences');
      return;
    }

    // Use the first user with preferences for testing
    const testUser = usersWithPreferences[0];
    console.log(`\nUsing test user: ${testUser.email}`);
    console.log('Test user preferences:', testUser.preferences);

    // 2. Check total listings in database
    console.log('\n2. Checking total listings in database...');
    const totalListings = await Listing.countDocuments();
    console.log(`Total listings in database: ${totalListings}`);

    if (totalListings === 0) {
      console.log('❌ No listings found in database');
      return;
    }

    // 3. Sample some listings to see their structure
    console.log('\n3. Sampling listings structure...');
    const sampleListings = await Listing.find().limit(3).select('title price location propertyType rooms dateAdded');
    console.log('Sample listings:');
    sampleListings.forEach((listing, index) => {
      console.log(`Listing ${index + 1}:`, {
        title: listing.title,
        price: listing.price,
        location: listing.location,
        propertyType: listing.propertyType,
        rooms: listing.rooms,
        dateAdded: listing.dateAdded
      });
    });

    // 4. Test filtering with user preferences
    console.log('\n4. Testing filtering with user preferences...');
    const preferences = testUser.preferences;
    
    // Build filter query
    const filterQuery = {};
    
    // Price filtering
    if (preferences.minPrice !== undefined || preferences.maxPrice !== undefined) {
      console.log(`Testing price filter: ${preferences.minPrice} - ${preferences.maxPrice}`);
      
      // Add price extraction pipeline
      const priceFilterPipeline = [
        {
          $addFields: {
            numericPrice: {
              $let: {
                vars: {
                  extractedPrice: {
                    $regexFind: {
                      input: { $ifNull: ["$price", ""] },
                      regex: /€\s*([\d.,]+)/,
                    },
                  },
                },
                in: {
                  $convert: {
                    input: {
                      $cond: {
                        if: {
                          $and: [
                            { $ne: ["$extractedPrice", null] },
                            { $isArray: "$extractedPrice.captures" },
                            { $gt: [{ $size: "$extractedPrice.captures" }, 0] },
                          ],
                        },
                        then: {
                          $replaceAll: {
                            input: {
                              $replaceAll: {
                                input: {
                                  $toString: {
                                    $arrayElemAt: ["$extractedPrice.captures", 0],
                                  },
                                },
                                find: ".",
                                replacement: "",
                              },
                            },
                            find: ",",
                            replacement: ".",
                          },
                        },
                        else: "0",
                      },
                    },
                    to: "double",
                    onError: 0,
                    onNull: 0,
                  },
                },
              },
            },
          },
        }
      ];

      const priceMatchStage = { numericPrice: {} };
      if (preferences.minPrice) {
        priceMatchStage.numericPrice.$gte = parseFloat(preferences.minPrice);
      }
      if (preferences.maxPrice) {
        priceMatchStage.numericPrice.$lte = parseFloat(preferences.maxPrice);
      }

      priceFilterPipeline.push({ $match: priceMatchStage });
      priceFilterPipeline.push({ $limit: 10 });

      const priceFilteredListings = await Listing.aggregate(priceFilterPipeline);
      console.log(`Found ${priceFilteredListings.length} listings matching price filter`);
      
      if (priceFilteredListings.length > 0) {
        console.log('Sample price-filtered listing:', {
          title: priceFilteredListings[0].title,
          price: priceFilteredListings[0].price,
          numericPrice: priceFilteredListings[0].numericPrice,
          location: priceFilteredListings[0].location
        });
      }
    }

    // Location filtering
    if (preferences.preferredLocations && preferences.preferredLocations.length > 0) {
      console.log(`\nTesting location filter: ${preferences.preferredLocations.join(', ')}`);
      
      const locationQuery = {
        $or: preferences.preferredLocations.map(location => ({
          location: { $regex: location, $options: 'i' }
        }))
      };

      const locationFilteredListings = await Listing.find(locationQuery).limit(10);
      console.log(`Found ${locationFilteredListings.length} listings matching location filter`);
      
      if (locationFilteredListings.length > 0) {
        console.log('Sample location-filtered listing:', {
          title: locationFilteredListings[0].title,
          price: locationFilteredListings[0].price,
          location: locationFilteredListings[0].location
        });
      }
    }

    // Property type filtering
    if (preferences.propertyTypes && preferences.propertyTypes.length > 0) {
      console.log(`\nTesting property type filter: ${preferences.propertyTypes.join(', ')}`);
      
      const propertyTypeQuery = {
        $or: preferences.propertyTypes.map(type => ({
          propertyType: { $regex: type, $options: 'i' }
        }))
      };

      const propertyTypeFilteredListings = await Listing.find(propertyTypeQuery).limit(10);
      console.log(`Found ${propertyTypeFilteredListings.length} listings matching property type filter`);
      
      if (propertyTypeFilteredListings.length > 0) {
        console.log('Sample property-type-filtered listing:', {
          title: propertyTypeFilteredListings[0].title,
          price: propertyTypeFilteredListings[0].price,
          propertyType: propertyTypeFilteredListings[0].propertyType
        });
      }
    }

    // Rooms filtering
    if (preferences.minRooms !== undefined || preferences.maxRooms !== undefined) {
      console.log(`\nTesting rooms filter: ${preferences.minRooms} - ${preferences.maxRooms}`);
      
      const roomsQuery = {};
      if (preferences.minRooms) {
        roomsQuery.$gte = parseInt(preferences.minRooms);
      }
      if (preferences.maxRooms) {
        roomsQuery.$lte = parseInt(preferences.maxRooms);
      }

      const roomsFilteredListings = await Listing.find({ rooms: roomsQuery }).limit(10);
      console.log(`Found ${roomsFilteredListings.length} listings matching rooms filter`);
      
      if (roomsFilteredListings.length > 0) {
        console.log('Sample rooms-filtered listing:', {
          title: roomsFilteredListings[0].title,
          price: roomsFilteredListings[0].price,
          rooms: roomsFilteredListings[0].rooms
        });
      }
    }

    // 5. Test combined filtering (like the frontend does)
    console.log('\n5. Testing combined filtering...');
    
    const combinedPipeline = [];
    const combinedMatchStage = {};

    // Add price filtering
    if (preferences.minPrice !== undefined || preferences.maxPrice !== undefined) {
      combinedPipeline.push({
        $addFields: {
          numericPrice: {
            $let: {
              vars: {
                extractedPrice: {
                  $regexFind: {
                    input: { $ifNull: ["$price", ""] },
                    regex: /€\s*([\d.,]+)/,
                  },
                },
              },
              in: {
                $convert: {
                  input: {
                    $cond: {
                      if: {
                        $and: [
                          { $ne: ["$extractedPrice", null] },
                          { $isArray: "$extractedPrice.captures" },
                          { $gt: [{ $size: "$extractedPrice.captures" }, 0] },
                        ],
                      },
                      then: {
                        $replaceAll: {
                          input: {
                            $replaceAll: {
                              input: {
                                $toString: {
                                  $arrayElemAt: ["$extractedPrice.captures", 0],
                                },
                              },
                              find: ".",
                              replacement: "",
                            },
                          },
                          find: ",",
                          replacement: ".",
                        },
                      },
                      else: "0",
                    },
                  },
                  to: "double",
                  onError: 0,
                  onNull: 0,
                },
              },
            },
          },
        },
      });

      combinedMatchStage.numericPrice = {};
      if (preferences.minPrice) {
        combinedMatchStage.numericPrice.$gte = parseFloat(preferences.minPrice);
      }
      if (preferences.maxPrice) {
        combinedMatchStage.numericPrice.$lte = parseFloat(preferences.maxPrice);
      }
    }

    // Add location filtering
    if (preferences.preferredLocations && preferences.preferredLocations.length > 0) {
      combinedMatchStage.$or = (combinedMatchStage.$or || []).concat(
        preferences.preferredLocations.map(location => ({
          location: { $regex: location, $options: 'i' }
        }))
      );
    }

    // Add property type filtering
    if (preferences.propertyTypes && preferences.propertyTypes.length > 0) {
      combinedMatchStage.$or = (combinedMatchStage.$or || []).concat(
        preferences.propertyTypes.map(type => ({
          propertyType: { $regex: type, $options: 'i' }
        }))
      );
    }

    // Add rooms filtering
    if (preferences.minRooms !== undefined || preferences.maxRooms !== undefined) {
      combinedMatchStage.rooms = {};
      if (preferences.minRooms) {
        combinedMatchStage.rooms.$gte = parseInt(preferences.minRooms);
      }
      if (preferences.maxRooms) {
        combinedMatchStage.rooms.$lte = parseInt(preferences.maxRooms);
      }
    }

    if (Object.keys(combinedMatchStage).length > 0) {
      combinedPipeline.push({ $match: combinedMatchStage });
    }

    combinedPipeline.push({ $limit: 10 });
    combinedPipeline.push({ $sort: { dateAdded: -1 } });

    console.log('Combined filter pipeline:', JSON.stringify(combinedPipeline, null, 2));

    const combinedFilteredListings = await Listing.aggregate(combinedPipeline);
    console.log(`Found ${combinedFilteredListings.length} listings with combined filters`);

    if (combinedFilteredListings.length > 0) {
      console.log('✅ Combined filtering found listings!');
      console.log('Sample combined-filtered listing:', {
        title: combinedFilteredListings[0].title,
        price: combinedFilteredListings[0].price,
        location: combinedFilteredListings[0].location,
        propertyType: combinedFilteredListings[0].propertyType,
        rooms: combinedFilteredListings[0].rooms,
        numericPrice: combinedFilteredListings[0].numericPrice
      });
    } else {
      console.log('❌ Combined filtering found no listings - this explains the fallback!');
    }

    // 6. Check what locations and property types are actually available
    console.log('\n6. Checking available locations and property types...');
    
    const availableLocations = await Listing.distinct('location');
    console.log(`Available locations (${availableLocations.length}):`, availableLocations.slice(0, 10));
    
    const availablePropertyTypes = await Listing.distinct('propertyType');
    console.log(`Available property types (${availablePropertyTypes.length}):`, availablePropertyTypes);

    // 7. Check price ranges
    console.log('\n7. Checking price ranges...');
    const priceAnalysisPipeline = [
      {
        $addFields: {
          numericPrice: {
            $let: {
              vars: {
                extractedPrice: {
                  $regexFind: {
                    input: { $ifNull: ["$price", ""] },
                    regex: /€\s*([\d.,]+)/,
                  },
                },
              },
              in: {
                $convert: {
                  input: {
                    $cond: {
                      if: {
                        $and: [
                          { $ne: ["$extractedPrice", null] },
                          { $isArray: "$extractedPrice.captures" },
                          { $gt: [{ $size: "$extractedPrice.captures" }, 0] },
                        ],
                      },
                      then: {
                        $replaceAll: {
                          input: {
                            $replaceAll: {
                              input: {
                                $toString: {
                                  $arrayElemAt: ["$extractedPrice.captures", 0],
                                },
                              },
                              find: ".",
                              replacement: "",
                            },
                          },
                          find: ",",
                          replacement: ".",
                        },
                      },
                      else: "0",
                    },
                  },
                  to: "double",
                  onError: 0,
                  onNull: 0,
                },
              },
            },
          },
        },
      },
      {
        $match: { numericPrice: { $gt: 0 } }
      },
      {
        $group: {
          _id: null,
          minPrice: { $min: "$numericPrice" },
          maxPrice: { $max: "$numericPrice" },
          avgPrice: { $avg: "$numericPrice" },
          count: { $sum: 1 }
        }
      }
    ];

    const [priceStats] = await Listing.aggregate(priceAnalysisPipeline);
    if (priceStats) {
      console.log('Price statistics:', {
        minPrice: priceStats.minPrice,
        maxPrice: priceStats.maxPrice,
        avgPrice: Math.round(priceStats.avgPrice),
        validPriceCount: priceStats.count
      });
    }

  } catch (error) {
    console.error('❌ Error during debugging:', error.message);
    console.error('Stack trace:', error.stack);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

// Run the debug function
debugPreferencesBackend().then(() => {
  console.log('\n🏁 Debug complete');
  process.exit(0);
}).catch(error => {
  console.error('💥 Fatal error:', error);
  process.exit(1);
});