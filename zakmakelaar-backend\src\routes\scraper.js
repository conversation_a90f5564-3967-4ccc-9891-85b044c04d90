const express = require("express");
const scraperController = require("../controllers/scraperController");
const { scrapingLimiter } = require("../middleware/rateLimiter");

const router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     ScrapeResponse:
 *       type: object
 *       properties:
 *         status:
 *           type: string
 *           example: success
 *         message:
 *           type: string
 *           description: Status message about the scraping operation
 *         data:
 *           type: object
 *           properties:
 *             newListings:
 *               type: number
 *               description: Number of new listings found
 *             totalProcessed:
 *               type: number
 *               description: Total number of listings processed
 *             duplicatesSkipped:
 *               type: number
 *               description: Number of duplicate listings skipped
 *             duration:
 *               type: string
 *               description: Time taken for the scraping operation
 *               example: 15.2s
 *             sites:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   name:
 *                     type: string
 *                     description: Name of the scraped site
 *                   status:
 *                     type: string
 *                     description: Scraping status for this site
 *                   listings:
 *                     type: number
 *                     description: Number of listings found on this site
 *     ScraperInfo:
 *       type: object
 *       properties:
 *         enabled:
 *           type: boolean
 *           description: Whether the scraper is currently enabled
 *         available:
 *           type: boolean
 *           description: Whether the scraper is available for use
 *     ScraperStatusResponse:
 *       type: object
 *       properties:
 *         status:
 *           type: string
 *           example: success
 *         data:
 *           type: object
 *           properties:
 *             scrapers:
 *               type: object
 *               properties:
 *                 funda:
 *                   $ref: '#/components/schemas/ScraperInfo'
 *                 pararius:
 *                   $ref: '#/components/schemas/ScraperInfo'
 *                 huurwoningen:
 *                   $ref: '#/components/schemas/ScraperInfo'
 *             activeScrapers:
 *               type: array
 *               items:
 *                 type: string
 *               example: ["funda", "pararius"]
 *             totalActive:
 *               type: number
 *               example: 2
 *             totalAvailable:
 *               type: number
 *               example: 3
 *     ScraperActionResponse:
 *       type: object
 *       properties:
 *         status:
 *           type: string
 *           example: success
 *         message:
 *           type: string
 *           example: Scraper "funda" enabled successfully
 *         data:
 *           type: object
 *           properties:
 *             activeScrapers:
 *               type: array
 *               items:
 *                 type: string
 *               example: ["funda", "pararius"]
 *     ErrorResponse:
 *       type: object
 *       properties:
 *         status:
 *           type: string
 *           example: error
 *         message:
 *           type: string
 *           example: "Invalid scraper name. Valid options: funda, pararius, huurwoningen"
 *
 * tags:
 *   name: Scraper
 *   description: Web scraping operations for property listings
 */

/**
 * @swagger
 * /api/scraper:
 *   post:
 *     summary: Manually trigger the web scraper
 *     description: Triggers manual scraping of property listings from configured real estate websites. This operation is rate-limited to prevent abuse.
 *     tags: [Scraper]
 *     responses:
 *       200:
 *         description: Scraping completed successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ScrapeResponse'
 *       429:
 *         description: Too many scraping requests - rate limit exceeded
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: error
 *                 message:
 *                   type: string
 *                   example: Too many scraping requests. Please try again later.
 *       500:
 *         description: An error occurred while scraping
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: error
 *                 message:
 *                   type: string
 *                   example: Scraping failed
 *                 error:
 *                   type: string
 *                   description: Detailed error message
 */
router.post(
  "/",
  scrapingLimiter,
  (req, res, next) => {
    console.log("[/api/scrape] route hit. Request body:", req.body);
    next();
  },
  (req, res, next) => scraperController.scrape(req, res, next)
);

/**
 * @swagger
 * /api/scraper/metrics:
 *   get:
 *     summary: Get scraping performance metrics
 *     description: Returns detailed metrics about scraping performance, success rates, and error statistics
 *     tags: [Scraper]
 *     responses:
 *       200:
 *         description: Scraping metrics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     metrics:
 *                       type: object
 *                       properties:
 *                         totalScrapes:
 *                           type: number
 *                           description: Total number of scraping attempts
 *                         successfulScrapes:
 *                           type: number
 *                           description: Number of successful scraping operations
 *                         failedScrapes:
 *                           type: number
 *                           description: Number of failed scraping operations
 *                         successRate:
 *                           type: string
 *                           description: Success rate percentage
 *                         totalListingsFound:
 *                           type: number
 *                           description: Total listings discovered
 *                         totalListingsSaved:
 *                           type: number
 *                           description: Total listings successfully saved
 *                         duplicatesSkipped:
 *                           type: number
 *                           description: Number of duplicate listings skipped
 *                         averageScrapingTimeFormatted:
 *                           type: string
 *                           description: Average time per scraping operation
 *                         lastScrapeTime:
 *                           type: string
 *                           format: date-time
 *                           description: Timestamp of last scraping operation
 */
router.get("/metrics", scraperController.getMetrics);

/**
 * @swagger
 * /api/scraper/status:
 *   get:
 *     summary: Get scraper status
 *     description: Returns the current status of all available scrapers (enabled/disabled)
 *     tags: [Scraper]
 *     responses:
 *       200:
 *         description: Scraper status retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ScraperStatusResponse'
 */
router.get("/status", scraperController.getScraperStatus);

/**
 * @swagger
 * /api/scraper/enable/{scraperName}:
 *   post:
 *     summary: Enable a specific scraper
 *     description: Enables scraping for a specific website (funda, pararius, or huurwoningen)
 *     tags: [Scraper]
 *     parameters:
 *       - in: path
 *         name: scraperName
 *         required: true
 *         schema:
 *           type: string
 *           enum: [funda, pararius, huurwoningen]
 *         description: Name of the scraper to enable
 *         example: funda
 *     responses:
 *       200:
 *         description: Scraper enabled successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ScraperActionResponse'
 *       400:
 *         description: Invalid scraper name or scraper already enabled
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.post("/enable/:scraperName", scraperController.enableScraper);

/**
 * @swagger
 * /api/scraper/disable/{scraperName}:
 *   post:
 *     summary: Disable a specific scraper
 *     description: Disables scraping for a specific website (funda, pararius, or huurwoningen)
 *     tags: [Scraper]
 *     parameters:
 *       - in: path
 *         name: scraperName
 *         required: true
 *         schema:
 *           type: string
 *           enum: [funda, pararius, huurwoningen]
 *         description: Name of the scraper to disable
 *         example: funda
 *     responses:
 *       200:
 *         description: Scraper disabled successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ScraperActionResponse'
 *       400:
 *         description: Invalid scraper name or scraper already disabled
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.post("/disable/:scraperName", scraperController.disableScraper);

/**
 * @swagger
 * /api/scraper/agent/status:
 *   get:
 *     summary: Get scraping agent status
 *     description: Returns the current status of the automated scraping agent
 *     tags: [Scraper]
 *     responses:
 *       200:
 *         description: Agent status retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     isRunning:
 *                       type: boolean
 *                     currentTask:
 *                       type: string
 *                     config:
 *                       type: object
 */
router.get("/agent/status", scraperController.getAgentStatus);

/**
 * @swagger
 * /api/scraper/agent/stop:
 *   post:
 *     summary: Stop the scraping agent
 *     description: Stops the automated scraping agent
 *     tags: [Scraper]
 *     responses:
 *       200:
 *         description: Agent stopped successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: Agent stopped successfully
 */
router.post("/agent/stop", scraperController.stopAgent);

/**
 * @swagger
 * /api/scraper/force-stop:
 *   post:
 *     summary: Force stop all scraping processes
 *     description: Forcefully stops all scraping processes, disables all scrapers, and cleans up browser instances
 *     tags: [Scraper]
 *     responses:
 *       200:
 *         description: All scraping processes force stopped
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: All scraping processes force stopped
 *                 data:
 *                   type: object
 *                   properties:
 *                     agentStopped:
 *                       type: boolean
 *                     scrapersDisabled:
 *                       type: boolean
 *                     browsersClosed:
 *                       type: boolean
 */
router.post("/force-stop", scraperController.forceStopAll);

module.exports = router;
