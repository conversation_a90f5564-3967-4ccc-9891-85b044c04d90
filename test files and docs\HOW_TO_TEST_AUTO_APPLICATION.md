# How to Check if Auto Application is Working

This guide provides multiple ways to test and monitor the auto application system to ensure it's working correctly.

## Prerequisites

1. **Start Backend Server**: 
   ```bash
   cd zakmakelaar-backend
   npm run dev
   ```
   Make sure it's running on `http://localhost:3000`

2. **Create Test User**: 
   ```bash
   cd zakmakelaar-backend
   npm run create-test-user
   ```
   This creates a test user with email: `<EMAIL>` and password: `testpassword123`

3. **Node.js**: Ensure you have Node.js installed for running test scripts

## Method 1: Quick Health Check

Run a quick health check to see the overall system status:

```bash
cd zakmakelaar-backend
npm run test:auto-app:health
```

This will show:
- ✅ Settings configured
- ✅ Auto application enabled/disabled
- ✅ Queue status
- 📊 Basic statistics

## Method 2: Comprehensive Testing

Run the full test suite to check all auto application features:

```bash
cd zakmakelaar-backend
npm run test:auto-app
```

This tests:
- Settings management (get/update)
- Statistics retrieval
- Queue operations (get/add/pause/resume)
- Application results
- Scraper integration stats
- Manual queue processing

## Method 3: Real-time Monitoring

Monitor the system in real-time to see live updates:

```bash
cd zakmakelaar-backend
npm run monitor:auto-app
```

This provides:
- 🔧 System status (enabled/disabled)
- 📈 Live statistics
- 📋 Queue status with item counts
- 🔄 Updates every 10 seconds

To change update interval:
```bash
node ../monitor-auto-application.js --interval=5
```

## Method 4: Frontend Dashboard

Use the frontend dashboard for visual monitoring:

1. Open your React Native app
2. Navigate to **Auto-Application Dashboard**
3. Check the following tabs:
   - **Overview**: Statistics and quick actions
   - **Queue**: Current applications in queue
   - **Results**: Recent application results

## Method 5: Manual Testing Steps

### Step 1: Configure Settings
1. Go to Auto-Application Settings
2. Enable auto application
3. Set preferences (locations, property types, etc.)
4. Save settings

### Step 2: Add Test Listing
```bash
# Use the test script to add a mock listing
node test-auto-application.js
```

### Step 3: Check Queue Processing
1. Monitor the queue in the dashboard
2. Check if items move from "pending" to "processing" to "completed"
3. Verify notifications are sent

### Step 4: Verify Results
1. Check the Results tab in the dashboard
2. Look for successful applications
3. Check application quality scores

## Method 6: Database Inspection

If you have database access, check these collections:

```javascript
// Check auto application settings
db.users.find({}, { autoApplicationSettings: 1 })

// Check application queue
db.applicationqueues.find({}).sort({ createdAt: -1 })

// Check application results
db.applicationresults.find({}).sort({ createdAt: -1 })

// Check scraper integration stats
db.scraperintegrationsettings.find({})
```

## Method 7: Log Analysis

Check the backend logs for auto application activity:

```bash
# Check general logs
tail -f logs/combined.log | grep -i "auto.application"

# Check error logs
tail -f logs/error.log | grep -i "auto.application"

# Check specific service logs
tail -f logs/combined.log | grep -i "autoApplicationService"
```

## Method 8: API Testing with curl

Test individual endpoints manually:

```bash
# Login and get token
TOKEN=$(curl -s -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"Testpassword123"}' \
  | jq -r '.token')

# Get settings
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:3000/api/auto-application/settings/USER_ID

# Get statistics
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:3000/api/auto-application/stats/USER_ID

# Get queue
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:3000/api/auto-application/queue/USER_ID
```

## What to Look For

### ✅ Signs System is Working
- Settings can be retrieved and updated
- Queue shows pending/processing items
- Statistics show increasing application counts
- Results show successful applications
- Notifications are being sent
- No error messages in logs

### ❌ Signs of Problems
- Settings return null or error
- Queue is always empty
- Statistics show 0 applications
- No results in the results tab
- Error messages in logs
- Notifications not being sent

### 🔍 Common Issues and Solutions

**Issue**: Settings not saving
- **Solution**: Check user authentication and database connection

**Issue**: Queue not processing
- **Solution**: Check if auto application is enabled and cron jobs are running

**Issue**: No applications being made
- **Solution**: Verify scraper integration and listing matching criteria

**Issue**: Low success rate
- **Solution**: Review application templates and quality scoring

## Troubleshooting Commands

```bash
# Check if backend is running
curl http://localhost:3000/health

# Test database connection
npm run test

# Check notification system
npm run test:notifications

# View all running processes
ps aux | grep node

# Check port usage
lsof -i :3000
```

## Performance Monitoring

Monitor system performance:

```bash
# Check memory usage
node -e "console.log(process.memoryUsage())"

# Monitor CPU usage
top -p $(pgrep -f "node.*index.js")

# Check database performance
# (MongoDB specific commands)
```

## Automated Testing Schedule

Set up automated testing:

```bash
# Add to crontab for hourly health checks
0 * * * * cd /path/to/project && npm run test:auto-app:health >> /var/log/auto-app-health.log 2>&1
```

## Integration Testing

Test the full pipeline:

1. **Scraper** → **Auto Application** → **Notifications**
2. Verify data flows correctly between components
3. Check error handling and recovery
4. Test rate limiting and throttling

## Support and Debugging

If issues persist:

1. Enable debug logging in the backend
2. Check all environment variables
3. Verify database indexes
4. Test with minimal configuration
5. Check third-party service integrations (email, SMS)

## Setup Instructions

### Step 1: Start Backend Server
```bash
cd zakmakelaar-backend
npm run dev
```
Wait for the server to start on `http://localhost:3000`

### Step 2: Create Test User
```bash
npm run create-test-user
```
This creates a test user with:
- Email: `<EMAIL>`
- Password: `Testpassword123`

### Step 3: Run Tests
```bash
# Quick health check
npm run test:auto-app:health

# Full test suite
npm run test:auto-app

# Real-time monitoring
npm run monitor:auto-app
```

### Custom Test User (Optional)
Set environment variables to use different credentials:
```bash
export TEST_USER_EMAIL="<EMAIL>"
export TEST_USER_PASSWORD="your-password"
```