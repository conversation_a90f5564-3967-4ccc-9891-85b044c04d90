const autoApplicationNotificationService = require('../../services/autoApplicationNotificationService');
const User = require('../../models/User');
const AutoApplicationSettings = require('../../models/AutoApplicationSettings');
const ApplicationResult = require('../../models/ApplicationResult');
const websocketService = require('../../services/websocketService');

// Mock dependencies
jest.mock('../../models/User');
jest.mock('../../models/AutoApplicationSettings');
jest.mock('../../models/ApplicationResult');
jest.mock('../../services/websocketService');
jest.mock('../../services/logger', () => ({
  loggers: {
    app: {
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn()
    }
  }
}));

// Mock SendGrid and Twilio
jest.mock('@sendgrid/mail', () => ({
  setApiKey: jest.fn(),
  send: jest.fn().mockResolvedValue(true)
}));

jest.mock('twilio', () => {
  return jest.fn(() => ({
    messages: {
      create: jest.fn().mockResolvedValue({ sid: 'test-sid' })
    }
  }));
});

describe('AutoApplicationNotificationService', () => {
  let mockUser;
  let mockSettings;
  let mockApplicationData;

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Set up environment variables for testing
    process.env.SENDGRID_API_KEY = 'test-sendgrid-key';
    process.env.SENDGRID_FROM_EMAIL = '<EMAIL>';
    process.env.TWILIO_ACCOUNT_SID = 'test-twilio-sid';
    process.env.TWILIO_AUTH_TOKEN = 'test-twilio-token';
    process.env.TWILIO_PHONE_NUMBER = '+**********';
    process.env.FRONTEND_URL = 'https://app.zakmakelaar.com';

    mockUser = {
      _id: 'user123',
      email: '<EMAIL>',
      profile: {
        phone: '+***********'
      }
    };

    mockSettings = {
      userId: 'user123',
      enabled: true,
      settings: {
        notificationPreferences: {
          immediate: true,
          daily: true,
          weekly: false,
          email: true,
          sms: false,
          push: true
        }
      }
    };

    mockApplicationData = {
      _id: 'app123',
      userId: 'user123',
      listingUrl: 'https://funda.nl/property/123',
      listingSnapshot: {
        title: 'Beautiful Apartment in Amsterdam',
        location: 'Amsterdam Centrum',
        price: '€2,500/month'
      },
      confirmationNumber: 'CONF123456',
      status: 'submitted'
    };

    User.findById.mockResolvedValue(mockUser);
    AutoApplicationSettings.findByUserId.mockResolvedValue(mockSettings);
  });

  describe('sendApplicationStatusUpdate', () => {
    it('should send application status update notification', async () => {
      await autoApplicationNotificationService.sendApplicationStatusUpdate(
        'user123',
        mockApplicationData,
        'submitted',
        { oldStatus: 'pending' }
      );

      expect(User.findById).toHaveBeenCalledWith('user123');
      expect(AutoApplicationSettings.findByUserId).toHaveBeenCalledWith('user123');
      expect(websocketService.sendAutoApplicationUpdate).toHaveBeenCalledWith(
        'user123',
        expect.objectContaining({
          action: 'status_update',
          applicationId: 'app123',
          status: 'submitted'
        })
      );
    });

    it('should not send notification if user has immediate notifications disabled', async () => {
      mockSettings.settings.notificationPreferences.immediate = false;
      AutoApplicationSettings.findByUserId.mockResolvedValue(mockSettings);

      await autoApplicationNotificationService.sendApplicationStatusUpdate(
        'user123',
        mockApplicationData,
        'submitted'
      );

      expect(websocketService.sendAutoApplicationUpdate).not.toHaveBeenCalled();
    });

    it('should handle missing user gracefully', async () => {
      User.findById.mockResolvedValue(null);

      await expect(
        autoApplicationNotificationService.sendApplicationStatusUpdate(
          'nonexistent',
          mockApplicationData,
          'submitted'
        )
      ).resolves.not.toThrow();
    });

    it('should handle missing settings gracefully', async () => {
      AutoApplicationSettings.findByUserId.mockResolvedValue(null);

      await expect(
        autoApplicationNotificationService.sendApplicationStatusUpdate(
          'user123',
          mockApplicationData,
          'submitted'
        )
      ).resolves.not.toThrow();
    });
  });

  describe('sendDailySummary', () => {
    const mockSummaryData = {
      applicationsSubmitted: 5,
      applicationsSuccessful: 3,
      applicationsRejected: 1,
      applicationsPending: 1,
      successRate: 60,
      topPerformingLocation: 'Amsterdam',
      averageResponseTime: '2 hours',
      dailyLimit: 10,
      remainingApplications: 5,
      recommendations: ['Consider applying earlier in the day'],
      upcomingViewings: []
    };

    it('should send daily summary notification', async () => {
      await autoApplicationNotificationService.sendDailySummary('user123', mockSummaryData);

      expect(User.findById).toHaveBeenCalledWith('user123');
      expect(AutoApplicationSettings.findByUserId).toHaveBeenCalledWith('user123');
    });

    it('should not send daily summary if user has daily notifications disabled', async () => {
      mockSettings.settings.notificationPreferences.daily = false;
      AutoApplicationSettings.findByUserId.mockResolvedValue(mockSettings);

      await autoApplicationNotificationService.sendDailySummary('user123', mockSummaryData);

      // Should return early without queuing notification
      expect(User.findById).toHaveBeenCalled();
    });
  });

  describe('sendWeeklySummary', () => {
    const mockWeeklySummaryData = {
      weekStart: '2024-01-01',
      weekEnd: '2024-01-07',
      totalApplications: 25,
      successfulApplications: 18,
      landlordResponses: 12,
      viewingInvites: 8,
      acceptances: 2,
      successRate: 72,
      responseRate: 67,
      acceptanceRate: 17,
      topPerformingLocations: [
        { location: 'Amsterdam', applications: 10, successRate: 80 }
      ],
      bestApplicationTimes: [
        { time: 'Monday at 9:00', applications: 5, successRate: 100 }
      ],
      marketInsights: ['Market is competitive in Amsterdam area'],
      performanceComparison: {
        applications: { current: 25, previous: 20, change: 5 }
      },
      actionItems: [
        { priority: 'high', action: 'Focus on Amsterdam properties' }
      ]
    };

    it('should send weekly summary notification', async () => {
      mockSettings.settings.notificationPreferences.weekly = true;
      AutoApplicationSettings.findByUserId.mockResolvedValue(mockSettings);

      await autoApplicationNotificationService.sendWeeklySummary('user123', mockWeeklySummaryData);

      expect(User.findById).toHaveBeenCalledWith('user123');
      expect(AutoApplicationSettings.findByUserId).toHaveBeenCalledWith('user123');
    });

    it('should not send weekly summary if user has weekly notifications disabled', async () => {
      // weekly is false by default in mockSettings
      await autoApplicationNotificationService.sendWeeklySummary('user123', mockWeeklySummaryData);

      expect(User.findById).toHaveBeenCalled();
    });
  });

  describe('sendUrgentAlert', () => {
    it('should send urgent alert immediately', async () => {
      const alertData = {
        error: 'System error occurred',
        timestamp: new Date()
      };

      await autoApplicationNotificationService.sendUrgentAlert(
        'user123',
        'system_error',
        alertData
      );

      expect(User.findById).toHaveBeenCalledWith('user123');
      expect(websocketService.sendAutoApplicationUpdate).toHaveBeenCalledWith(
        'user123',
        expect.objectContaining({
          action: 'urgent_alert',
          alertType: 'system_error'
        })
      );
    });

    it('should handle different alert types', async () => {
      const alertTypes = [
        'daily_limit_reached',
        'account_blocked',
        'captcha_required',
        'profile_incomplete',
        'documents_missing'
      ];

      for (const alertType of alertTypes) {
        await autoApplicationNotificationService.sendUrgentAlert(
          'user123',
          alertType,
          { timestamp: new Date() }
        );

        expect(User.findById).toHaveBeenCalledWith('user123');
      }
    });
  });

  describe('sendMaintenanceNotification', () => {
    const mockMaintenanceData = {
      scheduledStart: new Date('2024-01-15T02:00:00Z'),
      estimatedDuration: '2 hours',
      affectedServices: ['Auto-Application'],
      impact: 'Auto-applications will be temporarily paused',
      alternativeActions: ['Manual applications will still be available'],
      contactInfo: '<EMAIL>'
    };

    it('should send maintenance notification to all active users', async () => {
      const mockActiveSettings = [
        { userId: 'user1', enabled: true },
        { userId: 'user2', enabled: true }
      ];
      const mockActiveUsers = [
        { _id: 'user1', email: '<EMAIL>' },
        { _id: 'user2', email: '<EMAIL>' }
      ];

      AutoApplicationSettings.find.mockResolvedValue(mockActiveSettings);
      User.find.mockResolvedValue(mockActiveUsers);

      await autoApplicationNotificationService.sendMaintenanceNotification(
        null,
        mockMaintenanceData
      );

      expect(AutoApplicationSettings.find).toHaveBeenCalledWith({ enabled: true });
      expect(User.find).toHaveBeenCalledWith({ _id: { $in: ['user1', 'user2'] } });
    });

    it('should send maintenance notification to specific users', async () => {
      const specificUserIds = ['user123'];
      const mockUsers = [mockUser];

      User.find.mockResolvedValue(mockUsers);

      await autoApplicationNotificationService.sendMaintenanceNotification(
        specificUserIds,
        mockMaintenanceData
      );

      expect(User.find).toHaveBeenCalledWith({ _id: { $in: specificUserIds } });
    });
  });

  describe('getNotificationChannels', () => {
    it('should return correct channels based on preferences and priority', () => {
      const preferences = {
        email: true,
        sms: false,
        push: true
      };

      // Test normal priority
      let channels = autoApplicationNotificationService.getNotificationChannels(
        mockUser,
        preferences,
        'medium'
      );
      expect(channels).toEqual(['email', 'push']);

      // Test urgent priority (should include SMS even if disabled)
      channels = autoApplicationNotificationService.getNotificationChannels(
        mockUser,
        preferences,
        'urgent'
      );
      expect(channels).toEqual(['email', 'sms', 'push']);
    });

    it('should handle missing user email', () => {
      const userWithoutEmail = { ...mockUser, email: null };
      const preferences = { email: true, sms: false, push: true };

      const channels = autoApplicationNotificationService.getNotificationChannels(
        userWithoutEmail,
        preferences,
        'medium'
      );

      expect(channels).toEqual(['push']);
    });

    it('should handle missing user phone', () => {
      const userWithoutPhone = { ...mockUser, profile: {} };
      const preferences = { email: true, sms: true, push: true };

      const channels = autoApplicationNotificationService.getNotificationChannels(
        userWithoutPhone,
        preferences,
        'urgent'
      );

      expect(channels).toEqual(['email', 'push']);
    });
  });

  describe('getStatusPriority', () => {
    it('should return correct priority for different statuses', () => {
      expect(autoApplicationNotificationService.getStatusPriority('submitted')).toBe('high');
      expect(autoApplicationNotificationService.getStatusPriority('failed')).toBe('urgent');
      expect(autoApplicationNotificationService.getStatusPriority('blocked')).toBe('urgent');
      expect(autoApplicationNotificationService.getStatusPriority('captcha_required')).toBe('urgent');
      expect(autoApplicationNotificationService.getStatusPriority('completed')).toBe('medium');
      expect(autoApplicationNotificationService.getStatusPriority('unknown')).toBe('low');
    });
  });

  describe('getNextSteps', () => {
    it('should return appropriate next steps for different statuses', () => {
      const submittedSteps = autoApplicationNotificationService.getNextSteps('submitted', mockApplicationData);
      expect(submittedSteps).toContain('Wait for landlord response');
      expect(submittedSteps).toContain('Check your email for confirmation');

      const failedSteps = autoApplicationNotificationService.getNextSteps('failed', mockApplicationData);
      expect(failedSteps).toContain('Review error details in your dashboard');
      expect(failedSteps).toContain('Check if manual application is needed');

      const blockedSteps = autoApplicationNotificationService.getNextSteps('blocked', mockApplicationData);
      expect(blockedSteps).toContain('Auto-application has been paused');
      expect(blockedSteps).toContain('Manual verification may be required');

      const captchaSteps = autoApplicationNotificationService.getNextSteps('captcha_required', mockApplicationData);
      expect(captchaSteps).toContain('Complete CAPTCHA verification');
      expect(captchaSteps).toContain('Check your dashboard for manual steps');
    });
  });

  describe('formatEmailContent', () => {
    it('should format application status update email correctly', () => {
      const data = {
        applicationId: 'app123',
        listingTitle: 'Test Property',
        listingLocation: 'Amsterdam',
        listingPrice: '€2,000/month',
        listingUrl: 'https://funda.nl/property/123',
        newStatus: 'submitted',
        confirmationNumber: 'CONF123',
        nextSteps: ['Wait for response']
      };

      const emailContent = autoApplicationNotificationService.formatEmailContent(
        'application_status_update',
        data
      );

      expect(emailContent.subject).toContain('Application Update');
      expect(emailContent.subject).toContain('Test Property');
      expect(emailContent.html).toContain('Test Property');
      expect(emailContent.html).toContain('Amsterdam');
      expect(emailContent.html).toContain('€2,000/month');
      expect(emailContent.html).toContain('SUBMITTED');
      expect(emailContent.html).toContain('CONF123');
      expect(emailContent.text).toContain('Test Property');
    });

    it('should format daily summary email correctly', () => {
      const data = {
        date: '2024-01-15',
        applicationsSubmitted: 5,
        applicationsSuccessful: 3,
        applicationsPending: 1,
        successRate: 60,
        remainingApplications: 5,
        dailyLimit: 10,
        topPerformingLocation: 'Amsterdam',
        recommendations: ['Apply earlier in the day']
      };

      const emailContent = autoApplicationNotificationService.formatEmailContent('daily_summary', data);

      expect(emailContent.subject).toContain('Daily Summary');
      expect(emailContent.subject).toContain('5 applications submitted');
      expect(emailContent.html).toContain('5');
      expect(emailContent.html).toContain('3');
      expect(emailContent.html).toContain('60%');
      expect(emailContent.html).toContain('Amsterdam');
      expect(emailContent.html).toContain('Apply earlier in the day');
    });

    it('should format urgent alert email correctly', () => {
      const data = {
        title: 'System Error',
        message: 'A critical error has occurred',
        actionRequired: 'Contact support immediately',
        timestamp: new Date('2024-01-15T10:00:00Z')
      };

      const emailContent = autoApplicationNotificationService.formatEmailContent('urgent_alert', data);

      expect(emailContent.subject).toContain('URGENT');
      expect(emailContent.subject).toContain('System Error');
      expect(emailContent.html).toContain('System Error');
      expect(emailContent.html).toContain('A critical error has occurred');
      expect(emailContent.html).toContain('Contact support immediately');
      expect(emailContent.text).toContain('URGENT ALERT');
    });
  });

  describe('formatSMSContent', () => {
    it('should format SMS content correctly for different notification types', () => {
      const applicationData = {
        listingTitle: 'Test Property',
        newStatus: 'submitted'
      };

      const smsContent = autoApplicationNotificationService.formatSMSContent(
        'application_status_update',
        applicationData
      );

      expect(smsContent).toContain('ZakMakelaar');
      expect(smsContent).toContain('Test Property');
      expect(smsContent).toContain('SUBMITTED');

      const urgentData = {
        title: 'System Error',
        message: 'Critical error occurred',
        actionRequired: 'Contact support'
      };

      const urgentSMS = autoApplicationNotificationService.formatSMSContent('urgent_alert', urgentData);

      expect(urgentSMS).toContain('ZakMakelaar URGENT');
      expect(urgentSMS).toContain('System Error');
      expect(urgentSMS).toContain('Critical error occurred');
    });
  });

  describe('error handling', () => {
    it('should handle SendGrid errors gracefully', async () => {
      const sgMail = require('@sendgrid/mail');
      sgMail.send.mockRejectedValue(new Error('SendGrid error'));

      await expect(
        autoApplicationNotificationService.sendApplicationStatusUpdate(
          'user123',
          mockApplicationData,
          'submitted'
        )
      ).resolves.not.toThrow();
    });

    it('should handle Twilio errors gracefully', async () => {
      const twilio = require('twilio');
      const mockTwilioClient = {
        messages: {
          create: jest.fn().mockRejectedValue(new Error('Twilio error'))
        }
      };
      twilio.mockReturnValue(mockTwilioClient);

      mockSettings.settings.notificationPreferences.sms = true;
      AutoApplicationSettings.findByUserId.mockResolvedValue(mockSettings);

      await expect(
        autoApplicationNotificationService.sendUrgentAlert(
          'user123',
          'system_error',
          { error: 'Test error' }
        )
      ).resolves.not.toThrow();
    });

    it('should handle database errors gracefully', async () => {
      User.findById.mockRejectedValue(new Error('Database error'));

      await expect(
        autoApplicationNotificationService.sendApplicationStatusUpdate(
          'user123',
          mockApplicationData,
          'submitted'
        )
      ).resolves.not.toThrow();
    });
  });

  describe('queue processing', () => {
    it('should process notification queue', async () => {
      // Queue a notification
      await autoApplicationNotificationService.queueNotification({
        type: 'test',
        userId: 'user123',
        priority: 'medium',
        data: { message: 'Test notification' }
      });

      // Process the queue
      await autoApplicationNotificationService.processNotificationQueue();

      expect(User.findById).toHaveBeenCalledWith('user123');
    });

    it('should retry failed notifications', async () => {
      User.findById.mockRejectedValueOnce(new Error('Temporary error'))
                  .mockResolvedValueOnce(mockUser);

      // Queue a notification
      await autoApplicationNotificationService.queueNotification({
        type: 'test',
        userId: 'user123',
        priority: 'medium',
        data: { message: 'Test notification' }
      });

      // Process the queue twice (first fails, second succeeds)
      await autoApplicationNotificationService.processNotificationQueue();
      await autoApplicationNotificationService.processNotificationQueue();

      expect(User.findById).toHaveBeenCalledTimes(2);
    });
  });

  describe('service lifecycle', () => {
    it('should start and stop queue processing', () => {
      expect(autoApplicationNotificationService.processingInterval).toBeDefined();
      
      autoApplicationNotificationService.shutdown();
      
      expect(autoApplicationNotificationService.processingInterval).toBeNull();
    });
  });
});

describe('Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should handle complete notification flow', async () => {
    const mockUser = {
      _id: 'user123',
      email: '<EMAIL>',
      profile: { phone: '+***********' }
    };

    const mockSettings = {
      userId: 'user123',
      enabled: true,
      settings: {
        notificationPreferences: {
          immediate: true,
          email: true,
          push: true
        }
      }
    };

    const mockApplicationData = {
      _id: 'app123',
      userId: 'user123',
      listingUrl: 'https://funda.nl/property/123',
      listingSnapshot: {
        title: 'Test Property',
        location: 'Amsterdam',
        price: '€2,000/month'
      },
      status: 'submitted'
    };

    User.findById.mockResolvedValue(mockUser);
    AutoApplicationSettings.findByUserId.mockResolvedValue(mockSettings);

    // Send notification
    await autoApplicationNotificationService.sendApplicationStatusUpdate(
      'user123',
      mockApplicationData,
      'submitted'
    );

    // Verify all components were called
    expect(User.findById).toHaveBeenCalledWith('user123');
    expect(AutoApplicationSettings.findByUserId).toHaveBeenCalledWith('user123');
    expect(websocketService.sendAutoApplicationUpdate).toHaveBeenCalled();
  });
});