const puppeteer = require("puppeteer");
const { setupPageStealth } = require("./src/services/scraperUtils");
let chromiumPath = undefined;
try {
  const chromium = require("chromium");
  chromiumPath = chromium.path;
} catch {}

const propertyUrl =
  "https://www.funda.nl/detail/huur/utrecht/appartement-eendrachtlaan-46-d/43728488/";
const applicant = {
  firstName: "Wellis",
  lastName: "Hant",
  email: "<EMAIL>",
  phone: "030 686 62 00",
  message: `<PERSON><PERSON>r<PERSON>,\n\nIk ben zeer geïnteresseerd in deze woning en zou graag meer informatie ontvangen over de bezichtigingsmogelijkheden en beschikbaarheid.\n\nAlvast hartelijk dank.\n\nMet vriendelijke groet,\nWellis <PERSON>`,
};

const delay = (ms) => new Promise((r) => setTimeout(r, ms));

(async () => {
  let browser;
  try {
    console.log("Launching Chrome headful...");
    browser = await puppeteer.launch({
      headless: false,
      slowMo: 50,
      defaultViewport: { width: 1366, height: 900 },
      executablePath: process.env.CHROME_EXECUTABLE_PATH || chromiumPath,
      args: [
        "--start-maximized",
        "--no-sandbox",
        "--disable-setuid-sandbox",
        "--disable-infobars",
      ],
      timeout: 45000,
    });

    const page = await browser.newPage();
    try {
      await setupPageStealth(page);
      await page.setExtraHTTPHeaders({
        "Accept-Language": "en-US,en;q=0.9,nl;q=0.8",
        "Accept-Encoding": "gzip, deflate, br",
        "Upgrade-Insecure-Requests": "1",
      });

      console.log("Navigating to listing...");
      await page.goto(propertyUrl, {
        waitUntil: "networkidle2",
        timeout: 60000,
      });
      await delay(1500);

      // Accept cookies
      try {
        await page.waitForSelector(
          '[data-testid="accept-all-cookies-button"], .cookie-accept-all, #accept-cookies',
          { timeout: 5000 }
        );
        await page.click(
          '[data-testid="accept-all-cookies-button"], .cookie-accept-all, #accept-cookies'
        );
        console.log("Accepted cookie banner");
        await delay(1000);
      } catch {}

      // Find and click contact button
      const contactSelectors = [
        '[data-optimizely="contact-email"]',
        'a[href*="makelaar-contact"]',
        '[data-testid="contact-button"]',
        '[data-testid="contact-form-button"]',
        '[data-testid="broker-contact-button"]',
        ".makelaar-contact-button",
        ".broker-contact",
        ".fd-button--contact",
        ".contact-button",
        'button[data-testid*="contact"]',
      ];

      let contactButton = null;
      for (const sel of contactSelectors) {
        try {
          await page.waitForSelector(sel, { timeout: 2500 });
          contactButton = await page.$(sel);
          if (contactButton) {
            console.log("Found contact button via selector:", sel);
            break;
          }
        } catch {}
      }

      if (!contactButton) {
        // Fallback by text/href
        const handle = await page.evaluateHandle(() => {
          const links = Array.from(document.querySelectorAll("a"));
          return links.find(
            (link) =>
              (link.textContent &&
                (link.textContent.includes("Neem contact op") ||
                  link.textContent.includes("Contact"))) ||
              (link.getAttribute("href") || "").includes("makelaar-contact")
          );
        });
        if (handle && handle.asElement()) {
          contactButton = handle.asElement();
          console.log("Found contact button via text/href fallback");
        }
      }

      if (!contactButton) throw new Error("Contact button not found");

      await contactButton.click();
      await delay(2500);

      // Fill helper
      async function fill(selector, value) {
        try {
          await page.waitForSelector(selector, { timeout: 3000 });
          const el = await page.$(selector);
          if (!el) return false;
          await page.evaluate((node) => {
            try {
              node.value = "";
            } catch (e) {}
          }, el);
          await el.type(value, { delay: 60 });
          return true;
        } catch {
          return false;
        }
      }

      // Message
      {
        const sels = [
          "#questionInput",
          'textarea[aria-labelledby="questionInput-label"]',
          'textarea[data-testid*="message"]',
          "textarea",
        ];
        for (const s of sels) {
          if (await fill(s, applicant.message)) {
            console.log("Filled message");
            break;
          }
        }
      }

      // Email
      {
        const sels = [
          "#emailAddress",
          'input[type="email"]',
          'input[name="email"]',
          'input[data-testid*="email"]',
        ];
        for (const s of sels) {
          if (await fill(s, applicant.email)) {
            console.log("Filled email");
            break;
          }
        }
      }

      // First/Last name
      await fill("#firstName", applicant.firstName);
      await fill("#lastName", applicant.lastName);

      // Phone
      {
        const sels = [
          "#phoneNumber",
          'input[type="tel"]',
          'input[name="phone"]',
          'input[name="telephone"]',
        ];
        for (const s of sels) {
          if (await fill(s, applicant.phone)) {
            console.log("Filled phone");
            break;
          }
        }
      }

      // Viewing request checkbox
      try {
        const cb = await page.$("#checkbox-viewingRequest");
        if (cb) {
          const isChecked = await page.evaluate((el) => el.checked, cb);
          if (!isChecked) {
            await cb.click();
            console.log("Checked viewing request");
          }
        }
      } catch {}

      await delay(800);

      // Submit by text or type
      let submitted = false;
      try {
        const submitButtonHandle = await page.evaluateHandle(() => {
          const texts = ["Verstuur", "Verzenden", "Send message", "Versturen"];
          const buttons = Array.from(document.querySelectorAll("button"));
          return (
            buttons.find(
              (btn) =>
                texts.some((t) => (btn.textContent || "").trim().includes(t)) ||
                btn.type === "submit"
            ) || null
          );
        });
        if (submitButtonHandle && submitButtonHandle.asElement()) {
          const btn = submitButtonHandle.asElement();
          await btn.click();
          submitted = true;
          console.log("Clicked submit");
        }
      } catch {}

      if (!submitted) {
        // Fallback: first form button
        const btn = await page.$("form button");
        if (btn) {
          await btn.click();
          submitted = true;
          console.log("Clicked generic form button");
        }
      }

      await delay(3500);
      console.log("Done. Submitted:", submitted);

      console.log(
        "Leaving the browser open for 30 seconds so you can inspect the UI..."
      );
      await delay(30000);
    } catch (err) {
      console.error("Headful run failed:", err);
    } finally {
      // Keep the browser open a bit after errors as well
      try {
        console.log("Keeping browser open 10s after error...");
        await delay(10000);
      } catch {}
      try {
        await page?.close();
      } catch {}
    }
  } catch (err) {
    console.error("Failed to launch/run headful script:", err);
  } finally {
    try {
      await browser?.close();
    } catch {}
  }
})();
