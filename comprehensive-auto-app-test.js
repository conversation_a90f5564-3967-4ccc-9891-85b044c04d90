const axios = require('axios');

const BASE_URL = 'http://localhost:3000';
const TEST_USER = {
  email: '<EMAIL>',
  password: 'Password123'
};

const TEST_PROPERTY = {
  "_id": "68aa22092a8c01773a0e9eaf",
  "title": "Acaciastraat 1",
  "price": "€ 1.000 per maand",
  "location": "Utrecht",
  "url": "https://www.funda.nl/detail/huur/utrecht/huis-acaciastraat-1/89474643/",
  "size": null,
  "bedrooms": null,
  "rooms": "1",
  "propertyType": "woning",
  "description": "Per direct beschikbaar\nKamer voor 1 persoon, de woning wordt gedeeld met 4 andere huurders\nHuurprijs: € 1000,- (inclusief gas, water en licht)\nBorg  € 1000,-\nVoor bezichtiging neem telefonisch contact met ons op.",
  "year": "1900",
  "interior": null,
  "source": "funda.nl"
};

let authToken = null;

async function login() {
  try {
    console.log('🔐 Logging in user...');
    const response = await axios.post(`${BASE_URL}/api/auth/login`, TEST_USER);
    authToken = response.data.token;
    console.log('✅ Login successful');
    return response.data;
  } catch (error) {
    console.error('❌ Login failed:', error.response?.data || error.message);
    throw error;
  }
}

async function testEndpoint(name, method, url, data = null) {
  try {
    console.log(`\n🧪 Testing ${name}...`);
    
    const config = {
      method,
      url: `${BASE_URL}${url}`,
      headers: { Authorization: `Bearer ${authToken}` }
    };
    
    if (data) {
      config.data = data;
    }
    
    const response = await axios(config);
    console.log(`✅ ${name} - SUCCESS`);
    console.log('Response:', JSON.stringify(response.data, null, 2));
    return { success: true, data: response.data };
  } catch (error) {
    console.log(`❌ ${name} - FAILED`);
    console.log('Error:', error.response?.data || error.message);
    return { success: false, error: error.response?.data || error.message };
  }
}

async function runComprehensiveTest() {
  console.log('🚀 Comprehensive Auto Application Test for Funda Listing');
  console.log('=' .repeat(70));
  
  try {
    // Login
    await login();
    
    // Test all available endpoints
    const tests = [
      {
        name: 'Get User Profile',
        method: 'GET',
        url: '/api/auth/me'
      },
      {
        name: 'Get Auto Application Settings',
        method: 'GET',
        url: '/api/auto-application/settings'
      },
      {
        name: 'Get Auto Application Status',
        method: 'GET',
        url: '/api/auto-application/status'
      },
      {
        name: 'Get Queue',
        method: 'GET',
        url: '/api/auto-application/queue'
      },
      {
        name: 'Get Results',
        method: 'GET',
        url: '/api/auto-application/results'
      },
      {
        name: 'Get Statistics',
        method: 'GET',
        url: '/api/auto-application/stats'
      },
      {
        name: 'Get Documents',
        method: 'GET',
        url: '/api/auto-application/documents'
      },
      {
        name: 'Get History',
        method: 'GET',
        url: '/api/auto-application/history'
      },
      {
        name: 'Enable Auto Application',
        method: 'POST',
        url: '/api/auto-application/enable'
      },
      {
        name: 'Add to Queue (Simple)',
        method: 'POST',
        url: '/api/auto-application/queue',
        data: {
          listingId: 'test-simple-123',
          listingUrl: 'https://example.com/simple-test',
          priority: 1
        }
      },
      {
        name: 'Add to Queue (Funda Property)',
        method: 'POST',
        url: '/api/auto-application/queue',
        data: {
          listingId: TEST_PROPERTY._id,
          listingTitle: TEST_PROPERTY.title,
          listingUrl: TEST_PROPERTY.url,
          price: parseInt(TEST_PROPERTY.price.replace(/[^\d]/g, '')) || 1000,
          location: TEST_PROPERTY.location,
          propertyType: TEST_PROPERTY.propertyType || 'apartment',
          rooms: parseInt(TEST_PROPERTY.rooms) || 1,
          description: TEST_PROPERTY.description,
          priority: 5
        }
      }
    ];
    
    const results = [];
    
    for (const test of tests) {
      const result = await testEndpoint(test.name, test.method, test.url, test.data);
      results.push({ ...test, ...result });
    }
    
    // Summary
    console.log('\n📊 Test Summary');
    console.log('=' .repeat(50));
    
    const passed = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;
    
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`📈 Success Rate: ${Math.round((passed / results.length) * 100)}%`);
    
    console.log('\n📋 Detailed Results:');
    results.forEach(result => {
      const status = result.success ? '✅' : '❌';
      console.log(`${status} ${result.name}`);
      if (!result.success) {
        console.log(`   Error: ${JSON.stringify(result.error)}`);
      }
    });
    
    // Analysis
    console.log('\n🔍 Analysis:');
    const settingsTest = results.find(r => r.name === 'Get Auto Application Settings');
    if (settingsTest && settingsTest.success) {
      const settings = settingsTest.data.data;
      console.log(`- Auto Application Enabled: ${settings.enabled}`);
      console.log(`- Profile Complete: ${settings.isProfileComplete}`);
      console.log(`- Documents Complete: ${settings.documentsComplete}`);
      console.log(`- Can Auto Apply: ${settings.canAutoApply}`);
      console.log(`- Daily Applications Remaining: ${settings.dailyApplicationsRemaining}`);
      
      if (!settings.canAutoApply) {
        console.log('\n⚠️ Auto Application Issues:');
        if (!settings.isProfileComplete) {
          console.log('- Profile is not complete');
          console.log('- Required fields: fullName, email, phone, dateOfBirth, nationality, occupation, employer, monthlyIncome, moveInDate, leaseDuration');
        }
        if (!settings.documentsComplete) {
          console.log('- Documents are not complete');
          console.log('- Required documents need to be uploaded and verified');
        }
      }
    }
    
    console.log('\n💡 Next Steps:');
    if (failed > 0) {
      console.log('- Fix backend issues causing 500 errors');
      console.log('- Check Docker container logs for detailed error messages');
      console.log('- Verify database connections and queue manager setup');
    }
    console.log('- Complete user profile with all required fields');
    console.log('- Upload and verify required documents');
    console.log('- Test queue functionality once backend issues are resolved');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    process.exit(1);
  }
}

runComprehensiveTest();