/**
 * Transformation Integration Tests
 * 
 * This file contains tests to verify that the transformation pipeline
 * integrates correctly with the existing scrapers.
 */

const assert = require('assert');
const { 
  validateAndNormalizeListing, 
  validateAndNormalizeListingEnhanced,
  initializeTransformationPipeline 
} = require('../services/transformationIntegration');
const { FieldMappingRegistry } = require('../services/fieldMappingRegistry');
const { SchemaTransformer } = require('../services/schemaTransformer');
const { MappingConfigLoader } = require('../services/mappingConfigLoader');

describe('Transformation Integration', () => {
  // Sample listing data for testing
  const fundaListing = {
    title: 'Beautiful Apartment in Amsterdam',
    description: 'A spacious apartment in the center of Amsterdam',
    url: 'https://www.funda.nl/huur/amsterdam/appartement-12345678/',
    location: 'Amsterdam, Noord-Holland',
    price: '€ 1.500 per maand',
    propertyType: 'appartement',
    size: '85 m²',
    rooms: '3',
    bedrooms: '2',
    year: '2010',
    interior: 'Gemeubileerd',
    source: 'funda.nl',
    images: [
      'https://example.com/image1.jpg',
      'https://example.com/image2.jpg'
    ]
  };

  const huurwoningenListing = {
    title: 'Modern Studio in Rotterdam',
    description: 'A modern studio apartment near Rotterdam Central',
    url: 'https://www.huurwoningen.nl/huren/rotterdam/studio-12345/',
    location: 'Rotterdam, Zuid-Holland',
    price: '€ 950 per maand',
    propertyType: 'studio',
    size: '45 m²',
    rooms: '1',
    bedrooms: '1',
    year: '2015',
    interior: 'Gestoffeerd',
    source: 'huurwoningen.nl',
    images: [
      'https://example.com/image3.jpg',
      'https://example.com/image4.jpg'
    ]
  };

  const parariusListing = {
    title: 'Family House in Utrecht',
    description: 'A spacious family house with garden',
    url: 'https://www.pararius.nl/huurwoningen/utrecht/huis-12345/',
    location: 'Utrecht, Utrecht',
    price: '€ 2.200 per maand',
    propertyType: 'huis',
    size: '120 m²',
    rooms: '5',
    bedrooms: '3',
    bathrooms: '2',
    year: '2005',
    interior: 'Kaal',
    source: 'pararius.nl',
    images: [
      'https://example.com/image5.jpg',
      'https://example.com/image6.jpg'
    ],
    garden: true,
    balcony: false,
    parking: true
  };

  before(async () => {
    // Initialize the transformation pipeline before running tests
    try {
      await initializeTransformationPipeline();
    } catch (error) {
      console.error('Failed to initialize transformation pipeline:', error);
    }
  });

  describe('validateAndNormalizeListingEnhanced', () => {
    it('should transform Funda listing correctly', async () => {
      const result = await validateAndNormalizeListingEnhanced(fundaListing);
      
      assert(typeof result === 'object');
      assert.strictEqual(result.title, fundaListing.title);
      assert.strictEqual(result.source, fundaListing.source);
      assert.strictEqual(result.location, fundaListing.location);
      assert(typeof result.propertyType === 'string');
      assert(typeof result.unifiedData === 'object');
    });

    it('should transform Huurwoningen listing correctly', async () => {
      const result = await validateAndNormalizeListingEnhanced(huurwoningenListing);
      
      assert(typeof result === 'object');
      assert.strictEqual(result.title, huurwoningenListing.title);
      assert.strictEqual(result.source, huurwoningenListing.source);
      assert.strictEqual(result.location, huurwoningenListing.location);
      assert(typeof result.propertyType === 'string');
      assert(typeof result.unifiedData === 'object');
    });

    it('should transform Pararius listing correctly', async () => {
      const result = await validateAndNormalizeListingEnhanced(parariusListing);
      
      assert(typeof result === 'object');
      assert.strictEqual(result.title, parariusListing.title);
      assert.strictEqual(result.source, parariusListing.source);
      assert.strictEqual(result.location, parariusListing.location);
      assert(typeof result.propertyType === 'string');
      assert(typeof result.unifiedData === 'object');
    });

    it('should handle invalid listing data gracefully', async () => {
      const invalidListing = {
        title: 'Invalid Listing',
        // Missing required fields
      };
      
      const result = await validateAndNormalizeListingEnhanced(invalidListing);
      assert.strictEqual(result, null);
    });

    it('should handle missing source gracefully', async () => {
      const listingWithoutSource = {
        ...fundaListing,
        source: undefined
      };
      
      const result = await validateAndNormalizeListingEnhanced(listingWithoutSource);
      assert(result !== null);
    });

    it('should preserve original data in unifiedData._internal.rawData', async () => {
      const result = await validateAndNormalizeListingEnhanced(fundaListing);
      
      assert(typeof result.unifiedData === 'object');
      assert(typeof result.unifiedData._internal === 'object');
      assert(typeof result.unifiedData._internal.rawData === 'object');
      assert.deepStrictEqual(result.unifiedData._internal.rawData.original, fundaListing);
    });
  });

  describe('validateAndNormalizeListing (synchronous compatibility wrapper)', () => {
    it('should return a valid result for Funda listing', () => {
      const result = validateAndNormalizeListing(fundaListing);
      
      assert(typeof result === 'object');
      assert(typeof result.title === 'string');
      assert(typeof result.source === 'string');
      assert(typeof result.location === 'string');
      assert(typeof result.propertyType === 'string');
    });

    it('should return a valid result for Huurwoningen listing', () => {
      const result = validateAndNormalizeListing(huurwoningenListing);
      
      assert(typeof result === 'object');
      assert(typeof result.title === 'string');
      assert(typeof result.source === 'string');
      assert(typeof result.location === 'string');
      assert(typeof result.propertyType === 'string');
    });

    it('should return a valid result for Pararius listing', () => {
      const result = validateAndNormalizeListing(parariusListing);
      
      assert(typeof result === 'object');
      assert(typeof result.title === 'string');
      assert(typeof result.source === 'string');
      assert(typeof result.location === 'string');
      assert(typeof result.propertyType === 'string');
    });

    it('should return null for invalid listing data', () => {
      const invalidListing = {
        title: 'Invalid Listing',
        // Missing required fields
      };
      
      const result = validateAndNormalizeListing(invalidListing);
      assert.strictEqual(result, null);
    });
  });

  describe('Integration with transformation pipeline components', () => {
    let registry;
    let transformer;
    
    before(() => {
      registry = new FieldMappingRegistry();
      const loader = new MappingConfigLoader(registry);
      
      // Load default mappings
      loader.loadFromObject('funda', {
        title: 'title',
        description: 'description',
        url: 'url',
        source: { value: 'funda.nl' },
        location: 'location',
        price: 'price',
        propertyType: 'propertyType',
        size: 'size',
        rooms: 'rooms',
        bedrooms: 'bedrooms',
        year: 'year',
        interior: 'interior',
        images: 'images'
      });
      
      transformer = new SchemaTransformer(registry);
    });
    
    it('should transform data using SchemaTransformer', async () => {
      const result = await transformer.transform(fundaListing, 'funda', {
        validateOutput: true,
        preserveRawData: true
      });
      
      assert(typeof result === 'object');
      assert.strictEqual(result.title, fundaListing.title);
      assert.strictEqual(result.source, fundaListing.source);
    });
    
    it('should handle transformation errors gracefully', async () => {
      const invalidData = {
        // Empty object will cause transformation errors
      };
      
      const result = await transformer.transform(invalidData, 'funda', {
        validateOutput: true,
        preserveRawData: true,
        throwOnError: false
      });
      
      assert(typeof result === 'object');
      assert(typeof result._internal === 'object');
      assert(typeof result._internal.processingMetadata === 'object');
      assert(Array.isArray(result._internal.processingMetadata.errors));
      assert(result._internal.processingMetadata.errors.length > 0);
    });
  });
});