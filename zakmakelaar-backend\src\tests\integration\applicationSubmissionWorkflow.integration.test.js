const mongoose = require('mongoose');
const applicationSubmissionWorkflow = require('../../services/applicationSubmissionWorkflow');
const ApplicationQueue = require('../../models/ApplicationQueue');
const ApplicationResult = require('../../models/ApplicationResult');
const AutoApplicationSettings = require('../../models/AutoApplicationSettings');
const User = require('../../models/User');
const { browserPool } = require('../../services/scraperUtils');

// Mock external dependencies
jest.mock('../../services/formAutomationEngine');
jest.mock('../../services/antiDetectionSystem');
jest.mock('../../services/applicationMonitor');
jest.mock('../../services/scraperUtils');

const formAutomationEngine = require('../../services/formAutomationEngine');
const antiDetectionSystem = require('../../services/antiDetectionSystem');
const applicationMonitor = require('../../services/applicationMonitor');

describe('ApplicationSubmissionWorkflow Integration Tests', () => {
  let testUser;
  let testSettings;
  let testQueueItem;
  let mockBrowser;
  let mockPage;

  beforeAll(async () => {
    // Connect to test database
    if (mongoose.connection.readyState === 0) {
      await mongoose.connect(process.env.MONGODB_TEST_URI || 'mongodb://localhost:27017/zakmakelaar_test');
    }
  });

  beforeEach(async () => {
    // Clear test data
    await User.deleteMany({});
    await AutoApplicationSettings.deleteMany({});
    await ApplicationQueue.deleteMany({});
    await ApplicationResult.deleteMany({});

    // Create test user
    testUser = new User({
      email: '<EMAIL>',
      name: 'Test User',
      role: 'user'
    });
    await testUser.save();

    // Create test settings
    testSettings = new AutoApplicationSettings({
      userId: testUser._id,
      enabled: true,
      settings: {
        maxApplicationsPerDay: 5,
        applicationTemplate: 'professional',
        autoSubmit: true,
        requireManualReview: false
      },
      personalInfo: {
        fullName: 'Test User',
        email: '<EMAIL>',
        phone: '+31612345678',
        dateOfBirth: new Date('1990-01-01'),
        nationality: 'Dutch',
        occupation: 'Software Developer',
        monthlyIncome: 5000
      },
      documents: [
        { type: 'id', filename: 'id.pdf', required: true, uploaded: true },
        { type: 'income', filename: 'income.pdf', required: true, uploaded: true }
      ]
    });
    await testSettings.save();

    // Create test queue item
    testQueueItem = new ApplicationQueue({
      userId: testUser._id,
      listingId: new mongoose.Types.ObjectId(),
      listingUrl: 'https://www.funda.nl/huur/amsterdam/appartement-12345/',
      priority: 1,
      status: 'pending',
      applicationData: {
        personalInfo: testSettings.personalInfo,
        documents: testSettings.documents
      },
      generatedContent: {
        subject: 'Application for apartment',
        message: 'Dear landlord, I would like to apply for this apartment...',
        personalizedElements: ['location', 'price']
      }
    });
    await testQueueItem.save();

    // Setup mocks
    mockPage = {
      goto: jest.fn().mockResolvedValue(undefined),
      content: jest.fn().mockResolvedValue('<html><body>Success page</body></html>'),
      evaluate: jest.fn().mockResolvedValue('bedankt voor je aanmelding'),
      screenshot: jest.fn().mockResolvedValue(undefined),
      waitForTimeout: jest.fn().mockResolvedValue(undefined),
      url: jest.fn().mockReturnValue('https://www.funda.nl/confirmation'),
      close: jest.fn().mockResolvedValue(undefined)
    };

    mockBrowser = {
      newPage: jest.fn().mockResolvedValue(mockPage)
    };

    browserPool.getBrowser.mockResolvedValue(mockBrowser);
    browserPool.releaseBrowser.mockResolvedValue(undefined);

    // Setup service mocks
    antiDetectionSystem.setupStealthBrowser.mockResolvedValue(undefined);
    antiDetectionSystem.randomizeFingerprint.mockResolvedValue(undefined);
    antiDetectionSystem.detectBlocking.mockResolvedValue({ isBlocked: false });
    antiDetectionSystem.simulateHumanBehavior.mockResolvedValue(undefined);
    antiDetectionSystem.getRandomDelay.mockResolvedValue(2000);

    formAutomationEngine.detectFormType.mockResolvedValue({ type: 'native' });
    formAutomationEngine.analyzeFormFields.mockResolvedValue([
      { name: 'name', type: 'text', required: true },
      { name: 'email', type: 'email', required: true },
      { name: 'message', type: 'textarea', required: true }
    ]);
    formAutomationEngine.fillApplicationForm.mockResolvedValue({
      success: true,
      fieldsFilledCount: 3,
      totalFieldsCount: 3,
      requiresDocuments: false
    });
    formAutomationEngine.submitForm.mockResolvedValue({
      success: true,
      method: 'POST',
      redirectUrl: 'https://www.funda.nl/confirmation'
    });

    applicationMonitor.trackApplication.mockResolvedValue(undefined);
  });

  afterEach(async () => {
    jest.clearAllMocks();
  });

  afterAll(async () => {
    await mongoose.connection.close();
  });

  describe('submitApplication', () => {
    it('should successfully submit an application with complete workflow', async () => {
      // Act
      const result = await applicationSubmissionWorkflow.submitApplication(testQueueItem._id.toString());

      // Assert
      expect(result.success).toBe(true);
      expect(result.applicationResultId).toBeDefined();
      expect(result.processingTime).toBeGreaterThan(0);

      // Verify queue item was updated
      const updatedQueueItem = await ApplicationQueue.findById(testQueueItem._id);
      expect(updatedQueueItem.status).toBe('completed');

      // Verify application result was created
      const applicationResult = await ApplicationResult.findById(result.applicationResultId);
      expect(applicationResult).toBeTruthy();
      expect(applicationResult.status).toBe('submitted');
      expect(applicationResult.userId.toString()).toBe(testUser._id.toString());

      // Verify browser interactions
      expect(browserPool.getBrowser).toHaveBeenCalled();
      expect(mockBrowser.newPage).toHaveBeenCalled();
      expect(mockPage.goto).toHaveBeenCalledWith(testQueueItem.listingUrl, expect.any(Object));
      expect(browserPool.releaseBrowser).toHaveBeenCalledWith(mockBrowser);

      // Verify anti-detection measures
      expect(antiDetectionSystem.setupStealthBrowser).toHaveBeenCalledWith(mockPage);
      expect(antiDetectionSystem.randomizeFingerprint).toHaveBeenCalledWith(mockPage);
      expect(antiDetectionSystem.detectBlocking).toHaveBeenCalledWith(mockPage);

      // Verify form automation
      expect(formAutomationEngine.detectFormType).toHaveBeenCalledWith(mockPage);
      expect(formAutomationEngine.analyzeFormFields).toHaveBeenCalledWith(mockPage);
      expect(formAutomationEngine.fillApplicationForm).toHaveBeenCalledWith(
        mockPage,
        testQueueItem.applicationData,
        testQueueItem.generatedContent
      );
      expect(formAutomationEngine.submitForm).toHaveBeenCalledWith(mockPage);

      // Verify monitoring
      expect(applicationMonitor.trackApplication).toHaveBeenCalledWith(
        result.applicationResultId,
        'submitted',
        expect.objectContaining({
          submittedAt: expect.any(Date),
          response: expect.any(Object),
          metrics: expect.any(Object)
        })
      );
    });

    it('should handle pre-submission validation failures', async () => {
      // Arrange - disable auto-application
      testSettings.enabled = false;
      await testSettings.save();

      // Act
      const result = await applicationSubmissionWorkflow.submitApplication(testQueueItem._id.toString());

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toContain('Auto-application is not enabled');

      // Verify queue item status
      const updatedQueueItem = await ApplicationQueue.findById(testQueueItem._id);
      expect(updatedQueueItem.status).toBe('failed');

      // Verify no browser interaction occurred
      expect(browserPool.getBrowser).not.toHaveBeenCalled();
    });

    it('should handle form submission failures with retry logic', async () => {
      // Arrange - mock form submission failure
      formAutomationEngine.submitForm.mockResolvedValue({
        success: false,
        error: 'Network timeout'
      });

      // Act
      const result = await applicationSubmissionWorkflow.submitApplication(testQueueItem._id.toString());

      // Assert
      expect(result.success).toBe(false);
      expect(result.shouldRetry).toBe(true);
      expect(result.retryDelay).toBeGreaterThan(0);

      // Verify queue item was updated for retry
      const updatedQueueItem = await ApplicationQueue.findById(testQueueItem._id);
      expect(updatedQueueItem.status).toBe('retrying');
      expect(updatedQueueItem.attempts).toBe(1);
      expect(updatedQueueItem.scheduledAt).toBeInstanceOf(Date);

      // Verify application result was created with failure status
      const applicationResult = await ApplicationResult.findOne({ queueItemId: testQueueItem._id });
      expect(applicationResult.status).toBe('failed');
    });

    it('should handle blocking detection and stop submission', async () => {
      // Arrange - mock blocking detection
      antiDetectionSystem.detectBlocking.mockResolvedValue({
        isBlocked: true,
        reason: 'CAPTCHA detected'
      });

      // Act
      const result = await applicationSubmissionWorkflow.submitApplication(testQueueItem._id.toString());

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toContain('Access blocked');
      expect(result.shouldRetry).toBe(false); // Blocking should not retry automatically

      // Verify form automation was not called
      expect(formAutomationEngine.fillApplicationForm).not.toHaveBeenCalled();
    });

    it('should handle duplicate application detection', async () => {
      // Arrange - create existing application result
      const existingResult = new ApplicationResult({
        userId: testUser._id,
        listingId: testQueueItem.listingId,
        queueItemId: new mongoose.Types.ObjectId(),
        status: 'submitted'
      });
      await existingResult.save();

      // Act
      const result = await applicationSubmissionWorkflow.submitApplication(testQueueItem._id.toString());

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toContain('Application already submitted');
    });

    it('should handle daily application limit exceeded', async () => {
      // Arrange - create applications to reach daily limit
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      for (let i = 0; i < 5; i++) {
        const result = new ApplicationResult({
          userId: testUser._id,
          listingId: new mongoose.Types.ObjectId(),
          queueItemId: new mongoose.Types.ObjectId(),
          status: 'submitted',
          submittedAt: new Date(today.getTime() + i * 1000)
        });
        await result.save();
      }

      // Act
      const result = await applicationSubmissionWorkflow.submitApplication(testQueueItem._id.toString());

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toContain('Daily application limit reached');
    });

    it('should handle document upload requirements', async () => {
      // Arrange - mock form requiring documents
      formAutomationEngine.fillApplicationForm.mockResolvedValue({
        success: true,
        fieldsFilledCount: 3,
        totalFieldsCount: 3,
        requiresDocuments: true
      });

      formAutomationEngine.uploadDocuments.mockResolvedValue({
        success: true,
        uploadedCount: 2
      });

      // Act
      const result = await applicationSubmissionWorkflow.submitApplication(testQueueItem._id.toString());

      // Assert
      expect(result.success).toBe(true);
      expect(formAutomationEngine.uploadDocuments).toHaveBeenCalledWith(
        mockPage,
        testQueueItem.applicationData.documents
      );
    });

    it('should handle document upload failures', async () => {
      // Arrange - mock document upload failure
      formAutomationEngine.fillApplicationForm.mockResolvedValue({
        success: true,
        fieldsFilledCount: 3,
        totalFieldsCount: 3,
        requiresDocuments: true
      });

      formAutomationEngine.uploadDocuments.mockResolvedValue({
        success: false,
        errors: ['File too large', 'Invalid format']
      });

      // Act
      const result = await applicationSubmissionWorkflow.submitApplication(testQueueItem._id.toString());

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toContain('Document upload failed');
    });

    it('should extract confirmation number from success page', async () => {
      // Arrange - mock page with confirmation number
      mockPage.evaluate.mockResolvedValue('Bedankt voor je aanmelding. Referentienummer: ABC123456');

      // Act
      const result = await applicationSubmissionWorkflow.submitApplication(testQueueItem._id.toString());

      // Assert
      expect(result.success).toBe(true);
      expect(result.confirmationNumber).toBe('ABC123456');

      const applicationResult = await ApplicationResult.findById(result.applicationResultId);
      expect(applicationResult.confirmationNumber).toBe('ABC123456');
    });

    it('should handle concurrent submission prevention', async () => {
      // Arrange - start first submission
      const firstSubmissionPromise = applicationSubmissionWorkflow.submitApplication(testQueueItem._id.toString());

      // Act - try to start second submission immediately
      const secondSubmissionPromise = applicationSubmissionWorkflow.submitApplication(testQueueItem._id.toString());

      // Assert
      const [firstResult, secondResult] = await Promise.allSettled([
        firstSubmissionPromise,
        secondSubmissionPromise
      ]);

      expect(firstResult.status).toBe('fulfilled');
      expect(secondResult.status).toBe('rejected');
      expect(secondResult.reason.message).toContain('already in progress');
    });
  });

  describe('processQueueBatch', () => {
    it('should process multiple queue items concurrently', async () => {
      // Arrange - create multiple queue items
      const queueItems = [];
      for (let i = 0; i < 3; i++) {
        const item = new ApplicationQueue({
          userId: testUser._id,
          listingId: new mongoose.Types.ObjectId(),
          listingUrl: `https://www.funda.nl/huur/amsterdam/appartement-${i}/`,
          priority: i,
          status: 'pending',
          applicationData: testQueueItem.applicationData,
          generatedContent: testQueueItem.generatedContent
        });
        await item.save();
        queueItems.push(item);
      }

      // Act
      const results = await applicationSubmissionWorkflow.processQueueBatch(3);

      // Assert
      expect(results).toHaveLength(3);
      expect(results.every(r => r.success)).toBe(true);

      // Verify all items were processed
      const processedItems = await ApplicationQueue.find({ status: 'completed' });
      expect(processedItems).toHaveLength(3);
    });

    it('should respect concurrent submission limits', async () => {
      // Arrange - create more items than concurrent limit
      const queueItems = [];
      for (let i = 0; i < 5; i++) {
        const item = new ApplicationQueue({
          userId: testUser._id,
          listingId: new mongoose.Types.ObjectId(),
          listingUrl: `https://www.funda.nl/huur/amsterdam/appartement-${i}/`,
          priority: i,
          status: 'pending',
          applicationData: testQueueItem.applicationData,
          generatedContent: testQueueItem.generatedContent
        });
        await item.save();
        queueItems.push(item);
      }

      // Act - process with limit of 2
      const results = await applicationSubmissionWorkflow.processQueueBatch(2);

      // Assert
      expect(results).toHaveLength(2); // Only 2 should be processed
    });

    it('should handle mixed success and failure results', async () => {
      // Arrange - create queue items, one will fail
      const successItem = new ApplicationQueue({
        userId: testUser._id,
        listingId: new mongoose.Types.ObjectId(),
        listingUrl: 'https://www.funda.nl/huur/amsterdam/success/',
        priority: 1,
        status: 'pending',
        applicationData: testQueueItem.applicationData,
        generatedContent: testQueueItem.generatedContent
      });
      await successItem.save();

      const failItem = new ApplicationQueue({
        userId: testUser._id,
        listingId: new mongoose.Types.ObjectId(),
        listingUrl: 'https://www.funda.nl/huur/amsterdam/fail/',
        priority: 0,
        status: 'pending',
        applicationData: testQueueItem.applicationData,
        generatedContent: testQueueItem.generatedContent
      });
      await failItem.save();

      // Mock failure for second item
      let callCount = 0;
      formAutomationEngine.submitForm.mockImplementation(() => {
        callCount++;
        if (callCount === 2) {
          return Promise.resolve({ success: false, error: 'Network error' });
        }
        return Promise.resolve({ success: true, method: 'POST' });
      });

      // Act
      const results = await applicationSubmissionWorkflow.processQueueBatch(2);

      // Assert
      expect(results).toHaveLength(2);
      expect(results.filter(r => r.success)).toHaveLength(1);
      expect(results.filter(r => !r.success)).toHaveLength(1);
    });
  });

  describe('Error Handling', () => {
    it('should categorize different error types correctly', async () => {
      const workflow = applicationSubmissionWorkflow;

      expect(workflow.categorizeError(new Error('Navigation timeout'))).toBe('timeout');
      expect(workflow.categorizeError(new Error('Network connection failed'))).toBe('network');
      expect(workflow.categorizeError(new Error('Access blocked by server'))).toBe('blocked');
      expect(workflow.categorizeError(new Error('CAPTCHA verification required'))).toBe('captcha');
      expect(workflow.categorizeError(new Error('Form selector not found'))).toBe('form_changed');
      expect(workflow.categorizeError(new Error('Validation failed: required field'))).toBe('validation');
      expect(workflow.categorizeError(new Error('Rate limit exceeded'))).toBe('rate_limit');
      expect(workflow.categorizeError(new Error('Unknown error'))).toBe('unknown');
    });

    it('should clean up resources on error', async () => {
      // Arrange - mock error during form submission
      formAutomationEngine.fillApplicationForm.mockRejectedValue(new Error('Form error'));

      // Act
      const result = await applicationSubmissionWorkflow.submitApplication(testQueueItem._id.toString());

      // Assert
      expect(result.success).toBe(false);

      // Verify cleanup occurred
      expect(mockPage.close).toHaveBeenCalled();
      expect(browserPool.releaseBrowser).toHaveBeenCalledWith(mockBrowser);
    });

    it('should take error screenshots when configured', async () => {
      // Arrange - mock error and enable screenshots
      formAutomationEngine.fillApplicationForm.mockRejectedValue(new Error('Form error'));

      // Act
      await applicationSubmissionWorkflow.submitApplication(testQueueItem._id.toString());

      // Assert
      expect(mockPage.screenshot).toHaveBeenCalledWith(
        expect.objectContaining({
          path: expect.stringContaining('error_'),
          fullPage: true
        })
      );
    });
  });

  describe('Status and Monitoring', () => {
    it('should provide current processing status', () => {
      const status = applicationSubmissionWorkflow.getStatus();

      expect(status).toHaveProperty('isProcessing');
      expect(status).toHaveProperty('activeSubmissions');
      expect(status).toHaveProperty('activeSubmissionIds');
      expect(Array.isArray(status.activeSubmissionIds)).toBe(true);
    });

    it('should track active submissions during processing', async () => {
      // Arrange - slow down form submission to check status
      formAutomationEngine.fillApplicationForm.mockImplementation(async () => {
        await new Promise(resolve => setTimeout(resolve, 100));
        return { success: true, fieldsFilledCount: 3, totalFieldsCount: 3 };
      });

      // Act - start submission and check status immediately
      const submissionPromise = applicationSubmissionWorkflow.submitApplication(testQueueItem._id.toString());
      
      // Check status during processing
      await new Promise(resolve => setTimeout(resolve, 50));
      const statusDuringProcessing = applicationSubmissionWorkflow.getStatus();
      
      await submissionPromise;
      const statusAfterProcessing = applicationSubmissionWorkflow.getStatus();

      // Assert
      expect(statusDuringProcessing.activeSubmissions).toBe(1);
      expect(statusDuringProcessing.activeSubmissionIds).toContain(testQueueItem._id.toString());
      expect(statusAfterProcessing.activeSubmissions).toBe(0);
    });
  });
});