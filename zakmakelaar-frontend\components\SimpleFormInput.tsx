import React, { useState, useEffect, forwardRef } from 'react';
import { 
  StyleSheet, 
  View, 
  TextInput, 
  Text, 
  TouchableOpacity,
  TextInputProps,
  ViewStyle,
  StyleProp
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withTiming, 
  withSequence,
  withSpring,
  Easing,
  FadeIn,
  FadeInDown,
  interpolateColor
} from 'react-native-reanimated';

// Define theme colors
const THEME = {
  primary: '#4361ee',
  secondary: '#7209b7',
  accent: '#f72585',
  dark: '#0a0a18',
  light: '#ffffff'
};

interface SimpleFormInputProps extends TextInputProps {
  label: string;
  error?: string;
  icon?: keyof typeof Ionicons.glyphMap;
  secureTextEntry?: boolean;
  containerStyle?: StyleProp<ViewStyle>;
  delay?: number;
  validateOnChange?: boolean;
  validationFunction?: (text: string) => { isValid: boolean; error?: string };
  primaryColor?: string;
  secondaryColor?: string;
}

export const SimpleFormInput = forwardRef<TextInput, SimpleFormInputProps>(({
  label,
  error,
  icon,
  secureTextEntry = false,
  containerStyle,
  delay = 0,
  validateOnChange = false,
  validationFunction,
  primaryColor = THEME.primary,
  secondaryColor = THEME.accent,
  onChangeText,
  ...props
}, ref) => {
  const [isFocused, setIsFocused] = useState(false);
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);
  const [localError, setLocalError] = useState<string | undefined>(error);
  const [text, setText] = useState(props.value || '');
  const [isValid, setIsValid] = useState(false);
  
  // Animation values
  const shake = useSharedValue(0);
  const focusAnim = useSharedValue(0);
  
  // Update local error when prop changes
  useEffect(() => {
    setLocalError(error);
  }, [error]);
  
  // Trigger shake animation when error changes
  useEffect(() => {
    if (localError) {
      shake.value = withSequence(
        withTiming(-5, { duration: 50 }),
        withTiming(5, { duration: 50 }),
        withTiming(-5, { duration: 50 }),
        withTiming(5, { duration: 50 }),
        withTiming(0, { duration: 50 })
      );
    }
  }, [localError]);
  
  // Handle focus animation
  useEffect(() => {
    focusAnim.value = withTiming(isFocused ? 1 : 0, { 
      duration: 200, 
      easing: Easing.inOut(Easing.ease) 
    });
  }, [isFocused]);
  
  // Handle text change with validation
  const handleChangeText = (value: string) => {
    setText(value);
    
    if (onChangeText) {
      onChangeText(value);
    }
    
    if (validateOnChange && validationFunction && value) {
      const validation = validationFunction(value);
      setIsValid(validation.isValid);
      if (!validation.isValid) {
        setLocalError(validation.error);
      } else {
        setLocalError(undefined);
      }
    } else if (validateOnChange && value) {
      setIsValid(true);
      setLocalError(undefined);
    }
  };
  
  const handleFocus = () => {
    setIsFocused(true);
  };
  
  const handleBlur = () => {
    setIsFocused(false);
    
    // Validate on blur if validation function exists
    if (validationFunction && text) {
      const validation = validationFunction(text);
      setIsValid(validation.isValid);
      if (!validation.isValid) {
        setLocalError(validation.error);
      } else {
        setLocalError(undefined);
      }
    }
  };
  
  // Animated styles
  const animatedContainerStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateX: shake.value }]
    };
  });
  
  const animatedLabelStyle = useAnimatedStyle(() => {
    return {
      color: interpolateColor(
        focusAnim.value,
        [0, 1],
        ['#6b7280', secondaryColor]
      )
    };
  });
  
  const animatedBorderStyle = useAnimatedStyle(() => {
    return {
      borderColor: interpolateColor(
        focusAnim.value,
        [0, 1],
        ['rgba(0, 0, 0, 0.2)', secondaryColor]
      ),
      backgroundColor: interpolateColor(
        focusAnim.value,
        [0, 1],
        ['rgba(255, 255, 255, 0.9)', 'rgba(255, 255, 255, 0.95)']
      )
    };
  });

  return (
    <Animated.View 
      style={[styles.container, containerStyle, animatedContainerStyle]}
      entering={FadeInDown.delay(delay).duration(400)}
    >
      {/* Label above input */}
      <Animated.Text style={[styles.label, animatedLabelStyle]}>
        {label}
      </Animated.Text>
      
      {/* Input container */}
      <Animated.View 
        style={[
          styles.inputContainer,
          animatedBorderStyle,
          localError && styles.inputContainerError
        ]}
      >
        {icon && (
          <Ionicons 
            name={icon} 
            size={20} 
            color={isFocused ? secondaryColor : "#9ca3af"} 
            style={styles.icon} 
          />
        )}
        
        <TextInput
          ref={ref}
          style={styles.input}
          placeholderTextColor="rgba(0, 0, 0, 0.4)"
          onFocus={handleFocus}
          onBlur={handleBlur}
          onChangeText={handleChangeText}
          secureTextEntry={secureTextEntry && !isPasswordVisible}
          value={text}
          {...props}
        />
        
        {secureTextEntry && (
          <TouchableOpacity 
            onPress={() => setIsPasswordVisible(!isPasswordVisible)}
            style={styles.visibilityToggle}
          >
            <Ionicons 
              name={isPasswordVisible ? "eye-off-outline" : "eye-outline"} 
              size={20} 
              color="#6b7280" 
            />
          </TouchableOpacity>
        )}
      </Animated.View>
      
      {/* Error message with animation */}
      {localError && (
        <Animated.View 
          style={styles.errorContainer}
          entering={FadeIn.duration(200)}
        >
          <Ionicons name="alert-circle-outline" size={14} color="#ef4444" style={styles.errorIcon} />
          <Text style={styles.errorText}>{localError}</Text>
        </Animated.View>
      )}
    </Animated.View>
  );
});

const styles = StyleSheet.create({
  container: {
    marginBottom: 24,
    width: '100%',
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6b7280',
    marginBottom: 8,
    marginLeft: 4,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.2)',
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    paddingHorizontal: 12,
    height: 56,
  },
  inputContainerError: {
    borderColor: 'rgba(239, 68, 68, 0.5)',
  },
  icon: {
    marginRight: 12,
  },
  input: {
    flex: 1,
    fontSize: 16,
    color: '#1f2937',
    height: '100%',
  },
  visibilityToggle: {
    padding: 8,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    paddingHorizontal: 4,
  },
  errorIcon: {
    marginRight: 4,
  },
  errorText: {
    fontSize: 12,
    color: '#ef4444',
  },
});