/**
 * Frontend Compatibility Layer Tests
 * 
 * Tests for the frontend compatibility layer that ensures unified property schema
 * data is compatible with frontend expectations.
 */

const {
  convertToFrontendFormat,
  convertFromLegacyListing,
  formatLocation,
  formatPrice,
  extractNumericSize,
  inferFurnishedStatus,
  normalizeRooms
} = require('../services/frontendCompatibilityLayer');

describe('Frontend Compatibility Layer', () => {
  describe('convertToFrontendFormat', () => {
    it('should convert unified property to frontend format', () => {
      const unifiedProperty = {
        _id: '123456789',
        title: 'Test Property',
        description: 'A nice property',
        price: 1500,
        location: {
          _unified: {
            address: {
              street: 'Test Street',
              houseNumber: '123',
              city: 'Amsterdam'
            }
          },
          _legacy: 'Test Street 123, Amsterdam'
        },
        propertyType: 'apartment',
        rooms: 3,
        bedrooms: 2,
        bathrooms: 1,
        area: 85,
        size: '85 m²',
        year: '2010',
        interior: 'Gemeubileerd',
        furnished: true,
        pets: false,
        smoking: false,
        garden: true,
        balcony: false,
        parking: true,
        energyLabel: 'A',
        images: ['image1.jpg', 'image2.jpg'],
        source: 'funda.nl',
        url: 'https://example.com',
        dateAdded: '2023-01-01T00:00:00.000Z',
        isActive: true,
        features: ['feature1', 'feature2'],
        deposit: 1500,
        utilities: 150,
        contactInfo: {
          name: 'Test Agent',
          phone: '123456789',
          email: '<EMAIL>'
        }
      };

      const result = convertToFrontendFormat(unifiedProperty);

      expect(result).toBeInstanceOf(Object);
      expect(result._id).toBe('123456789');
      expect(result.id).toBe('123456789');
      expect(result.title).toBe('Test Property');
      expect(result.price).toBe('€ 1.500 per maand');
      expect(result.location).toBe('Test Street 123, Amsterdam');
      expect(result.propertyType).toBe('apartment');
      expect(result.rooms).toBe(3);
      expect(result.bedrooms).toBe(2);
      expect(result.bathrooms).toBe(1);
      expect(result.area).toBe(85);
      expect(result.size).toBe('85 m²');
      expect(result.year).toBe('2010');
      expect(result.interior).toBe('Gemeubileerd');
      expect(result.furnished).toBe(true);
      expect(result.pets).toBe(false);
      expect(result.garden).toBe(true);
      expect(result.parking).toBe(true);
      expect(result.images).toEqual(['image1.jpg', 'image2.jpg']);
      expect(result.source).toBe('funda.nl');
      expect(result.isActive).toBe(true);
      expect(result.features).toEqual(['feature1', 'feature2']);
      expect(result.deposit).toBe(1500);
    });

    it('should handle null or undefined input', () => {
      expect(convertToFrontendFormat(null)).toBeNull();
      expect(convertToFrontendFormat(undefined)).toBeNull();
    });

    it('should handle string price format', () => {
      const property = {
        _id: '123',
        title: 'Test',
        price: '€ 1.500 per maand',
        location: 'Amsterdam',
        source: 'funda.nl',
        url: 'https://example.com'
      };

      const result = convertToFrontendFormat(property);
      expect(result.price).toBe('€ 1.500 per maand');
    });

    it('should handle numeric price format', () => {
      const property = {
        _id: '123',
        title: 'Test',
        price: 1500,
        location: 'Amsterdam',
        source: 'funda.nl',
        url: 'https://example.com'
      };

      const result = convertToFrontendFormat(property);
      expect(result.price).toBe('€ 1.500 per maand');
    });

    it('should handle high numeric price format', () => {
      const property = {
        _id: '123',
        title: 'Test',
        price: 350000,
        location: 'Amsterdam',
        source: 'funda.nl',
        url: 'https://example.com'
      };

      const result = convertToFrontendFormat(property);
      expect(result.price).toBe('€ 350.000');
    });
  });

  describe('convertFromLegacyListing', () => {
    it('should convert legacy listing to unified format', () => {
      const legacyListing = {
        _id: '123456789',
        title: 'Legacy Property',
        description: 'An old property',
        price: '€ 1.200 per maand',
        location: 'Rotterdam',
        propertyType: 'woning',
        rooms: '2',
        bedrooms: '1',
        size: '65 m²',
        year: '2000',
        interior: 'Gemeubileerd',
        source: 'huurwoningen.nl',
        url: 'https://example.com',
        images: ['old-image.jpg'],
        timestamp: '2022-01-01T00:00:00.000Z'
      };

      const result = convertFromLegacyListing(legacyListing);

      expect(result).toBeInstanceOf(Object);
      expect(result._id).toBe('123456789');
      expect(result.id).toBe('123456789');
      expect(result.title).toBe('Legacy Property');
      expect(result.price).toBe('€ 1.200 per maand');
      expect(result.location).toBe('Rotterdam');
      expect(result.propertyType).toBe('woning');
      expect(result.rooms).toBe('2');
      expect(result.bedrooms).toBe('1');
      expect(result.size).toBe('65 m²');
      expect(result.area).toBe(65);
      expect(result.year).toBe('2000');
      expect(result.interior).toBe('Gemeubileerd');
      expect(result.furnished).toBe(true);
      expect(result.source).toBe('huurwoningen.nl');
      expect(result.url).toBe('https://example.com');
      expect(result.images).toEqual(['old-image.jpg']);
      expect(result.dateAdded).toBe('2022-01-01T00:00:00.000Z');
      expect(result.bathrooms).toBe('1');
      expect(result.pets).toBe(false);
      expect(result.smoking).toBe(false);
      expect(result.garden).toBe(false);
      expect(result.balcony).toBe(false);
      expect(result.parking).toBe(false);
      expect(result.isActive).toBe(true);
      expect(result.features).toEqual([]);
      expect(result._internal).toBeInstanceOf(Object);
      expect(result._internal.sourceMetadata).toBeInstanceOf(Object);
      expect(result._internal.rawData.original).toEqual(legacyListing);
    });

    it('should handle null or undefined input', () => {
      expect(convertFromLegacyListing(null)).toBeNull();
      expect(convertFromLegacyListing(undefined)).toBeNull();
    });
  });

  describe('formatLocation', () => {
    it('should return string location as is', () => {
      expect(formatLocation('Amsterdam')).toBe('Amsterdam');
    });

    it('should use _legacy location if available', () => {
      const location = {
        _legacy: 'Amsterdam, Netherlands',
        _unified: {
          address: {
            city: 'Amsterdam',
            country: 'Netherlands'
          }
        }
      };
      expect(formatLocation(location)).toBe('Amsterdam, Netherlands');
    });

    it('should format unified address data', () => {
      const location = {
        _unified: {
          address: {
            street: 'Herengracht',
            houseNumber: '123',
            city: 'Amsterdam',
            country: 'Netherlands'
          }
        }
      };
      expect(formatLocation(location)).toBe('Herengracht 123, Amsterdam');
    });

    it('should handle city-only unified address data', () => {
      const location = {
        _unified: {
          address: {
            city: 'Amsterdam',
            country: 'Netherlands'
          }
        }
      };
      expect(formatLocation(location)).toBe('Amsterdam');
    });

    it('should handle null or undefined input', () => {
      expect(formatLocation(null)).toBe('');
      expect(formatLocation(undefined)).toBe('');
    });
  });

  describe('formatPrice', () => {
    it('should format numeric price with per maand for rental prices', () => {
      expect(formatPrice(1500)).toBe('€ 1.500 per maand');
    });

    it('should format high numeric price without per maand', () => {
      expect(formatPrice(350000)).toBe('€ 350.000');
    });

    it('should return string price as is', () => {
      expect(formatPrice('€ 1.500 per maand')).toBe('€ 1.500 per maand');
    });

    it('should handle zero or negative price', () => {
      expect(formatPrice(0)).toBe('Prijs op aanvraag');
      expect(formatPrice(-100)).toBe('Prijs op aanvraag');
    });

    it('should handle null or undefined input', () => {
      expect(formatPrice(null)).toBe('Prijs op aanvraag');
      expect(formatPrice(undefined)).toBe('Prijs op aanvraag');
    });
  });

  describe('extractNumericSize', () => {
    it('should extract numeric size from size string', () => {
      expect(extractNumericSize('85 m²')).toBe(85);
      expect(extractNumericSize('120m²')).toBe(120);
      expect(extractNumericSize('65')).toBe(65);
    });

    it('should handle null or undefined input', () => {
      expect(extractNumericSize(null)).toBeNull();
      expect(extractNumericSize(undefined)).toBeNull();
    });

    it('should handle invalid size string', () => {
      expect(extractNumericSize('unknown')).toBeNull();
      expect(extractNumericSize('')).toBeNull();
    });
  });

  describe('inferFurnishedStatus', () => {
    it('should infer furnished status from Dutch terms', () => {
      expect(inferFurnishedStatus('Gemeubileerd')).toBe(true);
      expect(inferFurnishedStatus('gemeubileerd')).toBe(true);
      expect(inferFurnishedStatus('Kaal')).toBe(false);
      expect(inferFurnishedStatus('kaal')).toBe(false);
      expect(inferFurnishedStatus('Gestoffeerd')).toBe(false);
    });

    it('should infer furnished status from English terms', () => {
      expect(inferFurnishedStatus('Furnished')).toBe(true);
      expect(inferFurnishedStatus('furnished')).toBe(true);
      expect(inferFurnishedStatus('Unfurnished')).toBe(false);
      expect(inferFurnishedStatus('unfurnished')).toBe(false);
    });

    it('should handle null or undefined input', () => {
      expect(inferFurnishedStatus(null)).toBe(false);
      expect(inferFurnishedStatus(undefined)).toBe(false);
    });
  });

  describe('normalizeRooms', () => {
    it('should normalize room count to string', () => {
      expect(normalizeRooms(3)).toBe('3');
      expect(normalizeRooms('3')).toBe('3');
      expect(normalizeRooms('3+')).toBe('3+');
    });

    it('should handle null or undefined input', () => {
      expect(normalizeRooms(null)).toBeNull();
      expect(normalizeRooms(undefined)).toBeNull();
    });
  });
});