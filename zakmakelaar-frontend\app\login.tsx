import React, { useState, useEffect, useRef } from "react";
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
  Dimensions,
  TextInput,
  Keyboard,
} from "react-native";
import { useRouter } from "expo-router";
import { StatusBar } from "expo-status-bar";
import * as Haptics from 'expo-haptics';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import Animated, {
  FadeIn,
  FadeInDown,
  FadeInUp,
  SlideInLeft,
  ZoomIn,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
  withSequence,
  withRepeat,
  Easing,
  interpolateColor
} from 'react-native-reanimated';

import { useAuthStore } from "../store/authStore";
import { authService } from "../services/authService";
import { apiService } from "../services/api";
import { AuthBackground } from "../components/AuthBackground";
import { SimpleFormInput } from "../components/SimpleFormInput";
import { PrimaryButton } from "../components/PrimaryButton";
import { SocialButton } from "../components/SocialButton";
import { ContextualHelp } from "../components/ContextualHelp";

const { width } = Dimensions.get('window');

// Define theme colors
const THEME = {
  primary: '#4361ee',
  secondary: '#7209b7',
  accent: '#f72585',
  dark: '#0a0a18',
  light: '#ffffff',
  success: '#10b981',
  warning: '#f59e0b',
  error: '#ef4444'
};

export default function LoginSignUpScreen() {
  const router = useRouter();
  const [isLogin, setIsLogin] = useState(true);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [emailError, setEmailError] = useState("");
  const [passwordError, setPasswordError] = useState("");
  const [firstNameError, setFirstNameError] = useState("");
  const [lastNameError, setLastNameError] = useState("");
  const [activeTab, setActiveTab] = useState<'login' | 'register'>('login');
  const [isKeyboardVisible, setKeyboardVisible] = useState(false);
  const [formProgress, setFormProgress] = useState(0);
  const [userType, setUserType] = useState<'tenant' | 'propertyOwner'>('tenant');

  // Refs for form fields
  const emailRef = useRef<TextInput>(null);
  const passwordRef = useRef<TextInput>(null);
  const firstNameRef = useRef<TextInput>(null);
  const lastNameRef = useRef<TextInput>(null);

  const { login, register, isLoading, error, clearError, isAuthenticated } =
    useAuthStore();

  // Animation values
  const tabIndicatorPosition = useSharedValue(0);
  const logoScale = useSharedValue(1);
  const logoRotate = useSharedValue(0);
  const progressValue = useSharedValue(0);
  const shimmerPosition = useSharedValue(-width);

  // Keyboard listeners
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => {
        setKeyboardVisible(true);
      }
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        setKeyboardVisible(false);
      }
    );

    return () => {
      keyboardDidHideListener.remove();
      keyboardDidShowListener.remove();
    };
  }, []);

  // Start animations
  useEffect(() => {
    // Logo animations
    logoScale.value = withRepeat(
      withSequence(
        withTiming(1.08, { duration: 2000, easing: Easing.inOut(Easing.ease) }),
        withTiming(1, { duration: 2000, easing: Easing.inOut(Easing.ease) })
      ),
      -1,
      true
    );

    logoRotate.value = withRepeat(
      withSequence(
        withTiming(8, { duration: 4000, easing: Easing.inOut(Easing.ease) }),
        withTiming(-8, { duration: 4000, easing: Easing.inOut(Easing.ease) })
      ),
      -1,
      true
    );

    // Shimmer animation
    shimmerPosition.value = withRepeat(
      withTiming(width, {
        duration: 2000,
        easing: Easing.inOut(Easing.ease)
      }),
      -1,
      false
    );
  }, [logoScale, logoRotate, shimmerPosition]);

  // Update tab indicator position
  useEffect(() => {
    // Calculate the width of each tab (half of the total width)
    const tabWidth = width * 0.4;

    tabIndicatorPosition.value = withTiming(
      activeTab === 'login' ? 0 : tabWidth,
      { duration: 300, easing: Easing.inOut(Easing.ease) }
    );

    // Update isLogin state based on active tab
    setIsLogin(activeTab === 'login');

    // Reset form progress when switching tabs
    setFormProgress(0);
    progressValue.value = withTiming(0, { duration: 300 });
  }, [activeTab, tabIndicatorPosition, progressValue]);

  // Update form progress
  useEffect(() => {
    // Calculate form progress
    let progress = 0;

    if (isLogin) {
      if (email) progress += 50;
      if (password) progress += 50;
    } else {
      if (email) progress += 40;
      if (password) progress += 40;
      if (firstName) progress += 10;
      if (lastName) progress += 10;
    }

    setFormProgress(progress);
    progressValue.value = withTiming(progress / 100, { duration: 300 });
  }, [email, password, firstName, lastName, isLogin, progressValue]);

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      // Check redirect blocker before navigating
      import('../services/redirectBlockerService').then(({ redirectBlockerService }) => {
        if (redirectBlockerService.areRedirectsBlocked()) {
          console.log('LOGIN: Redirects are blocked, not navigating to dashboard');
          return;
        }
        console.log('LOGIN: Navigating to dashboard from login screen');
        router.replace("/dashboard");
      }).catch(() => {
        // Fallback if import fails
        router.replace("/dashboard");
      });
    }
  }, [isAuthenticated, router]);

  // Clear errors when switching between login/register
  useEffect(() => {
    clearError();
    setEmailError("");
    setPasswordError("");
    setFirstNameError("");
    setLastNameError("");
  }, [isLogin, clearError]);

  // Animated styles

  const logoAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { scale: logoScale.value },
        { rotate: `${logoRotate.value}deg` }
      ]
    };
  });

  const progressBarStyle = useAnimatedStyle(() => {
    return {
      width: `${formProgress}%`,
      backgroundColor: interpolateColor(
        progressValue.value,
        [0, 0.5, 1],
        [THEME.error, THEME.warning, THEME.success]
      )
    };
  });

  const shimmerAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateX: shimmerPosition.value }]
    };
  });

  // Email validation function for real-time validation
  const validateEmail = (text: string) => {
    if (!text) return { isValid: false, error: "Email is required" };
    
    if (!authService.validateEmail(text)) {
      return { isValid: false, error: "Please enter a valid email address" };
    }
    return { isValid: true };
  };

  // Password validation function for real-time validation
  const validatePassword = (text: string) => {
    if (!text) return { isValid: false, error: "Password is required" };
    if (!isLogin) {
      const validation = authService.validatePassword(text);
      if (!validation.isValid) {
        return { isValid: false, error: validation.errors[0] };
      }
    }
    return { isValid: true };
  };

  const validateForm = () => {
    let isValid = true;

    // Clear previous errors
    setEmailError("");
    setPasswordError("");
    setFirstNameError("");
    setLastNameError("");

    // Validate email
    const emailValidation = validateEmail(email);
    if (!emailValidation.isValid) {
      setEmailError(emailValidation.error || "Invalid email");
      isValid = false;
    }

    // Validate password
    const passwordValidation = validatePassword(password);
    if (!passwordValidation.isValid) {
      setPasswordError(passwordValidation.error || "Invalid password");
      isValid = false;
    }

    return isValid;
  };

  const handleAuth = async () => {
    if (!validateForm()) {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      return;
    }

    try {
      let success = false;

      if (isLogin) {
        success = await login(email.trim(), password);
      } else {
        // Pass isPropertyOwner option based on user type selection
        success = await register(
          email.trim(),
          password,
          firstName.trim() || undefined,
          lastName.trim() || undefined,
          { isPropertyOwner: userType === 'propertyOwner' }
        );
      }

      if (success) {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

        // Use navigation service to track onboarding progress and navigate
        const { navigationService } = await import('../services/navigationService');
        
        // Enhanced logging for debugging navigation issues
        console.log('==========================================');
        console.log('AUTHENTICATION FLOW - PRE-NAVIGATION DEBUG');
        console.log('==========================================');
        console.log('Is Property Owner Registration:', userType === 'propertyOwner');
        console.log('Is Login (vs Register):', isLogin);
        
        // Log detailed user state before navigation
        const currentUser = await authService.getCachedUser();
        console.log('User ID:', currentUser?._id || currentUser?.id);
        console.log('User Email:', currentUser?.email);
        console.log('User Role:', currentUser?.role);
        console.log('Property Owner Object:', JSON.stringify(currentUser?.propertyOwner));
        console.log('Is Property Owner Flag:', !!currentUser?.propertyOwner?.isPropertyOwner);
        console.log('Has Preferences:', !!currentUser?.preferences);
        console.log('Full User Object:', JSON.stringify(currentUser));
        console.log('==========================================');
        
        // Force update user role if property owner registration but role is not 'owner'
        if (!isLogin && userType === 'propertyOwner' && currentUser && currentUser.role !== 'owner') {
          console.log('WARNING: Property owner registered but role is not "owner"');
          console.log('Applying frontend fix to ensure correct role...');
          
          // Create updated user with correct role
          const updatedUser = {
            ...currentUser,
            role: 'owner',
            propertyOwner: currentUser.propertyOwner || {
              isPropertyOwner: true,
              properties: [],
              verificationStatus: 'pending'
            }
          };
          
          // Save updated user data to storage
          await apiService.saveAuthData(
            await apiService.getAuthToken() || "",
            undefined,
            updatedUser
          );
          
          console.log('User role updated to "owner" in local storage');
        }
        
        // Navigate based on auth state
        console.log('Calling navigationService.navigateAfterAuth()...');
        await navigationService.navigateAfterAuth();
        console.log('Navigation completed');
      } else {
        // Error is handled by the store and displayed below
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
        console.log("Authentication failed");
      }
    } catch (err) {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      console.error("Authentication error:", err);
      Alert.alert("Error", "An unexpected error occurred. Please try again.", [
        { text: "OK" },
      ]);
    }
  };

  const handleSocialLogin = (provider: string) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    Alert.alert(
      "Coming Soon",
      `${provider} login will be available in a future update.`,
      [{ text: "OK" }]
    );
  };

  const toggleAuthMode = (mode: 'login' | 'register') => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setActiveTab(mode);
  };

  // Help content for contextual help
  const helpItems = [
    {
      title: "Secure Authentication",
      content: "Your credentials are securely encrypted and never stored on our servers in plain text.",
      icon: "shield-checkmark" as keyof typeof Ionicons.glyphMap
    },
    {
      title: "AI-Powered Features",
      content: "After logging in, you'll get access to our AI rental assistant that helps you find and apply for properties.",
      icon: "bulb" as keyof typeof Ionicons.glyphMap
    },
    {
      title: "Password Requirements",
      content: "For security, passwords must be at least 6 characters with uppercase, lowercase, and numbers.",
      icon: "lock-closed" as keyof typeof Ionicons.glyphMap
    },
    {
      title: "Biometric Authentication",
      content: "Coming soon: Use your fingerprint or face recognition for faster login.",
      icon: "finger-print" as keyof typeof Ionicons.glyphMap
    }
  ];

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="dark" />

      {/* Enhanced Animated Background */}
      <AuthBackground
        primaryColor={THEME.primary}
        secondaryColor={THEME.secondary}
        accentColor={THEME.accent}
      />

      <KeyboardAvoidingView
        style={styles.keyboardAvoid}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        keyboardVerticalOffset={Platform.OS === "ios" ? 0 : 20}
      >
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Header with back button and help */}
          {/*<View style={styles.header}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => router.back()}
            >
              <Ionicons name="arrow-back" size={24} color="#1f2937" />
            </TouchableOpacity>

            <View style={styles.headerRight}>
              <ContextualHelp
                items={helpItems}
                screenName={isLogin ? "Login" : "Registration"}
              />
            </View>
          </View>*/}

          {/* Logo and Title */}
          <View style={[
            styles.logoSection,
            isKeyboardVisible && styles.logoSectionCompact
          ]}>
            <View style={[
              styles.logoWrapper,
              isKeyboardVisible && styles.logoWrapperCompact
            ]}>
              {/* Glow effect behind logo */}
              <Animated.View
                style={[styles.logoGlow, logoAnimatedStyle]}
                entering={FadeIn.duration(1000)}
              >
                <LinearGradient
                  colors={[THEME.accent, THEME.primary]}
                  style={styles.logoGlowGradient}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                />
              </Animated.View>

              {/* Logo container with gradient */}
              <Animated.View
                style={[styles.logoContainer, logoAnimatedStyle]}
                entering={ZoomIn.duration(800).springify()}
              >
                <LinearGradient
                  colors={[THEME.primary, THEME.secondary]}
                  style={styles.logoGradient}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                >
                  <Text style={styles.logoText}>ZM</Text>
                </LinearGradient>
              </Animated.View>

              {/* Tech decoration elements */}
              <Animated.View
                style={[styles.techDecoration, { top: -5, right: -5 }]}
                entering={FadeIn.delay(800).duration(600)}
              >
                <View style={styles.techCircle} />
                <View style={[styles.techLine, { width: 15 }]} />
              </Animated.View>

              <Animated.View
                style={[styles.techDecoration, { bottom: -3, left: -8 }]}
                entering={FadeIn.delay(1000).duration(600)}
              >
                <View style={styles.techCircle} />
                <View style={[styles.techLine, { width: 10 }]} />
              </Animated.View>
            </View>

            {!isKeyboardVisible && (
              <>
                <Animated.Text
                  style={styles.title}
                  entering={FadeInDown.delay(200).duration(600)}
                >
                  {isLogin ? "Welcome Back" : "Create Account"}
                </Animated.Text>

                <Animated.Text
                  style={styles.subtitle}
                  entering={FadeInDown.delay(400).duration(600)}
                >
                  {isLogin
                    ? "Log in to access your AI rental assistant"
                    : "Sign up to start your rental journey with AI"}
                </Animated.Text>
              </>
            )}
          </View>

          {/* Tab Switcher */}
          <Animated.View
            style={styles.tabContainer}
            entering={FadeIn.delay(500).duration(400)}
          >
            <View style={styles.tabBackground}>
              {/* Login Tab */}
              <TouchableOpacity
                style={[styles.tab, { borderTopLeftRadius: 12, borderBottomLeftRadius: 12 }]}
                onPress={() => toggleAuthMode('login')}
              >
                {activeTab === 'login' ? (
                  <LinearGradient
                    colors={[THEME.accent, THEME.primary]}
                    style={styles.tabGradient}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                  >
                    <View style={styles.tabContent}>
                      <Ionicons
                        name="log-in-outline"
                        size={18}
                        color="#ffffff"
                        style={styles.tabIcon}
                      />
                      <Text style={styles.activeTabText}>Log In</Text>
                    </View>
                  </LinearGradient>
                ) : (
                  <View style={styles.tabContent}>
                    <Ionicons
                      name="log-in-outline"
                      size={18}
                      color="#1f2937"
                      style={styles.tabIcon}
                    />
                    <Text style={styles.tabText}>Log In</Text>
                  </View>
                )}
              </TouchableOpacity>

              {/* Sign Up Tab */}
              <TouchableOpacity
                style={[styles.tab, { borderTopRightRadius: 12, borderBottomRightRadius: 12 }]}
                onPress={() => toggleAuthMode('register')}
              >
                {activeTab === 'register' ? (
                  <LinearGradient
                    colors={[THEME.accent, THEME.primary]}
                    style={styles.tabGradient}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                  >
                    <View style={styles.tabContent}>
                      <Ionicons
                        name="person-add-outline"
                        size={18}
                        color="#ffffff"
                        style={styles.tabIcon}
                      />
                      <Text style={styles.activeTabText}>Sign Up</Text>
                    </View>
                  </LinearGradient>
                ) : (
                  <View style={styles.tabContent}>
                    <Ionicons
                      name="person-add-outline"
                      size={18}
                      color="#1f2937"
                      style={styles.tabIcon}
                    />
                    <Text style={styles.tabText}>Sign Up</Text>
                  </View>
                )}
              </TouchableOpacity>
            </View>
          </Animated.View>

          {/* Form Progress Bar */}
          <Animated.View
            style={styles.progressContainer}
            entering={FadeIn.delay(600).duration(400)}
          >
            <View style={styles.progressBarBackground}>
              <Animated.View style={[styles.progressBar, progressBarStyle]} />

              {/* Shimmer effect */}
              <Animated.View style={[styles.shimmer, shimmerAnimatedStyle]}>
                <LinearGradient
                  colors={['rgba(255,255,255,0)', 'rgba(255,255,255,0.2)', 'rgba(255,255,255,0)']}
                  style={styles.shimmerGradient}
                  start={{ x: 0, y: 0.5 }}
                  end={{ x: 1, y: 0.5 }}
                />
              </Animated.View>
            </View>
            <Text style={styles.progressText}>{formProgress}% Complete</Text>
          </Animated.View>

          {/* Error Display */}
          {error && (
            <Animated.View
              style={styles.errorContainer}
              entering={FadeIn.duration(300)}
            >
              <Ionicons name="alert-circle-outline" size={20} color={THEME.error} />
              <Text style={styles.errorText}>{error}</Text>
            </Animated.View>
          )}

          {/* Form Fields */}
          <View style={styles.formContainer}>
            {!isLogin && (
              <>
                <Animated.View
                  style={styles.nameFieldsContainer}
                  entering={SlideInLeft.delay(600).duration(400)}
                >
                  <SimpleFormInput
                    label="First Name (Optional)"
                    placeholder="John"
                    value={firstName}
                    onChangeText={setFirstName}
                    autoCapitalize="words"
                    icon="person-outline"
                    error={firstNameError}
                    delay={0}
                    primaryColor={THEME.primary}
                    secondaryColor={THEME.accent}
                    containerStyle={styles.nameField}
                    ref={firstNameRef}
                    returnKeyType="next"
                    onSubmitEditing={() => lastNameRef.current?.focus()}
                  />

                  <SimpleFormInput
                    label="Last Name (Optional)"
                    placeholder="Doe"
                    value={lastName}
                    onChangeText={setLastName}
                    autoCapitalize="words"
                    icon="people-outline"
                    error={lastNameError}
                    delay={100}
                    primaryColor={THEME.primary}
                    secondaryColor={THEME.accent}
                    containerStyle={styles.nameField}
                    ref={lastNameRef}
                    returnKeyType="next"
                    onSubmitEditing={() => emailRef.current?.focus()}
                  />
                </Animated.View>

                {/* User Type Selection */}
                <Animated.View
                  style={styles.userTypeContainer}
                  entering={FadeIn.delay(300).duration(400)}
                >
                  <Text style={styles.userTypeLabel}>I am registering as a:</Text>
                  <View style={styles.userTypeToggleContainer}>
                    <TouchableOpacity
                      style={[
                        styles.userTypeButton,
                        userType === 'tenant' && styles.userTypeButtonActive
                      ]}
                      onPress={() => {
                        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                        setUserType('tenant');
                      }}
                    >
                      <Ionicons
                        name="home-outline"
                        size={20}
                        color={userType === 'tenant' ? THEME.light : THEME.dark}
                        style={styles.userTypeIcon}
                      />
                      <Text
                        style={[
                          styles.userTypeText,
                          userType === 'tenant' && styles.userTypeTextActive
                        ]}
                      >
                        Tenant
                      </Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={[
                        styles.userTypeButton,
                        userType === 'propertyOwner' && styles.userTypeButtonActive
                      ]}
                      onPress={() => {
                        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                        setUserType('propertyOwner');
                      }}
                    >
                      <Ionicons
                        name="business-outline"
                        size={20}
                        color={userType === 'propertyOwner' ? THEME.light : THEME.dark}
                        style={styles.userTypeIcon}
                      />
                      <Text
                        style={[
                          styles.userTypeText,
                          userType === 'propertyOwner' && styles.userTypeTextActive
                        ]}
                      >
                        Property Owner
                      </Text>
                    </TouchableOpacity>
                  </View>
                </Animated.View>
              </>
            )}

            <SimpleFormInput
              label="Email"
              placeholder="<EMAIL>"
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
              icon="mail-outline"
              error={emailError}
              delay={isLogin ? 600 : 200}
              validateOnChange={true}
              validationFunction={validateEmail}
              primaryColor={THEME.primary}
              secondaryColor={THEME.accent}
              ref={emailRef}
              returnKeyType="next"
              onSubmitEditing={() => passwordRef.current?.focus()}
            />

            <SimpleFormInput
              label="Password"
              value={password}
              onChangeText={setPassword}
              secureTextEntry
              icon="lock-closed-outline"
              error={passwordError}
              delay={isLogin ? 700 : 300}
              validateOnChange={true}
              validationFunction={validatePassword}
              primaryColor={THEME.primary}
              secondaryColor={THEME.accent}
              ref={passwordRef}
              returnKeyType="done"
              onSubmitEditing={handleAuth}
            />

            {!isLogin && (
              <Animated.View
                style={styles.passwordHintContainer}
                entering={FadeIn.delay(400)}
              >
                <Ionicons name="information-circle-outline" size={16} color={THEME.accent} style={styles.hintIcon} />
                <Text style={styles.passwordHint}>
                  Password must be at least 6 characters with uppercase, lowercase, and number.
                </Text>
              </Animated.View>
            )}

            {isLogin && (
              <Animated.View
                style={styles.forgotPasswordContainer}
                entering={FadeIn.delay(800)}
              >
                <TouchableOpacity
                  onPress={() => Alert.alert("Coming Soon", "Password reset will be available in a future update.")}
                  style={styles.forgotPasswordButton}
                >
                  <Ionicons name="key-outline" size={16} color={THEME.accent} style={styles.forgotPasswordIcon} />
                  <Text style={styles.forgotPasswordText}>Forgot password?</Text>
                </TouchableOpacity>
              </Animated.View>
            )}

            {/* Primary Button */}
            <PrimaryButton
              title={isLogin ? "Log In" : "Create Account"}
              onPress={handleAuth}
              isLoading={isLoading}
              style={styles.primaryButton}
              delay={isLogin ? 900 : 500}
              primaryColor={THEME.primary}
              secondaryColor={THEME.accent}
              iconName={isLogin ? "log-in-outline" : "person-add-outline"}
            />

            {/* Divider */}
            <Animated.View
              style={styles.dividerContainer}
              entering={FadeIn.delay(isLogin ? 1000 : 600)}
            >
              <View style={styles.dividerLine} />
              <Text style={styles.dividerText}>or continue with</Text>
              <View style={styles.dividerLine} />
            </Animated.View>

            {/* Social Buttons */}
            <View style={styles.socialButtonsContainer}>
              <SocialButton
                title="Google"
                icon="logo-google"
                onPress={() => handleSocialLogin("Google")}
                style={styles.socialButton}
                delay={isLogin ? 1100 : 700}
                iconColor="#DB4437"
                iconBackgroundColor="rgba(255, 255, 255, 0.9)"
              />

              <SocialButton
                title="Apple"
                icon="logo-apple"
                onPress={() => handleSocialLogin("Apple")}
                style={styles.socialButton}
                delay={isLogin ? 1200 : 800}
                iconColor="#000000"
                iconBackgroundColor="rgba(255, 255, 255, 0.9)"
              />
            </View>

            {/* Biometric Auth Placeholder */}
            <Animated.View
              style={styles.biometricContainer}
              entering={FadeInUp.delay(isLogin ? 1300 : 900)}
            >
              <TouchableOpacity
                style={styles.biometricButton}
                onPress={() => Alert.alert("Coming Soon", "Biometric authentication will be available in a future update.")}
              >
                <LinearGradient
                  colors={[THEME.primary, THEME.accent]}
                  style={styles.biometricGradient}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                >
                  <Ionicons name="finger-print-outline" size={28} color="rgba(255, 255, 255, 0.9)" />
                </LinearGradient>
              </TouchableOpacity>
              <Text style={styles.biometricText}>Use biometric login</Text>
            </Animated.View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: THEME.dark,
    paddingTop: 60
  },
  // User Type Selection Styles
  userTypeContainer: {
    marginBottom: 16,
    width: '100%',
  },
  userTypeLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#e5e7eb',
    marginBottom: 12,
  },
  userTypeToggleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginBottom: 8,
  },
  userTypeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    width: '48%',
  },
  userTypeButtonActive: {
    backgroundColor: THEME.primary,
    borderColor: THEME.accent,
  },
  userTypeIcon: {
    marginRight: 8,
  },
  userTypeText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1f2937',
  },
  userTypeTextActive: {
    color: '#ffffff',
  },
  keyboardAvoid: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 24,
    paddingTop: Platform.OS === "android" ? 20 : 10,
    paddingBottom: 40,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 20,
  },
  headerRight: {
    flexDirection: "row",
    alignItems: "center",
  },
  backButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.2)",
  },
  logoSection: {
    alignItems: "center",
    marginBottom: 32,
  },
  logoSectionCompact: {
    marginBottom: 16,
  },
  logoWrapper: {
    position: 'relative',
    width: 100,
    height: 100,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  logoWrapperCompact: {
    width: 70,
    height: 70,
    marginBottom: 12,
  },
  logoGlow: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    borderRadius: 30,
    overflow: 'hidden',
  },
  logoGlowGradient: {
    width: '100%',
    height: '100%',
  },
  logoContainer: {
    width: '80%',
    height: '80%',
    borderRadius: 24,
    justifyContent: "center",
    alignItems: "center",
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.2)",
  },
  logoGradient: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  logoText: {
    fontSize: 32,
    fontWeight: "bold",
    color: THEME.light,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  techDecoration: {
    position: 'absolute',
    flexDirection: 'row',
    alignItems: 'center',
  },
  techCircle: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: THEME.accent,
  },
  techLine: {
    height: 2,
    backgroundColor: THEME.accent,
    opacity: 0.7,
  },
  title: {
    fontSize: 28,
    fontWeight: "bold",
    color: "#1f2937",
    marginBottom: 12,
    textAlign: "center",
  },
  subtitle: {
    fontSize: 16,
    color: "#4b5563",
    textAlign: "center",
    maxWidth: "80%",
  },
  tabContainer: {
    marginBottom: 24,
    alignItems: 'center',
  },
  tabBackground: {
    flexDirection: 'row',
    backgroundColor: '#e5e7eb',
    borderRadius: 12,
    width: width * 0.8,
    overflow: 'hidden',
  },
  tab: {
    flex: 1,
    height: 48,
    overflow: 'hidden',
  },
  tabContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    height: '100%',
    paddingVertical: 12,
  },
  tabGradient: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  tabIcon: {
    marginRight: 6,
  },
  activeTab: {
    // Styles for active tab
  },
  tabText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1f2937',
  },
  activeTabText: {
    color: '#ffffff',
    fontWeight: '600',
  },
  progressContainer: {
    marginBottom: 20,
  },
  progressBarBackground: {
    height: 6,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    borderRadius: 3,
    overflow: 'hidden',
    marginBottom: 6,
    position: 'relative',
  },
  progressBar: {
    height: '100%',
    borderRadius: 3,
  },
  shimmer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  shimmerGradient: {
    width: width * 0.3,
    height: '100%',
  },
  progressText: {
    fontSize: 12,
    color: '#4b5563',
    textAlign: 'right',
  },
  errorContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(239, 68, 68, 0.15)",
    borderRadius: 12,
    padding: 12,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: "rgba(239, 68, 68, 0.3)",
  },
  errorText: {
    color: THEME.error,
    fontSize: 14,
    marginLeft: 8,
    flex: 1,
  },
  formContainer: {
    width: "100%",
  },
  nameFieldsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  nameField: {
    width: '48%',
  },
  passwordHintContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: -8,
    marginBottom: 16,
    paddingHorizontal: 4,
  },
  hintIcon: {
    marginRight: 6,
  },
  passwordHint: {
    fontSize: 12,
    color: "#4b5563",
    flex: 1,
  },
  forgotPasswordContainer: {
    alignItems: "flex-end",
    marginBottom: 24,
  },
  forgotPasswordButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 6,
  },
  forgotPasswordIcon: {
    marginRight: 4,
  },
  forgotPasswordText: {
    fontSize: 14,
    color: THEME.accent,
    fontWeight: "500",
  },
  primaryButton: {
    marginBottom: 24,
  },
  dividerContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 24,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: "rgba(0, 0, 0, 0.15)",
  },
  dividerText: {
    fontSize: 14,
    color: "#4b5563",
    paddingHorizontal: 16,
  },
  socialButtonsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 32,
  },
  socialButton: {
    flex: 1,
    marginHorizontal: 6,
  },
  biometricContainer: {
    alignItems: 'center',
    marginTop: 8,
    marginBottom: 16,
  },
  biometricButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    overflow: 'hidden',
    marginBottom: 8,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.15)',
  },
  biometricGradient: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  biometricText: {
    fontSize: 14,
    color: '#4b5563',
  }
});