const request = require('supertest');
const express = require('express');

// Mock dependencies first
jest.mock('../../services/learningOptimizationService');
jest.mock('../../models/ApplicationResult');
jest.mock('../../middleware/auth', () => ({
  authenticateToken: (req, res, next) => {
    req.user = { id: 'user123' };
    next();
  }
}));
jest.mock('../../services/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn()
  }
}));

const learningOptimizationRoutes = require('../../routes/learningOptimization');
const LearningOptimizationService = require('../../services/learningOptimizationService');
const ApplicationResult = require('../../models/ApplicationResult');

describe('Learning Optimization Routes', () => {
  let app;
  let mockLearningService;

  beforeEach(() => {
    app = express();
    app.use(express.json());
    app.use('/api/learning-optimization', learningOptimizationRoutes);

    mockLearningService = {
      trackSuccessRates: jest.fn(),
      generateMLInsights: jest.fn(),
      recommendOptimizations: jest.fn(),
      analyzeMarketTrends: jest.fn(),
      analyzeSuccessPatterns: jest.fn(),
      clearCache: jest.fn(),
      getStartDate: jest.fn()
    };

    LearningOptimizationService.mockImplementation(() => mockLearningService);
    jest.clearAllMocks();
  });

  describe('GET /success-rates', () => {
    it('should return success rates for user', async () => {
      const mockSuccessRates = {
        userId: 'user123',
        timeframe: '30d',
        totalApplications: 10,
        successfulApplications: 3,
        successRate: 0.3,
        responseRate: 0.6,
        patterns: {
          propertyTypes: { apartment: { successRate: 0.4 } }
        },
        lastUpdated: new Date()
      };

      mockLearningService.trackSuccessRates.mockResolvedValue(mockSuccessRates);

      const response = await request(app)
        .get('/api/learning-optimization/success-rates')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toEqual(mockSuccessRates);
      expect(mockLearningService.trackSuccessRates).toHaveBeenCalledWith('user123', '30d');
    });

    it('should accept custom timeframe parameter', async () => {
      const mockSuccessRates = {
        userId: 'user123',
        timeframe: '7d',
        totalApplications: 5,
        successfulApplications: 2,
        successRate: 0.4,
        responseRate: 0.8
      };

      mockLearningService.trackSuccessRates.mockResolvedValue(mockSuccessRates);

      const response = await request(app)
        .get('/api/learning-optimization/success-rates?timeframe=7d')
        .expect(200);

      expect(mockLearningService.trackSuccessRates).toHaveBeenCalledWith('user123', '7d');
    });

    it('should handle service errors', async () => {
      mockLearningService.trackSuccessRates.mockRejectedValue(new Error('Service error'));

      const response = await request(app)
        .get('/api/learning-optimization/success-rates')
        .expect(500);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Failed to get success rates');
    });
  });

  describe('GET /ml-insights', () => {
    it('should return ML insights for user', async () => {
      const mockInsights = {
        insights: {
          optimalTiming: { optimalHours: [10, 11, 14] },
          contentOptimization: { recommendations: [] },
          marketTrends: { condition: 'favorable' },
          personalizedRecommendations: []
        },
        confidence: 0.8,
        lastUpdated: new Date()
      };

      mockLearningService.generateMLInsights.mockResolvedValue(mockInsights);

      const response = await request(app)
        .get('/api/learning-optimization/ml-insights')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toEqual(mockInsights);
      expect(mockLearningService.generateMLInsights).toHaveBeenCalledWith('user123');
    });

    it('should handle service errors', async () => {
      mockLearningService.generateMLInsights.mockRejectedValue(new Error('Insufficient data'));

      const response = await request(app)
        .get('/api/learning-optimization/ml-insights')
        .expect(500);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Failed to generate ML insights');
    });
  });

  describe('GET /recommendations', () => {
    it('should return optimization recommendations', async () => {
      const mockRecommendations = {
        recommendations: [
          {
            type: 'template_change',
            current: 'casual',
            recommended: 'professional',
            reason: 'Professional template shows 25% better success rate',
            confidence: 0.8
          }
        ],
        totalAnalyzed: 50,
        lastUpdated: new Date()
      };

      mockLearningService.recommendOptimizations.mockResolvedValue(mockRecommendations);

      const response = await request(app)
        .get('/api/learning-optimization/recommendations')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toEqual(mockRecommendations);
      expect(mockLearningService.recommendOptimizations).toHaveBeenCalledWith('user123');
    });

    it('should handle service errors', async () => {
      mockLearningService.recommendOptimizations.mockRejectedValue(new Error('User settings not found'));

      const response = await request(app)
        .get('/api/learning-optimization/recommendations')
        .expect(500);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Failed to get optimization recommendations');
    });
  });

  describe('GET /market-trends', () => {
    it('should return market trends analysis', async () => {
      const mockApplications = [
        {
          userId: 'user123',
          status: 'submitted',
          response: { success: true },
          createdAt: new Date(),
          listingId: { price: 1500, propertyType: 'apartment' }
        }
      ];

      const mockTrends = {
        trends: {
          competitionLevel: { current: 0.6, trend: 'improving' },
          priceMovement: { current: 1600, trend: 'increasing' },
          successRateChange: { current: 0.3, trend: 'stable' }
        },
        adaptiveStrategies: [
          {
            type: 'timing',
            recommendation: 'Increase application frequency during low competition periods',
            confidence: 0.7
          }
        ],
        marketCondition: 'favorable',
        lastUpdated: new Date()
      };

      ApplicationResult.find.mockReturnValue({
        populate: jest.fn().mockReturnValue({
          sort: jest.fn().mockReturnValue({
            limit: jest.fn().mockResolvedValue(mockApplications)
          })
        })
      });

      mockLearningService.analyzeMarketTrends.mockResolvedValue(mockTrends);

      const response = await request(app)
        .get('/api/learning-optimization/market-trends')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toEqual(mockTrends);
      expect(mockLearningService.analyzeMarketTrends).toHaveBeenCalledWith(mockApplications);
    });

    it('should handle service errors', async () => {
      ApplicationResult.find.mockImplementation(() => {
        throw new Error('Database error');
      });

      const response = await request(app)
        .get('/api/learning-optimization/market-trends')
        .expect(500);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Failed to analyze market trends');
    });
  });

  describe('POST /patterns', () => {
    it('should analyze patterns for specific applications', async () => {
      const applicationIds = ['app1', 'app2', 'app3'];
      const mockApplications = [
        {
          _id: 'app1',
          userId: 'user123',
          status: 'submitted',
          response: { success: true },
          listingId: { propertyType: 'apartment' }
        },
        {
          _id: 'app2',
          userId: 'user123',
          status: 'failed',
          listingId: { propertyType: 'house' }
        }
      ];

      const mockPatterns = {
        propertyTypes: {
          apartment: { successRate: 1, totalApplications: 1 },
          house: { successRate: 0, totalApplications: 1 }
        },
        priceRanges: {},
        locations: {},
        applicationTiming: {},
        templateTypes: {},
        propertyFeatures: {}
      };

      ApplicationResult.find.mockReturnValue({
        populate: jest.fn().mockResolvedValue(mockApplications)
      });

      mockLearningService.analyzeSuccessPatterns.mockResolvedValue(mockPatterns);

      const response = await request(app)
        .post('/api/learning-optimization/patterns')
        .send({ applicationIds })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.patterns).toEqual(mockPatterns);
      expect(response.body.data.totalAnalyzed).toBe(2);
      expect(ApplicationResult.find).toHaveBeenCalledWith({
        _id: { $in: applicationIds },
        userId: 'user123'
      });
    });

    it('should validate applicationIds parameter', async () => {
      const response = await request(app)
        .post('/api/learning-optimization/patterns')
        .send({ applicationIds: 'invalid' })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('applicationIds must be an array');
    });

    it('should handle empty application results', async () => {
      ApplicationResult.find.mockReturnValue({
        populate: jest.fn().mockResolvedValue([])
      });

      const response = await request(app)
        .post('/api/learning-optimization/patterns')
        .send({ applicationIds: ['app1', 'app2'] })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('No applications found for analysis');
    });

    it('should handle missing applicationIds', async () => {
      const response = await request(app)
        .post('/api/learning-optimization/patterns')
        .send({})
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('applicationIds must be an array');
    });
  });

  describe('DELETE /cache', () => {
    it('should clear learning optimization cache', async () => {
      const response = await request(app)
        .delete('/api/learning-optimization/cache')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Learning optimization cache cleared successfully');
      expect(mockLearningService.clearCache).toHaveBeenCalled();
    });

    it('should handle service errors', async () => {
      mockLearningService.clearCache.mockImplementation(() => {
        throw new Error('Cache error');
      });

      const response = await request(app)
        .delete('/api/learning-optimization/cache')
        .expect(500);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Failed to clear cache');
    });
  });

  describe('GET /performance-metrics', () => {
    it('should return detailed performance metrics', async () => {
      const mockSuccessRates = {
        userId: 'user123',
        totalApplications: 20,
        successfulApplications: 6,
        successRate: 0.3,
        responseRate: 0.5,
        patterns: {
          applicationTiming: {
            10: { successRate: 0.8, totalApplications: 5 },
            14: { successRate: 0.6, totalApplications: 3 },
            16: { successRate: 0.2, totalApplications: 2 }
          }
        }
      };

      const mockApplications = [
        {
          status: 'submitted',
          processingTime: 1500,
          responseTime: 3600,
          response: { message: 'Response' }
        },
        {
          status: 'failed',
          processingTime: 2000
        },
        {
          status: 'pending',
          processingTime: 1000
        }
      ];

      mockLearningService.trackSuccessRates.mockResolvedValue(mockSuccessRates);
      mockLearningService.getStartDate.mockReturnValue(new Date());

      ApplicationResult.find.mockReturnValue({
        populate: jest.fn().mockResolvedValue(mockApplications)
      });

      const response = await request(app)
        .get('/api/learning-optimization/performance-metrics?timeframe=7d&groupBy=day')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.metrics).toHaveProperty('avgProcessingTime');
      expect(response.body.data.metrics).toHaveProperty('avgResponseTime');
      expect(response.body.data.metrics).toHaveProperty('applicationsByStatus');
      expect(response.body.data.metrics).toHaveProperty('topPerformingHours');
      expect(response.body.data.timeframe).toBe('7d');
      expect(response.body.data.groupBy).toBe('day');
    });

    it('should use default parameters when not provided', async () => {
      const mockSuccessRates = {
        userId: 'user123',
        patterns: { applicationTiming: {} }
      };

      mockLearningService.trackSuccessRates.mockResolvedValue(mockSuccessRates);
      mockLearningService.getStartDate.mockReturnValue(new Date());

      ApplicationResult.find.mockReturnValue({
        populate: jest.fn().mockResolvedValue([])
      });

      const response = await request(app)
        .get('/api/learning-optimization/performance-metrics')
        .expect(200);

      expect(mockLearningService.trackSuccessRates).toHaveBeenCalledWith('user123', '30d');
      expect(response.body.data.timeframe).toBe('30d');
      expect(response.body.data.groupBy).toBe('day');
    });

    it('should handle applications without processing time', async () => {
      const mockSuccessRates = {
        userId: 'user123',
        patterns: { applicationTiming: {} }
      };

      const mockApplications = [
        { status: 'submitted' }, // No processingTime
        { status: 'failed', processingTime: null }
      ];

      mockLearningService.trackSuccessRates.mockResolvedValue(mockSuccessRates);
      mockLearningService.getStartDate.mockReturnValue(new Date());

      ApplicationResult.find.mockReturnValue({
        populate: jest.fn().mockResolvedValue(mockApplications)
      });

      const response = await request(app)
        .get('/api/learning-optimization/performance-metrics')
        .expect(200);

      expect(response.body.data.metrics.avgProcessingTime).toBe(0);
      expect(response.body.data.metrics.avgResponseTime).toBe(0);
    });

    it('should handle service errors', async () => {
      mockLearningService.trackSuccessRates.mockRejectedValue(new Error('Service error'));

      const response = await request(app)
        .get('/api/learning-optimization/performance-metrics')
        .expect(500);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Failed to get performance metrics');
    });
  });

  describe('error handling', () => {
    it('should handle unexpected errors gracefully', async () => {
      mockLearningService.trackSuccessRates.mockImplementation(() => {
        throw new Error('Unexpected error');
      });

      const response = await request(app)
        .get('/api/learning-optimization/success-rates')
        .expect(500);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Unexpected error');
    });
  });
});