import React from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
  Easing,
  FadeIn,
} from 'react-native-reanimated';
import { MarketInsights } from '../../store/aiStore';

// Define theme colors
const THEME = {
  primary: '#4361ee',
  secondary: '#7209b7',
  accent: '#f72585',
  dark: '#0a0a18',
  light: '#ffffff',
  gray: '#6b7280',
};

interface MarketInsightCardProps {
  insight: MarketInsights;
  onUpdatePreferences?: () => void;
}

export const MarketInsightCard: React.FC<MarketInsightCardProps> = ({
  insight,
  onUpdatePreferences,
}) => {
  // Get trend icon and color
  const getTrendIcon = () => {
    const trend = insight.analysis.marketTrend;
    
    if (trend === 'rising') {
      return {
        name: 'trending-up',
        color: '#ef4444', // Red for rising prices (negative for renters)
      };
    } else if (trend === 'declining') {
      return {
        name: 'trending-down',
        color: '#10b981', // Green for declining prices (positive for renters)
      };
    } else {
      return {
        name: 'remove',
        color: '#f59e0b', // Yellow for stable prices
      };
    }
  };
  
  // Format price range
  const formatPriceRange = (min: number, max: number) => {
    return `€${min.toLocaleString()} - €${max.toLocaleString()}`;
  };
  
  // Get demand level color
  const getDemandLevelColor = (level: string) => {
    if (level === 'high') return '#ef4444'; // Red for high demand (competitive)
    if (level === 'medium') return '#f59e0b'; // Yellow for medium demand
    return '#10b981'; // Green for low demand (less competitive)
  };
  
  const trendIcon = getTrendIcon();
  
  return (
    <Animated.View 
      style={styles.container}
      entering={FadeIn.duration(600).delay(400)}
    >
      <View style={styles.header}>
        <Text style={styles.title}>Market Insights</Text>
        <Text style={styles.location}>{insight.location}</Text>
      </View>
      
      <View style={styles.insightRow}>
        <View style={styles.insightItem}>
          <Text style={styles.insightLabel}>Average Price</Text>
          <Text style={styles.insightValue}>
            €{insight.analysis.averagePrice.toLocaleString()}
          </Text>
        </View>
        
        <View style={styles.insightItem}>
          <Text style={styles.insightLabel}>Price Range</Text>
          <Text style={styles.insightValue}>
            {formatPriceRange(
              insight.analysis.priceRange.min,
              insight.analysis.priceRange.max
            )}
          </Text>
        </View>
      </View>
      
      <View style={styles.insightRow}>
        <View style={styles.insightItem}>
          <Text style={styles.insightLabel}>Market Trend</Text>
          <View style={styles.trendContainer}>
            <Ionicons name={trendIcon.name as any} size={16} color={trendIcon.color} />
            <Text style={[styles.trendText, { color: trendIcon.color }]}>
              {insight.analysis.marketTrend.charAt(0).toUpperCase() + insight.analysis.marketTrend.slice(1)}
            </Text>
          </View>
        </View>
        
        <View style={styles.insightItem}>
          <Text style={styles.insightLabel}>Demand Level</Text>
          <View style={styles.demandContainer}>
            <View 
              style={[
                styles.demandIndicator, 
                { backgroundColor: getDemandLevelColor(insight.analysis.demandLevel) }
              ]} 
            />
            <Text style={styles.demandText}>
              {insight.analysis.demandLevel.charAt(0).toUpperCase() + insight.analysis.demandLevel.slice(1)}
            </Text>
          </View>
        </View>
      </View>
      
      {/* Key Insights */}
      <View style={styles.keyInsightsContainer}>
        {insight.analysis.keyInsights.slice(0, 2).map((keyInsight, index) => (
          <View key={`insight-${index}`} style={styles.keyInsightItem}>
            <Ionicons name="information-circle" size={16} color={THEME.primary} />
            <Text style={styles.keyInsightText}>{keyInsight}</Text>
          </View>
        ))}
      </View>
      
      {/* Prediction */}
      <View style={styles.predictionContainer}>
        <Text style={styles.predictionLabel}>Price Prediction</Text>
        <Text style={styles.predictionText}>{insight.analysis.pricePrediction}</Text>
      </View>
      
      {/* Update Preferences Button */}
      {onUpdatePreferences && (
        <TouchableOpacity 
          style={styles.updateButton}
          onPress={onUpdatePreferences}
        >
          <Text style={styles.updateButtonText}>Optimize Preferences</Text>
          <Ionicons name="options" size={16} color={THEME.light} />
        </TouchableOpacity>
      )}
      
      {/* Last Updated */}
      <Text style={styles.lastUpdated}>
        Last updated: {new Date(insight.lastUpdated).toLocaleDateString()}
      </Text>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 2,
  },
  header: {
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: THEME.dark,
    marginBottom: 4,
  },
  location: {
    fontSize: 14,
    color: THEME.gray,
  },
  insightRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  insightItem: {
    flex: 1,
  },
  insightLabel: {
    fontSize: 12,
    color: THEME.gray,
    marginBottom: 4,
  },
  insightValue: {
    fontSize: 16,
    fontWeight: '600',
    color: THEME.dark,
  },
  trendContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  trendText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 4,
  },
  demandContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  demandIndicator: {
    width: 10,
    height: 10,
    borderRadius: 5,
    marginRight: 6,
  },
  demandText: {
    fontSize: 16,
    fontWeight: '600',
    color: THEME.dark,
  },
  keyInsightsContainer: {
    marginBottom: 16,
  },
  keyInsightItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  keyInsightText: {
    fontSize: 14,
    color: THEME.dark,
    marginLeft: 8,
    flex: 1,
    lineHeight: 20,
  },
  predictionContainer: {
    backgroundColor: 'rgba(67, 97, 238, 0.1)',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  predictionLabel: {
    fontSize: 12,
    fontWeight: '500',
    color: THEME.primary,
    marginBottom: 4,
  },
  predictionText: {
    fontSize: 14,
    color: THEME.dark,
    lineHeight: 20,
  },
  updateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: THEME.secondary,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 12,
  },
  updateButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: THEME.light,
    marginRight: 8,
  },
  lastUpdated: {
    fontSize: 12,
    color: THEME.gray,
    textAlign: 'center',
  },
});