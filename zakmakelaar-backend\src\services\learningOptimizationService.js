const ApplicationResult = require('../models/ApplicationResult');
const AutoApplicationSettings = require('../models/AutoApplicationSettings');
const { logger } = require('./logger');

/**
 * Learning and Optimization Service
 * Implements machine learning insights for improving application success rates
 */
class LearningOptimizationService {
  constructor() {
    this.analysisCache = new Map();
    this.cacheExpiry = 24 * 60 * 60 * 1000; // 24 hours
  }

  /**
   * Track application success rates and patterns
   * Requirement 7.1: Track response rates and success patterns
   */
  async trackSuccessRates(userId, timeframe = '30d') {
    try {
      const startDate = this.getStartDate(timeframe);
      
      const applications = await ApplicationResult.find({
        userId,
        createdAt: { $gte: startDate }
      }).populate('listingId');

      const totalApplications = applications.length;
      const successfulApplications = applications.filter(app => 
        app.status === 'submitted' && app.response?.success
      ).length;
      
      const responseRate = applications.filter(app => 
        app.response && app.response.message
      ).length;

      const successRate = totalApplications > 0 ? successfulApplications / totalApplications : 0;
      const responseRatePercent = totalApplications > 0 ? responseRate / totalApplications : 0;

      // Analyze patterns by property type, location, price range
      const patterns = await this.analyzeSuccessPatterns(applications);

      const metrics = {
        userId,
        timeframe,
        totalApplications,
        successfulApplications,
        successRate,
        responseRate: responseRatePercent,
        patterns,
        lastUpdated: new Date()
      };

      // Cache the results
      this.analysisCache.set(`success_rates_${userId}_${timeframe}`, {
        data: metrics,
        timestamp: Date.now()
      });

      logger.info(`Success rate tracking completed for user ${userId}`, metrics);
      return metrics;
    } catch (error) {
      logger.error('Error tracking success rates:', error);
      throw error;
    }
  }

  /**
   * Analyze patterns in successful applications
   * Requirement 7.2: Identify common factors and adjust strategies
   */
  async analyzeSuccessPatterns(applications) {
    try {
      const successful = applications.filter(app => 
        app.status === 'submitted' && app.response?.success
      );
      
      const failed = applications.filter(app => 
        app.status === 'failed' || (app.status === 'submitted' && !app.response?.success)
      );

      const patterns = {
        propertyTypes: this.analyzeByCategory(successful, failed, 'propertyType'),
        priceRanges: this.analyzePriceRanges(successful, failed),
        locations: this.analyzeByCategory(successful, failed, 'location'),
        applicationTiming: this.analyzeApplicationTiming(successful, failed),
        templateTypes: this.analyzeTemplatePerformance(successful, failed),
        propertyFeatures: this.analyzePropertyFeatures(successful, failed)
      };

      return patterns;
    } catch (error) {
      logger.error('Error analyzing success patterns:', error);
      throw error;
    }
  }

  /**
   * Generate machine learning insights for application improvement
   * Requirement 7.3: Use ML insights to improve letter quality and timing
   */
  async generateMLInsights(userId) {
    try {
      const cacheKey = `ml_insights_${userId}`;
      const cached = this.analysisCache.get(cacheKey);
      
      if (cached && (Date.now() - cached.timestamp) < this.cacheExpiry) {
        return cached.data;
      }

      // Get user's application history
      const applications = await ApplicationResult.find({ userId })
        .populate('listingId')
        .sort({ createdAt: -1 })
        .limit(100);

      if (applications.length < 5) {
        return {
          insights: [],
          recommendations: ['Need more application data for meaningful insights'],
          confidence: 0
        };
      }

      const insights = {
        optimalTiming: await this.analyzeOptimalTiming(applications),
        contentOptimization: await this.analyzeContentPerformance(applications),
        marketTrends: await this.analyzeMarketTrends(applications),
        personalizedRecommendations: await this.generatePersonalizedRecommendations(userId, applications)
      };

      const result = {
        insights,
        confidence: this.calculateConfidenceScore(applications),
        lastUpdated: new Date()
      };

      // Cache the insights
      this.analysisCache.set(cacheKey, {
        data: result,
        timestamp: Date.now()
      });

      return result;
    } catch (error) {
      logger.error('Error generating ML insights:', error);
      throw error;
    }
  }

  /**
   * Recommend template and strategy optimizations
   * Requirement 7.4: Recommend template changes based on performance
   */
  async recommendOptimizations(userId) {
    try {
      const userSettings = await AutoApplicationSettings.findOne({ userId });
      if (!userSettings) {
        throw new Error('User settings not found');
      }

      const applications = await ApplicationResult.find({ userId })
        .populate('listingId')
        .sort({ createdAt: -1 })
        .limit(50);

      const recommendations = [];

      // Template performance analysis
      const templatePerformance = this.analyzeTemplatePerformance(
        applications.filter(app => app.status === 'submitted' && app.response?.success),
        applications.filter(app => app.status === 'failed' || !app.response?.success)
      );

      if (templatePerformance.bestPerforming && 
          templatePerformance.bestPerforming !== userSettings.settings.applicationTemplate) {
        recommendations.push({
          type: 'template_change',
          current: userSettings.settings.applicationTemplate,
          recommended: templatePerformance.bestPerforming,
          reason: `${templatePerformance.bestPerforming} template shows ${templatePerformance.improvement}% better success rate`,
          confidence: templatePerformance.confidence
        });
      }

      // Timing optimization
      const timingAnalysis = await this.analyzeOptimalTiming(applications);
      if (timingAnalysis.optimalHours.length > 0) {
        recommendations.push({
          type: 'timing_optimization',
          recommended: timingAnalysis.optimalHours,
          reason: `Applications submitted during ${timingAnalysis.optimalHours.join(', ')}:00 show higher success rates`,
          confidence: timingAnalysis.confidence
        });
      }

      // Content optimization
      const contentAnalysis = await this.analyzeContentPerformance(applications);
      if (contentAnalysis.recommendations.length > 0) {
        recommendations.push(...contentAnalysis.recommendations.map(rec => ({
          type: 'content_optimization',
          ...rec
        })));
      }

      return {
        recommendations,
        totalAnalyzed: applications.length,
        lastUpdated: new Date()
      };
    } catch (error) {
      logger.error('Error generating optimization recommendations:', error);
      throw error;
    }
  }

  /**
   * Integrate market trend analysis for adaptive strategies
   * Requirement 7.5: Adapt strategies based on market trends
   */
  async analyzeMarketTrends(applications) {
    try {
      const now = new Date();
      const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      const sixtyDaysAgo = new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000);

      const recentApplications = applications.filter(app => 
        app.createdAt >= thirtyDaysAgo
      );
      const olderApplications = applications.filter(app => 
        app.createdAt >= sixtyDaysAgo && app.createdAt < thirtyDaysAgo
      );

      const trends = {
        competitionLevel: this.analyzeCompetitionTrends(recentApplications, olderApplications),
        priceMovement: this.analyzePriceTrends(recentApplications, olderApplications),
        responseTime: this.analyzeResponseTimeTrends(recentApplications, olderApplications),
        successRateChange: this.analyzeSuccessRateChange(recentApplications, olderApplications),
        seasonalPatterns: this.analyzeSeasonalPatterns(applications)
      };

      const adaptiveStrategies = this.generateAdaptiveStrategies(trends);

      return {
        trends,
        adaptiveStrategies,
        marketCondition: this.assessMarketCondition(trends),
        lastUpdated: new Date()
      };
    } catch (error) {
      logger.error('Error analyzing market trends:', error);
      throw error;
    }
  }

  /**
   * Helper method to analyze success patterns by category
   */
  analyzeByCategory(successful, failed, category) {
    const successCounts = {};
    const failCounts = {};

    successful.forEach(app => {
      const value = this.extractCategoryValue(app, category);
      if (value) {
        successCounts[value] = (successCounts[value] || 0) + 1;
      }
    });

    failed.forEach(app => {
      const value = this.extractCategoryValue(app, category);
      if (value) {
        failCounts[value] = (failCounts[value] || 0) + 1;
      }
    });

    const analysis = {};
    const allCategories = new Set([...Object.keys(successCounts), ...Object.keys(failCounts)]);

    allCategories.forEach(cat => {
      const successCount = successCounts[cat] || 0;
      const failCount = failCounts[cat] || 0;
      const total = successCount + failCount;
      
      if (total > 0) {
        analysis[cat] = {
          successRate: successCount / total,
          totalApplications: total,
          successCount,
          failCount
        };
      }
    });

    return analysis;
  }

  /**
   * Extract category value from application
   */
  extractCategoryValue(application, category) {
    switch (category) {
      case 'propertyType':
        return application.listingId?.propertyType;
      case 'location':
        return application.listingId?.location?.city;
      default:
        return null;
    }
  }

  /**
   * Analyze price ranges for success patterns
   */
  analyzePriceRanges(successful, failed) {
    const priceRanges = [
      { min: 0, max: 1000, label: '€0-1000' },
      { min: 1000, max: 1500, label: '€1000-1500' },
      { min: 1500, max: 2000, label: '€1500-2000' },
      { min: 2000, max: 2500, label: '€2000-2500' },
      { min: 2500, max: Infinity, label: '€2500+' }
    ];

    const analysis = {};

    priceRanges.forEach(range => {
      const successInRange = successful.filter(app => {
        const price = app.listingId?.price || 0;
        return price >= range.min && price < range.max;
      }).length;

      const failInRange = failed.filter(app => {
        const price = app.listingId?.price || 0;
        return price >= range.min && price < range.max;
      }).length;

      const total = successInRange + failInRange;
      if (total > 0) {
        analysis[range.label] = {
          successRate: successInRange / total,
          totalApplications: total,
          successCount: successInRange,
          failCount: failInRange
        };
      }
    });

    return analysis;
  }

  /**
   * Analyze application timing patterns
   */
  analyzeApplicationTiming(successful, failed) {
    const hourlySuccess = {};
    const hourlyFail = {};

    successful.forEach(app => {
      const hour = new Date(app.createdAt).getHours();
      hourlySuccess[hour] = (hourlySuccess[hour] || 0) + 1;
    });

    failed.forEach(app => {
      const hour = new Date(app.createdAt).getHours();
      hourlyFail[hour] = (hourlyFail[hour] || 0) + 1;
    });

    const analysis = {};
    for (let hour = 0; hour < 24; hour++) {
      const successCount = hourlySuccess[hour] || 0;
      const failCount = hourlyFail[hour] || 0;
      const total = successCount + failCount;

      if (total > 0) {
        analysis[hour] = {
          successRate: successCount / total,
          totalApplications: total,
          successCount,
          failCount
        };
      }
    }

    return analysis;
  }

  /**
   * Analyze template performance
   */
  analyzeTemplatePerformance(successful, failed) {
    const templates = ['professional', 'casual', 'student', 'expat'];
    const analysis = {};
    let bestTemplate = null;
    let bestRate = 0;

    templates.forEach(template => {
      const successCount = successful.filter(app => 
        app.formData?.template === template
      ).length;
      
      const failCount = failed.filter(app => 
        app.formData?.template === template
      ).length;

      const total = successCount + failCount;
      if (total > 0) {
        const successRate = successCount / total;
        analysis[template] = {
          successRate,
          totalApplications: total,
          successCount,
          failCount
        };

        if (successRate > bestRate && total >= 3) {
          bestRate = successRate;
          bestTemplate = template;
        }
      }
    });

    return {
      ...analysis,
      bestPerforming: bestTemplate,
      improvement: bestRate > 0 ? Math.round((bestRate - 0.5) * 100) : 0,
      confidence: this.calculateTemplateConfidence(analysis)
    };
  }

  /**
   * Analyze property features that correlate with success
   */
  analyzePropertyFeatures(successful, failed) {
    const features = ['balcony', 'garden', 'parking', 'furnished', 'pets_allowed'];
    const analysis = {};

    features.forEach(feature => {
      const successWithFeature = successful.filter(app => 
        app.listingId?.features?.includes(feature)
      ).length;
      
      const failWithFeature = failed.filter(app => 
        app.listingId?.features?.includes(feature)
      ).length;

      const successWithoutFeature = successful.length - successWithFeature;
      const failWithoutFeature = failed.length - failWithFeature;

      const withFeatureTotal = successWithFeature + failWithFeature;
      const withoutFeatureTotal = successWithoutFeature + failWithoutFeature;

      if (withFeatureTotal > 0 && withoutFeatureTotal > 0) {
        const withFeatureRate = successWithFeature / withFeatureTotal;
        const withoutFeatureRate = successWithoutFeature / withoutFeatureTotal;

        analysis[feature] = {
          withFeature: {
            successRate: withFeatureRate,
            totalApplications: withFeatureTotal
          },
          withoutFeature: {
            successRate: withoutFeatureRate,
            totalApplications: withoutFeatureTotal
          },
          impact: withFeatureRate - withoutFeatureRate
        };
      }
    });

    return analysis;
  }

  /**
   * Analyze optimal timing for applications
   */
  async analyzeOptimalTiming(applications) {
    const successful = applications.filter(app => 
      app.status === 'submitted' && app.response?.success
    );

    if (successful.length < 5) {
      return { optimalHours: [], confidence: 0 };
    }

    const hourlyAnalysis = this.analyzeApplicationTiming(successful, 
      applications.filter(app => app.status === 'failed' || !app.response?.success)
    );

    const optimalHours = Object.entries(hourlyAnalysis)
      .filter(([hour, data]) => data.successRate > 0.6 && data.totalApplications >= 3)
      .map(([hour]) => parseInt(hour))
      .sort((a, b) => hourlyAnalysis[b].successRate - hourlyAnalysis[a].successRate);

    return {
      optimalHours: optimalHours.slice(0, 3),
      hourlyAnalysis,
      confidence: this.calculateTimingConfidence(hourlyAnalysis)
    };
  }

  /**
   * Analyze content performance patterns
   */
  async analyzeContentPerformance(applications) {
    const successful = applications.filter(app => 
      app.status === 'submitted' && app.response?.success
    );

    const recommendations = [];

    // Analyze message length
    const lengthAnalysis = this.analyzeMessageLength(successful, applications);
    if (lengthAnalysis.optimalRange) {
      recommendations.push({
        type: 'message_length',
        recommended: lengthAnalysis.optimalRange,
        reason: `Messages with ${lengthAnalysis.optimalRange} characters show higher success rates`,
        confidence: lengthAnalysis.confidence
      });
    }

    // Analyze personalization elements
    const personalizationAnalysis = this.analyzePersonalizationElements(successful, applications);
    if (personalizationAnalysis.recommendations.length > 0) {
      recommendations.push(...personalizationAnalysis.recommendations);
    }

    return {
      recommendations,
      lengthAnalysis,
      personalizationAnalysis
    };
  }

  /**
   * Generate personalized recommendations for user
   */
  async generatePersonalizedRecommendations(userId, applications) {
    const userSettings = await AutoApplicationSettings.findOne({ userId });
    const recommendations = [];

    // Analyze user's current performance
    const successRate = applications.filter(app => 
      app.status === 'submitted' && app.response?.success
    ).length / applications.length;

    if (successRate < 0.3) {
      recommendations.push({
        priority: 'high',
        type: 'strategy_change',
        message: 'Consider adjusting your application strategy - current success rate is below average',
        actions: ['Review template choice', 'Adjust timing', 'Refine property criteria']
      });
    }

    // Check application frequency
    const recentApps = applications.filter(app => 
      app.createdAt >= new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
    );

    if (recentApps.length > userSettings?.settings?.maxApplicationsPerDay * 7) {
      recommendations.push({
        priority: 'medium',
        type: 'frequency_adjustment',
        message: 'Consider reducing application frequency to improve quality over quantity',
        actions: ['Lower daily application limit', 'Be more selective with criteria']
      });
    }

    return recommendations;
  }

  /**
   * Helper methods for trend analysis
   */
  analyzeCompetitionTrends(recent, older) {
    const recentResponseRate = recent.filter(app => app.response?.message).length / recent.length;
    const olderResponseRate = older.filter(app => app.response?.message).length / older.length;
    
    return {
      current: recentResponseRate,
      previous: olderResponseRate,
      trend: recentResponseRate > olderResponseRate ? 'improving' : 'declining',
      change: recentResponseRate - olderResponseRate
    };
  }

  analyzePriceTrends(recent, older) {
    const recentAvgPrice = recent.reduce((sum, app) => 
      sum + (app.listingId?.price || 0), 0) / recent.length;
    const olderAvgPrice = older.reduce((sum, app) => 
      sum + (app.listingId?.price || 0), 0) / older.length;

    return {
      current: recentAvgPrice,
      previous: olderAvgPrice,
      trend: recentAvgPrice > olderAvgPrice ? 'increasing' : 'decreasing',
      change: ((recentAvgPrice - olderAvgPrice) / olderAvgPrice) * 100
    };
  }

  analyzeResponseTimeTrends(recent, older) {
    // This would analyze how quickly landlords respond to applications
    // For now, return a placeholder structure
    return {
      current: 0,
      previous: 0,
      trend: 'stable',
      change: 0
    };
  }

  analyzeSuccessRateChange(recent, older) {
    const recentSuccess = recent.filter(app => 
      app.status === 'submitted' && app.response?.success
    ).length / recent.length;
    
    const olderSuccess = older.filter(app => 
      app.status === 'submitted' && app.response?.success
    ).length / older.length;

    return {
      current: recentSuccess,
      previous: olderSuccess,
      trend: recentSuccess > olderSuccess ? 'improving' : 'declining',
      change: recentSuccess - olderSuccess
    };
  }

  analyzeSeasonalPatterns(applications) {
    const monthlyData = {};
    
    applications.forEach(app => {
      const month = new Date(app.createdAt).getMonth();
      if (!monthlyData[month]) {
        monthlyData[month] = { total: 0, successful: 0 };
      }
      monthlyData[month].total++;
      if (app.status === 'submitted' && app.response?.success) {
        monthlyData[month].successful++;
      }
    });

    const patterns = {};
    Object.entries(monthlyData).forEach(([month, data]) => {
      patterns[month] = {
        successRate: data.successful / data.total,
        totalApplications: data.total
      };
    });

    return patterns;
  }

  generateAdaptiveStrategies(trends) {
    const strategies = [];

    if (trends.competitionLevel.trend === 'declining') {
      strategies.push({
        type: 'timing',
        recommendation: 'Increase application frequency during low competition periods',
        confidence: 0.7
      });
    }

    if (trends.successRateChange.trend === 'declining') {
      strategies.push({
        type: 'content',
        recommendation: 'Enhance application personalization and quality',
        confidence: 0.8
      });
    }

    return strategies;
  }

  assessMarketCondition(trends) {
    let score = 0;
    
    if (trends.competitionLevel.trend === 'improving') score += 1;
    if (trends.successRateChange.trend === 'improving') score += 1;
    if (trends.priceMovement.trend === 'decreasing') score += 1;

    if (score >= 2) return 'favorable';
    if (score === 1) return 'neutral';
    return 'challenging';
  }

  /**
   * Helper methods for confidence calculation
   */
  calculateConfidenceScore(applications) {
    const sampleSize = applications.length;
    if (sampleSize < 10) return 0.3;
    if (sampleSize < 30) return 0.6;
    if (sampleSize < 50) return 0.8;
    return 0.9;
  }

  calculateTemplateConfidence(analysis) {
    const totalSamples = Object.values(analysis).reduce((sum, data) => 
      sum + (data.totalApplications || 0), 0);
    
    if (totalSamples < 10) return 0.3;
    if (totalSamples < 30) return 0.6;
    return 0.8;
  }

  calculateTimingConfidence(hourlyAnalysis) {
    const totalSamples = Object.values(hourlyAnalysis).reduce((sum, data) => 
      sum + data.totalApplications, 0);
    
    if (totalSamples < 20) return 0.3;
    if (totalSamples < 50) return 0.6;
    return 0.8;
  }

  /**
   * Helper methods for content analysis
   */
  analyzeMessageLength(successful, allApplications) {
    const lengths = successful.map(app => 
      app.generatedContent?.message?.length || 0
    ).filter(length => length > 0);

    if (lengths.length < 5) {
      return { optimalRange: null, confidence: 0 };
    }

    const avgLength = lengths.reduce((sum, len) => sum + len, 0) / lengths.length;
    const optimalRange = `${Math.round(avgLength * 0.8)}-${Math.round(avgLength * 1.2)}`;

    return {
      optimalRange,
      averageLength: avgLength,
      confidence: lengths.length >= 10 ? 0.7 : 0.4
    };
  }

  analyzePersonalizationElements(successful, allApplications) {
    const recommendations = [];
    
    // Analyze property-specific mentions
    const withPropertyMentions = successful.filter(app => 
      app.generatedContent?.personalizedElements?.includes('property_features')
    ).length;
    
    if (withPropertyMentions / successful.length > 0.7) {
      recommendations.push({
        type: 'personalization',
        element: 'property_features',
        recommended: true,
        reason: 'Applications mentioning specific property features show higher success rates',
        confidence: 0.8
      });
    }

    return { recommendations };
  }

  /**
   * Get start date for timeframe analysis
   */
  getStartDate(timeframe) {
    const now = new Date();
    switch (timeframe) {
      case '7d':
        return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      case '30d':
        return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      case '90d':
        return new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
      default:
        return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    }
  }

  /**
   * Clear analysis cache
   */
  clearCache() {
    this.analysisCache.clear();
  }

  /**
   * Get cached analysis if available
   */
  getCachedAnalysis(key) {
    const cached = this.analysisCache.get(key);
    if (cached && (Date.now() - cached.timestamp) < this.cacheExpiry) {
      return cached.data;
    }
    return null;
  }
}

module.exports = LearningOptimizationService;