const mongoose = require('mongoose');
const scraperAutoApplicationIntegration = require('../../services/scraperAutoApplicationIntegration');
const AutoApplicationSettings = require('../../models/AutoApplicationSettings');
const ApplicationQueue = require('../../models/ApplicationQueue');
const Listing = require('../../models/Listing');
const User = require('../../models/User');

describe('Scraper Auto-Application Integration Tests', () => {
  let testUser;
  let testUserSettings;

  beforeAll(async () => {
    // Connect to test database
    if (mongoose.connection.readyState === 0) {
      await mongoose.connect(process.env.MONGODB_TEST_URI || 'mongodb://localhost:27017/zakmakelaar_test');
    }
  });

  beforeEach(async () => {
    // Clean up test data
    await User.deleteMany({});
    await AutoApplicationSettings.deleteMany({});
    await ApplicationQueue.deleteMany({});
    await Listing.deleteMany({});

    // Create test user
    testUser = new User({
      email: '<EMAIL>',
      password: 'hashedpassword',
      firstName: 'Test',
      lastName: 'User',
      role: 'user'
    });
    await testUser.save();

    // Create auto-application settings
    testUserSettings = new AutoApplicationSettings({
      userId: testUser._id,
      enabled: true,
      settings: {
        maxApplicationsPerDay: 5,
        applicationTemplate: 'professional',
        autoSubmit: true,
        requireManualReview: false,
        notificationPreferences: {
          immediate: true,
          daily: true,
          weekly: false
        }
      },
      criteria: {
        maxPrice: 2000,
        minRooms: 2,
        maxRooms: 4,
        propertyTypes: ['appartement', 'huis'],
        locations: ['utrecht', 'amsterdam'],
        excludeKeywords: ['student'],
        includeKeywords: [],
        minSize: 50,
        maxSize: 150
      },
      personalInfo: {
        fullName: 'Test User',
        email: '<EMAIL>',
        phone: '+31612345678'
      },
      documents: []
    });
    await testUserSettings.save();

    // Clear integration caches
    scraperAutoApplicationIntegration.clearCaches();
  });

  afterEach(async () => {
    // Clean up test data
    await User.deleteMany({});
    await AutoApplicationSettings.deleteMany({});
    await ApplicationQueue.deleteMany({});
    await Listing.deleteMany({});
  });

  afterAll(async () => {
    await mongoose.connection.close();
  });

  describe('processNewListings', () => {
    test('should process new listings and trigger auto-applications for matching criteria', async () => {
      const mockListings = [
        {
          title: 'Beautiful Apartment in Utrecht',
          price: '€ 1.800 per maand',
          location: 'Utrecht',
          url: 'https://www.funda.nl/huur/utrecht/appartement-test-1/',
          size: '80',
          bedrooms: '2',
          rooms: '3',
          propertyType: 'appartement',
          description: 'A lovely apartment in the city center',
          source: 'funda.nl',
          dateAdded: new Date(),
          images: ['https://example.com/image1.jpg']
        },
        {
          title: 'Student Room in Amsterdam',
          price: '€ 900 per maand',
          location: 'Amsterdam',
          url: 'https://www.funda.nl/huur/amsterdam/kamer-test-2/',
          size: '25',
          bedrooms: '1',
          rooms: '1',
          propertyType: 'kamer',
          description: 'Perfect for students',
          source: 'funda.nl',
          dateAdded: new Date(),
          images: []
        },
        {
          title: 'Expensive House in Utrecht',
          price: '€ 3.500 per maand',
          location: 'Utrecht',
          url: 'https://www.funda.nl/huur/utrecht/huis-test-3/',
          size: '200',
          bedrooms: '4',
          rooms: '6',
          propertyType: 'huis',
          description: 'Large family house',
          source: 'funda.nl',
          dateAdded: new Date(),
          images: ['https://example.com/image2.jpg']
        }
      ];

      const result = await scraperAutoApplicationIntegration.processNewListings(mockListings, 'funda.nl');

      expect(result.processed).toBe(3);
      expect(result.duplicates).toBe(0);
      expect(result.autoApplicationTriggered).toBe(1); // Only first listing should match criteria
      expect(result.errors).toBe(0);
      expect(result.qualityScores).toHaveLength(3);

      // Check that listings were saved to database
      const savedListings = await Listing.find({});
      expect(savedListings).toHaveLength(3);

      // Check that application was queued for matching listing
      const queuedApplications = await ApplicationQueue.find({});
      expect(queuedApplications).toHaveLength(1);
      expect(queuedApplications[0].listingUrl).toBe('https://www.funda.nl/huur/utrecht/appartement-test-1/');
    });

    test('should skip duplicate listings', async () => {
      // Create existing listing
      const existingListing = new Listing({
        title: 'Existing Apartment',
        price: '€ 1.500 per maand',
        location: 'Utrecht',
        url: 'https://www.funda.nl/huur/utrecht/appartement-existing/',
        source: 'funda.nl'
      });
      await existingListing.save();

      const mockListings = [
        {
          title: 'Existing Apartment',
          price: '€ 1.500 per maand',
          location: 'Utrecht',
          url: 'https://www.funda.nl/huur/utrecht/appartement-existing/',
          source: 'funda.nl',
          dateAdded: new Date()
        }
      ];

      const result = await scraperAutoApplicationIntegration.processNewListings(mockListings, 'funda.nl');

      expect(result.processed).toBe(1);
      expect(result.duplicates).toBe(1);
      expect(result.autoApplicationTriggered).toBe(0);

      // Should not create duplicate listing
      const allListings = await Listing.find({});
      expect(allListings).toHaveLength(1);
    });

    test('should handle listings with no matching users', async () => {
      // Disable auto-application for test user
      testUserSettings.enabled = false;
      await testUserSettings.save();

      const mockListings = [
        {
          title: 'Great Apartment',
          price: '€ 1.500 per maand',
          location: 'Utrecht',
          url: 'https://www.funda.nl/huur/utrecht/appartement-test/',
          source: 'funda.nl',
          dateAdded: new Date()
        }
      ];

      const result = await scraperAutoApplicationIntegration.processNewListings(mockListings, 'funda.nl');

      expect(result.processed).toBe(1);
      expect(result.autoApplicationTriggered).toBe(0);

      // Should still save listing to database
      const savedListings = await Listing.find({});
      expect(savedListings).toHaveLength(1);
    });

    test('should respect daily application limits', async () => {
      // Create existing applications for today
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      for (let i = 0; i < 5; i++) {
        const application = new ApplicationQueue({
          userId: testUser._id,
          listingUrl: `https://example.com/listing-${i}/`,
          status: 'completed',
          createdAt: new Date(today.getTime() + i * 1000)
        });
        await application.save();
      }

      const mockListings = [
        {
          title: 'New Apartment',
          price: '€ 1.500 per maand',
          location: 'Utrecht',
          url: 'https://www.funda.nl/huur/utrecht/appartement-new/',
          size: '75',
          rooms: '3',
          propertyType: 'appartement',
          source: 'funda.nl',
          dateAdded: new Date()
        }
      ];

      const result = await scraperAutoApplicationIntegration.processNewListings(mockListings, 'funda.nl');

      expect(result.processed).toBe(1);
      expect(result.autoApplicationTriggered).toBe(0); // Should not trigger due to daily limit

      // Should still save listing
      const savedListings = await Listing.find({});
      expect(savedListings).toHaveLength(1);
    });
  });

  describe('quality scoring', () => {
    test('should calculate quality scores correctly', async () => {
      const highQualityListing = {
        title: 'Premium Apartment with Full Details',
        price: '€ 1.800 per maand',
        location: 'Utrecht Center',
        url: 'https://www.funda.nl/huur/utrecht/appartement-premium/',
        size: '85',
        bedrooms: '2',
        rooms: '3',
        year: '2020',
        propertyType: 'appartement',
        description: 'This is a very detailed description of a beautiful apartment with all amenities included. The apartment features modern finishes and is located in the heart of the city.',
        source: 'funda.nl',
        images: ['https://example.com/image1.jpg', 'https://example.com/image2.jpg'],
        dateAdded: new Date()
      };

      const lowQualityListing = {
        title: 'Basic Room',
        price: 'Prijs op aanvraag',
        location: 'Unknown',
        url: 'https://www.funda.nl/huur/unknown/kamer-basic/',
        source: 'funda.nl',
        dateAdded: new Date()
      };

      const result = await scraperAutoApplicationIntegration.processNewListings(
        [highQualityListing, lowQualityListing], 
        'funda.nl'
      );

      expect(result.qualityScores).toHaveLength(2);
      expect(result.qualityScores[0]).toBeGreaterThan(result.qualityScores[1]);
      expect(result.qualityScores[0]).toBeGreaterThan(0.8); // High quality
      expect(result.qualityScores[1]).toBeLessThan(0.6); // Low quality
    });
  });

  describe('criteria matching', () => {
    test('should match listings based on price criteria', async () => {
      const expensiveListing = {
        title: 'Expensive Apartment',
        price: '€ 2.500 per maand',
        location: 'Utrecht',
        url: 'https://www.funda.nl/huur/utrecht/appartement-expensive/',
        propertyType: 'appartement',
        rooms: '3',
        source: 'funda.nl',
        dateAdded: new Date()
      };

      const affordableListing = {
        title: 'Affordable Apartment',
        price: '€ 1.500 per maand',
        location: 'Utrecht',
        url: 'https://www.funda.nl/huur/utrecht/appartement-affordable/',
        propertyType: 'appartement',
        rooms: '3',
        source: 'funda.nl',
        dateAdded: new Date()
      };

      const result = await scraperAutoApplicationIntegration.processNewListings(
        [expensiveListing, affordableListing], 
        'funda.nl'
      );

      expect(result.autoApplicationTriggered).toBe(1); // Only affordable listing should match

      const queuedApplications = await ApplicationQueue.find({});
      expect(queuedApplications).toHaveLength(1);
      expect(queuedApplications[0].listingUrl).toBe('https://www.funda.nl/huur/utrecht/appartement-affordable/');
    });

    test('should match listings based on location criteria', async () => {
      const utrechtListing = {
        title: 'Utrecht Apartment',
        price: '€ 1.500 per maand',
        location: 'Utrecht',
        url: 'https://www.funda.nl/huur/utrecht/appartement-utrecht/',
        propertyType: 'appartement',
        rooms: '3',
        source: 'funda.nl',
        dateAdded: new Date()
      };

      const rotterdamListing = {
        title: 'Rotterdam Apartment',
        price: '€ 1.500 per maand',
        location: 'Rotterdam',
        url: 'https://www.funda.nl/huur/rotterdam/appartement-rotterdam/',
        propertyType: 'appartement',
        rooms: '3',
        source: 'funda.nl',
        dateAdded: new Date()
      };

      const result = await scraperAutoApplicationIntegration.processNewListings(
        [utrechtListing, rotterdamListing], 
        'funda.nl'
      );

      expect(result.autoApplicationTriggered).toBe(1); // Only Utrecht listing should match

      const queuedApplications = await ApplicationQueue.find({});
      expect(queuedApplications).toHaveLength(1);
      expect(queuedApplications[0].listingUrl).toBe('https://www.funda.nl/huur/utrecht/appartement-utrecht/');
    });

    test('should exclude listings with excluded keywords', async () => {
      const studentListing = {
        title: 'Student Apartment',
        price: '€ 1.500 per maand',
        location: 'Utrecht',
        url: 'https://www.funda.nl/huur/utrecht/appartement-student/',
        propertyType: 'appartement',
        rooms: '3',
        description: 'Perfect for students',
        source: 'funda.nl',
        dateAdded: new Date()
      };

      const regularListing = {
        title: 'Regular Apartment',
        price: '€ 1.500 per maand',
        location: 'Utrecht',
        url: 'https://www.funda.nl/huur/utrecht/appartement-regular/',
        propertyType: 'appartement',
        rooms: '3',
        description: 'Great apartment for professionals',
        source: 'funda.nl',
        dateAdded: new Date()
      };

      const result = await scraperAutoApplicationIntegration.processNewListings(
        [studentListing, regularListing], 
        'funda.nl'
      );

      expect(result.autoApplicationTriggered).toBe(1); // Only regular listing should match

      const queuedApplications = await ApplicationQueue.find({});
      expect(queuedApplications).toHaveLength(1);
      expect(queuedApplications[0].listingUrl).toBe('https://www.funda.nl/huur/utrecht/appartement-regular/');
    });
  });

  describe('error handling', () => {
    test('should handle invalid listing data gracefully', async () => {
      const invalidListings = [
        null,
        undefined,
        {},
        { title: 'No URL' },
        { url: 'https://example.com', title: null }
      ];

      const result = await scraperAutoApplicationIntegration.processNewListings(invalidListings, 'funda.nl');

      expect(result.processed).toBeGreaterThanOrEqual(0);
      expect(result.errors).toBeGreaterThan(0);
    });

    test('should continue processing other listings if one fails', async () => {
      const mixedListings = [
        {
          title: 'Valid Apartment',
          price: '€ 1.500 per maand',
          location: 'Utrecht',
          url: 'https://www.funda.nl/huur/utrecht/appartement-valid/',
          propertyType: 'appartement',
          rooms: '3',
          source: 'funda.nl',
          dateAdded: new Date()
        },
        null, // Invalid listing
        {
          title: 'Another Valid Apartment',
          price: '€ 1.600 per maand',
          location: 'Utrecht',
          url: 'https://www.funda.nl/huur/utrecht/appartement-valid-2/',
          propertyType: 'appartement',
          rooms: '3',
          source: 'funda.nl',
          dateAdded: new Date()
        }
      ];

      const result = await scraperAutoApplicationIntegration.processNewListings(mixedListings, 'funda.nl');

      expect(result.processed).toBe(2); // Should process valid listings
      expect(result.errors).toBe(1); // Should record error for invalid listing
    });
  });

  describe('caching and performance', () => {
    test('should cache quality scores', async () => {
      const listing = {
        title: 'Test Apartment',
        price: '€ 1.500 per maand',
        location: 'Utrecht',
        url: 'https://www.funda.nl/huur/utrecht/appartement-test/',
        propertyType: 'appartement',
        rooms: '3',
        source: 'funda.nl',
        dateAdded: new Date()
      };

      // Process same listing twice
      await scraperAutoApplicationIntegration.processNewListings([listing], 'funda.nl');
      await scraperAutoApplicationIntegration.processNewListings([listing], 'funda.nl');

      const stats = scraperAutoApplicationIntegration.getStatistics();
      expect(stats.qualityScoreCacheSize).toBeGreaterThan(0);
    });

    test('should provide integration statistics', async () => {
      const stats = scraperAutoApplicationIntegration.getStatistics();

      expect(stats).toHaveProperty('processedListingsCount');
      expect(stats).toHaveProperty('qualityScoreCacheSize');
      expect(stats).toHaveProperty('currentlyProcessingCount');
      expect(stats).toHaveProperty('cacheExpiryMs');
      expect(stats).toHaveProperty('minQualityScore');
    });

    test('should clear caches when requested', async () => {
      const listing = {
        title: 'Test Apartment',
        price: '€ 1.500 per maand',
        location: 'Utrecht',
        url: 'https://www.funda.nl/huur/utrecht/appartement-test/',
        source: 'funda.nl',
        dateAdded: new Date()
      };

      await scraperAutoApplicationIntegration.processNewListings([listing], 'funda.nl');
      
      let stats = scraperAutoApplicationIntegration.getStatistics();
      expect(stats.processedListingsCount).toBeGreaterThan(0);

      scraperAutoApplicationIntegration.clearCaches();
      
      stats = scraperAutoApplicationIntegration.getStatistics();
      expect(stats.processedListingsCount).toBe(0);
      expect(stats.qualityScoreCacheSize).toBe(0);
    });
  });
});