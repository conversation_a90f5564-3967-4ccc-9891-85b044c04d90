# ZakMakelaar Frontend-Backend Connection Guide

## Overview

The frontend has been successfully connected to the backend with the following features:

### ✅ Implemented Features

1. **API Service Layer** (`services/api.ts`)
   - HTTP client with Axios
   - Automatic token management
   - Request/response interceptors
   - Error handling and retry logic
   - Secure token storage

2. **Authentication Service** (`services/authService.ts`)
   - Login/Register functionality
   - Token management
   - User profile management
   - Password validation
   - Preferences handling

3. **Listings Service** (`services/listingsService.ts`)
   - Fetch listings with filters
   - Search functionality
   - Save/unsave listings
   - Property details
   - Statistics and metadata

4. **AI Services** (`services/aiService.ts`)
   - Property matching
   - Contract analysis
   - Application generation
   - Market analysis
   - Text summarization and translation

5. **State Management** (Zustand stores)
   - Authentication state (`store/authStore.ts`)
   - Listings state (`store/listingsStore.ts`)
   - Persistent storage with AsyncStorage

6. **UI Components**
   - Updated login screen with real API integration
   - Dashboard with real data from backend
   - Error handling and loading states
   - App initialization and error boundaries

## Configuration

### API Configuration (`config/api.ts`)

Update the API base URL in `config/api.ts`:

```typescript
export const API_CONFIG = {
  // Development API URL - change this to your backend URL
  DEV_BASE_URL: 'http://localhost:3000/api',
  
  // Production API URL - update this when you deploy
  PROD_BASE_URL: 'https://your-production-api.com/api',
};
```

### Environment Setup

1. **Backend Requirements:**
   - Backend running on `http://localhost:3000`
   - MongoDB connected
   - All API endpoints functional

2. **Frontend Requirements:**
   - Expo CLI installed
   - Dependencies installed (`npm install`)
   - Metro bundler running

## Testing the Connection

### 1. Start the Backend

```bash
cd zakmakelaar-backend
npm start
```

Verify the backend is running by visiting:
- Health check: `http://localhost:3000/health`
- API docs: `http://localhost:3000/api-docs`

### 2. Start the Frontend

```bash
cd zakmakelaar-frontend
npm start
```

### 3. Test Authentication Flow

1. **Open the app** - Should show welcome screen
2. **Tap "Get Started"** - Navigate to login screen
3. **Try registration:**
   - Switch to "Sign Up" mode
   - Enter email, password, and optional name
   - Should create account and navigate to preferences
4. **Try login:**
   - Use existing credentials
   - Should authenticate and navigate to dashboard

### 4. Test Dashboard Features

1. **View listings** - Should load real data from backend
2. **Pull to refresh** - Should reload listings
3. **Error handling** - Disconnect internet to test error states
4. **Logout** - Should clear tokens and return to welcome

## API Endpoints Used

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `GET /api/auth/me` - Get current user
- `PUT /api/auth/preferences` - Update preferences
- `POST /api/auth/logout` - Logout

### Listings
- `GET /api/listings` - Get listings with filters
- `GET /api/listings/:id` - Get single listing
- `POST /api/listings/:id/save` - Save listing
- `DELETE /api/listings/:id/save` - Unsave listing

### AI Services
- `POST /api/ai/match` - Property matching
- `POST /api/ai/contract-analysis` - Contract analysis
- `POST /api/ai/application-gen` - Application generation

## Troubleshooting

### Common Issues

1. **Network Error**
   - Check backend is running on correct port
   - Verify API base URL in config
   - Check firewall/network settings

2. **Authentication Issues**
   - Clear app data/cache
   - Check JWT secret configuration
   - Verify token storage permissions

3. **CORS Issues**
   - Update CORS settings in backend
   - Add frontend URL to allowed origins

4. **Data Loading Issues**
   - Check MongoDB connection
   - Verify API endpoints are working
   - Check console for error messages

### Debug Mode

Enable debug logging by checking `__DEV__` flag:
- API requests/responses logged to console
- Error details shown in development
- Network activity visible in React Native debugger

## Next Steps

1. **Test all screens** - Verify each screen works with real data
2. **Add more features** - Implement remaining AI services
3. **Error handling** - Add more specific error messages
4. **Performance** - Add caching and optimization
5. **Testing** - Write unit and integration tests

## File Structure

```
zakmakelaar-frontend/
├── services/
│   ├── api.ts              # Core API service
│   ├── authService.ts      # Authentication
│   ├── listingsService.ts  # Listings management
│   └── aiService.ts        # AI features
├── store/
│   ├── authStore.ts        # Auth state management
│   └── listingsStore.ts    # Listings state
├── config/
│   └── api.ts              # API configuration
├── components/
│   ├── AppInitializer.tsx  # App initialization
│   └── ErrorBoundary.tsx   # Error handling
└── app/
    ├── _layout.tsx         # Root layout
    ├── login.tsx           # Login screen
    └── dashboard.tsx       # Dashboard screen
```

## Security Notes

- Tokens stored in Expo SecureStore
- Automatic token refresh implemented
- Network requests use HTTPS in production
- Input validation on both client and server
- Error messages don't expose sensitive data

The frontend is now fully connected to the backend and ready for testing!
