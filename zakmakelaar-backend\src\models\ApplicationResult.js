const mongoose = require("mongoose");

const applicationResultSchema = new mongoose.Schema({
  userId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User', 
    required: true
  },
  
  listingId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'Listing', 
    required: true
  },
  
  queueItemId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'ApplicationQueue', 
    required: true
  },
  
  // Application outcome
  status: { 
    type: String, 
    enum: ['submitted', 'failed', 'blocked', 'captcha_required', 'form_changed', 'duplicate', 'expired'],
    required: true
  },
  
  submittedAt: { 
    type: Date, 
    default: Date.now
  },
  
  // Submission details
  confirmationNumber: { type: String },
  
  confirmationEmail: { type: Boolean, default: false },
  
  screenshots: [{ 
    type: String,
    description: String,
    timestamp: Date
  }],
  
  // Form data that was submitted
  formData: {
    personalInfo: {
      fullName: { type: String },
      email: { type: String },
      phone: { type: String },
      dateOfBirth: { type: Date },
      nationality: { type: String },
      occupation: { type: String },
      employer: { type: String },
      monthlyIncome: { type: Number },
      moveInDate: { type: Date },
      leaseDuration: { type: Number },
      numberOfOccupants: { type: Number },
      emergencyContact: {
        name: { type: String },
        phone: { type: String },
        relationship: { type: String }
      }
    },
    
    applicationMessage: { type: String },
    
    documentsSubmitted: [{
      type: { type: String },
      filename: { type: String },
      size: { type: Number },
      submitted: { type: Boolean }
    }],
    
    additionalFields: { type: mongoose.Schema.Types.Mixed }
  },
  
  // Response from the application system
  response: {
    success: { type: Boolean, required: true },
    message: { type: String },
    redirectUrl: { type: String },
    responseCode: { type: String },
    responseTime: { type: Number }, // in milliseconds
    headers: { type: mongoose.Schema.Types.Mixed },
    cookies: { type: mongoose.Schema.Types.Mixed }
  },
  
  // Performance and processing metrics
  metrics: {
    processingTime: { type: Number }, // Total time from start to finish (ms)
    formDetectionTime: { type: Number }, // Time to detect and analyze form (ms)
    formFillingTime: { type: Number }, // Time to fill form fields (ms)
    submissionTime: { type: Number }, // Time for actual submission (ms)
    
    formComplexity: { 
      type: String, 
      enum: ['simple', 'moderate', 'complex', 'very_complex'] 
    },
    
    fieldsDetected: { type: Number },
    fieldsSuccessfullyFilled: { type: Number },
    documentsUploaded: { type: Number },
    
    successProbability: { 
      type: Number, 
      min: 0, 
      max: 100 
    }, // AI-estimated success probability
    
    antiDetectionMeasuresUsed: [{ type: String }],
    
    browserFingerprint: { type: String },
    userAgentUsed: { type: String },
    
    networkLatency: { type: Number },
    pageLoadTime: { type: Number }
  },
  
  // Error details (if failed)
  error: {
    category: { 
      type: String, 
      enum: ['network', 'form', 'detection', 'data', 'system', 'validation'] 
    },
    code: { type: String },
    message: { type: String },
    details: { type: String },
    stack: { type: String },
    retryable: { type: Boolean, default: false },
    
    // Specific error contexts
    formErrors: [{
      field: { type: String },
      error: { type: String },
      value: { type: String }
    }],
    
    networkError: {
      statusCode: { type: Number },
      timeout: { type: Boolean },
      connectionRefused: { type: Boolean }
    },
    
    detectionError: {
      captchaType: { type: String },
      blockingMethod: { type: String },
      suspiciousActivity: { type: Boolean }
    }
  },
  
  // Follow-up actions required
  followUp: {
    required: { type: Boolean, default: false },
    type: { 
      type: String, 
      enum: ['manual_verification', 'document_upload', 'captcha_solve', 'form_update', 'contact_landlord'] 
    },
    description: { type: String },
    scheduledAt: { type: Date },
    completedAt: { type: Date },
    notes: { type: String }
  },
  
  // Listing snapshot at time of application
  listingSnapshot: {
    title: { type: String },
    address: { type: String },
    price: { type: Number },
    rooms: { type: Number },
    size: { type: Number },
    propertyType: { type: String },
    landlord: { type: String },
    description: { type: String },
    availableFrom: { type: Date },
    applicationDeadline: { type: Date },
    url: { type: String }
  },
  
  // AI-generated content used
  aiContent: {
    subject: { type: String },
    message: { type: String },
    personalizedElements: [{ type: String }],
    template: { type: String },
    language: { type: String },
    confidence: { type: Number },
    model: { type: String },
    generatedAt: { type: Date }
  },
  
  // Tracking and analytics
  analytics: {
    userAgent: { type: String },
    ipAddress: { type: String },
    sessionId: { type: String },
    
    // Timing analytics
    timeToSubmission: { type: Number }, // Time from listing discovery to submission
    queueWaitTime: { type: Number }, // Time spent in queue
    
    // Success factors
    successFactors: [{
      factor: { type: String },
      weight: { type: Number },
      value: { type: String }
    }],
    
    // Market context
    marketCondition: { 
      type: String, 
      enum: ['high_demand', 'moderate_demand', 'low_demand'] 
    },
    competitionLevel: { 
      type: String, 
      enum: ['very_high', 'high', 'moderate', 'low'] 
    },
    
    // Application ranking (if available)
    estimatedRank: { type: Number },
    totalApplications: { type: Number }
  },
  
  // Landlord/Property Owner response tracking
  landlordResponse: {
    responseReceived: { type: Boolean, default: false },
    responseDate: { type: Date },
    responseType: { 
      type: String, 
      enum: ['acceptance', 'rejection', 'request_info', 'viewing_invite', 'no_response'] 
    },
    responseMessage: { type: String },
    
    // Viewing details if invited
    viewingInvite: {
      invited: { type: Boolean, default: false },
      scheduledDate: { type: Date },
      location: { type: String },
      duration: { type: Number },
      type: { type: String, enum: ['individual', 'group', 'virtual'] },
      attended: { type: Boolean },
      outcome: { type: String }
    },
    
    // Final decision
    finalDecision: { 
      type: String, 
      enum: ['accepted', 'rejected', 'pending', 'withdrawn'] 
    },
    decisionDate: { type: Date },
    rejectionReason: { type: String }
  },
  
  // System metadata
  metadata: {
    applicationVersion: { type: String },
    browserVersion: { type: String },
    platform: { type: String },
    
    // Processing environment
    serverId: { type: String },
    processingNode: { type: String },
    
    // Quality assurance
    reviewRequired: { type: Boolean, default: false },
    reviewedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    reviewedAt: { type: Date },
    reviewNotes: { type: String },
    
    // Data retention
    retentionDate: { type: Date },
    archived: { type: Boolean, default: false }
  },
  
  // Timestamps
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// Indexes for performance optimization
applicationResultSchema.index({ userId: 1, submittedAt: -1 });
applicationResultSchema.index({ listingId: 1, status: 1 });
applicationResultSchema.index({ status: 1, submittedAt: -1 });
applicationResultSchema.index({ queueItemId: 1 }, { unique: true });
applicationResultSchema.index({ 'response.success': 1 });
applicationResultSchema.index({ 'landlordResponse.responseReceived': 1 });
applicationResultSchema.index({ 'landlordResponse.finalDecision': 1 });
applicationResultSchema.index({ 'followUp.required': 1, 'followUp.scheduledAt': 1 });
applicationResultSchema.index({ 'metrics.successProbability': -1 });
applicationResultSchema.index({ createdAt: -1 });

// Compound indexes for analytics
applicationResultSchema.index({ userId: 1, status: 1, submittedAt: -1 });
applicationResultSchema.index({ 'listingSnapshot.propertyType': 1, status: 1 });
applicationResultSchema.index({ 'analytics.marketCondition': 1, 'response.success': 1 });

// Virtual fields
applicationResultSchema.virtual('isSuccessful').get(function() {
  return this.status === 'submitted' && this.response.success;
});

applicationResultSchema.virtual('needsFollowUp').get(function() {
  return this.followUp.required && !this.followUp.completedAt;
});

applicationResultSchema.virtual('responseTime').get(function() {
  if (this.landlordResponse.responseDate && this.submittedAt) {
    return this.landlordResponse.responseDate.getTime() - this.submittedAt.getTime();
  }
  return null;
});

applicationResultSchema.virtual('daysSinceSubmission').get(function() {
  return Math.floor((new Date() - this.submittedAt) / (1000 * 60 * 60 * 24));
});

applicationResultSchema.virtual('successScore').get(function() {
  let score = 0;
  
  // Base success (submitted successfully)
  if (this.isSuccessful) score += 40;
  
  // Landlord response received
  if (this.landlordResponse.responseReceived) score += 20;
  
  // Viewing invitation
  if (this.landlordResponse.viewingInvite.invited) score += 20;
  
  // Final acceptance
  if (this.landlordResponse.finalDecision === 'accepted') score += 20;
  
  return score;
});

// Pre-save middleware
applicationResultSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  
  // Set retention date (1 year from creation)
  if (this.isNew && !this.metadata.retentionDate) {
    this.metadata.retentionDate = new Date(Date.now() + 365 * 24 * 60 * 60 * 1000);
  }
  
  // Auto-calculate success probability if not set
  if (this.isNew && !this.metrics.successProbability) {
    this.metrics.successProbability = this.calculateSuccessProbability();
  }
  
  next();
});

// Static methods
applicationResultSchema.statics.findByUser = function(userId, status = null) {
  const query = { userId };
  if (status) query.status = status;
  return this.find(query).sort({ submittedAt: -1 });
};

applicationResultSchema.statics.findByListing = function(listingId) {
  return this.find({ listingId }).sort({ submittedAt: -1 });
};

applicationResultSchema.statics.getSuccessRate = function(userId = null, timeframe = null) {
  const matchStage = {};
  
  if (userId) matchStage.userId = mongoose.Types.ObjectId(userId);
  
  if (timeframe) {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - timeframe);
    matchStage.submittedAt = { $gte: startDate };
  }
  
  return this.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: null,
        total: { $sum: 1 },
        successful: {
          $sum: {
            $cond: [
              { $and: [{ $eq: ['$status', 'submitted'] }, { $eq: ['$response.success', true] }] },
              1,
              0
            ]
          }
        },
        accepted: {
          $sum: {
            $cond: [{ $eq: ['$landlordResponse.finalDecision', 'accepted'] }, 1, 0]
          }
        }
      }
    },
    {
      $project: {
        total: 1,
        successful: 1,
        accepted: 1,
        successRate: { $multiply: [{ $divide: ['$successful', '$total'] }, 100] },
        acceptanceRate: { $multiply: [{ $divide: ['$accepted', '$total'] }, 100] }
      }
    }
  ]);
};

applicationResultSchema.statics.getPerformanceMetrics = function(timeframe = 30) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - timeframe);
  
  return this.aggregate([
    { $match: { submittedAt: { $gte: startDate } } },
    {
      $group: {
        _id: null,
        avgProcessingTime: { $avg: '$metrics.processingTime' },
        avgSuccessProbability: { $avg: '$metrics.successProbability' },
        totalApplications: { $sum: 1 },
        successfulSubmissions: {
          $sum: { $cond: [{ $eq: ['$response.success', true] }, 1, 0] }
        },
        captchaEncounters: {
          $sum: { $cond: [{ $eq: ['$status', 'captcha_required'] }, 1, 0] }
        },
        blockingIncidents: {
          $sum: { $cond: [{ $eq: ['$status', 'blocked'] }, 1, 0] }
        }
      }
    }
  ]);
};

applicationResultSchema.statics.findPendingFollowUps = function() {
  return this.find({
    'followUp.required': true,
    'followUp.completedAt': { $exists: false },
    'followUp.scheduledAt': { $lte: new Date() }
  }).sort({ 'followUp.scheduledAt': 1 });
};

applicationResultSchema.statics.getErrorAnalysis = function(timeframe = 7) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - timeframe);
  
  return this.aggregate([
    { 
      $match: { 
        submittedAt: { $gte: startDate },
        status: 'failed'
      } 
    },
    {
      $group: {
        _id: '$error.category',
        count: { $sum: 1 },
        commonErrors: { $push: '$error.message' }
      }
    },
    { $sort: { count: -1 } }
  ]);
};

applicationResultSchema.statics.findExpiredResults = function() {
  return this.find({
    'metadata.retentionDate': { $lt: new Date() },
    'metadata.archived': false
  });
};

// Instance methods
applicationResultSchema.methods.updateLandlordResponse = function(responseData) {
  this.landlordResponse = { ...this.landlordResponse, ...responseData };
  this.landlordResponse.responseReceived = true;
  this.landlordResponse.responseDate = new Date();
  return this.save();
};

applicationResultSchema.methods.scheduleFollowUp = function(type, description, scheduledAt = null) {
  this.followUp = {
    required: true,
    type,
    description,
    scheduledAt: scheduledAt || new Date(Date.now() + 24 * 60 * 60 * 1000) // Default: 24 hours
  };
  return this.save();
};

applicationResultSchema.methods.completeFollowUp = function(notes = '') {
  this.followUp.completedAt = new Date();
  this.followUp.notes = notes;
  return this.save();
};

applicationResultSchema.methods.calculateSuccessProbability = function() {
  let probability = 50; // Base probability
  
  // Adjust based on form complexity
  switch (this.metrics.formComplexity) {
    case 'simple': probability += 20; break;
    case 'moderate': probability += 10; break;
    case 'complex': probability -= 10; break;
    case 'very_complex': probability -= 20; break;
  }
  
  // Adjust based on processing time (faster is better)
  if (this.metrics.processingTime) {
    const avgTime = 5 * 60 * 1000; // 5 minutes average
    if (this.metrics.processingTime < avgTime) {
      probability += 10;
    } else if (this.metrics.processingTime > avgTime * 2) {
      probability -= 10;
    }
  }
  
  // Adjust based on market conditions
  switch (this.analytics.marketCondition) {
    case 'low_demand': probability += 15; break;
    case 'moderate_demand': probability += 5; break;
    case 'high_demand': probability -= 15; break;
  }
  
  // Adjust based on competition level
  switch (this.analytics.competitionLevel) {
    case 'low': probability += 20; break;
    case 'moderate': probability += 10; break;
    case 'high': probability -= 10; break;
    case 'very_high': probability -= 20; break;
  }
  
  // Ensure probability is within bounds
  return Math.max(0, Math.min(100, probability));
};

applicationResultSchema.methods.addScreenshot = function(screenshotPath, description = '') {
  this.screenshots.push({
    type: screenshotPath,
    description,
    timestamp: new Date()
  });
  return this.save();
};

applicationResultSchema.methods.markForReview = function(reason = '') {
  this.metadata.reviewRequired = true;
  this.metadata.reviewNotes = reason;
  return this.save();
};

applicationResultSchema.methods.archive = function() {
  this.metadata.archived = true;
  return this.save();
};

applicationResultSchema.methods.isEligibleForRetry = function() {
  return this.status === 'failed' && 
         this.error.retryable && 
         this.daysSinceSubmission < 7;
};

// Ensure virtual fields are serialized
applicationResultSchema.set('toJSON', { virtuals: true });
applicationResultSchema.set('toObject', { virtuals: true });

module.exports = mongoose.model("ApplicationResult", applicationResultSchema);