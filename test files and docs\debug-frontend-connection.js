// Debug frontend connection issues
async function debugConnection() {
  console.log('🔍 Debugging frontend connection issues...\n');
  
  // Test 1: Check if backend is running
  console.log('1️⃣ Testing backend availability...');
  try {
    const response = await fetch('http://localhost:3000/api/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email: 'test', password: 'test' })
    });
    console.log('✅ Backend is running and responding');
  } catch (error) {
    console.log('❌ Backend is not accessible:', error.message);
    return;
  }
  
  // Test 2: Test authentication
  console.log('\n2️⃣ Testing authentication...');
  try {
    const loginResponse = await fetch('http://localhost:3000/api/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123'
      })
    });
    
    if (loginResponse.ok) {
      const loginData = await loginResponse.json();
      console.log('✅ Authentication successful');
      console.log('   User role:', loginData.user?.role);
      console.log('   Is property owner:', loginData.user?.propertyOwner?.isPropertyOwner);
      
      // Test 3: Test property owner properties endpoint
      console.log('\n3️⃣ Testing properties endpoint...');
      const propertiesResponse = await fetch('http://localhost:3000/api/property-owner/properties', {
        headers: {
          'Authorization': `Bearer ${loginData.token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (propertiesResponse.ok) {
        const propertiesData = await propertiesResponse.json();
        console.log('✅ Properties endpoint working');
        console.log('   Properties found:', propertiesData.total || 0);
        console.log('   Status:', propertiesData.status);
        
        if (propertiesData.data && propertiesData.data.length > 0) {
          console.log('   Sample property:', {
            id: propertiesData.data[0]._id,
            title: propertiesData.data[0].title,
            status: propertiesData.data[0].status,
            city: propertiesData.data[0].address?.city
          });
        }
      } else {
        const errorText = await propertiesResponse.text();
        console.log('❌ Properties endpoint failed:', propertiesResponse.status);
        console.log('   Error:', errorText);
      }
      
    } else {
      const errorText = await loginResponse.text();
      console.log('❌ Authentication failed:', loginResponse.status);
      console.log('   Error:', errorText);
    }
  } catch (error) {
    console.log('❌ Authentication test failed:', error.message);
  }
  
  // Test 4: Check database directly
  console.log('\n4️⃣ Checking database directly...');
  try {
    const mongoose = require('mongoose');
    const config = require('./zakmakelaar-backend/src/config/config');
    const Property = require('./zakmakelaar-backend/src/models/Property');
    const User = require('./zakmakelaar-backend/src/models/User');
    
    await mongoose.connect(config.mongoURI);
    
    const propertyCount = await Property.countDocuments();
    const ownerCount = await User.countDocuments({ 'propertyOwner.isPropertyOwner': true });
    
    console.log('✅ Database connection successful');
    console.log('   Total properties in DB:', propertyCount);
    console.log('   Total property owners in DB:', ownerCount);
    
    if (propertyCount > 0) {
      const sampleProperty = await Property.findOne();
      console.log('   Sample property from DB:', {
        id: sampleProperty._id,
        title: sampleProperty.title,
        status: sampleProperty.status,
        owner: sampleProperty.owner?.userId
      });
    }
    
    await mongoose.connection.close();
  } catch (error) {
    console.log('❌ Database check failed:', error.message);
  }
  
  console.log('\n🎯 Summary:');
  console.log('- If all tests pass, the issue is likely in the frontend configuration');
  console.log('- Check that the frontend is using http://localhost:3000/api');
  console.log('- Ensure the frontend is running in web browser, not mobile emulator');
  console.log('- Check browser console for any CORS or network errors');
}

debugConnection();