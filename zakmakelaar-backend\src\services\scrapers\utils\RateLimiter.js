/**
 * Intelligent rate limiter and anti-detection system
 */

class RateLimiter {
  constructor() {
    this.siteConfigs = {
      funda: {
        baseDelay: { min: 3000, max: 8000 },
        requestsPerMinute: 8,
        burstLimit: 3,
        backoffMultiplier: 1.5,
        maxBackoff: 60000
      },
      pararius: {
        baseDelay: { min: 2000, max: 5000 },
        requestsPerMinute: 12,
        burstLimit: 4,
        backoffMultiplier: 1.3,
        maxBackoff: 30000
      },
      huurwoningen: {
        baseDelay: { min: 4000, max: 10000 },
        requestsPerMinute: 6,
        burstLimit: 2,
        backoffMultiplier: 2.0,
        maxBackoff: 120000
      }
    };
    
    this.requestHistory = new Map(); // Track requests per site
    this.backoffLevels = new Map(); // Track backoff levels per site
    this.lastRequestTime = new Map(); // Track last request time per site
  }

  /**
   * Calculate delay before next request
   */
  async calculateDelay(siteName, isRetry = false, errorType = null) {
    const config = this.siteConfigs[siteName];
    if (!config) {
      throw new Error(`No rate limit config for site: ${siteName}`);
    }

    // Get base delay
    let delay = this.getRandomDelay(config.baseDelay.min, config.baseDelay.max);

    // Apply backoff if needed
    if (isRetry || this.shouldApplyBackoff(siteName)) {
      delay = this.applyBackoff(siteName, delay, errorType);
    }

    // Ensure minimum time between requests
    delay = this.enforceMinimumInterval(siteName, delay);

    // Apply burst protection
    delay = this.applyBurstProtection(siteName, delay);

    console.log(`⏱️  Rate limiter: ${siteName} delay = ${delay}ms`);
    return delay;
  }

  /**
   * Wait for the calculated delay
   */
  async waitForDelay(siteName, isRetry = false, errorType = null) {
    const delay = await this.calculateDelay(siteName, isRetry, errorType);
    
    if (delay > 0) {
      console.log(`🕐 Waiting ${delay}ms before next ${siteName} request...`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }

    // Record this request
    this.recordRequest(siteName);
  }

  /**
   * Get random delay within range
   */
  getRandomDelay(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  /**
   * Check if backoff should be applied
   */
  shouldApplyBackoff(siteName) {
    const history = this.getRequestHistory(siteName);
    const recentErrors = history.filter(req => 
      req.timestamp > Date.now() - 300000 && // Last 5 minutes
      req.error
    );

    return recentErrors.length > 2; // Apply backoff if 2+ errors in 5 minutes
  }

  /**
   * Apply exponential backoff
   */
  applyBackoff(siteName, baseDelay, errorType) {
    const config = this.siteConfigs[siteName];
    let currentLevel = this.backoffLevels.get(siteName) || 0;

    // Increase backoff level based on error type
    if (errorType === 'BLOCKED' || errorType === 'RATE_LIMIT') {
      currentLevel += 2; // More aggressive backoff for blocking
    } else if (errorType) {
      currentLevel += 1;
    }

    // Calculate backoff delay
    const backoffDelay = baseDelay * Math.pow(config.backoffMultiplier, currentLevel);
    const finalDelay = Math.min(backoffDelay, config.maxBackoff);

    // Update backoff level
    this.backoffLevels.set(siteName, currentLevel);

    console.log(`📈 Applying backoff level ${currentLevel} for ${siteName}: ${finalDelay}ms`);
    return finalDelay;
  }

  /**
   * Enforce minimum interval between requests
   */
  enforceMinimumInterval(siteName, delay) {
    const config = this.siteConfigs[siteName];
    const lastRequest = this.lastRequestTime.get(siteName) || 0;
    const minInterval = 60000 / config.requestsPerMinute; // Convert RPM to interval
    const timeSinceLastRequest = Date.now() - lastRequest;
    
    if (timeSinceLastRequest < minInterval) {
      const additionalDelay = minInterval - timeSinceLastRequest;
      return Math.max(delay, additionalDelay);
    }
    
    return delay;
  }

  /**
   * Apply burst protection
   */
  applyBurstProtection(siteName, delay) {
    const config = this.siteConfigs[siteName];
    const recentRequests = this.getRecentRequests(siteName, 60000); // Last minute
    
    if (recentRequests.length >= config.burstLimit) {
      // Apply burst penalty
      const burstPenalty = delay * 2;
      console.log(`🚦 Burst protection applied for ${siteName}: +${burstPenalty}ms`);
      return delay + burstPenalty;
    }
    
    return delay;
  }

  /**
   * Record a request
   */
  recordRequest(siteName, error = null) {
    const history = this.getRequestHistory(siteName);
    
    history.push({
      timestamp: Date.now(),
      error: error ? error.type : null
    });

    // Keep only last 100 requests
    if (history.length > 100) {
      history.splice(0, history.length - 100);
    }

    this.lastRequestTime.set(siteName, Date.now());

    // Reset backoff on successful request
    if (!error) {
      const currentLevel = this.backoffLevels.get(siteName) || 0;
      if (currentLevel > 0) {
        this.backoffLevels.set(siteName, Math.max(0, currentLevel - 1));
      }
    }
  }

  /**
   * Record an error
   */
  recordError(siteName, error) {
    this.recordRequest(siteName, error);
    
    // Increase backoff level immediately
    const currentLevel = this.backoffLevels.get(siteName) || 0;
    this.backoffLevels.set(siteName, currentLevel + 1);
  }

  /**
   * Get request history for a site
   */
  getRequestHistory(siteName) {
    if (!this.requestHistory.has(siteName)) {
      this.requestHistory.set(siteName, []);
    }
    return this.requestHistory.get(siteName);
  }

  /**
   * Get recent requests within timeframe
   */
  getRecentRequests(siteName, timeframe) {
    const history = this.getRequestHistory(siteName);
    const cutoff = Date.now() - timeframe;
    return history.filter(req => req.timestamp > cutoff);
  }

  /**
   * Get rate limiting stats
   */
  getStats(siteName = null) {
    if (siteName) {
      const history = this.getRequestHistory(siteName);
      const recentRequests = this.getRecentRequests(siteName, 3600000); // Last hour
      const errors = recentRequests.filter(req => req.error);
      
      return {
        siteName,
        totalRequests: history.length,
        recentRequests: recentRequests.length,
        recentErrors: errors.length,
        backoffLevel: this.backoffLevels.get(siteName) || 0,
        lastRequest: this.lastRequestTime.get(siteName),
        errorRate: recentRequests.length > 0 ? errors.length / recentRequests.length : 0
      };
    }

    // Return stats for all sites
    const allStats = {};
    for (const site of Object.keys(this.siteConfigs)) {
      allStats[site] = this.getStats(site);
    }
    return allStats;
  }

  /**
   * Reset backoff for a site (manual intervention)
   */
  resetBackoff(siteName) {
    this.backoffLevels.set(siteName, 0);
    console.log(`🔄 Reset backoff for ${siteName}`);
  }

  /**
   * Check if site is currently in backoff
   */
  isInBackoff(siteName) {
    return (this.backoffLevels.get(siteName) || 0) > 0;
  }

  /**
   * Get recommended next request time
   */
  getNextRequestTime(siteName) {
    const lastRequest = this.lastRequestTime.get(siteName) || 0;
    const config = this.siteConfigs[siteName];
    const minInterval = 60000 / config.requestsPerMinute;
    
    return lastRequest + minInterval;
  }
}

/**
 * Anti-detection utilities
 */
class AntiDetection {
  constructor() {
    this.userAgents = [
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15'
    ];
    
    this.viewports = [
      { width: 1920, height: 1080 },
      { width: 1366, height: 768 },
      { width: 1440, height: 900 },
      { width: 1536, height: 864 },
      { width: 1280, height: 720 }
    ];
    
    this.currentRotation = {
      userAgent: 0,
      viewport: 0
    };
  }

  /**
   * Get random user agent
   */
  getRandomUserAgent() {
    return this.userAgents[Math.floor(Math.random() * this.userAgents.length)];
  }

  /**
   * Get rotated user agent (systematic rotation)
   */
  getRotatedUserAgent() {
    const userAgent = this.userAgents[this.currentRotation.userAgent];
    this.currentRotation.userAgent = (this.currentRotation.userAgent + 1) % this.userAgents.length;
    return userAgent;
  }

  /**
   * Get random viewport
   */
  getRandomViewport() {
    return this.viewports[Math.floor(Math.random() * this.viewports.length)];
  }

  /**
   * Setup page with anti-detection measures
   */
  async setupAntiDetection(page, siteName) {
    // Set random user agent
    const userAgent = this.getRotatedUserAgent();
    await page.setUserAgent(userAgent);

    // Set random viewport
    const viewport = this.getRandomViewport();
    await page.setViewport(viewport);

    // Remove webdriver traces
    await page.evaluateOnNewDocument(() => {
      Object.defineProperty(navigator, 'webdriver', {
        get: () => undefined,
      });
      
      // Mock plugins
      Object.defineProperty(navigator, 'plugins', {
        get: () => [1, 2, 3, 4, 5],
      });
      
      // Mock languages
      Object.defineProperty(navigator, 'languages', {
        get: () => ['en-US', 'en'],
      });
      
      // Mock permissions
      const originalQuery = window.navigator.permissions.query;
      window.navigator.permissions.query = (parameters) => (
        parameters.name === 'notifications' ?
          Promise.resolve({ state: Notification.permission }) :
          originalQuery(parameters)
      );
    });

    // Set extra headers
    await page.setExtraHTTPHeaders({
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
      'Accept-Language': 'en-US,en;q=0.5',
      'Accept-Encoding': 'gzip, deflate',
      'DNT': '1',
      'Connection': 'keep-alive',
      'Upgrade-Insecure-Requests': '1',
      'Sec-Fetch-Dest': 'document',
      'Sec-Fetch-Mode': 'navigate',
      'Sec-Fetch-Site': 'none',
      'Cache-Control': 'max-age=0'
    });

    console.log(`🥸 Anti-detection setup for ${siteName}: ${userAgent.split(' ')[0]} ${viewport.width}x${viewport.height}`);
  }

  /**
   * Add human-like behavior
   */
  async addHumanBehavior(page) {
    // Random mouse movements
    await page.mouse.move(
      Math.random() * 100 + 100,
      Math.random() * 100 + 100
    );

    // Random scroll
    await page.evaluate(() => {
      window.scrollBy(0, Math.random() * 200);
    });

    // Random small delay
    await new Promise(resolve => setTimeout(resolve, Math.random() * 1000 + 500));
  }
}

// Create singleton instances
const rateLimiter = new RateLimiter();
const antiDetection = new AntiDetection();

module.exports = {
  RateLimiter,
  AntiDetection,
  rateLimiter,
  antiDetection
};
