# Quick Translation Fix - Get It Working in 5 Minutes! 🚀

## 🎯 Current Issue
The translation is failing because the API keys in your `.env` file are placeholder values:
```
OPENROUTER_API_KEY=your-openrouter-api-key-here  ❌ (placeholder)
OPENAI_API_KEY=your-openai-api-key-here          ❌ (placeholder)
```

## ⚡ Quick Fix (5 Minutes)

### Step 1: Get a Free API Key from OpenRouter
1. **Visit**: https://openrouter.ai
2. **Click**: "Sign Up" (top right)
3. **Sign up** with your email
4. **Go to**: "Keys" section in dashboard
5. **Create** a new API key
6. **Copy** the key (starts with `sk-or-...`)

### Step 2: Update Your .env File
1. **Open**: `zakmakelaar-backend/.env`
2. **Find this line**:
   ```
   OPENROUTER_API_KEY=your-openrouter-api-key-here
   ```
3. **Replace with your actual key**:
   ```
   OPENROUTER_API_KEY=sk-or-v1-your-actual-key-here
   ```
4. **Save** the file

### Step 3: Restart Backend
```bash
cd zakmakelaar-backend
npm start
```

### Step 4: Test Translation
1. Open the app
2. Go to any property listing
3. Click the **"Translate"** button
4. ✅ **It should work now!**

## 🆓 Why OpenRouter?
- **Free models available** (deepseek/deepseek-r1-0528:free)
- **No billing required** for testing
- **High quality** AI translations
- **Easy setup**

## 🧪 Test Results You Should See
```
Input:  "Modern appartement met 2 slaapkamers"
Output: "Modern apartment with 2 bedrooms"
Time:   ~3 seconds
Status: ✅ SUCCESS
```

## 🔧 Alternative: Use OpenAI (Requires Billing)
If you prefer OpenAI:
1. Visit: https://platform.openai.com
2. Sign up and add billing
3. Get API key
4. Update `.env`:
   ```
   OPENAI_API_KEY=sk-your-actual-openai-key
   ```

## 🎉 Expected Behavior After Fix
- ✅ **Translate button works** on all property descriptions
- ✅ **Professional quality** translations
- ✅ **Any text length** supported
- ✅ **Real estate terminology** properly handled
- ✅ **Fast response** (~2-5 seconds)

## 🐛 Troubleshooting

### Still Getting 500 Error?
1. **Check**: API key is correctly pasted (no extra spaces)
2. **Verify**: Backend restarted after .env change
3. **Test**: API key works by making a direct request

### "Authentication Required" Error?
- **Cause**: API key is invalid or expired
- **Fix**: Generate a new key from OpenRouter/OpenAI

### "Rate Limit" Error?
- **Cause**: Too many requests (unlikely with new account)
- **Fix**: Wait a few minutes and try again

## 📞 Need Help?
If you're still having issues:
1. **Check**: Backend console logs for detailed errors
2. **Verify**: API key format is correct
3. **Test**: Try a different API key

## 🎯 Summary
**Before**: Translation fails with 500 error
**After**: Professional AI translations working perfectly

**Time to fix**: ~5 minutes
**Cost**: Free with OpenRouter
**Result**: Fully functional AI translation! 🎉