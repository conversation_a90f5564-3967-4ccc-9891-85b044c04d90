/**
 * Schema Errors Tests
 * 
 * This file contains tests for the schema error classification system.
 */

const { ErrorTypes, SchemaError } = require('../utils/schemaErrors');

describe('SchemaErrors', () => {
  test('should define error types', () => {
    expect(ErrorTypes).toHaveProperty('TRANSFORMATION_ERROR');
    expect(ErrorTypes).toHaveProperty('VALIDATION_ERROR');
    expect(ErrorTypes).toHaveProperty('MAPPING_ERROR');
    expect(ErrorTypes).toHaveProperty('DATA_QUALITY_ERROR');
  });
  
  test('should create SchemaError with type and message', () => {
    const error = new SchemaError(
      ErrorTypes.TRANSFORMATION_ERROR,
      'Test error message'
    );
    
    expect(error).toBeInstanceOf(Error);
    expect(error).toBeInstanceOf(SchemaError);
    expect(error.name).toBe('SchemaError');
    expect(error.type).toBe(ErrorTypes.TRANSFORMATION_ERROR);
    expect(error.message).toBe('Test error message');
    expect(error.context).toEqual({});
    expect(error.timestamp).toBeInstanceOf(Date);
  });
  
  test('should create SchemaError with context', () => {
    const context = { source: 'test', field: 'price' };
    const error = new SchemaError(
      ErrorTypes.VALIDATION_ERROR,
      'Test error message',
      context
    );
    
    expect(error.context).toEqual(context);
  });
  
  test('should convert SchemaError to JSON', () => {
    const error = new SchemaError(
      ErrorTypes.MAPPING_ERROR,
      'Test error message',
      { source: 'test' }
    );
    
    const json = error.toJSON();
    
    expect(json).toHaveProperty('name', 'SchemaError');
    expect(json).toHaveProperty('type', ErrorTypes.MAPPING_ERROR);
    expect(json).toHaveProperty('message', 'Test error message');
    expect(json).toHaveProperty('context', { source: 'test' });
    expect(json).toHaveProperty('timestamp');
    expect(json).toHaveProperty('stack');
  });
  
  test('should create transformation error', () => {
    const error = SchemaError.transformationError('Test transformation error');
    
    expect(error).toBeInstanceOf(SchemaError);
    expect(error.type).toBe(ErrorTypes.TRANSFORMATION_ERROR);
    expect(error.message).toBe('Test transformation error');
  });
  
  test('should create validation error', () => {
    const error = SchemaError.validationError('Test validation error');
    
    expect(error).toBeInstanceOf(SchemaError);
    expect(error.type).toBe(ErrorTypes.VALIDATION_ERROR);
    expect(error.message).toBe('Test validation error');
  });
  
  test('should create mapping error', () => {
    const error = SchemaError.mappingError('Test mapping error');
    
    expect(error).toBeInstanceOf(SchemaError);
    expect(error.type).toBe(ErrorTypes.MAPPING_ERROR);
    expect(error.message).toBe('Test mapping error');
  });
  
  test('should create data quality error', () => {
    const error = SchemaError.dataQualityError('Test data quality error');
    
    expect(error).toBeInstanceOf(SchemaError);
    expect(error.type).toBe(ErrorTypes.DATA_QUALITY_ERROR);
    expect(error.message).toBe('Test data quality error');
  });
});