const ApplicationQueue = require('../models/ApplicationQueue');
const AutoApplicationSettings = require('../models/AutoApplicationSettings');
const RateLimitingService = require('./rateLimitingService');
const ErrorHandlingService = require('./errorHandlingService');
const { logger } = require('./logger');

/**
 * ApplicationQueueManager - Manages the priority-based queue system for auto-applications
 * Handles rate limiting, random delays, retry mechanisms, and queue monitoring
 */
class ApplicationQueueManager {
  constructor() {
    this.isProcessing = false;
    this.processingInterval = null;
    this.rateLimitingService = new RateLimitingService();
    this.errorHandlingService = new ErrorHandlingService();
    this.queueStats = {
      processed: 0,
      successful: 0,
      failed: 0,
      retries: 0,
      averageProcessingTime: 0
    };
  }

  /**
   * Initialize the queue manager and start processing
   */
  async initialize() {
    try {
      logger.info('Initializing ApplicationQueueManager');
      
      // Reset any stuck processing items
      await this.resetStuckItems();
      
      // Start the queue processing loop
      this.startProcessing();
      
      // Schedule cleanup of expired items
      this.scheduleCleanup();
      
      logger.info('ApplicationQueueManager initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize ApplicationQueueManager:', error);
      throw error;
    }
  }

  /**
   * Add an application to the queue with priority calculation
   */
  async addToQueue(applicationData, priority = null) {
    try {
      console.log('AddToQueue called with:', applicationData);
      
      const { userId, listingId, listingUrl, personalInfo, preferences, documents, listingSnapshot } = applicationData;

      if (!userId) {
        throw new Error('userId is required');
      }
      if (!listingId) {
        throw new Error('listingId is required');
      }
      if (!listingUrl) {
        throw new Error('listingUrl is required');
      }

      console.log('Checking for existing application...');
      // Check if application already exists for this user and listing
      const existingApplication = await ApplicationQueue.findOne({ userId, listingId });
      if (existingApplication) {
        console.log('Application already exists, returning existing:', existingApplication._id);
        logger.warn(`Application already exists for user ${userId} and listing ${listingId}`);
        return existingApplication;
      }

      console.log('Calculating priority...');
      // Calculate priority if not provided
      if (priority === null) {
        try {
          priority = await this.calculatePriority(applicationData);
        } catch (error) {
          console.log('Priority calculation failed, using default:', error.message);
          priority = 5; // Default priority
        }
      }

      console.log('Checking rate limits...');
      // Check rate limits using the new rate limiting service
      try {
        const rateLimitCheck = await this.rateLimitingService.checkRateLimit(userId);
        if (!rateLimitCheck.allowed) {
          throw new Error(`Rate limit exceeded: ${rateLimitCheck.reason} - ${rateLimitCheck.message || ''}`);
        }
        
        // Use intelligent scheduling from rate limiting service
        const schedulingDelay = rateLimitCheck.scheduledDelay || this.generateRandomDelay();
        const scheduledAt = new Date(Date.now() + schedulingDelay);

        console.log('Creating queue item...');
        // Create queue item
        const queueItem = new ApplicationQueue({
          userId,
          listingId,
          listingUrl,
          priority,
          scheduledAt,
          randomDelay: schedulingDelay,
          applicationData: {
            personalInfo: personalInfo || {},
            preferences: preferences || {},
            documents: documents || []
          },
          listingSnapshot: listingSnapshot || {},
          status: 'pending'
        });

        console.log('Saving queue item...');
        await queueItem.save();
        
        console.log('Recording application attempt...');
        // Record application attempt in rate limiting service
        await this.rateLimitingService.recordApplicationAttempt(userId, applicationData);

        console.log('Queue item created successfully:', queueItem._id);
        logger.info(`Added application to queue: ${queueItem._id} for user ${userId}, listing ${listingId}, priority ${priority}`);
        
        return queueItem;
      } catch (rateLimitError) {
        console.log('Rate limit check failed, proceeding anyway:', rateLimitError.message);
        
        // If rate limiting fails, proceed with default scheduling
        const scheduledAt = new Date(Date.now() + this.generateRandomDelay());
        
        const queueItem = new ApplicationQueue({
          userId,
          listingId,
          listingUrl,
          priority: priority || 5,
          scheduledAt,
          randomDelay: 30000,
          applicationData: {
            personalInfo: personalInfo || {},
            preferences: preferences || {},
            documents: documents || []
          },
          listingSnapshot: listingSnapshot || {},
          status: 'pending'
        });

        await queueItem.save();
        logger.info(`Added application to queue (rate limit bypass): ${queueItem._id} for user ${userId}, listing ${listingId}`);
        
        return queueItem;
      }
    } catch (error) {
      console.error('Failed to add application to queue:', error);
      console.error('Error stack:', error.stack);
      logger.error('Failed to add application to queue:', error);
      throw error;
    }
  }

  /**
   * Start the queue processing loop
   */
  startProcessing() {
    if (this.isProcessing) {
      logger.warn('Queue processing already started');
      return;
    }

    this.isProcessing = true;
    logger.info('Starting queue processing');

    // Process queue every 30 seconds
    this.processingInterval = setInterval(async () => {
      try {
        await this.processQueue();
      } catch (error) {
        logger.error('Error in queue processing loop:', error);
      }
    }, 30000);
  }

  /**
   * Stop the queue processing loop
   */
  stopProcessing() {
    if (!this.isProcessing) {
      return;
    }

    this.isProcessing = false;
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = null;
    }
    
    logger.info('Stopped queue processing');
  }

  /**
   * Process the next items in the queue
   */
  async processQueue() {
    try {
      // Get ready items from queue (respecting rate limits)
      const readyItems = await this.getReadyItems();
      
      if (readyItems.length === 0) {
        return;
      }

      logger.info(`Processing ${readyItems.length} ready queue items`);

      // Process items concurrently but with rate limiting
      const processingPromises = readyItems.map(item => this.processQueueItem(item));
      await Promise.allSettled(processingPromises);

      // Update queue statistics
      await this.updateQueueStats();

    } catch (error) {
      logger.error('Error processing queue:', error);
    }
  }

  /**
   * Get ready items from queue respecting rate limits
   */
  async getReadyItems(limit = 5) {
    try {
      // Get pending items sorted by priority and scheduled time
      const readyItems = await ApplicationQueue.findReadyItems(limit);
      
      // Filter items based on rate limits using the rate limiting service
      const filteredItems = [];
      for (const item of readyItems) {
        const rateLimitCheck = await this.rateLimitingService.checkRateLimit(item.userId);
        if (rateLimitCheck.allowed) {
          filteredItems.push(item);
        } else {
          logger.info(`Skipping item ${item._id} due to rate limit: ${rateLimitCheck.reason}`);
        }
      }

      return filteredItems;
    } catch (error) {
      logger.error('Error getting ready items:', error);
      return [];
    }
  }

  /**
   * Process a single queue item
   */
  async processQueueItem(queueItem) {
    try {
      logger.info(`Processing queue item: ${queueItem._id}`);

      // Update status to processing
      await queueItem.updateStatus('processing');

      // Apply additional random delay to mimic human behavior
      const additionalDelay = Math.floor(Math.random() * 30000) + 10000; // 10-40 seconds
      await this.sleep(additionalDelay);

      // Here we would integrate with the FormAutomationEngine
      // For now, we'll simulate the processing
      const processingResult = await this.simulateApplicationProcessing(queueItem);

      if (processingResult.success) {
        await queueItem.updateStatus('completed');
        this.queueStats.successful++;
        
        // Record successful completion
        await this.rateLimitingService.recordApplicationCompletion(queueItem.userId, true);
        
        logger.info(`Successfully processed application: ${queueItem._id}`);
      } else {
        // Check if the failure is due to Funda rate limiting
        const rateLimitDetection = this.rateLimitingService.detectFundaRateLimit(
          processingResult.responseText || '',
          processingResult.responseHeaders || {},
          processingResult.statusCode || 200
        );

        if (rateLimitDetection.isRateLimit) {
          logger.warn(`Funda rate limit detected for application ${queueItem._id}:`, rateLimitDetection);
          
          // Pause user based on detection severity
          if (rateLimitDetection.recommendedAction === 'pause_user') {
            await this.rateLimitingService.pauseUser(
              queueItem.userId,
              `Funda rate limit: ${rateLimitDetection.reason}`,
              rateLimitDetection.pauseDuration
            );
          } else if (rateLimitDetection.recommendedAction === 'pause_user_manual') {
            await this.rateLimitingService.pauseUser(
              queueItem.userId,
              `Manual intervention required: ${rateLimitDetection.reason}`,
              rateLimitDetection.pauseDuration,
              true
            );
          }
        }

        // Record failed completion
        await this.rateLimitingService.recordApplicationCompletion(queueItem.userId, false);
        
        // Handle failure with retry logic
        await this.handleProcessingFailure(queueItem, processingResult.error);
      }

      this.queueStats.processed++;

    } catch (error) {
      logger.error(`Error processing queue item ${queueItem._id}:`, error);
      
      // Use comprehensive error handling
      const recoveryResult = await this.errorHandlingService.handleError(error, {
        service: 'ApplicationQueueManager',
        method: 'processQueueItem',
        queueItemId: queueItem._id,
        userId: queueItem.userId,
        attemptNumber: queueItem.attempts + 1,
        operation: 'process_queue_item'
      });
      
      // If error handling didn't handle the retry, fall back to original logic
      if (!recoveryResult.retryScheduled) {
        await this.handleProcessingFailure(queueItem, error);
      }
    }
  }

  /**
   * Handle processing failure with retry mechanism
   */
  async handleProcessingFailure(queueItem, error) {
    try {
      logger.warn(`Processing failed for queue item ${queueItem._id}: ${error.message}`);

      // Increment attempt counter
      await queueItem.incrementAttempt();

      if (queueItem.canRetry) {
        // Schedule retry with exponential backoff
        const backoffDelay = this.calculateExponentialBackoff(queueItem.attempts);
        await queueItem.scheduleRetry(backoffDelay);
        
        this.queueStats.retries++;
        logger.info(`Scheduled retry for queue item ${queueItem._id}, attempt ${queueItem.attempts}`);
      } else {
        // Max attempts reached, mark as failed
        await queueItem.updateStatus('failed', error);
        this.queueStats.failed++;
        logger.error(`Max attempts reached for queue item ${queueItem._id}, marking as failed`);
      }
    } catch (retryError) {
      logger.error(`Error handling processing failure for ${queueItem._id}:`, retryError);
    }
  }

  /**
   * Calculate exponential backoff delay in minutes
   */
  calculateExponentialBackoff(attempts) {
    const baseDelay = 5; // 5 minutes
    const maxDelay = 120; // 2 hours
    const backoffDelay = Math.min(baseDelay * Math.pow(2, attempts - 1), maxDelay);
    
    // Add jitter (±20%)
    const jitter = backoffDelay * 0.2 * (Math.random() - 0.5);
    return Math.max(1, Math.floor(backoffDelay + jitter));
  }

  /**
   * Generate random delay to mimic human behavior (2-10 minutes)
   */
  generateRandomDelay() {
    const minDelay = 2 * 60 * 1000; // 2 minutes
    const maxDelay = 10 * 60 * 1000; // 10 minutes
    return Math.floor(Math.random() * (maxDelay - minDelay + 1)) + minDelay;
  }

  /**
   * Calculate priority for an application based on various factors
   */
  async calculatePriority(applicationData) {
    let priority = 0;

    try {
      const { listingSnapshot, userId } = applicationData;

      // Base priority factors
      if (listingSnapshot) {
        // Higher priority for lower priced properties (more competitive)
        if (listingSnapshot.price && listingSnapshot.price < 1500) {
          priority += 20;
        } else if (listingSnapshot.price && listingSnapshot.price < 2000) {
          priority += 10;
        }

        // Higher priority for properties available soon
        if (listingSnapshot.availableFrom) {
          const daysUntilAvailable = Math.ceil((new Date(listingSnapshot.availableFrom) - new Date()) / (1000 * 60 * 60 * 24));
          if (daysUntilAvailable <= 7) {
            priority += 15;
          } else if (daysUntilAvailable <= 30) {
            priority += 10;
          }
        }

        // Higher priority for popular property types
        if (listingSnapshot.propertyType === 'apartment') {
          priority += 5;
        }
      }

      // User-specific priority factors
      const userSettings = await AutoApplicationSettings.findOne({ userId });
      if (userSettings && userSettings.settings.requireManualReview === false) {
        priority += 10; // Higher priority for users who allow auto-submit
      }

      // Time-based priority (newer listings get higher priority)
      if (listingSnapshot && listingSnapshot.scrapedAt) {
        const hoursOld = (new Date() - new Date(listingSnapshot.scrapedAt)) / (1000 * 60 * 60);
        if (hoursOld < 1) {
          priority += 25; // Very new listing
        } else if (hoursOld < 6) {
          priority += 15; // Recent listing
        } else if (hoursOld < 24) {
          priority += 5; // Day-old listing
        }
      }

      return Math.max(0, priority);
    } catch (error) {
      logger.error('Error calculating priority:', error);
      return 0;
    }
  }



  /**
   * Get user's queue items
   */
  async getUserQueue(userId, options = {}) {
    try {
      const { status, limit = 20, offset = 0 } = options;
      
      // Build query
      const query = { userId };
      if (status) {
        query.status = status;
      }

      // Get queue items with pagination
      const applications = await ApplicationQueue.find(query)
        .sort({ priority: -1, scheduledAt: 1 })
        .limit(parseInt(limit))
        .skip(parseInt(offset))
        .lean();

      // Get total count for pagination
      const total = await ApplicationQueue.countDocuments(query);

      return {
        applications,
        total,
        limit: parseInt(limit),
        offset: parseInt(offset),
        hasMore: total > (parseInt(offset) + parseInt(limit))
      };
    } catch (error) {
      logger.error(`Error getting user queue for ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Pause the queue for a specific user or globally
   */
  async pauseQueue(userId = null, reason = 'Manual pause') {
    try {
      if (userId) {
        // Pause queue for specific user
        await ApplicationQueue.updateMany(
          { userId, status: 'pending' },
          { status: 'paused', 'metadata.pauseReason': reason }
        );
        logger.info(`Paused queue for user ${userId}: ${reason}`);
      } else {
        // Pause entire queue
        this.stopProcessing();
        await ApplicationQueue.updateMany(
          { status: 'pending' },
          { status: 'paused', 'metadata.pauseReason': reason }
        );
        logger.info(`Paused entire queue: ${reason}`);
      }
    } catch (error) {
      logger.error('Error pausing queue:', error);
      throw error;
    }
  }

  /**
   * Resume the queue for a specific user or globally
   */
  async resumeQueue(userId = null) {
    try {
      if (userId) {
        // Resume queue for specific user
        await ApplicationQueue.updateMany(
          { userId, status: 'paused' },
          { status: 'pending', $unset: { 'metadata.pauseReason': 1 } }
        );
        logger.info(`Resumed queue for user ${userId}`);
      } else {
        // Resume entire queue
        await ApplicationQueue.updateMany(
          { status: 'paused' },
          { status: 'pending', $unset: { 'metadata.pauseReason': 1 } }
        );
        this.startProcessing();
        logger.info('Resumed entire queue');
      }
    } catch (error) {
      logger.error('Error resuming queue:', error);
      throw error;
    }
  }

  /**
   * Get queue status and statistics
   */
  async getQueueStatus() {
    try {
      const stats = await ApplicationQueue.getQueueStats();
      const readyCount = await ApplicationQueue.countDocuments({
        status: 'pending',
        scheduledAt: { $lte: new Date() },
        $or: [
          { delayUntil: { $exists: false } },
          { delayUntil: { $lte: new Date() } }
        ]
      });

      return {
        isProcessing: this.isProcessing,
        queueStats: this.queueStats,
        statusBreakdown: stats,
        readyToProcess: readyCount,
        rateLimitingStatus: this.rateLimitingService.getRateLimitingStatus()
      };
    } catch (error) {
      logger.error('Error getting queue status:', error);
      throw error;
    }
  }

  /**
   * Update rate limits configuration
   */
  updateRateLimits(newLimits) {
    try {
      this.rateLimitingService.updateRateLimits(newLimits);
      logger.info('Updated rate limits via rate limiting service');
    } catch (error) {
      logger.error('Error updating rate limits:', error);
      throw error;
    }
  }

  /**
   * Reset any items stuck in processing state
   */
  async resetStuckItems() {
    try {
      const stuckThreshold = new Date(Date.now() - 30 * 60 * 1000); // 30 minutes ago
      
      const result = await ApplicationQueue.updateMany(
        { 
          status: 'processing',
          updatedAt: { $lt: stuckThreshold }
        },
        { 
          status: 'pending',
          $unset: { processedAt: 1 }
        }
      );

      if (result.modifiedCount > 0) {
        logger.info(`Reset ${result.modifiedCount} stuck processing items`);
      }
    } catch (error) {
      logger.error('Error resetting stuck items:', error);
    }
  }

  /**
   * Schedule cleanup of expired items
   */
  scheduleCleanup() {
    // Run cleanup every hour
    setInterval(async () => {
      try {
        await this.cleanupExpiredItems();
      } catch (error) {
        logger.error('Error in cleanup task:', error);
      }
    }, 60 * 60 * 1000);
  }

  /**
   * Clean up expired queue items
   */
  async cleanupExpiredItems() {
    try {
      const expiredItems = await ApplicationQueue.findExpiredItems();
      
      for (const item of expiredItems) {
        await item.updateStatus('cancelled', new Error('Item expired'));
      }

      if (expiredItems.length > 0) {
        logger.info(`Cleaned up ${expiredItems.length} expired queue items`);
      }
    } catch (error) {
      logger.error('Error cleaning up expired items:', error);
    }
  }

  /**
   * Update queue statistics
   */
  async updateQueueStats() {
    try {
      const stats = await ApplicationQueue.aggregate([
        {
          $group: {
            _id: null,
            totalProcessed: { $sum: { $cond: [{ $in: ['$status', ['completed', 'failed']] }, 1, 0] } },
            successful: { $sum: { $cond: [{ $eq: ['$status', 'completed'] }, 1, 0] } },
            failed: { $sum: { $cond: [{ $eq: ['$status', 'failed'] }, 1, 0] } },
            avgProcessingTime: { $avg: '$metadata.processingTime' }
          }
        }
      ]);

      if (stats.length > 0) {
        const stat = stats[0];
        this.queueStats.processed = stat.totalProcessed || 0;
        this.queueStats.successful = stat.successful || 0;
        this.queueStats.failed = stat.failed || 0;
        this.queueStats.averageProcessingTime = stat.avgProcessingTime || 0;
      }
    } catch (error) {
      logger.error('Error updating queue stats:', error);
    }
  }

  /**
   * Simulate application processing (placeholder for actual form automation)
   */
  async simulateApplicationProcessing(queueItem) {
    // This is a placeholder - in real implementation, this would integrate with FormAutomationEngine
    const processingTime = Math.floor(Math.random() * 60000) + 30000; // 30-90 seconds
    await this.sleep(processingTime);

    // Update processing time in metadata
    await queueItem.updateMetadata({ processingTime });

    // Simulate success/failure (80% success rate for simulation)
    const success = Math.random() > 0.2;
    
    if (success) {
      return { success: true };
    } else {
      return { 
        success: false, 
        error: new Error('Simulated processing failure') 
      };
    }
  }

  /**
   * Utility method to sleep for specified milliseconds
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Shutdown the queue manager gracefully
   */
  async shutdown() {
    try {
      logger.info('Shutting down ApplicationQueueManager');
      
      this.stopProcessing();
      
      // Clear any remaining intervals
      if (this.processingInterval) {
        clearInterval(this.processingInterval);
      }
      
      logger.info('ApplicationQueueManager shutdown complete');
    } catch (error) {
      logger.error('Error during ApplicationQueueManager shutdown:', error);
    }
  }
}

module.exports = ApplicationQueueManager;