/**
 * Enhanced Property Model with Unified Schema Support
 * 
 * This model extends the existing Property model to include unified schema fields,
 * source metadata tracking, data quality indicators, and processing metadata.
 * It provides backward compatibility with the existing Property model while
 * supporting the new unified schema for scraped properties.
 */

const mongoose = require('mongoose');
const { Schema } = mongoose;

// Import the base Property model schema
const Property = require('./Property');
const propertySchema = Property.schema;

// Define the unified schema extension fields
const unifiedSchemaExtension = {
  // Unified data from the schema transformer
  unifiedData: {
    type: Schema.Types.Mixed,
    default: {}
  },
  
  // Source metadata tracking for scraping provenance
  sourceMetadata: {
    website: { type: String }, // 'funda.nl', 'huurwoningen.nl', 'pararius.nl'
    externalId: { type: String },
    scraperId: { type: String },
    scrapedAt: { type: Date },
    lastUpdated: { type: Date },
    version: { type: Number, default: 1 }
  },
  
  // Data quality indicators
  dataQuality: {
    completeness: { type: Number, min: 0, max: 100, default: 0 }, // Percentage of fields filled
    accuracy: { type: Number, min: 0, max: 100, default: 0 }, // Confidence score
    lastValidated: { type: Date },
    validationErrors: [{ type: String }],
    validationWarnings: [{ type: String }]
  },
  
  // Processing metadata
  processingMetadata: {
    transformationVersion: { type: String },
    processingTime: { type: Number }, // in milliseconds
    processingDate: { type: Date, default: Date.now },
    errors: [{ 
      type: { type: String },
      message: { type: String },
      field: { type: String },
      timestamp: { type: Date, default: Date.now }
    }],
    warnings: [{
      type: { type: String },
      message: { type: String },
      field: { type: String },
      timestamp: { type: Date, default: Date.now }
    }]
  },
  
  // Raw data preservation for debugging and reprocessing
  rawData: {
    original: { type: Schema.Types.Mixed },
    processed: { type: Schema.Types.Mixed },
    metadata: { type: Schema.Types.Mixed }
  },
  
  // Flag to indicate if this property was migrated from a legacy Listing
  isLegacyMigrated: { type: Boolean, default: false },
  
  // Original listing ID if migrated from legacy Listing
  originalListingId: { type: Schema.Types.ObjectId, ref: 'Listing' }
};

// Add the unified schema extension fields to the property schema
Object.keys(unifiedSchemaExtension).forEach(key => {
  propertySchema.add({ [key]: unifiedSchemaExtension[key] });
});

// Add additional indexes for efficient querying of unified data
propertySchema.index({ 'sourceMetadata.website': 1 });
propertySchema.index({ 'sourceMetadata.externalId': 1, 'sourceMetadata.website': 1 }, { unique: true, sparse: true });
propertySchema.index({ 'dataQuality.completeness': 1 });
propertySchema.index({ 'dataQuality.accuracy': 1 });
propertySchema.index({ 'sourceMetadata.scrapedAt': -1 });
propertySchema.index({ 'processingMetadata.processingDate': -1 });
propertySchema.index({ isLegacyMigrated: 1 });

// Compound indexes for common queries on unified data
propertySchema.index({ 'sourceMetadata.website': 1, 'dataQuality.completeness': -1 });
propertySchema.index({ 'sourceMetadata.website': 1, 'sourceMetadata.scrapedAt': -1 });

// Add methods for unified schema support
propertySchema.methods.updateFromUnifiedSchema = function(unifiedData) {
  // Map unified schema fields to property model fields
  if (unifiedData.title) this.title = unifiedData.title;
  if (unifiedData.description) this.description = unifiedData.description;
  
  // Map location data
  if (unifiedData.location && unifiedData.location._unified) {
    const address = unifiedData.location._unified.address;
    if (address) {
      this.address = {
        street: address.street || this.address?.street,
        houseNumber: address.houseNumber || this.address?.houseNumber,
        postalCode: address.postalCode || this.address?.postalCode,
        city: address.city || this.address?.city,
        province: address.province || this.address?.province,
        country: address.country || this.address?.country || 'Netherlands'
      };
      
      if (unifiedData.location._unified.coordinates) {
        this.address.coordinates = unifiedData.location._unified.coordinates;
      }
    }
  }
  
  // Map property type
  if (unifiedData.propertyType) {
    // Normalize property type
    const typeMapping = {
      'apartment': 'apartment',
      'appartement': 'apartment',
      'house': 'house',
      'huis': 'house',
      'studio': 'studio',
      'room': 'room',
      'kamer': 'room',
      'woning': 'house'
    };
    this.propertyType = typeMapping[unifiedData.propertyType.toLowerCase()] || 'house';
  }
  
  // Map size and rooms
  if (unifiedData.area) this.size = unifiedData.area;
  if (unifiedData.rooms) this.rooms = typeof unifiedData.rooms === 'string' ? parseInt(unifiedData.rooms) : unifiedData.rooms;
  if (unifiedData.bedrooms) this.bedrooms = typeof unifiedData.bedrooms === 'string' ? parseInt(unifiedData.bedrooms) : unifiedData.bedrooms;
  if (unifiedData.bathrooms) this.bathrooms = typeof unifiedData.bathrooms === 'string' ? parseInt(unifiedData.bathrooms) : unifiedData.bathrooms;
  
  // Map price information
  if (unifiedData.price) {
    if (typeof unifiedData.price === 'number') {
      this.rent.amount = unifiedData.price;
    } else {
      // Extract numeric value from price string
      const priceMatch = unifiedData.price.match(/\d+/g);
      if (priceMatch) {
        this.rent.amount = parseInt(priceMatch.join(''));
      }
    }
  }
  
  // Map deposit and utilities
  if (unifiedData.deposit) this.rent.deposit = unifiedData.deposit;
  if (unifiedData.utilities) this.rent.additionalCosts = {
    ...this.rent.additionalCosts,
    utilities: unifiedData.utilities
  };
  
  // Map features
  if (unifiedData.interior) {
    const interiorMapping = {
      'kaal': 'kaal',
      'gestoffeerd': 'gestoffeerd',
      'gemeubileerd': 'gemeubileerd',
      'unfurnished': 'kaal',
      'semi-furnished': 'gestoffeerd',
      'furnished': 'gemeubileerd'
    };
    this.features.interior = interiorMapping[unifiedData.interior.toLowerCase()] || unifiedData.interior;
  }
  
  if (unifiedData.furnished !== undefined) this.features.furnished = unifiedData.furnished;
  if (unifiedData.parking !== undefined) this.features.parking = unifiedData.parking;
  if (unifiedData.garden !== undefined) this.features.garden = unifiedData.garden;
  if (unifiedData.balcony !== undefined) this.features.balcony = unifiedData.balcony;
  if (unifiedData.energyLabel) this.features.energyLabel = unifiedData.energyLabel;
  
  // Map policies
  if (unifiedData.pets !== undefined) this.policies.petsAllowed = unifiedData.pets;
  if (unifiedData.smoking !== undefined) this.policies.smokingAllowed = unifiedData.smoking;
  
  // Map availability
  if (unifiedData.dateAvailable) {
    this.availability.availableFrom = new Date(unifiedData.dateAvailable);
  }
  
  // Map images
  if (unifiedData.images && unifiedData.images.length > 0) {
    this.images = unifiedData.images.map((url, index) => ({
      url,
      caption: '',
      isPrimary: index === 0,
      uploadedAt: new Date()
    }));
  }
  
  // Store the complete unified data
  this.unifiedData = unifiedData;
  
  // Update source metadata
  if (unifiedData.source) {
    this.sourceMetadata = {
      ...this.sourceMetadata,
      website: unifiedData.source,
      lastUpdated: new Date()
    };
  }
  
  // Update processing metadata
  this.processingMetadata = {
    ...this.processingMetadata,
    processingDate: new Date()
  };
  
  return this;
};

// Static method to create a property from unified schema data
propertySchema.statics.createFromUnifiedSchema = async function(unifiedData) {
  const property = new this();
  property.updateFromUnifiedSchema(unifiedData);
  
  // Set default values for required fields if not provided
  if (!property.title) property.title = 'Untitled Property';
  if (!property.status) property.status = 'draft';
  
  // Set owner to system if not provided
  if (!property.owner || !property.owner.userId) {
    property.owner = {
      userId: mongoose.Types.ObjectId('000000000000000000000000'), // System user ID
      contactPreference: 'email'
    };
  }
  
  return property;
};

// Static method to find properties by source metadata
propertySchema.statics.findBySourceMetadata = function(website, externalId) {
  return this.findOne({
    'sourceMetadata.website': website,
    'sourceMetadata.externalId': externalId
  });
};

// Static method to find properties with data quality issues
propertySchema.statics.findWithQualityIssues = function(minCompleteness = 50, minAccuracy = 50) {
  return this.find({
    $or: [
      { 'dataQuality.completeness': { $lt: minCompleteness } },
      { 'dataQuality.accuracy': { $lt: minAccuracy } },
      { 'dataQuality.validationErrors.0': { $exists: true } }
    ]
  });
};

// Create and export the enhanced Property model
const EnhancedProperty = mongoose.model('Property', propertySchema);
module.exports = EnhancedProperty;