import React from 'react';
import { StyleSheet, TouchableOpacity, Text, ActivityIndicator, View } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Animated, { FadeIn } from 'react-native-reanimated';

// Define theme colors
const THEME = {
  primary: '#4361ee',
  secondary: '#7209b7',
  accent: '#f72585',
  dark: '#0a0a18',
  light: '#ffffff',
  gray: '#6b7280',
};

interface PreferencesSaveButtonProps {
  onPress: () => void;
  isLoading: boolean;
  isLastStep: boolean;
  label?: string;
}

export const PreferencesSaveButton: React.FC<PreferencesSaveButtonProps> = ({
  onPress,
  isLoading,
  isLastStep,
  label
}) => {
  const buttonText = label || (isLastStep ? 'Complete & Save' : 'Next');
  const iconName = isLastStep ? "checkmark-circle" : "arrow-forward";
  
  return (
    <TouchableOpacity
      style={[
        styles.button,
        isLastStep ? styles.completeButton : styles.nextButton,
        isLoading ? styles.disabledButton : null,
      ]}
      onPress={onPress}
      disabled={isLoading}
    >
      {isLoading ? (
        <ActivityIndicator color={THEME.light} size="small" />
      ) : (
        <>
          <Text style={styles.buttonText}>
            {buttonText}
          </Text>
          <Ionicons
            name={iconName}
            size={20}
            color={THEME.light}
          />
          
          {/* Pulsing indicator for last step */}
          {isLastStep && (
            <Animated.View 
              style={styles.completeBadge}
              entering={FadeIn}
            >
              <Ionicons name="checkmark" size={12} color={THEME.light} />
            </Animated.View>
          )}
        </>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 12,
    minWidth: 120,
    position: 'relative',
  },
  nextButton: {
    backgroundColor: THEME.accent,
  },
  completeButton: {
    backgroundColor: THEME.primary,
  },
  disabledButton: {
    opacity: 0.7,
  },
  buttonText: {
    color: THEME.light,
    fontSize: 16,
    fontWeight: '600',
    marginRight: 8,
  },
  completeBadge: {
    position: 'absolute',
    top: -6,
    right: -6,
    backgroundColor: '#10b981',
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: THEME.light,
  },
});

export default PreferencesSaveButton;