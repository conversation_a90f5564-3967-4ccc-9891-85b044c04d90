/**
 * Schema Transformation Engine
 * 
 * This module provides the core transformation logic for converting raw scraper data
 * into the unified property schema format. It applies field mappings from the registry
 * and executes transformation functions to normalize data across different sources.
 */

const { validateProperty } = require('../schemas/unifiedPropertySchema');
const { ErrorTypes, SchemaError } = require('../utils/schemaErrors');
const { transformationLogger } = require('../utils/transformationLogger');
const { transformationMonitor } = require('../monitoring/transformationMonitor');
const lodashGet = require('lodash/get');
const lodashSet = require('lodash/set');

/**
 * Schema Transformer Class
 * Handles transformation of raw scraper data to unified schema
 */
class SchemaTransformer {
  /**
   * Create a new SchemaTransformer
   * @param {FieldMappingRegistry} mappingRegistry - Field mapping registry instance
   */
  constructor(mappingRegistry) {
    if (!mappingRegistry) {
      throw new Error('MappingRegistry is required');
    }
    this.mappingRegistry = mappingRegistry;
  }

  /**
   * Transform raw scraper data to unified schema
   * @param {Object} rawData - Raw data from scraper
   * @param {string} source - Source identifier (e.g., 'funda', 'huurwoningen')
   * @param {Object} options - Transformation options
   * @param {boolean} options.validateOutput - Whether to validate the transformed data
   * @param {boolean} options.preserveRawData - Whether to preserve raw data in _internal
   * @param {boolean} options.throwOnError - Whether to throw on transformation errors
   * @returns {Promise<Object>} Transformed property data
   * @throws {SchemaError} If transformation fails and throwOnError is true
   */
  async transform(rawData, source, options = {}) {
    // Start monitoring and logging
    const context = transformationLogger.startTransformation(source, options);
    
    const {
      validateOutput = true,
      preserveRawData = true,
      throwOnError = false
    } = options;

    if (!rawData || typeof rawData !== 'object') {
      const error = new SchemaError(
        ErrorTypes.TRANSFORMATION_ERROR,
        'Raw data must be an object',
        { source }
      );
      
      // Log error
      await transformationLogger.logError(context, error);
      
      if (throwOnError) throw error;
      return this._createErrorResult(error, rawData);
    }

    if (!source || !this.mappingRegistry.hasSource(source)) {
      const error = new SchemaError(
        ErrorTypes.MAPPING_ERROR,
        `No mapping configuration found for source: ${source}`,
        { source }
      );
      
      // Log error
      await transformationLogger.logError(context, error);
      
      if (throwOnError) throw error;
      return this._createErrorResult(error, rawData);
    }

    try {
      // Get all mappings for the source
      const mappings = this.mappingRegistry.getAllMappings(source);
      
      // Apply field mappings to raw data
      const transformedData = await this._applyFieldMappings(rawData, mappings, source);
      
      // Preserve raw data if requested
      if (preserveRawData) {
        this._preserveRawData(transformedData, rawData, source);
      }

      // Validate the transformed data if requested
      let validationError = null;
      if (validateOutput) {
        const validation = validateProperty(transformedData);
        if (validation.error) {
          validationError = new SchemaError(
            ErrorTypes.VALIDATION_ERROR,
            `Validation failed after transformation: ${validation.error.message}`,
            { 
              source,
              validationErrors: validation.error.details
            }
          );
          
          if (throwOnError) {
            await transformationLogger.logError(context, validationError);
            throw validationError;
          }
          
          // Add validation errors to the result
          if (transformedData._internal && transformedData._internal.dataQuality) {
            transformedData._internal.dataQuality.validationErrors = 
              validation.error.details.map(detail => detail.message);
          }
        }
      }

      // Calculate data quality metrics
      const dataQuality = this.calculateDataQuality(transformedData);
      
      // Update data quality in the transformed data
      this.updateDataQuality(transformedData);
      
      // Log successful transformation with data quality metrics
      await transformationLogger.endTransformation(
        context, 
        { error: validationError }, 
        dataQuality
      );
      
      return transformedData;
    } catch (error) {
      // Log error
      await transformationLogger.logError(context, error);
      
      if (error instanceof SchemaError) {
        if (throwOnError) throw error;
        return this._createErrorResult(error, rawData);
      }
      
      const schemaError = new SchemaError(
        ErrorTypes.TRANSFORMATION_ERROR,
        `Transformation failed: ${error.message}`,
        { source, originalError: error }
      );
      
      if (throwOnError) throw schemaError;
      return this._createErrorResult(schemaError, rawData);
    }
  }

  /**
   * Apply field mappings to raw data
   * @param {Object} rawData - Raw data from scraper
   * @param {Object} mappings - Field mappings
   * @param {string} source - Source identifier
   * @returns {Promise<Object>} Transformed data
   * @private
   */
  async _applyFieldMappings(rawData, mappings, source) {
    const result = {};
    const errors = [];
    
    // Process each field mapping
    for (const [targetField, mapping] of Object.entries(mappings)) {
      try {
        const value = await this._processFieldMapping(rawData, targetField, mapping, source);
        if (value !== undefined) {
          lodashSet(result, targetField, value);
        }
      } catch (error) {
        errors.push({
          field: targetField,
          error: error.message
        });
      }
    }
    
    // Add source metadata
    if (!result._internal) {
      result._internal = {};
    }
    
    if (!result._internal.sourceMetadata) {
      result._internal.sourceMetadata = {
        website: source,
        scrapedAt: new Date(),
        lastUpdated: new Date(),
        version: 1
      };
    }
    
    // Add processing metadata
    if (!result._internal.processingMetadata) {
      result._internal.processingMetadata = {
        transformationVersion: '1.0',
        processingTime: Date.now(),
        errors: errors,
        warnings: []
      };
    }
    
    return result;
  }

  /**
   * Process a single field mapping
   * @param {Object} rawData - Raw data from scraper
   * @param {string} targetField - Target field name
   * @param {string|Object} mapping - Field mapping configuration
   * @param {string} source - Source identifier
   * @returns {Promise<*>} Processed field value
   * @private
   */
  async _processFieldMapping(rawData, targetField, mapping, source) {
    // Handle string mapping (direct field mapping)
    if (typeof mapping === 'string') {
      const value = lodashGet(rawData, mapping);
      transformationLogger.logFieldMapping(source, mapping, targetField, value, value, true);
      return value;
    }
    
    // Handle object mapping
    if (typeof mapping === 'object') {
      // Static value mapping
      if ('value' in mapping) {
        transformationLogger.logFieldMapping(source, 'static', targetField, mapping.value, mapping.value, true);
        return mapping.value;
      }
      
      // Get source value if path is specified
      let value;
      let sourcePath = 'undefined';
      if ('path' in mapping && mapping.path !== '') {
        sourcePath = mapping.path;
        value = lodashGet(rawData, mapping.path);
      }
      
      // Apply transformation if specified
      if ('transform' in mapping && mapping.transform) {
        const transformFunc = this.mappingRegistry.getTransformation(mapping.transform);
        if (!transformFunc) {
          const error = new Error(`Transformation function '${mapping.transform}' not found`);
          transformationLogger.logFieldMapping(source, sourcePath, targetField, value, null, false, error);
          throw error;
        }
        
        try {
          const startTime = Date.now();
          const transformedValue = await transformFunc(value, rawData);
          const duration = Date.now() - startTime;
          
          transformationLogger.logTransformationFunction(
            mapping.transform, 
            value, 
            transformedValue, 
            duration, 
            true
          );
          
          value = transformedValue;
        } catch (error) {
          transformationLogger.logTransformationFunction(
            mapping.transform, 
            value, 
            null, 
            0, 
            false, 
            error
          );
          
          throw new Error(`Transformation '${mapping.transform}' failed: ${error.message}`);
        }
      }
      
      // Apply validation if specified
      if ('validate' in mapping && mapping.validate) {
        const validateFunc = this.mappingRegistry.getValidation(mapping.validate);
        if (!validateFunc) {
          const error = new Error(`Validation function '${mapping.validate}' not found`);
          transformationLogger.logValidation(targetField, value, false, error.message);
          throw error;
        }
        
        try {
          const isValid = await validateFunc(value);
          transformationLogger.logValidation(targetField, value, isValid);
          
          if (!isValid) {
            const error = new Error(`Validation failed for field '${targetField}'`);
            transformationLogger.logValidation(targetField, value, false, error.message);
            throw error;
          }
        } catch (error) {
          transformationLogger.logValidation(targetField, value, false, error.message);
          throw new Error(`Validation '${mapping.validate}' failed: ${error.message}`);
        }
      }
      
      // Use default value if value is undefined or null
      if ((value === undefined || value === null) && 'default' in mapping) {
        value = mapping.default;
        transformationLogger.logFieldMapping(
          source, 
          sourcePath, 
          targetField, 
          null, 
          value, 
          true, 
          null, 
          'Using default value'
        );
      }
      
      // Check if field is required
      if (mapping.required && (value === undefined || value === null)) {
        const error = new Error(`Required field '${targetField}' is missing or null`);
        transformationLogger.logFieldMapping(source, sourcePath, targetField, null, null, false, error);
        throw error;
      }
      
      transformationLogger.logFieldMapping(source, sourcePath, targetField, null, value, true);
      return value;
    }
    
    return undefined;
  }

  /**
   * Preserve raw data in the transformed object
   * @param {Object} transformedData - Transformed data
   * @param {Object} rawData - Original raw data
   * @param {string} source - Source identifier
   * @private
   */
  _preserveRawData(transformedData, rawData, source) {
    if (!transformedData._internal) {
      transformedData._internal = {};
    }
    
    if (!transformedData._internal.rawData) {
      transformedData._internal.rawData = {};
    }
    
    transformedData._internal.rawData.original = rawData;
    transformedData._internal.rawData.metadata = {
      preserved: true,
      timestamp: new Date(),
      source: source
    };
  }

  /**
   * Create error result object
   * @param {SchemaError} error - Schema error
   * @param {Object} rawData - Original raw data
   * @returns {Object} Error result with minimal valid structure
   * @private
   */
  _createErrorResult(error, rawData) {
    // Create a minimal valid property object
    const result = {
      title: 'Error: Failed to transform property',
      source: error.context?.source || 'unknown',
      url: rawData?.url || 'https://example.com/error',
      location: rawData?.location || 'Unknown Location',
      price: rawData?.price || 'Unknown Price',
      propertyType: 'woning',
      dateAdded: new Date().toISOString(),
      _internal: {
        sourceMetadata: {
          website: error.context?.source || 'unknown',
          scrapedAt: new Date(),
          lastUpdated: new Date(),
          version: 1
        },
        rawData: {
          original: rawData,
          metadata: {
            preserved: true,
            timestamp: new Date(),
            transformationFailed: true
          }
        },
        dataQuality: {
          completeness: 0,
          accuracy: 0,
          lastValidated: new Date(),
          validationErrors: [error.message]
        },
        processingMetadata: {
          transformationVersion: '1.0',
          processingTime: Date.now(),
          errors: [{
            type: error.type,
            message: error.message,
            context: error.context
          }],
          warnings: []
        }
      }
    };
    
    return result;
  }

  /**
   * Calculate data quality metrics
   * @param {Object} transformedData - Transformed data
   * @returns {Object} Data quality metrics
   */
  calculateDataQuality(transformedData) {
    if (!transformedData) return { completeness: 0, accuracy: 0 };
    
    // Define critical fields for completeness calculation
    const criticalFields = [
      'title', 'description', 'location', 'price', 
      'propertyType', 'size', 'rooms', 'images'
    ];
    
    // Calculate completeness (percentage of critical fields present)
    let presentFields = 0;
    for (const field of criticalFields) {
      if (transformedData[field] !== undefined && 
          transformedData[field] !== null && 
          transformedData[field] !== '') {
        presentFields++;
      }
    }
    
    const completeness = Math.round((presentFields / criticalFields.length) * 100);
    
    // Calculate accuracy (based on validation errors)
    let accuracy = 100;
    if (transformedData._internal && 
        transformedData._internal.dataQuality && 
        transformedData._internal.dataQuality.validationErrors) {
      const errorCount = transformedData._internal.dataQuality.validationErrors.length;
      accuracy = Math.max(0, 100 - (errorCount * 10)); // Reduce accuracy by 10% per error
    }
    
    return {
      completeness,
      accuracy
    };
  }

  /**
   * Update data quality metrics in transformed data
   * @param {Object} transformedData - Transformed data
   * @returns {Object} Updated transformed data
   */
  updateDataQuality(transformedData) {
    if (!transformedData) return transformedData;
    
    const quality = this.calculateDataQuality(transformedData);
    
    if (!transformedData._internal) {
      transformedData._internal = {};
    }
    
    if (!transformedData._internal.dataQuality) {
      transformedData._internal.dataQuality = {};
    }
    
    transformedData._internal.dataQuality.completeness = quality.completeness;
    transformedData._internal.dataQuality.accuracy = quality.accuracy;
    transformedData._internal.dataQuality.lastValidated = new Date();
    
    return transformedData;
  }

  /**
   * Batch transform multiple raw data items
   * @param {Array<Object>} rawDataItems - Array of raw data items
   * @param {string} source - Source identifier
   * @param {Object} options - Transformation options
   * @returns {Promise<Array<Object>>} Array of transformed items
   */
  async batchTransform(rawDataItems, source, options = {}) {
    // Start monitoring and logging for batch operation
    const context = transformationLogger.startTransformation(source, {
      ...options,
      batch: true,
      batchSize: rawDataItems.length
    });
    
    if (!Array.isArray(rawDataItems)) {
      const error = new SchemaError(
        ErrorTypes.TRANSFORMATION_ERROR,
        'Raw data items must be an array',
        { source }
      );
      
      await transformationLogger.logError(context, error);
      throw error;
    }
    
    const results = [];
    const errors = [];
    
    for (const rawData of rawDataItems) {
      try {
        const transformed = await this.transform(rawData, source, {
          ...options,
          throwOnError: false
        });
        results.push(transformed);
      } catch (error) {
        errors.push({
          rawData,
          error: error instanceof SchemaError ? error : new SchemaError(
            ErrorTypes.TRANSFORMATION_ERROR,
            error.message,
            { source, originalError: error }
          )
        });
        
        if (options.continueOnError !== false) {
          results.push(this._createErrorResult(
            error instanceof SchemaError ? error : new SchemaError(
              ErrorTypes.TRANSFORMATION_ERROR,
              error.message,
              { source, originalError: error }
            ),
            rawData
          ));
        }
      }
    }
    
    const batchResult = {
      results,
      errors,
      success: errors.length === 0,
      totalProcessed: rawDataItems.length,
      successCount: results.length,
      errorCount: errors.length
    };
    
    // Log batch results
    await transformationLogger.logBatchResults(context, batchResult);
    
    return batchResult;
  }
}

module.exports = {
  SchemaTransformer
};