/**
 * Scraper-to-Database Integration Tests
 * 
 * This file contains integration tests for the complete scraper-to-database pipeline,
 * verifying that scraped data is correctly transformed and stored in the database.
 */

const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const { validateAndNormalizeListingEnhanced } = require('../services/transformationIntegration');
const { SchemaTransformer } = require('../services/schemaTransformer');
const { FieldMappingRegistry } = require('../services/fieldMappingRegistry');
const { MappingConfigLoader } = require('../services/mappingConfigLoader');
const { ValidationEngine } = require('../services/validationEngine');
const { rawScraperData } = require('./testData/unifiedSchemaTestData');
const EnhancedProperty = require('../models/EnhancedProperty');

// Mock the database operations
jest.mock('../models/EnhancedProperty', () => {
  return {
    create: jest.fn().mockImplementation(data => Promise.resolve({ ...data, _id: 'mock-id' })),
    findOne: jest.fn().mockImplementation(() => Promise.resolve(null)),
    findOneAndUpdate: jest.fn().mockImplementation((query, data) => Promise.resolve({ ...data, _id: 'mock-id' }))
  };
});

describe('Scraper-to-Database Integration', () => {
  let registry;
  let transformer;
  let validationEngine;
  let mongoServer;
  
  beforeAll(async () => {
    // Set up in-memory MongoDB server for testing
    mongoServer = await MongoMemoryServer.create();
    const uri = mongoServer.getUri();
    
    // Connect to the in-memory database
    await mongoose.connect(uri, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
  });
  
  afterAll(async () => {
    // Disconnect from the in-memory database
    await mongoose.disconnect();
    await mongoServer.stop();
  });
  
  beforeEach(() => {
    // Set up the transformation pipeline components
    registry = new FieldMappingRegistry();
    const loader = new MappingConfigLoader(registry);
    
    // Load mappings for all sources
    loader.loadFromObject('funda.nl', {
      'title': 'title',
      'description': 'description',
      'url': 'url',
      'source': { value: 'funda.nl' },
      'location': {
        path: 'location',
        transform: 'normalizeLocation'
      },
      'price': {
        path: 'price',
        transform: 'normalizePrice'
      },
      'propertyType': {
        path: 'propertyType',
        transform: 'normalizePropertyType'
      },
      'size': {
        path: 'size',
        transform: 'normalizeSize'
      },
      'area': {
        path: 'size',
        transform: 'extractNumericSize'
      },
      'rooms': {
        path: 'rooms',
        transform: 'normalizeRooms'
      },
      'bedrooms': {
        path: 'bedrooms',
        transform: 'normalizeRooms'
      },
      'year': {
        path: 'year',
        transform: 'normalizeYear'
      },
      'interior': {
        path: 'interior',
        transform: 'normalizeInterior'
      },
      'furnished': {
        path: 'interior',
        transform: 'inferFurnishedStatus'
      },
      'images': {
        path: 'images',
        transform: 'normalizeImageArray'
      },
      'energyLabel': {
        path: 'energyLabel',
        transform: 'normalizeEnergyLabel'
      },
      'dateAdded': {
        transform: 'getCurrentISOString'
      }
    });
    
    loader.loadFromObject('huurwoningen.nl', {
      'title': 'title',
      'description': 'description',
      'url': 'url',
      'source': { value: 'huurwoningen.nl' },
      'location': {
        path: 'location',
        transform: 'normalizeLocation'
      },
      'price': {
        path: 'price',
        transform: 'normalizePrice'
      },
      'propertyType': {
        path: 'propertyType',
        transform: 'normalizePropertyType'
      },
      'size': {
        path: 'size',
        transform: 'normalizeSize'
      },
      'area': {
        path: 'oppervlakte',
        transform: 'extractNumericSize'
      },
      'rooms': {
        path: 'rooms',
        transform: 'normalizeRooms'
      },
      'bedrooms': {
        path: 'bedrooms',
        transform: 'normalizeRooms'
      },
      'year': {
        path: 'year',
        transform: 'normalizeYear'
      },
      'interior': {
        path: 'interior',
        transform: 'normalizeInterior'
      },
      'furnished': {
        path: 'interior',
        transform: 'inferFurnishedStatus'
      },
      'images': {
        path: 'images',
        transform: 'normalizeImageArray'
      },
      'dateAdded': {
        transform: 'getCurrentISOString'
      },
      'pets': {
        path: 'huisdierenToegstaan',
        transform: 'normalizeBoolean'
      },
      'smoking': {
        path: 'rokenToegstaan',
        transform: 'normalizeBoolean'
      },
      'deposit': {
        path: 'waarborgsom',
        transform: 'normalizePrice'
      }
    });
    
    loader.loadFromObject('pararius.nl', {
      'title': 'title',
      'description': 'description',
      'url': 'url',
      'source': { value: 'pararius.nl' },
      'location': {
        path: 'location',
        transform: 'normalizeLocation'
      },
      'price': {
        path: 'price',
        transform: 'normalizePrice'
      },
      'propertyType': {
        path: 'propertyType',
        transform: 'normalizePropertyType'
      },
      'size': {
        path: 'size',
        transform: 'normalizeSize'
      },
      'area': {
        path: 'woonoppervlakte',
        transform: 'extractNumericSize'
      },
      'rooms': {
        path: 'rooms',
        transform: 'normalizeRooms'
      },
      'bedrooms': {
        path: 'bedrooms',
        transform: 'normalizeRooms'
      },
      'bathrooms': {
        path: 'bathrooms',
        transform: 'normalizeRooms'
      },
      'year': {
        path: 'year',
        transform: 'normalizeYear'
      },
      'interior': {
        path: 'interior',
        transform: 'normalizeInterior'
      },
      'furnished': {
        path: 'interior',
        transform: 'inferFurnishedStatus'
      },
      'images': {
        path: 'images',
        transform: 'normalizeImageArray'
      },
      'dateAdded': {
        transform: 'getCurrentISOString'
      },
      'garden': {
        path: 'tuin',
        transform: 'normalizeBoolean'
      },
      'balcony': {
        path: 'balkon',
        transform: 'normalizeBoolean'
      },
      'parking': {
        path: 'parkeren',
        transform: 'normalizeBoolean'
      },
      'energyLabel': {
        path: 'energielabel',
        transform: 'normalizeEnergyLabel'
      }
    });
    
    transformer = new SchemaTransformer(registry);
    validationEngine = new ValidationEngine();
  });
  
  test('should transform and save Funda listing to database', async () => {
    // Transform the raw data
    const transformedData = await transformer.transform(rawScraperData.funda, 'funda.nl');
    
    // Validate the transformed data
    const validationResult = validationEngine.validate(transformedData);
    expect(validationResult.valid).toBe(true);
    
    // Create a property record
    const property = await EnhancedProperty.create({
      ...transformedData,
      unifiedData: transformedData
    });
    
    // Verify the property was created
    expect(EnhancedProperty.create).toHaveBeenCalled();
    expect(property).toHaveProperty('_id', 'mock-id');
    expect(property).toHaveProperty('title', rawScraperData.funda.title);
    expect(property).toHaveProperty('unifiedData.price', 1500);
  });
  
  test('should transform and save Huurwoningen listing to database', async () => {
    // Transform the raw data
    const transformedData = await transformer.transform(rawScraperData.huurwoningen, 'huurwoningen.nl');
    
    // Validate the transformed data
    const validationResult = validationEngine.validate(transformedData);
    expect(validationResult.valid).toBe(true);
    
    // Create a property record
    const property = await EnhancedProperty.create({
      ...transformedData,
      unifiedData: transformedData
    });
    
    // Verify the property was created
    expect(EnhancedProperty.create).toHaveBeenCalled();
    expect(property).toHaveProperty('_id', 'mock-id');
    expect(property).toHaveProperty('title', rawScraperData.huurwoningen.title);
    expect(property).toHaveProperty('unifiedData.propertyType', 'studio');
  });
  
  test('should transform and save Pararius listing to database', async () => {
    // Transform the raw data
    const transformedData = await transformer.transform(rawScraperData.pararius, 'pararius.nl');
    
    // Validate the transformed data
    const validationResult = validationEngine.validate(transformedData);
    expect(validationResult.valid).toBe(true);
    
    // Create a property record
    const property = await EnhancedProperty.create({
      ...transformedData,
      unifiedData: transformedData
    });
    
    // Verify the property was created
    expect(EnhancedProperty.create).toHaveBeenCalled();
    expect(property).toHaveProperty('_id', 'mock-id');
    expect(property).toHaveProperty('title', rawScraperData.pararius.title);
    expect(property).toHaveProperty('unifiedData.bathrooms', '2');
  });
  
  test('should update existing property if URL already exists', async () => {
    // Mock finding an existing property
    EnhancedProperty.findOne.mockImplementationOnce(() => Promise.resolve({
      _id: 'existing-id',
      title: 'Existing Property',
      url: rawScraperData.funda.url
    }));
    
    // Transform the raw data
    const transformedData = await transformer.transform(rawScraperData.funda, 'funda.nl');
    
    // Check if property exists
    const existingProperty = await EnhancedProperty.findOne({ url: transformedData.url });
    
    // Update if exists
    if (existingProperty) {
      await EnhancedProperty.findOneAndUpdate(
        { _id: existingProperty._id },
        {
          ...transformedData,
          unifiedData: transformedData,
          updatedAt: new Date()
        }
      );
    }
    
    // Verify the property was updated
    expect(EnhancedProperty.findOne).toHaveBeenCalled();
    expect(EnhancedProperty.findOneAndUpdate).toHaveBeenCalled();
  });
  
  test('should handle invalid data gracefully', async () => {
    // Create invalid data
    const invalidData = {
      title: 'Invalid Property',
      // Missing required fields
    };
    
    // Try to transform the invalid data
    const transformedData = await transformer.transform(invalidData, 'funda.nl');
    
    // Validate the transformed data
    const validationResult = validationEngine.validate(transformedData);
    expect(validationResult.valid).toBe(false);
    
    // Should not create a property record for invalid data
    try {
      await EnhancedProperty.create({
        ...transformedData,
        unifiedData: transformedData
      });
    } catch (error) {
      expect(error).toBeDefined();
    }
  });
  
  test('should use validateAndNormalizeListingEnhanced for integration', async () => {
    // Mock the transformer and validation engine
    const originalTransform = transformer.transform;
    transformer.transform = jest.fn().mockImplementation((data, source) => {
      return {
        ...data,
        source,
        transformed: true,
        _internal: {
          sourceMetadata: { website: source },
          rawData: { original: data }
        }
      };
    });
    
    // Set up the integration function with mocked components
    const integrationFn = async (listing) => {
      if (!listing || !listing.source) {
        return null;
      }
      
      try {
        const transformedData = await transformer.transform(listing, listing.source);
        return {
          ...listing,
          unifiedData: transformedData
        };
      } catch (error) {
        console.error('Transformation error:', error);
        return null;
      }
    };
    
    // Test the integration function
    const result = await integrationFn(rawScraperData.funda);
    
    // Verify the result
    expect(result).toBeDefined();
    expect(result).toHaveProperty('unifiedData.transformed', true);
    expect(result).toHaveProperty('unifiedData.source', 'funda.nl');
    
    // Restore the original transform function
    transformer.transform = originalTransform;
  });
});