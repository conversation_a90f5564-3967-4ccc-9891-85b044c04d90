const request = require('supertest');
const mongoose = require('mongoose');
const app = require('../../index');
const User = require('../../models/User');
const jwt = require('jsonwebtoken');
const config = require('../../config/config');
const documentVaultService = require('../../services/documentVaultService');

// Mock the document vault service
jest.mock('../../services/documentVaultService');

describe('User Profile Routes', () => {
  let testUser;
  let authToken;

  beforeEach(async () => {
    // Create a test user
    testUser = new User({
      email: '<EMAIL>',
      password: 'password123',
      role: 'user'
    });
    await testUser.save();

    // Generate auth token
    authToken = jwt.sign(
      { _id: testUser._id, email: testUser.email, role: testUser.role },
      config.jwtSecret,
      { expiresIn: '1h' }
    );
  });

  afterEach(async () => {
    await User.deleteMany({});
    jest.clearAllMocks();
  });

  describe('GET /api/user-profile/auto-application', () => {
    it('should get auto-application profile', async () => {
      const response = await request(app)
        .get('/api/user-profile/auto-application')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('enabled', false);
      expect(response.body.data).toHaveProperty('settings');
      expect(response.body.data).toHaveProperty('criteria');
      expect(response.body.data).toHaveProperty('personalInfo');
      expect(response.body.data).toHaveProperty('requiredDocuments');
      expect(response.body.data).toHaveProperty('profileCompleteness');
    });

    it('should require authentication', async () => {
      await request(app)
        .get('/api/user-profile/auto-application')
        .expect(401);
    });
  });

  describe('POST /api/user-profile/auto-application/initialize', () => {
    it('should initialize auto-application profile', async () => {
      const response = await request(app)
        .post('/api/user-profile/auto-application/initialize')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('enabled', false);
      expect(response.body.data.requiredDocuments).toHaveLength(4);
    });
  });

  describe('PUT /api/user-profile/auto-application/personal-info', () => {
    beforeEach(async () => {
      // Initialize profile first
      await request(app)
        .post('/api/user-profile/auto-application/initialize')
        .set('Authorization', `Bearer ${authToken}`);
    });

    it('should update personal information', async () => {
      const personalInfo = {
        fullName: 'John Doe',
        email: '<EMAIL>',
        phone: '+31612345678',
        dateOfBirth: '1990-01-01',
        nationality: 'Dutch',
        occupation: 'Software Engineer',
        employer: 'Tech Company',
        monthlyIncome: 5000,
        moveInDate: '2024-03-01',
        leaseDuration: 12,
        numberOfOccupants: 1
      };

      const response = await request(app)
        .put('/api/user-profile/auto-application/personal-info')
        .set('Authorization', `Bearer ${authToken}`)
        .send(personalInfo)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.personalInfo.fullName).toBe('John Doe');
      expect(response.body.data.profileCompleteness.personalInfo).toBeGreaterThan(0);
    });

    it('should validate email format', async () => {
      const personalInfo = {
        email: 'invalid-email'
      };

      const response = await request(app)
        .put('/api/user-profile/auto-application/personal-info')
        .set('Authorization', `Bearer ${authToken}`)
        .send(personalInfo)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Invalid email format');
    });

    it('should validate phone number format', async () => {
      const personalInfo = {
        phone: 'invalid-phone'
      };

      const response = await request(app)
        .put('/api/user-profile/auto-application/personal-info')
        .set('Authorization', `Bearer ${authToken}`)
        .send(personalInfo)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Invalid phone number format');
    });

    it('should validate age range', async () => {
      const personalInfo = {
        dateOfBirth: '2010-01-01' // Too young
      };

      const response = await request(app)
        .put('/api/user-profile/auto-application/personal-info')
        .set('Authorization', `Bearer ${authToken}`)
        .send(personalInfo)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Age must be between 18 and 100 years');
    });
  });

  describe('PUT /api/user-profile/auto-application/settings', () => {
    beforeEach(async () => {
      await request(app)
        .post('/api/user-profile/auto-application/initialize')
        .set('Authorization', `Bearer ${authToken}`);
    });

    it('should update settings', async () => {
      const settings = {
        maxApplicationsPerDay: 10,
        applicationTemplate: 'student',
        autoSubmit: true,
        requireManualReview: false
      };

      const response = await request(app)
        .put('/api/user-profile/auto-application/settings')
        .set('Authorization', `Bearer ${authToken}`)
        .send(settings)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.settings.maxApplicationsPerDay).toBe(10);
      expect(response.body.data.settings.applicationTemplate).toBe('student');
    });

    it('should validate max applications per day', async () => {
      const settings = {
        maxApplicationsPerDay: 25 // Too many
      };

      const response = await request(app)
        .put('/api/user-profile/auto-application/settings')
        .set('Authorization', `Bearer ${authToken}`)
        .send(settings)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Max applications per day must be between 1 and 20');
    });

    it('should validate application template', async () => {
      const settings = {
        applicationTemplate: 'invalid-template'
      };

      const response = await request(app)
        .put('/api/user-profile/auto-application/settings')
        .set('Authorization', `Bearer ${authToken}`)
        .send(settings)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Invalid application template');
    });
  });

  describe('PUT /api/user-profile/auto-application/criteria', () => {
    beforeEach(async () => {
      await request(app)
        .post('/api/user-profile/auto-application/initialize')
        .set('Authorization', `Bearer ${authToken}`);
    });

    it('should update criteria', async () => {
      const criteria = {
        maxPrice: 2000,
        minRooms: 2,
        maxRooms: 4,
        propertyTypes: ['apartment', 'house'],
        locations: ['Amsterdam', 'Utrecht'],
        excludeKeywords: ['no pets'],
        includeKeywords: ['balcony']
      };

      const response = await request(app)
        .put('/api/user-profile/auto-application/criteria')
        .set('Authorization', `Bearer ${authToken}`)
        .send(criteria)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.criteria.maxPrice).toBe(2000);
      expect(response.body.data.criteria.propertyTypes).toEqual(['apartment', 'house']);
    });

    it('should validate price range', async () => {
      const criteria = {
        maxPrice: -1000
      };

      const response = await request(app)
        .put('/api/user-profile/auto-application/criteria')
        .set('Authorization', `Bearer ${authToken}`)
        .send(criteria)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Max price cannot be negative');
    });

    it('should validate room range', async () => {
      const criteria = {
        minRooms: 5,
        maxRooms: 3
      };

      const response = await request(app)
        .put('/api/user-profile/auto-application/criteria')
        .set('Authorization', `Bearer ${authToken}`)
        .send(criteria)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Min rooms cannot be greater than max rooms');
    });
  });

  describe('GET /api/user-profile/auto-application/documents', () => {
    beforeEach(async () => {
      await request(app)
        .post('/api/user-profile/auto-application/initialize')
        .set('Authorization', `Bearer ${authToken}`);
    });

    it('should check document requirements with no uploaded documents', async () => {
      documentVaultService.getDocuments.mockResolvedValue([]);

      const response = await request(app)
        .get('/api/user-profile/auto-application/documents')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.allRequiredUploaded).toBe(false);
      expect(response.body.data.missingDocuments).toHaveLength(4);
      expect(response.body.data.documentCompleteness).toBe(0);
    });

    it('should check document requirements with some uploaded documents', async () => {
      documentVaultService.getDocuments.mockResolvedValue([
        { id: 'doc1', type: 'income_proof' },
        { id: 'doc2', type: 'id_document' }
      ]);

      const response = await request(app)
        .get('/api/user-profile/auto-application/documents')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.allRequiredUploaded).toBe(false);
      expect(response.body.data.missingDocuments).toHaveLength(2);
      expect(response.body.data.documentCompleteness).toBe(50);
    });

    it('should check document requirements with all uploaded documents', async () => {
      documentVaultService.getDocuments.mockResolvedValue([
        { id: 'doc1', type: 'income_proof' },
        { id: 'doc2', type: 'employment_contract' },
        { id: 'doc3', type: 'bank_statement' },
        { id: 'doc4', type: 'id_document' }
      ]);

      const response = await request(app)
        .get('/api/user-profile/auto-application/documents')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.allRequiredUploaded).toBe(true);
      expect(response.body.data.missingDocuments).toHaveLength(0);
      expect(response.body.data.documentCompleteness).toBe(100);
    });
  });

  describe('GET /api/user-profile/auto-application/guidance', () => {
    beforeEach(async () => {
      await request(app)
        .post('/api/user-profile/auto-application/initialize')
        .set('Authorization', `Bearer ${authToken}`);
    });

    it('should provide guidance for empty profile', async () => {
      const response = await request(app)
        .get('/api/user-profile/auto-application/guidance')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.nextSteps).toHaveLength(2);
      expect(response.body.data.nextSteps[0].type).toBe('personal_info');
      expect(response.body.data.nextSteps[1].type).toBe('documents');
      expect(response.body.data.warnings).toHaveLength(1);
      expect(response.body.data.warnings[0].type).toBe('low_completeness');
    });
  });

  describe('POST /api/user-profile/auto-application/enable', () => {
    beforeEach(async () => {
      await request(app)
        .post('/api/user-profile/auto-application/initialize')
        .set('Authorization', `Bearer ${authToken}`);
    });

    it('should not enable auto-application for incomplete profile', async () => {
      const response = await request(app)
        .post('/api/user-profile/auto-application/enable')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Profile not ready for auto-application');
    });

    it('should enable auto-application for complete profile', async () => {
      // Complete personal info
      await request(app)
        .put('/api/user-profile/auto-application/personal-info')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          fullName: 'John Doe',
          email: '<EMAIL>',
          phone: '+31612345678',
          dateOfBirth: '1990-01-01',
          nationality: 'Dutch',
          occupation: 'Software Engineer',
          employer: 'Tech Company',
          monthlyIncome: 5000,
          moveInDate: '2024-03-01',
          leaseDuration: 12,
          numberOfOccupants: 1
        });

      // Mock all documents uploaded
      documentVaultService.getDocuments.mockResolvedValue([
        { id: 'doc1', type: 'income_proof' },
        { id: 'doc2', type: 'employment_contract' },
        { id: 'doc3', type: 'bank_statement' },
        { id: 'doc4', type: 'id_document' }
      ]);

      // Check documents to update status
      await request(app)
        .get('/api/user-profile/auto-application/documents')
        .set('Authorization', `Bearer ${authToken}`);

      const response = await request(app)
        .post('/api/user-profile/auto-application/enable')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.enabled).toBe(true);
    });
  });

  describe('POST /api/user-profile/auto-application/disable', () => {
    beforeEach(async () => {
      await request(app)
        .post('/api/user-profile/auto-application/initialize')
        .set('Authorization', `Bearer ${authToken}`);
    });

    it('should disable auto-application', async () => {
      const response = await request(app)
        .post('/api/user-profile/auto-application/disable')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.enabled).toBe(false);
    });
  });

  describe('GET /api/user-profile/auto-application/completeness', () => {
    beforeEach(async () => {
      await request(app)
        .post('/api/user-profile/auto-application/initialize')
        .set('Authorization', `Bearer ${authToken}`);
    });

    it('should calculate profile completeness', async () => {
      const response = await request(app)
        .get('/api/user-profile/auto-application/completeness')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('personalInfo');
      expect(response.body.data).toHaveProperty('documents');
      expect(response.body.data).toHaveProperty('overall');
      expect(response.body.data).toHaveProperty('lastCalculated');
    });
  });
});