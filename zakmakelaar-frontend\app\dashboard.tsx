import { Ionicons } from "@expo/vector-icons";
import * as Haptics from "expo-haptics";
import { LinearGradient } from "expo-linear-gradient";
import { useRouter } from "expo-router";
import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  Image,
  RefreshControl,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import Animated, { FadeInUp, SlideInRight } from "react-native-reanimated";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { AutonomousStatusIndicator } from "../components/autonomous/AutonomousStatusIndicator";
import { debugLogger } from "../services/debugLogger";
import { listingsService } from "../services/listingsService";
import { LogService } from "../services/logService";
import { useAuthStore } from "../store/authStore";
import { useListingsStore } from "../store/listingsStore";

// Define theme colors
const THEME = {
  primary: "#4361ee",
  secondary: "#7209b7",
  accent: "#f72585",
  dark: "#0a0a18",
  light: "#ffffff",
  gray: "#6b7280",
  lightGray: "#f3f4f6",
  success: "#10b981",
  warning: "#f59e0b",
  danger: "#ef4444",
};

// Enhanced Header Component
const Header = ({ user }: { user: any }) => {
  const insets = useSafeAreaInsets();
  const router = useRouter();

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return "Good morning";
    if (hour < 18) return "Good afternoon";
    return "Good evening";
  };

  return (
    <LinearGradient
      colors={[THEME.primary, THEME.secondary]}
      style={[styles.header, { paddingTop: Math.max(insets.top, 16) }]}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
    >
      <Animated.View
        style={styles.headerContent}
        entering={FadeInUp.duration(600)}
      >
        <View style={styles.headerLeft}>
          <View style={styles.logoContainer}>
            <Text style={styles.logoText}>ZM</Text>
          </View>
          <View style={styles.headerTextContainer}>
            <Text style={styles.greetingText}>{getGreeting()}</Text>
            <Text style={styles.userNameText}>
              {user?.firstName ? `${user.firstName}!` : "Welcome!"}
            </Text>
          </View>
        </View>

        <View style={{ flexDirection: "row", alignItems: "center" }}>
          <TouchableOpacity
            style={styles.profileButton}
            onPress={() => router.push("/profile")}
          >
            <View style={styles.profileAvatar}>
              <Ionicons name="person" size={20} color={THEME.primary} />
            </View>
          </TouchableOpacity>
        </View>
      </Animated.View>
    </LinearGradient>
  );
};

export default function DashboardScreen() {
  const router = useRouter();
  const { user, isAuthenticated, logout } = useAuthStore();
  const {
    listings,
    recentListings,
    isLoading,
    error,
    fallbackMessage,
    fetchRecentListings,
    fetchPreferenceBasedListings,
    clearError,
    clearFallbackMessage,
    searchListings,
  } = useListingsStore();

  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [showPreferenceBasedListings, setShowPreferenceBasedListings] =
    useState(true);
  const [quickStats, setQuickStats] = useState({
    totalListings: 0,
    averagePrice: 0,
    newToday: 0,
  });
  const [quickStatsLoading, setQuickStatsLoading] = useState(true);

  // Get the correct listings to display based on search state
  const displayListings = searchQuery.trim() ? listings : recentListings;

  // Log mount/unmount exactly once
  useEffect(() => {
    debugLogger.log("DASHBOARD", "DashboardScreen mounted");
    return () => {
      debugLogger.log("DASHBOARD", "DashboardScreen unmounted");
    };
  }, []);

  // Import redirect blocker service
  const [redirectsChecked, setRedirectsChecked] = useState(false);
  const [initialCheckDone, setInitialCheckDone] = useState(false);

  // Check redirect blocker and handle authentication
  useEffect(() => {
    // Prevent multiple runs
    if (initialCheckDone) {
      debugLogger.log(
        "DASHBOARD",
        "useEffect skipped - initial check already done"
      );
      return;
    }

    debugLogger.log("DASHBOARD", "useEffect triggered", {
      isAuthenticated,
      hasUser: !!user,
      userId: user?.id || user?._id,
      userPreferencesHash: user?.preferences
        ? JSON.stringify(user.preferences).length
        : 0,
    });

    const checkRedirects = async () => {
      if (!isAuthenticated) {
        debugLogger.log(
          "DASHBOARD",
          "User not authenticated, redirecting to login"
        );
        router.replace("/login");
        return;
      }

      // Import redirect blocker service
      const { redirectBlockerService } = await import(
        "../services/redirectBlockerService"
      );

      // Check if redirects are blocked
      const areBlocked = redirectBlockerService.areRedirectsBlocked();
      debugLogger.log("DASHBOARD", "Checked redirect blocker", { areBlocked });

      if (areBlocked) {
        console.log(
          "🚫 Dashboard: Redirects are BLOCKED, staying on dashboard"
        );
        debugLogger.log(
          "DASHBOARD",
          "Redirects are blocked, staying on dashboard"
        );
        setRedirectsChecked(true);
        return;
      }

      // Check if user has meaningful preferences
      const hasValidPreferences =
        user?.preferences &&
        (user.preferences.maxPrice > 0 ||
          (user.preferences.preferredLocations?.length ?? 0) > 0 ||
          (user.preferences.propertyTypes?.length ?? 0) > 0 ||
          (user.preferences.amenities?.length ?? 0) > 0);

      debugLogger.log("DASHBOARD", "Checked user preferences", {
        hasValidPreferences,
        hasPreferences: !!user?.preferences,
        maxPrice: user?.preferences?.maxPrice,
        locationsCount: user?.preferences?.preferredLocations?.length || 0,
      });

      if (!hasValidPreferences) {
        console.log(
          "🔄 Dashboard: User has no preferences, redirecting to preferences"
        );
        debugLogger.log(
          "DASHBOARD",
          "No valid preferences, calling router.replace(/preferences)"
        );
        router.replace("/preferences");
        debugLogger.log("DASHBOARD", "router.replace(/preferences) completed");
      } else {
        console.log("✅ Dashboard: User has preferences, staying on dashboard");
        debugLogger.log(
          "DASHBOARD",
          "User has valid preferences, staying on dashboard"
        );
      }

      setRedirectsChecked(true);
      setInitialCheckDone(true);
    };

    checkRedirects();
  }, [isAuthenticated, user?.id || user?._id, initialCheckDone]); // Only depend on user ID, not the entire user object

  // Load initial data
  useEffect(() => {
    if (isAuthenticated) {
      // Check if user has meaningful preferences
      const hasPreferences =
        user?.preferences &&
        (user.preferences.maxPrice > 0 ||
          (user.preferences.preferredLocations?.length ?? 0) > 0 ||
          (user.preferences.propertyTypes?.length ?? 0) > 0);

      setShowPreferenceBasedListings(hasPreferences || false);
      loadDashboardData();
    }
  }, [isAuthenticated, user?.preferences]);

  const loadDashboardData = async () => {
    try {
      // Load quick stats immediately from backend (independent of listings)
      loadQuickStats();

      // Check if user has preferences and use preference-based listings if available
      const hasPreferences =
        user?.preferences &&
        (user.preferences.maxPrice > 0 ||
          (user.preferences.preferredLocations?.length ?? 0) > 0 ||
          (user.preferences.propertyTypes?.length ?? 0) > 0);

      if (hasPreferences && showPreferenceBasedListings) {
        await fetchPreferenceBasedListings(10);
      } else {
        await fetchRecentListings(10);
      }
    } catch (error) {
      // Only log critical errors
      LogService.error(
        "DashboardScreen",
        "Failed to load dashboard data",
        error
      );
    }
  };

  const loadQuickStats = async () => {
    setQuickStatsLoading(true);
    try {
      // Use the new backend quick stats endpoint
      const statsResponse = await listingsService.getQuickStats();

      if (statsResponse.success && statsResponse.data) {
        setQuickStats({
          totalListings: statsResponse.data.totalListings,
          averagePrice: statsResponse.data.averagePrice,
          newToday: statsResponse.data.newToday,
        });
      } else {
        throw new Error(statsResponse.message || "Failed to fetch quick stats");
      }
    } catch (error) {
      // Log the error
      LogService.error(
        "DashboardScreen",
        "Failed to load quick stats from backend",
        error
      );

      // Fallback to calculating stats from recent listings
      try {
        const today = new Date().toDateString();
        const newTodayCount = recentListings.filter((listing) => {
          const listingDate = new Date(listing.dateAdded).toDateString();
          return today === listingDate;
        }).length;

        // Calculate average price from recent listings as fallback
        let avgPrice = 0;
        if (recentListings.length > 0) {
          const prices = recentListings
            .map((listing) => {
              const priceStr = listing.price?.toString() || "";
              // Match numbers with commas (e.g., "1,500" or "2,800") and dots (e.g., "1.500")
              const match = priceStr.match(
                /(\d{1,3}(?:[,\.]\d{3})*(?:\.\d{2})?)/
              );
              if (match) {
                // Remove commas and convert to number
                const cleanPrice = match[1].replace(/,/g, "");
                return parseFloat(cleanPrice);
              }
              return 0;
            })
            .filter((price) => price > 0);

          if (prices.length > 0) {
            avgPrice =
              prices.reduce((sum, price) => sum + price, 0) / prices.length;
          }
        }

        setQuickStats({
          totalListings: recentListings.length,
          averagePrice: avgPrice,
          newToday: newTodayCount,
        });

        // Show a subtle warning that we're using fallback data
        if (__DEV__) {
          console.warn("Using fallback stats calculation due to backend error");
        }
      } catch (fallbackError) {
        LogService.error(
          "DashboardScreen",
          "Fallback stats calculation also failed",
          fallbackError
        );

        // Final fallback with zero values
        setQuickStats({
          totalListings: 0,
          averagePrice: 0,
          newToday: 0,
        });
      }
    } finally {
      setQuickStatsLoading(false);
    }
  };

  const handleSearch = async () => {
    if (searchQuery.trim()) {
      try {
        console.log("🔍 Searching for:", searchQuery.trim());
        await searchListings(searchQuery.trim());
        console.log("📊 Search results:", listings.length);
      } catch (error) {
        console.error("Search failed:", error);
      }
    } else {
      console.log("🔄 Clearing search, loading listings based on preference");
      await loadDashboardData();
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    clearFallbackMessage();
    try {
      await loadDashboardData();
    } finally {
      setRefreshing(false);
    }
  };

  const formatPrice = (price: number | string | undefined) => {
    // Use the service's formatPrice function directly - it has the proper logic
    return listingsService.formatPrice(price);
  };

  const getListingImage = (listing: any) => {
    if (listing.images && listing.images.length > 0) {
      return { uri: listing.images[0] };
    }
    return { uri: "https://placehold.co/300x200/e0e0e0/333333?text=No+Image" };
  };

  return (
    <SafeAreaView style={styles.container}>
      <Header user={user} />
      <ScrollView
        style={styles.scrollContainer}
        contentContainerStyle={styles.dashboardContainer}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            tintColor={THEME.primary}
            colors={[THEME.primary]}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Search Bar */}
        <Animated.View
          style={styles.searchContainer}
          entering={FadeInUp.duration(600).delay(200)}
        >
          <View style={styles.searchInputContainer}>
            <Ionicons
              name="search"
              size={20}
              color={THEME.gray}
              style={styles.searchIcon}
            />
            <TextInput
              style={styles.searchInput}
              placeholder="Search for properties..."
              placeholderTextColor={THEME.gray}
              value={searchQuery}
              onChangeText={setSearchQuery}
              onSubmitEditing={handleSearch}
              returnKeyType="search"
            />
          </View>
          <TouchableOpacity
            onPress={handleSearch}
            style={styles.searchButton}
            activeOpacity={0.8}
          >
            <LinearGradient
              colors={[THEME.accent, THEME.secondary]}
              style={styles.searchButtonGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
            >
              <Ionicons name="search" size={20} color={THEME.light} />
            </LinearGradient>
          </TouchableOpacity>
        </Animated.View>

        {/* Quick Stats */}
        <Animated.View
          style={styles.statsContainer}
          entering={FadeInUp.duration(600).delay(400)}
        >
          <Animated.View
            style={styles.statCard}
            entering={SlideInRight.duration(600).delay(400)}
          >
            <LinearGradient
              colors={["rgba(67, 97, 238, 0.1)", "rgba(67, 97, 238, 0.05)"]}
              style={styles.statCardGradient}
            >
              <View style={styles.statIconContainer}>
                <Ionicons name="home" size={24} color={THEME.primary} />
              </View>
              {quickStatsLoading ? (
                <ActivityIndicator
                  size="small"
                  color={THEME.primary}
                  style={{ height: 32 }}
                />
              ) : (
                <Text style={[styles.statNumber, { color: THEME.primary }]}>
                  {quickStats.totalListings.toLocaleString()}
                </Text>
              )}
              <Text style={styles.statLabel}>Total Listings</Text>
            </LinearGradient>
          </Animated.View>

          <Animated.View
            style={styles.statCard}
            entering={SlideInRight.duration(600).delay(500)}
          >
            <LinearGradient
              colors={["rgba(247, 37, 133, 0.1)", "rgba(247, 37, 133, 0.05)"]}
              style={styles.statCardGradient}
            >
              <View style={styles.statIconContainer}>
                <Ionicons name="trending-up" size={24} color={THEME.accent} />
              </View>
              {quickStatsLoading ? (
                <ActivityIndicator
                  size="small"
                  color={THEME.accent}
                  style={{ height: 32 }}
                />
              ) : (
                <Text style={[styles.statNumber, { color: THEME.accent }]}>
                  {quickStats.averagePrice > 0
                    ? `€${Math.round(quickStats.averagePrice).toLocaleString()}`
                    : "-"}
                </Text>
              )}
              <Text style={styles.statLabel}>Avg Price</Text>
            </LinearGradient>
          </Animated.View>

          <Animated.View
            style={styles.statCard}
            entering={SlideInRight.duration(600).delay(600)}
          >
            <LinearGradient
              colors={["rgba(16, 185, 129, 0.1)", "rgba(16, 185, 129, 0.05)"]}
              style={styles.statCardGradient}
            >
              <View style={styles.statIconContainer}>
                <Ionicons name="add-circle" size={24} color={THEME.success} />
              </View>
              {quickStatsLoading ? (
                <ActivityIndicator
                  size="small"
                  color={THEME.success}
                  style={{ height: 32 }}
                />
              ) : (
                <Text style={[styles.statNumber, { color: THEME.success }]}>
                  {quickStats.newToday}
                </Text>
              )}
              <Text style={styles.statLabel}>New Today</Text>
            </LinearGradient>
          </Animated.View>
        </Animated.View>

        {/* Autonomous Status */}
        <Animated.View entering={FadeInUp.duration(600).delay(700)}>
          <AutonomousStatusIndicator
            showControls={true}
            onPress={() => router.push("/autonomous-status")}
          />
        </Animated.View>

        {/* Error Display */}
        {error && (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>{error}</Text>
            <TouchableOpacity onPress={clearError} style={styles.errorButton}>
              <Text style={styles.errorButtonText}>Dismiss</Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Fallback Message Display */}
        {fallbackMessage && !error && (
          <Animated.View
            style={styles.fallbackMessageContainer}
            entering={FadeInUp.duration(400)}
          >
            <View style={styles.fallbackMessageContent}>
              <Ionicons
                name="information-circle"
                size={20}
                color={THEME.warning}
              />
              <Text style={styles.fallbackMessageText}>{fallbackMessage}</Text>
            </View>
            <TouchableOpacity
              onPress={clearFallbackMessage}
              style={styles.fallbackMessageButton}
            >
              <Ionicons name="close" size={16} color={THEME.warning} />
            </TouchableOpacity>
          </Animated.View>
        )}

        {/* Loading State */}
        {isLoading && displayListings.length === 0 && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#f72585" />
            <Text style={styles.loadingText}>Loading listings...</Text>
          </View>
        )}

        {/* Empty State */}
        {!isLoading && displayListings.length === 0 && !error && (
          <View style={styles.emptyContainer}>
            <Ionicons name="home-outline" size={64} color="#d1d5db" />
            <Text style={styles.emptyTitle}>No listings found</Text>
            <Text style={styles.emptyText}>
              We couldn&apos;t find any listings at the moment. Try refreshing
              or check back later.
            </Text>
            <TouchableOpacity
              onPress={handleRefresh}
              style={styles.refreshButton}
            >
              <Ionicons
                name="refresh"
                size={16}
                color="#ffffff"
                style={{ marginRight: 8 }}
              />
              <Text style={styles.refreshButtonText}>Refresh</Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Section Title */}
        <Animated.View
          style={styles.sectionHeader}
          entering={FadeInUp.duration(600).delay(900)}
        >
          <Text style={styles.sectionTitle}>
            {searchQuery
              ? `Search Results`
              : showPreferenceBasedListings &&
                user?.preferences &&
                (user.preferences.maxPrice > 0 ||
                  (user.preferences.preferredLocations?.length ?? 0) > 0 ||
                  (user.preferences.propertyTypes?.length ?? 0) > 0)
              ? "Recommended for You"
              : "Recent Listings"}
          </Text>
          {searchQuery && (
            <TouchableOpacity
              onPress={() => {
                setSearchQuery("");
                loadDashboardData();
              }}
              style={styles.clearSearchButton}
              activeOpacity={0.8}
            >
              <Text style={styles.clearSearchText}>Clear</Text>
            </TouchableOpacity>
          )}
        </Animated.View>

        {/* Listing Type Toggle */}
        {!searchQuery &&
          user?.preferences &&
          (user.preferences.maxPrice > 0 ||
            (user.preferences.preferredLocations?.length ?? 0) > 0 ||
            (user.preferences.propertyTypes?.length ?? 0) > 0) && (
            <Animated.View
              style={styles.toggleContainer}
              entering={FadeInUp.duration(600).delay(950)}
            >
              <TouchableOpacity
                style={[
                  styles.toggleButton,
                  showPreferenceBasedListings && styles.toggleButtonActive,
                ]}
                onPress={() => {
                  setShowPreferenceBasedListings(true);
                  clearFallbackMessage();
                  loadDashboardData();
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                }}
                activeOpacity={0.8}
              >
                <Ionicons
                  name="heart"
                  size={16}
                  color={showPreferenceBasedListings ? THEME.light : THEME.gray}
                />
                <Text
                  style={[
                    styles.toggleButtonText,
                    showPreferenceBasedListings &&
                      styles.toggleButtonTextActive,
                  ]}
                >
                  For You
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.toggleButton,
                  !showPreferenceBasedListings && styles.toggleButtonActive,
                ]}
                onPress={() => {
                  setShowPreferenceBasedListings(false);
                  clearFallbackMessage();
                  fetchRecentListings(10);
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                }}
                activeOpacity={0.8}
              >
                <Ionicons
                  name="time"
                  size={16}
                  color={
                    !showPreferenceBasedListings ? THEME.light : THEME.gray
                  }
                />
                <Text
                  style={[
                    styles.toggleButtonText,
                    !showPreferenceBasedListings &&
                      styles.toggleButtonTextActive,
                  ]}
                >
                  Recent
                </Text>
              </TouchableOpacity>
            </Animated.View>
          )}

        {/* Listings Grid */}
        {displayListings.length > 0 && (
          <View style={styles.listingsContainer}>
            {displayListings.map((listing) => {
              return (
                <Animated.View
                  key={listing._id}
                  entering={FadeInUp.duration(600).delay(
                    1000 + displayListings.indexOf(listing) * 100
                  )}
                >
                  <TouchableOpacity
                    style={styles.listingCard}
                    onPress={() => {
                      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                      router.push(`/listing-details?id=${listing._id}`);
                    }}
                    activeOpacity={0.95}
                  >
                    <View style={styles.listingImageContainer}>
                      <Image
                        source={getListingImage(listing)}
                        style={styles.listingImage}
                        defaultSource={{
                          uri: "https://placehold.co/300x200/e0e0e0/333333?text=Loading...",
                        }}
                      />
                      <LinearGradient
                        colors={["transparent", "rgba(0,0,0,0.3)"]}
                        style={styles.imageOverlay}
                      />
                      <TouchableOpacity
                        style={styles.favoriteButton}
                        onPress={(e) => {
                          e.stopPropagation();
                          Haptics.impactAsync(
                            Haptics.ImpactFeedbackStyle.Light
                          );
                          Alert.alert(
                            "Favorite",
                            "Favorite functionality coming soon!"
                          );
                        }}
                        activeOpacity={0.8}
                      >
                        <View style={styles.favoriteButtonInner}>
                          <Ionicons
                            name="heart-outline"
                            size={18}
                            color={THEME.dark}
                          />
                        </View>
                      </TouchableOpacity>
                    </View>
                    <View style={styles.listingContent}>
                      <View style={styles.listingHeader}>
                        <Text style={styles.listingTitle} numberOfLines={2}>
                          {listing.title}
                        </Text>
                        <View style={styles.priceContainer}>
                          <Text style={styles.listingPrice}>
                            {formatPrice(listing.price)}
                          </Text>
                        </View>
                      </View>
                      <View style={styles.listingFooter}>
                        <View style={styles.listingDetailsContainer}>
                          <View style={styles.locationContainer}>
                            <Ionicons
                              name="location"
                              size={14}
                              color={THEME.accent}
                            />
                            <Text
                              style={styles.listingDetails}
                              numberOfLines={1}
                            >
                              {typeof listing.location === "string"
                                ? listing.location
                                : listing.location?.city || "Unknown location"}
                            </Text>
                          </View>
                          <View style={styles.roomsContainer}>
                            <Ionicons
                              name="bed"
                              size={14}
                              color={THEME.primary}
                            />
                            <Text style={styles.listingDetails}>
                              {listing.rooms || listing.bedrooms || "N/A"} rooms
                            </Text>
                          </View>
                        </View>
                        <View style={styles.sourceContainer}>
                          <Text style={styles.listingSource}>
                            via {listing.source}
                          </Text>
                        </View>
                      </View>
                    </View>
                  </TouchableOpacity>
                </Animated.View>
              );
            })}
          </View>
        )}
      </ScrollView>

      {/* Bottom Navigation */}
      <LinearGradient
        colors={["rgba(255, 255, 255, 0.95)", "rgba(255, 255, 255, 1)"]}
        style={styles.bottomNavigationGradient}
      >
        <View style={styles.bottomNavigation}>
          <TouchableOpacity
            onPress={() => {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              router.push("/dashboard");
            }}
            style={[styles.navButton, styles.navButtonActive]}
            activeOpacity={0.8}
          >
            <View
              style={[styles.navIconContainer, styles.navIconContainerActive]}
            >
              <Ionicons name="home" size={22} color={THEME.light} />
            </View>
            <Text style={[styles.navLabel, styles.navLabelActive]}>Home</Text>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() => {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              router.push("/auto-application-dashboard");
            }}
            style={styles.navButton}
            activeOpacity={0.8}
          >
            <View style={styles.navIconContainer}>
              <Ionicons name="flash-outline" size={22} color={THEME.gray} />
            </View>
            <Text style={styles.navLabel}>Auto-App</Text>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() => {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              router.push("/settings");
            }}
            style={styles.navButton}
            activeOpacity={0.8}
          >
            <View style={styles.navIconContainer}>
              <Ionicons name="settings-outline" size={22} color={THEME.gray} />
            </View>
            <Text style={styles.navLabel}>Settings</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.navButton}
            onPress={() => {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              router.push("/profile");
            }}
            activeOpacity={0.8}
          >
            <View style={styles.navIconContainer}>
              <Ionicons name="person-outline" size={22} color={THEME.gray} />
            </View>
            <Text style={styles.navLabel}>Profile</Text>
          </TouchableOpacity>
        </View>
      </LinearGradient>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: THEME.lightGray,
  },
  header: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  headerContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  headerLeft: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  logoContainer: {
    width: 48,
    height: 48,
    backgroundColor: THEME.light,
    borderRadius: 24,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  logoText: {
    fontSize: 18,
    fontWeight: "800",
    color: THEME.primary,
  },
  headerTextContainer: {
    flex: 1,
  },
  greetingText: {
    fontSize: 14,
    color: "rgba(255, 255, 255, 0.8)",
    fontWeight: "500",
  },
  userNameText: {
    fontSize: 24,
    fontWeight: "700",
    color: THEME.light,
    marginTop: 2,
  },
  profileButton: {
    padding: 4,
  },
  profileAvatar: {
    width: 40,
    height: 40,
    backgroundColor: THEME.light,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  scrollContainer: {
    flex: 1,
  },
  dashboardContainer: {
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 100,
  },

  // Listings styles
  listingsContainer: {
    gap: 20,
  },
  listingCard: {
    backgroundColor: THEME.light,
    borderRadius: 20,
    overflow: "hidden",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
  },
  listingImageContainer: {
    position: "relative",
    height: 200,
  },
  listingImage: {
    width: "100%",
    height: "100%",
  },
  imageOverlay: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    height: 60,
  },
  favoriteButton: {
    position: "absolute",
    top: 16,
    right: 16,
  },
  favoriteButtonInner: {
    backgroundColor: "rgba(255, 255, 255, 0.95)",
    borderRadius: 20,
    width: 36,
    height: 36,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  listingContent: {
    padding: 20,
  },
  listingHeader: {
    marginBottom: 16,
  },
  listingTitle: {
    fontSize: 18,
    fontWeight: "700",
    color: THEME.dark,
    marginBottom: 8,
    lineHeight: 24,
  },
  priceContainer: {
    backgroundColor: "rgba(247, 37, 133, 0.1)",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
    alignSelf: "flex-start",
  },
  listingPrice: {
    fontSize: 20,
    fontWeight: "800",
    color: THEME.accent,
  },
  listingFooter: {
    gap: 12,
  },
  listingDetailsContainer: {
    flexDirection: "row",
    gap: 16,
  },
  locationContainer: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
    gap: 6,
  },
  roomsContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
  },
  listingDetails: {
    fontSize: 14,
    color: THEME.gray,
    fontWeight: "500",
  },
  sourceContainer: {
    alignSelf: "flex-end",
  },
  listingSource: {
    fontSize: 12,
    color: THEME.gray,
    fontStyle: "italic",
    opacity: 0.8,
  },

  // Error and Loading states
  errorContainer: {
    backgroundColor: "rgba(239, 68, 68, 0.1)",
    borderColor: "rgba(239, 68, 68, 0.2)",
    borderWidth: 1,
    borderRadius: 16,
    padding: 16,
    marginBottom: 20,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  errorText: {
    color: THEME.danger,
    fontSize: 14,
    flex: 1,
    fontWeight: "500",
  },
  errorButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: THEME.danger,
    borderRadius: 8,
  },
  errorButtonText: {
    color: THEME.light,
    fontSize: 12,
    fontWeight: "600",
  },
  fallbackMessageContainer: {
    backgroundColor: "rgba(245, 158, 11, 0.1)",
    borderColor: "rgba(245, 158, 11, 0.3)",
    borderWidth: 1,
    borderRadius: 16,
    padding: 16,
    marginBottom: 20,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  fallbackMessageContent: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
    gap: 12,
  },
  fallbackMessageText: {
    color: THEME.warning,
    fontSize: 14,
    flex: 1,
    fontWeight: "500",
    lineHeight: 20,
  },
  fallbackMessageButton: {
    padding: 4,
    borderRadius: 12,
    backgroundColor: "rgba(245, 158, 11, 0.1)",
  },
  loadingContainer: {
    alignItems: "center",
    justifyContent: "center",
    padding: 60,
    backgroundColor: THEME.light,
    borderRadius: 20,
    marginVertical: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: THEME.gray,
    fontWeight: "500",
  },
  emptyContainer: {
    alignItems: "center",
    justifyContent: "center",
    padding: 60,
    backgroundColor: THEME.light,
    borderRadius: 20,
    marginVertical: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: "700",
    color: THEME.dark,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 14,
    color: THEME.gray,
    textAlign: "center",
    marginBottom: 20,
    lineHeight: 20,
    fontWeight: "500",
  },
  refreshButton: {
    backgroundColor: THEME.accent,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 12,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  refreshButtonText: {
    color: THEME.light,
    fontSize: 14,
    fontWeight: "600",
  },
  // Bottom Navigation styles
  bottomNavigationGradient: {
    borderTopWidth: 1,
    borderTopColor: "rgba(0, 0, 0, 0.05)",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 12,
  },
  bottomNavigation: {
    flexDirection: "row",
    justifyContent: "space-around",
    paddingVertical: 20,
    paddingHorizontal: 16,
    paddingBottom: 24,
  },
  navButton: {
    alignItems: "center",
    paddingVertical: 8,
    paddingHorizontal: 8,
    borderRadius: 16,
    minWidth: 70,
  },
  navButtonActive: {
    transform: [{ scale: 1.05 }],
  },
  navIconContainer: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: "rgba(107, 114, 128, 0.1)",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 6,
  },
  navIconContainerActive: {
    backgroundColor: THEME.accent,
    shadowColor: THEME.accent,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  navLabel: {
    fontSize: 11,
    color: THEME.gray,
    fontWeight: "600",
    textAlign: "center",
  },
  navLabelActive: {
    color: THEME.accent,
    fontWeight: "700",
  },
  // Search styles
  searchContainer: {
    flexDirection: "row",
    marginBottom: 24,
    gap: 12,
  },
  searchInputContainer: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: THEME.light,
    borderRadius: 16,
    paddingHorizontal: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  searchIcon: {
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    paddingVertical: 16,
    fontSize: 16,
    color: THEME.dark,
  },
  searchButton: {
    borderRadius: 16,
    overflow: "hidden",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  searchButtonGradient: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    justifyContent: "center",
    alignItems: "center",
  },

  // Stats styles
  statsContainer: {
    flexDirection: "row",
    marginBottom: 24,
    gap: 12,
  },
  statCard: {
    flex: 1,
    borderRadius: 16,
    overflow: "hidden",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  statCardGradient: {
    padding: 20,
    alignItems: "center",
    backgroundColor: THEME.light,
  },
  statIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: "rgba(255, 255, 255, 0.8)",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 12,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: "800",
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: THEME.gray,
    textAlign: "center",
    fontWeight: "600",
  },
  // Section styles
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20,
    paddingHorizontal: 4,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: "800",
    color: THEME.dark,
  },
  clearSearchButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: THEME.light,
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  clearSearchText: {
    fontSize: 14,
    color: THEME.primary,
    fontWeight: "600",
  },
  toggleContainer: {
    flexDirection: "row",
    backgroundColor: THEME.lightGray,
    borderRadius: 12,
    padding: 4,
    marginBottom: 20,
  },
  toggleButton: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    gap: 8,
  },
  toggleButtonActive: {
    backgroundColor: THEME.primary,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  toggleButtonText: {
    fontSize: 14,
    fontWeight: "600",
    color: THEME.gray,
  },
  toggleButtonTextActive: {
    color: THEME.light,
  },
  // Quick Actions styles
  quickActionsContainer: {
    flexDirection: "row",
    marginBottom: 24,
    justifyContent: "space-between",
    paddingHorizontal: 4,
  },
  quickActionWrapper: {
    flex: 1,
    marginHorizontal: 6,
  },
  quickActionButton: {
    backgroundColor: THEME.light,
    borderRadius: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  quickActionContent: {
    padding: 18,
    alignItems: "center",
    minHeight: 85,
    justifyContent: "center",
  },
  quickActionIcon: {
    width: 34,
    height: 34,
    borderRadius: 17,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 9,
  },
  quickActionText: {
    fontSize: 12,
    color: THEME.dark,
    fontWeight: "600",
    textAlign: "center",
  },
});
