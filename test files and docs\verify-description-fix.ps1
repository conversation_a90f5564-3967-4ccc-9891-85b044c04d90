#!/usr/bin/env pwsh

# Verification script for Huurwoningen description extraction fix
# Usage: .\verify-description-fix.ps1

Write-Host "🔍 Verifying Huurwoningen Description Fix" -ForegroundColor Cyan
Write-Host "=========================================" -ForegroundColor Cyan

# Check if Node.js is available
if (-not (Get-Command node -ErrorAction SilentlyContinue)) {
    Write-Host "❌ Node.js is not installed or not in PATH" -ForegroundColor Red
    exit 1
}

# Check if the verification file exists
if (-not (Test-Path "verify-description-fix.js")) {
    Write-Host "❌ Verification file not found: verify-description-fix.js" -ForegroundColor Red
    exit 1
}

try {
    Write-Host "🚀 Running verification..." -ForegroundColor Green
    
    # Run the verification script
    $result = node verify-description-fix.js
    
    # Display the result
    Write-Host $result
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "`n✅ Verification completed successfully!" -ForegroundColor Green
        Write-Host "🎯 The huurwoningen description fix has been properly implemented." -ForegroundColor Green
        
        Write-Host "`n📋 Quick Test Available:" -ForegroundColor Cyan
        Write-Host "Run the following command to test the extraction logic:" -ForegroundColor White
        Write-Host "cd zakmakelaar-backend && node test-description-extraction-simple.js" -ForegroundColor Yellow
        
    } else {
        Write-Host "`n❌ Verification failed with exit code: $LASTEXITCODE" -ForegroundColor Red
        Write-Host "Some components may be missing or incomplete." -ForegroundColor Red
    }
    
} catch {
    Write-Host "💥 Error running verification: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "`n🎉 Summary:" -ForegroundColor Cyan
Write-Host "The huurwoningen.nl scraper has been enhanced with:" -ForegroundColor White
Write-Host "- Multi-selector description extraction (20+ selectors)" -ForegroundColor White
Write-Host "- JSON-LD structured data fallback" -ForegroundColor White
Write-Host "- Meta tag description fallback" -ForegroundColor White
Write-Host "- Content quality filtering" -ForegroundColor White
Write-Host "- Comprehensive test suite" -ForegroundColor White

exit $LASTEXITCODE