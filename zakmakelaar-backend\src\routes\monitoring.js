const express = require("express");
const router = express.Router();
const { getScrapingMetrics } = require("../services/scraper");
const { catchAsync } = require("../middleware/errorHandler");

/**
 * @swagger
 * /api/monitoring/health:
 *   get:
 *     summary: Get scraper health status
 *     tags: [Monitoring]
 *     responses:
 *       200:
 *         description: Health status retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   enum: [healthy, degraded, unhealthy]
 *                 uptime:
 *                   type: number
 *                 timestamp:
 *                   type: string
 *                 metrics:
 *                   type: object
 */
router.get(
  "/health",
  catchAsync(async (req, res) => {
    const metrics = getScrapingMetrics();
    const uptime = process.uptime();

    // Determine health status based on metrics
    let status = "healthy";
    const successRate = parseFloat(metrics.successRate);

    if (successRate < 50) {
      status = "unhealthy";
    } else if (successRate < 80) {
      status = "degraded";
    }

    // Check if last scrape was too long ago (more than 1 hour)
    if (metrics.lastScrapeTime) {
      const timeSinceLastScrape =
        Date.now() - new Date(metrics.lastScrapeTime).getTime();
      if (timeSinceLastScrape > 3600000) {
        // 1 hour
        status = "degraded";
      }
    }

    res.json({
      status,
      uptime: Math.floor(uptime),
      timestamp: new Date().toISOString(),
      metrics: {
        totalScrapes: metrics.totalScrapes,
        successRate: metrics.successRate,
        lastScrapeTime: metrics.lastScrapeTime,
        errorsByType: metrics.errorsByType,
      },
    });
  })
);

/**
 * @swagger
 * /api/monitoring/metrics:
 *   get:
 *     summary: Get detailed scraping metrics
 *     tags: [Monitoring]
 *     responses:
 *       200:
 *         description: Metrics retrieved successfully
 */
router.get(
  "/metrics",
  catchAsync(async (req, res) => {
    const metrics = getScrapingMetrics();

    res.json({
      status: "success",
      data: {
        ...metrics,
        performance: {
          averageScrapingTime: metrics.averageScrapingTimeFormatted || "0s",
          listingsPerScrape:
            metrics.totalScrapes > 0
              ? (metrics.totalListingsFound / metrics.totalScrapes).toFixed(2)
              : "0",
          duplicateRate:
            metrics.totalListingsFound > 0 && metrics.duplicatesSkipped > 0
              ? (
                  (metrics.duplicatesSkipped /
                    (metrics.totalListingsFound + metrics.duplicatesSkipped)) *
                  100
                ).toFixed(2) + "%"
              : "0%",
        },
      },
    });
  })
);

/**
 * @swagger
 * /api/monitoring/dashboard:
 *   get:
 *     summary: Get dashboard data for monitoring UI
 *     tags: [Monitoring]
 *     responses:
 *       200:
 *         description: Dashboard data retrieved successfully
 */
router.get(
  "/dashboard",
  catchAsync(async (req, res) => {
    const metrics = getScrapingMetrics();
    const uptime = process.uptime();

    // Calculate additional dashboard metrics
    const successRate = parseFloat(metrics.successRate);
    const avgListingsPerScrape =
      metrics.totalScrapes > 0
        ? (metrics.totalListingsFound / metrics.totalScrapes).toFixed(1)
        : "0";

    const duplicateRate =
      metrics.totalListingsFound > 0
        ? (
            (metrics.duplicatesSkipped /
              (metrics.totalListingsFound + metrics.duplicatesSkipped)) *
            100
          ).toFixed(1)
        : "0";

    // Determine status indicators
    const indicators = {
      overall:
        successRate >= 80 ? "good" : successRate >= 50 ? "warning" : "critical",
      performance:
        parseFloat(metrics.averageScrapingTime) < 120000 ? "good" : "warning", // 2 minutes
      duplicates: parseFloat(duplicateRate) < 50 ? "good" : "warning",
    };

    // Recent activity summary
    const recentActivity = {
      lastScrapeTime: metrics.lastScrapeTime,
      timeSinceLastScrape: metrics.lastScrapeTime
        ? Math.floor(
            (Date.now() - new Date(metrics.lastScrapeTime).getTime()) /
              1000 /
              60
          ) // minutes
        : null,
      recentErrors: Object.entries(metrics.errorsByType)
        .sort(([, a], [, b]) => b - a)
        .slice(0, 5)
        .map(([type, count]) => ({ type, count })),
    };

    res.json({
      status: "success",
      data: {
        overview: {
          totalScrapes: metrics.totalScrapes,
          successfulScrapes: metrics.successfulScrapes,
          failedScrapes: metrics.failedScrapes,
          successRate: successRate + "%",
          uptime: Math.floor(uptime),
        },
        performance: {
          averageScrapingTime: metrics.averageScrapingTimeFormatted || "0s",
          avgListingsPerScrape,
          totalListingsFound: metrics.totalListingsFound || 0,
          totalListingsSaved: metrics.totalListingsSaved || 0,
          duplicatesSkipped: metrics.duplicatesSkipped || 0,
          duplicateRate: duplicateRate + "%",
        },
        // Per-site metrics
        siteMetrics: metrics.siteMetrics || {},
        indicators,
        recentActivity,
        charts: {
          errorDistribution: Object.entries(metrics.errorsByType).map(
            ([type, count]) => ({
              label: type,
              value: count,
            })
          ),
          successVsFailure: [
            { label: "Successful", value: metrics.successfulScrapes },
            { label: "Failed", value: metrics.failedScrapes },
          ],
          // Site-specific charts
          siteListings: Object.entries(metrics.siteMetrics || {}).map(
            ([site, data]) => ({
              label: site.charAt(0).toUpperCase() + site.slice(1),
              value: data.listingsFound || 0,
            })
          ),
        },
      },
    });
  })
);

/**
 * @swagger
 * /api/monitoring/alerts:
 *   get:
 *     summary: Get current system alerts
 *     tags: [Monitoring]
 *     responses:
 *       200:
 *         description: Alerts retrieved successfully
 */
router.get(
  "/alerts",
  catchAsync(async (req, res) => {
    const metrics = getScrapingMetrics();
    const alerts = [];

    const successRate = parseFloat(metrics.successRate);

    // Check for various alert conditions
    if (successRate < 50) {
      alerts.push({
        level: "critical",
        message: `Success rate is critically low: ${successRate}%`,
        timestamp: new Date().toISOString(),
        type: "performance",
      });
    } else if (successRate < 80) {
      alerts.push({
        level: "warning",
        message: `Success rate is below optimal: ${successRate}%`,
        timestamp: new Date().toISOString(),
        type: "performance",
      });
    }

    // Check if scraper hasn't run recently
    if (metrics.lastScrapeTime) {
      const timeSinceLastScrape =
        Date.now() - new Date(metrics.lastScrapeTime).getTime();
      if (timeSinceLastScrape > 3600000) {
        // 1 hour
        alerts.push({
          level: "warning",
          message: `No scraping activity for ${Math.floor(
            timeSinceLastScrape / 1000 / 60
          )} minutes`,
          timestamp: new Date().toISOString(),
          type: "activity",
        });
      }
    }

    // Check for high error rates
    const totalErrors = Object.values(metrics.errorsByType).reduce(
      (sum, count) => sum + count,
      0
    );
    if (totalErrors > metrics.totalScrapes * 0.5) {
      alerts.push({
        level: "warning",
        message: `High error rate detected: ${totalErrors} errors in ${metrics.totalScrapes} scrapes`,
        timestamp: new Date().toISOString(),
        type: "errors",
      });
    }

    res.json({
      status: "success",
      data: {
        alerts,
        alertCount: alerts.length,
        criticalCount: alerts.filter((a) => a.level === "critical").length,
        warningCount: alerts.filter((a) => a.level === "warning").length,
      },
    });
  })
);

/**
 * @swagger
 * /api/monitoring/test-data:
 *   get:
 *     summary: Populate test data for dashboard verification
 *     tags: [Monitoring]
 *     responses:
 *       200:
 *         description: Test data populated successfully
 */
router.get(
  "/test-data",
  catchAsync(async (req, res) => {
    const { scrapingMetrics } = require("../services/scraperUtils");
    scrapingMetrics.populateTestData();
    
    res.json({
      status: "success",
      message: "Test data populated successfully",
      data: scrapingMetrics.getMetrics()
    });
  })
);

/**
 * @swagger
 * /api/monitoring/reset-metrics:
 *   post:
 *     summary: Reset scraping metrics to zero
 *     tags: [Monitoring]
 *     responses:
 *       200:
 *         description: Metrics reset successfully
 */
router.post(
  "/reset-metrics",
  catchAsync(async (req, res) => {
    const { scrapingMetrics } = require("../services/scraperUtils");
    scrapingMetrics.resetMetrics();
    
    res.json({
      status: "success",
      message: "Metrics reset successfully",
      data: scrapingMetrics.getMetrics()
    });
  })
);

/**
 * @swagger
 * /api/monitoring/database-counts:
 *   get:
 *     summary: Get actual database listing counts by site
 *     tags: [Monitoring]
 *     responses:
 *       200:
 *         description: Database counts retrieved successfully
 */
router.get(
  "/database-counts",
  catchAsync(async (req, res) => {
    const Listing = require("../models/Listing");
    
    try {
      // Get total count
      const totalCount = await Listing.countDocuments();
      
      // Get counts by source (the actual field name in the database)
      const siteCounts = await Listing.aggregate([
        {
          $group: {
            _id: "$source",
            count: { $sum: 1 }
          }
        }
      ]);
      
      // Format the results
      const siteCountsFormatted = {
        funda: 0,
        pararius: 0,
        huurwoningen: 0
      };
      
      siteCounts.forEach(item => {
        if (item._id) {
          let sourceName = item._id.toLowerCase();
          // Handle .nl suffix in source names
          if (sourceName.endsWith('.nl')) {
            sourceName = sourceName.replace('.nl', '');
          }
          if (siteCountsFormatted.hasOwnProperty(sourceName)) {
            siteCountsFormatted[sourceName] = item.count;
          }
        }
      });
      
      res.json({
        status: "success",
        data: {
          totalListings: totalCount,
          siteCounts: siteCountsFormatted,
          lastUpdated: new Date()
        }
      });
    } catch (error) {
      res.status(500).json({
        status: "error",
        message: "Failed to get database counts",
        error: error.message
      });
    }
  })
);

/**
 * @swagger
 * /api/monitoring/quick-stats:
 *   get:
 *     summary: Get comprehensive monitoring data for quick stats endpoint
 *     tags: [Monitoring]
 *     description: |
 *       Returns detailed monitoring information for the quick stats endpoint including:
 *       - Health status (overall, database, cache)
 *       - Performance metrics and thresholds
 *       - Cache connectivity and statistics
 *       - Database connectivity status
 *       - Last test execution results
 *     responses:
 *       200:
 *         description: Quick stats monitoring data retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/MonitoringData'
 *       500:
 *         description: Monitoring check failed
 */
router.get(
  "/quick-stats",
  catchAsync(async (req, res) => {
    const { logHelpers } = require("../services/logger");
    const cacheService = require("../services/cacheService");
    const searchService = require("../services/searchService");
    
    const startTime = Date.now();
    
    try {
      // Check cache status
      const cacheKey = 'quick-stats';
      const cacheExists = await cacheService.exists(cacheKey);
      const cachedData = cacheExists ? await cacheService.get(cacheKey) : null;
      
      // Get cache statistics
      const cacheStats = await cacheService.getStats();
      
      // Test database connectivity
      const Listing = require("../models/Listing");
      let dbHealth = "healthy";
      let dbError = null;
      let sampleCount = 0;
      
      try {
        sampleCount = await Listing.countDocuments().limit(1);
      } catch (error) {
        dbHealth = "unhealthy";
        dbError = error.message;
      }
      
      // Calculate performance metrics
      const testStartTime = Date.now();
      let testStats = null;
      let testError = null;
      
      try {
        testStats = await searchService.getQuickStats();
      } catch (error) {
        testError = error.message;
      }
      
      const testDuration = Date.now() - testStartTime;
      const totalDuration = Date.now() - startTime;
      
      // Determine overall health
      let overallHealth = "healthy";
      if (dbHealth === "unhealthy" || testError) {
        overallHealth = "unhealthy";
      } else if (testDuration > 1000 || !cacheExists) {
        overallHealth = "degraded";
      }
      
      // Log monitoring check
      logHelpers.logPerformance("quick-stats-monitoring", totalDuration, {
        overallHealth,
        dbHealth,
        cacheExists,
        testDuration: `${testDuration}ms`,
        hasTestError: !!testError
      });
      
      const monitoringData = {
        status: "success",
        data: {
          health: {
            overall: overallHealth,
            database: dbHealth,
            cache: cacheStats ? "healthy" : "unhealthy"
          },
          performance: {
            lastTestDuration: `${testDuration}ms`,
            monitoringDuration: `${totalDuration}ms`,
            threshold: "500ms",
            withinThreshold: testDuration <= 500
          },
          cache: {
            exists: cacheExists,
            hasData: !!cachedData,
            connected: cacheStats !== null,
            stats: cacheStats
          },
          database: {
            connected: dbHealth === "healthy",
            sampleCount,
            error: dbError
          },
          lastTest: {
            timestamp: new Date().toISOString(),
            success: !testError,
            error: testError,
            data: testStats ? {
              totalListings: testStats.totalListings,
              averagePrice: testStats.averagePrice,
              newToday: testStats.newToday,
              cached: testStats.cached,
              degraded: testStats.degraded
            } : null
          }
        },
        timestamp: new Date().toISOString()
      };
      
      res.json(monitoringData);
      
    } catch (error) {
      const duration = Date.now() - startTime;
      
      logHelpers.logPerformance("quick-stats-monitoring-error", duration, {
        error: error.message,
        stack: error.stack
      });
      
      res.status(500).json({
        status: "error",
        message: "Quick stats monitoring failed",
        error: error.message,
        timestamp: new Date().toISOString(),
        performance: {
          duration: `${duration}ms`
        }
      });
    }
  })
);

/**
 * @swagger
 * /api/monitoring/quick-stats/health:
 *   get:
 *     summary: Get health check for quick stats endpoint
 *     tags: [Monitoring]
 *     description: |
 *       Performs a health check on the quick stats endpoint by executing a test calculation
 *       and evaluating performance, errors, and response times against defined thresholds.
 *       
 *       **Health Status Levels:**
 *       - `healthy`: All systems operational, performance within thresholds
 *       - `degraded`: Some issues detected but service still functional
 *       - `unhealthy`: Critical issues detected, service may be impaired
 *     responses:
 *       200:
 *         description: Health check completed successfully (healthy or degraded)
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/HealthStatus'
 *       503:
 *         description: Service unhealthy (critical issues detected)
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: unhealthy
 *                 healthy:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: string
 *                   description: Error message describing the issue
 *                 performance:
 *                   type: object
 *                   properties:
 *                     responseTime:
 *                       type: string
 *                       description: Health check response time
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *                   description: Health check timestamp
 */
router.get(
  "/quick-stats/health",
  catchAsync(async (req, res) => {
    const { logHelpers } = require("../services/logger");
    const searchService = require("../services/searchService");
    
    const startTime = Date.now();
    
    try {
      // Perform a quick health check
      const stats = await searchService.getQuickStats();
      const duration = Date.now() - startTime;
      
      // Determine health status
      let status = "healthy";
      let issues = [];
      
      if (stats.error) {
        status = "unhealthy";
        issues.push(`Calculation error: ${stats.error}`);
      } else if (stats.degraded) {
        status = "degraded";
        if (stats.warnings) {
          issues.push(...stats.warnings);
        }
      }
      
      if (duration > 1000) {
        status = status === "healthy" ? "degraded" : status;
        issues.push(`Slow response time: ${duration}ms (threshold: 1000ms)`);
      }
      
      // Log health check
      logHelpers.logPerformance("quick-stats-health-check", duration, {
        status,
        issueCount: issues.length,
        cached: stats.cached
      });
      
      const healthData = {
        status,
        healthy: status === "healthy",
        performance: {
          responseTime: `${duration}ms`,
          withinThreshold: duration <= 1000,
          threshold: "1000ms"
        },
        data: {
          totalListings: stats.totalListings,
          averagePrice: stats.averagePrice,
          newToday: stats.newToday,
          cached: stats.cached
        },
        issues,
        timestamp: new Date().toISOString()
      };
      
      // Return appropriate HTTP status
      const httpStatus = status === "healthy" ? 200 : status === "degraded" ? 200 : 503;
      
      res.status(httpStatus).json(healthData);
      
    } catch (error) {
      const duration = Date.now() - startTime;
      
      logHelpers.logPerformance("quick-stats-health-check-error", duration, {
        error: error.message
      });
      
      res.status(503).json({
        status: "unhealthy",
        healthy: false,
        error: error.message,
        performance: {
          responseTime: `${duration}ms`
        },
        timestamp: new Date().toISOString()
      });
    }
  })
);

/**
 * @swagger
 * /api/monitoring/quick-stats/metrics:
 *   get:
 *     summary: Get detailed performance metrics for quick stats endpoint
 *     tags: [Monitoring]
 *     description: |
 *       Returns comprehensive performance metrics collected by the performance monitoring system.
 *       Includes request counts, response times, error rates, and threshold compliance data.
 *       
 *       **Metrics Include:**
 *       - Request count and frequency
 *       - Average response times
 *       - Error rates and counts
 *       - Slow request rates
 *       - Threshold compliance
 *     responses:
 *       200:
 *         description: Performance metrics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     quickStats:
 *                       type: object
 *                       description: Metrics specific to quick stats endpoint
 *                       properties:
 *                         count:
 *                           type: number
 *                           description: Total number of requests processed
 *                           example: 150
 *                         totalDuration:
 *                           type: number
 *                           description: Total processing time across all requests
 *                           example: 45000
 *                         errors:
 *                           type: number
 *                           description: Total number of errors encountered
 *                           example: 2
 *                         slowRequests:
 *                           type: number
 *                           description: Number of requests exceeding threshold
 *                           example: 5
 *                         averageDuration:
 *                           type: number
 *                           description: Average response time in milliseconds
 *                           example: 300
 *                         errorRate:
 *                           type: string
 *                           description: Error rate as percentage
 *                           example: "1.33"
 *                         slowRequestRate:
 *                           type: string
 *                           description: Slow request rate as percentage
 *                           example: "3.33"
 *                         threshold:
 *                           type: number
 *                           description: Performance threshold in milliseconds
 *                           example: 500
 *                     allEndpoints:
 *                       type: object
 *                       description: Metrics for all monitored endpoints
 *                       additionalProperties:
 *                         type: object
 *                         description: Metrics for individual endpoints
 *                     summary:
 *                       type: object
 *                       description: Overall system metrics summary
 *                       properties:
 *                         totalEndpoints:
 *                           type: number
 *                           description: Number of monitored endpoints
 *                         totalRequests:
 *                           type: number
 *                           description: Total requests across all endpoints
 *                         totalErrors:
 *                           type: number
 *                           description: Total errors across all endpoints
 *                         overallErrorRate:
 *                           type: string
 *                           description: Overall error rate as percentage
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *                   description: Metrics collection timestamp
 *       500:
 *         description: Failed to retrieve performance metrics
 */
router.get(
  "/quick-stats/metrics",
  catchAsync(async (req, res) => {
    const { performanceMonitor } = require("../utils/performanceMonitor");
    
    try {
      // Get performance metrics
      const quickStatsMetrics = performanceMonitor.getMetrics("quick-stats");
      const allMetrics = performanceMonitor.getAllMetrics();
      
      // Get current timestamp
      const timestamp = new Date().toISOString();
      
      const metricsData = {
        status: "success",
        data: {
          quickStats: quickStatsMetrics || {
            count: 0,
            totalDuration: 0,
            errors: 0,
            slowRequests: 0,
            averageDuration: 0,
            errorRate: "0.00",
            slowRequestRate: "0.00",
            threshold: 500
          },
          allEndpoints: allMetrics,
          summary: {
            totalEndpoints: Object.keys(allMetrics).length,
            totalRequests: Object.values(allMetrics).reduce((sum, metric) => sum + metric.count, 0),
            totalErrors: Object.values(allMetrics).reduce((sum, metric) => sum + metric.errors, 0),
            overallErrorRate: Object.keys(allMetrics).length > 0 
              ? (Object.values(allMetrics).reduce((sum, metric) => sum + metric.errors, 0) / 
                 Object.values(allMetrics).reduce((sum, metric) => sum + metric.count, 0) * 100).toFixed(2)
              : "0.00"
          }
        },
        timestamp
      };
      
      res.json(metricsData);
      
    } catch (error) {
      res.status(500).json({
        status: "error",
        message: "Failed to retrieve performance metrics",
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  })
);

module.exports = router;
