/**
 * Mapping Configuration Loader
 * 
 * This module provides utilities for loading and managing field mapping
 * configurations from various sources (JSON files, directories, etc.)
 * and integrating them with the FieldMappingRegistry.
 */

const fs = require('fs').promises;
const path = require('path');
const { FieldMappingRegistry } = require('./fieldMappingRegistry');

/**
 * Mapping Configuration Loader Class
 * Handles loading mapping configurations from various sources
 */
class MappingConfigLoader {
  constructor(registry = null) {
    this.registry = registry || new FieldMappingRegistry();
    this.configPaths = new Map(); // Track where configs were loaded from
  }

  /**
   * Set the registry instance
   * @param {FieldMappingRegistry} registry - Registry instance
   */
  setRegistry(registry) {
    if (!(registry instanceof FieldMappingRegistry)) {
      throw new Error('Registry must be an instance of FieldMappingRegistry');
    }
    this.registry = registry;
  }

  /**
   * Load configuration from a single JSON file
   * @param {string} filePath - Path to JSON configuration file
   * @param {string} source - Source identifier (optional, derived from filename if not provided)
   * @returns {Promise<Object>} Loaded configuration
   */
  async loadFromFile(filePath, source = null) {
    if (!this.registry) {
      throw new Error('Registry not set. Use setRegistry() first.');
    }

    const resolvedPath = path.resolve(filePath);
    const sourceId = source || path.basename(filePath, path.extname(filePath));

    try {
      const config = await this.registry.loadMappingFromFile(resolvedPath);
      this.registry.registerMapping(sourceId, config);
      this.configPaths.set(sourceId, resolvedPath);
      
      return {
        source: sourceId,
        config,
        path: resolvedPath
      };
    } catch (error) {
      throw new Error(`Failed to load mapping from ${filePath}: ${error.message}`);
    }
  }

  /**
   * Load configurations from multiple files
   * @param {Array<string|Object>} files - Array of file paths or {path, source} objects
   * @returns {Promise<Array>} Array of loaded configurations
   */
  async loadFromFiles(files) {
    const results = [];
    const errors = [];

    for (const file of files) {
      try {
        let filePath, source;
        
        if (typeof file === 'string') {
          filePath = file;
          source = null;
        } else if (typeof file === 'object' && file.path) {
          filePath = file.path;
          source = file.source || null;
        } else {
          throw new Error('Invalid file specification');
        }

        const result = await this.loadFromFile(filePath, source);
        results.push(result);
      } catch (error) {
        errors.push({
          file: typeof file === 'string' ? file : file.path,
          error: error.message
        });
      }
    }

    if (errors.length > 0) {
      console.warn('Some mapping files failed to load:', errors);
    }

    return results;
  }

  /**
   * Load all JSON files from a directory
   * @param {string} directoryPath - Path to directory containing mapping files
   * @param {Object} options - Loading options
   * @param {RegExp} options.pattern - File pattern to match (default: /\.json$/)
   * @param {boolean} options.recursive - Whether to search recursively (default: false)
   * @param {Function} options.sourceNameExtractor - Function to extract source name from file path
   * @returns {Promise<Array>} Array of loaded configurations
   */
  async loadFromDirectory(directoryPath, options = {}) {
    const {
      pattern = /\.json$/,
      recursive = false,
      sourceNameExtractor = (filePath) => path.basename(filePath, path.extname(filePath))
    } = options;

    if (!this.registry) {
      throw new Error('Registry not set. Use setRegistry() first.');
    }

    const resolvedPath = path.resolve(directoryPath);
    const files = await this._findFiles(resolvedPath, pattern, recursive);
    const results = [];
    const errors = [];

    for (const filePath of files) {
      try {
        const source = sourceNameExtractor(filePath);
        const result = await this.loadFromFile(filePath, source);
        results.push(result);
      } catch (error) {
        errors.push({
          file: filePath,
          error: error.message
        });
      }
    }

    if (errors.length > 0) {
      console.warn(`Some mapping files failed to load from ${directoryPath}:`, errors);
    }

    return results;
  }

  /**
   * Find files matching pattern in directory
   * @private
   */
  async _findFiles(directoryPath, pattern, recursive) {
    const files = [];
    
    try {
      const entries = await fs.readdir(directoryPath, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(directoryPath, entry.name);
        
        if (entry.isFile() && pattern.test(entry.name)) {
          files.push(fullPath);
        } else if (entry.isDirectory() && recursive) {
          const subFiles = await this._findFiles(fullPath, pattern, recursive);
          files.push(...subFiles);
        }
      }
    } catch (error) {
      throw new Error(`Failed to read directory ${directoryPath}: ${error.message}`);
    }

    return files;
  }

  /**
   * Load configuration from object
   * @param {string} source - Source identifier
   * @param {Object} config - Configuration object
   */
  loadFromObject(source, config) {
    if (!this.registry) {
      throw new Error('Registry not set. Use setRegistry() first.');
    }

    this.registry.registerMapping(source, config);
    this.configPaths.set(source, '<object>');
    
    return {
      source,
      config,
      path: '<object>'
    };
  }

  /**
   * Load default configurations for common scrapers
   * @returns {Promise<Array>} Array of loaded default configurations
   */
  async loadDefaults() {
    const defaultConfigs = this._getDefaultConfigurations();
    const results = [];

    for (const [source, config] of Object.entries(defaultConfigs)) {
      try {
        const result = this.loadFromObject(source, config);
        results.push(result);
      } catch (error) {
        console.warn(`Failed to load default config for ${source}:`, error.message);
      }
    }

    return results;
  }

  /**
   * Get default mapping configurations
   * @private
   */
  _getDefaultConfigurations() {
    return {
      funda: {
        // Basic fields
        title: 'title',
        description: 'description',
        url: 'url',
        source: { value: 'funda.nl' },
        
        // Property classification
        propertyType: {
          path: 'propertyType',
          transform: 'normalizePropertyType',
          default: 'woning'
        },
        
        // Price
        price: {
          path: 'price',
          transform: 'normalizePrice',
          default: 'Prijs op aanvraag'
        },
        
        // Location
        location: {
          path: 'location',
          transform: 'normalizeLocation',
          required: true
        },
        
        // Size information
        size: {
          path: 'size',
          transform: 'formatSizeString'
        },
        area: {
          path: 'size',
          transform: 'extractNumericSize'
        },
        
        // Room information
        rooms: {
          path: 'rooms',
          transform: 'normalizeRooms'
        },
        bedrooms: {
          path: 'bedrooms',
          transform: 'normalizeRooms'
        },
        bathrooms: {
          path: 'bathrooms',
          transform: 'normalizeRooms',
          default: "1"
        },
        
        // Build year
        year: {
          path: 'year',
          transform: 'normalizeYear'
        },
        
        // Interior
        interior: {
          path: 'interior',
          transform: 'normalizeInterior'
        },
        furnished: {
          path: 'interior',
          transform: 'inferFurnishedStatus'
        },
        
        // Features
        garden: {
          path: 'garden',
          transform: 'normalizeBoolean',
          default: false
        },
        balcony: {
          path: 'balcony',
          transform: 'normalizeBoolean',
          default: false
        },
        parking: {
          path: 'parking',
          transform: 'normalizeBoolean',
          default: false
        },
        
        // Energy label
        energyLabel: {
          path: 'energyLabel'
        },
        
        // Media
        images: {
          path: 'images',
          transform: 'normalizeImageArray',
          default: []
        },
        
        // Dates
        dateAdded: {
          transform: 'getCurrentISOString'
        },
        
        // Default values for frontend compatibility
        isActive: { value: true },
        pets: { value: false },
        smoking: { value: false },
        features: { value: [] },
        contactInfo: { value: {} }
      },

      huurwoningen: {
        // Basic fields
        title: 'title',
        description: 'description',
        url: 'url',
        source: { value: 'huurwoningen.nl' },
        
        // Property classification
        propertyType: {
          path: 'propertyType',
          transform: 'normalizePropertyType',
          default: 'woning'
        },
        
        // Price
        price: {
          path: 'price',
          transform: 'normalizePrice',
          default: 'Prijs op aanvraag'
        },
        
        // Location
        location: {
          path: 'location',
          transform: 'normalizeLocation',
          required: true
        },
        
        // Size information
        size: {
          path: 'size',
          transform: 'formatSizeString'
        },
        area: {
          path: 'size',
          transform: 'extractNumericSize'
        },
        
        // Room information
        rooms: {
          path: 'rooms',
          transform: 'normalizeRooms'
        },
        bedrooms: {
          path: 'bedrooms',
          transform: 'normalizeRooms'
        },
        bathrooms: {
          path: 'bathrooms',
          transform: 'normalizeRooms',
          default: "1"
        },
        
        // Build year
        year: {
          path: 'year',
          transform: 'normalizeYear'
        },
        
        // Interior
        interior: {
          path: 'interior',
          transform: 'normalizeInterior'
        },
        furnished: {
          path: 'interior',
          transform: 'inferFurnishedStatus'
        },
        
        // Features
        garden: {
          path: 'garden',
          transform: 'normalizeBoolean',
          default: false
        },
        balcony: {
          path: 'balcony',
          transform: 'normalizeBoolean',
          default: false
        },
        parking: {
          path: 'parking',
          transform: 'normalizeBoolean',
          default: false
        },
        
        // Energy label
        energyLabel: {
          path: 'energyLabel'
        },
        
        // Media
        images: {
          path: 'images',
          transform: 'normalizeImageArray',
          default: []
        },
        
        // Dates
        dateAdded: {
          transform: 'getCurrentISOString'
        },
        
        // Default values for frontend compatibility
        isActive: { value: true },
        pets: { value: false },
        smoking: { value: false },
        features: { value: [] },
        contactInfo: { value: {} }
      },

      pararius: {
        // Basic fields
        title: 'title',
        description: 'description',
        url: 'url',
        source: { value: 'pararius.nl' },
        
        // Property classification
        propertyType: {
          path: 'propertyType',
          transform: 'normalizePropertyType',
          default: 'woning'
        },
        
        // Price
        price: {
          path: 'price',
          transform: 'normalizePrice',
          default: 'Prijs op aanvraag'
        },
        
        // Location
        location: {
          path: 'location',
          transform: 'normalizeLocation',
          required: true
        },
        
        // Size information
        size: {
          path: 'size',
          transform: 'formatSizeString'
        },
        area: {
          path: 'size',
          transform: 'extractNumericSize'
        },
        
        // Room information
        rooms: {
          path: 'rooms',
          transform: 'normalizeRooms'
        },
        bedrooms: {
          path: 'bedrooms',
          transform: 'normalizeRooms'
        },
        bathrooms: {
          path: 'bathrooms',
          transform: 'normalizeRooms',
          default: "1"
        },
        
        // Build year
        year: {
          path: 'year',
          transform: 'normalizeYear'
        },
        
        // Interior
        interior: {
          path: 'interior',
          transform: 'normalizeInterior'
        },
        furnished: {
          path: 'interior',
          transform: 'inferFurnishedStatus'
        },
        
        // Features
        garden: {
          path: 'garden',
          transform: 'normalizeBoolean',
          default: false
        },
        balcony: {
          path: 'balcony',
          transform: 'normalizeBoolean',
          default: false
        },
        parking: {
          path: 'parking',
          transform: 'normalizeBoolean',
          default: false
        },
        
        // Energy label
        energyLabel: {
          path: 'energyLabel'
        },
        
        // Media
        images: {
          path: 'images',
          transform: 'normalizeImageArray',
          default: []
        },
        
        // Dates
        dateAdded: {
          transform: 'getCurrentISOString'
        },
        
        // Default values for frontend compatibility
        isActive: { value: true },
        pets: { value: false },
        smoking: { value: false },
        features: { value: [] },
        contactInfo: { value: {} }
      }
    };
  }

  /**
   * Reload configuration for a specific source
   * @param {string} source - Source identifier
   * @returns {Promise<Object>} Reloaded configuration
   */
  async reloadSource(source) {
    const configPath = this.configPaths.get(source);
    if (!configPath || configPath === '<object>') {
      throw new Error(`Cannot reload source ${source}: no file path available`);
    }

    return await this.loadFromFile(configPath, source);
  }

  /**
   * Get information about loaded configurations
   * @returns {Object} Configuration information
   */
  getLoadedConfigs() {
    const configs = {};
    for (const [source, path] of this.configPaths.entries()) {
      configs[source] = {
        path,
        fieldCount: Object.keys(this.registry.getAllMappings(source) || {}).length
      };
    }
    return configs;
  }

  /**
   * Validate all loaded configurations
   * @returns {Object} Validation results
   */
  validateAllConfigs() {
    const results = {};
    
    for (const source of this.registry.getRegisteredSources()) {
      const mappings = this.registry.getAllMappings(source);
      const validation = this.registry.validateMapping(mappings);
      
      results[source] = {
        valid: !validation.error,
        error: validation.error ? validation.error.message : null,
        path: this.configPaths.get(source)
      };
    }

    return results;
  }

  /**
   * Clear all loaded configurations
   */
  clear() {
    if (this.registry) {
      this.registry.clearAll();
    }
    this.configPaths.clear();
  }
}

module.exports = {
  MappingConfigLoader
};