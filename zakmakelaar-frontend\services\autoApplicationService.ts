import { apiService, ApiResponse } from './api';
import { Listing } from './listingsService';
import { User } from './authService';

// Types for Auto-Application System
export interface AutoApplicationSettings {
    _id?: string;
    userId: string;
    enabled: boolean;
    settings: {
        maxApplicationsPerDay: number;
        applicationTemplate: 'professional' | 'casual' | 'student' | 'expat';
        autoSubmit: boolean;
        requireManualReview: boolean;
        notificationPreferences: {
            immediate: boolean;
            daily: boolean;
            weekly: boolean;
        };
        language: 'english' | 'dutch';
    };
    criteria: {
        maxPrice?: number;
        minRooms?: number;
        maxRooms?: number;
        propertyTypes: string[];
        locations: string[];
        excludeKeywords: string[];
        includeKeywords: string[];
        minSize?: number;
        maxSize?: number;
        furnished?: boolean;
        petsAllowed?: boolean;
        smokingAllowed?: boolean;
    };
    personalInfo: {
        fullName: string;
        email: string;
        phone: string;
        dateOfBirth?: string;
        nationality?: string;
        occupation?: string;
        monthlyIncome?: number;
        employmentStatus?: 'employed' | 'self-employed' | 'student' | 'unemployed';
        employer?: string;
        moveInDate?: string;
        leaseDuration?: number;
        numberOfOccupants?: number;
        hasPets?: boolean;
        smokingAllowed?: boolean;
        additionalInfo?: string;
    };
    documents: Array<{
        type: 'id' | 'income' | 'employment' | 'bank' | 'reference' | 'other';
        name: string;
        url: string;
        uploadedAt: Date;
    }>;
    statistics: {
        totalApplications: number;
        successfulApplications: number;
        pendingApplications: number;
        rejectedApplications: number;
        averageResponseTime: number;
        lastApplicationDate?: Date;
    };
    status: {
        isActive: boolean;
        lastActivity?: Date;
        currentQueue: number;
        dailyApplicationsUsed: number;
        weeklyApplicationsUsed: number;
        monthlyApplicationsUsed: number;
        lastResetDate: Date;
    };
    createdAt: Date;
    updatedAt: Date;
}

export interface ApplicationQueue {
    _id: string;
    userId: string;
    listingId: string;
    listingUrl: string;
    listingTitle: string;
    status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
    priority: number;
    scheduledFor: Date;
    attempts: number;
    maxAttempts: number;
    lastAttempt?: Date;
    errorMessage?: string;
    estimatedSubmissionTime?: Date;
    createdAt: Date;
    updatedAt: Date;
}

export interface ApplicationResult {
    _id: string;
    userId: string;
    queueId: string;
    listingId: string;
    listingUrl: string;
    listingTitle: string;
    status: 'success' | 'failed' | 'cancelled';
    submittedAt: Date;
    responseTime: number;
    formData: Record<string, any>;
    screenshots: string[];
    errorDetails?: {
        message: string;
        stack?: string;
        captchaDetected: boolean;
        blockingDetected: boolean;
    };
    landlordResponse?: {
        status: 'accepted' | 'rejected' | 'pending';
        message?: string;
        receivedAt: Date;
    };
    metadata: {
        applicationVersion?: string;
        browserVersion?: string;
        platform?: string;
        serverId?: string;
        processingNode?: string;
        reviewRequired?: boolean;
        reviewedBy?: string;
        reviewedAt?: Date;
        reviewNotes?: string;
        retentionDate?: Date;
        archived?: boolean;
    };
    metrics?: {
        processingTime?: number;
        formDetectionTime?: number;
        formFillingTime?: number;
        submissionTime?: number;
        formComplexity?: 'simple' | 'moderate' | 'complex' | 'very_complex';
        fieldsDetected?: number;
        fieldsSuccessfullyFilled?: number;
        documentsUploaded?: number;
        successProbability?: number;
        antiDetectionMeasuresUsed?: string[];
        browserFingerprint?: string;
        userAgentUsed?: string;
        networkLatency?: number;
        pageLoadTime?: number;
    };
    response?: {
        success?: boolean;
        message?: string;
        redirectUrl?: string;
        responseCode?: string;
        responseTime?: number;
        headers?: any;
        cookies?: any;
    };
    createdAt: Date;
}

export interface AutoApplicationStats {
    totalApplications: number;
    successfulApplications: number;
    failedApplications: number;
    pendingApplications: number;
    successRate: number;
    averageResponseTime: number;
    applicationsToday: number;
    applicationsThisWeek: number;
    applicationsThisMonth: number;
    lastApplicationDate?: Date;
    topPerformingCriteria: Array<{
        criteria: string;
        successRate: number;
        applications: number;
    }>;
    recentActivity: Array<{
        date: Date;
        applications: number;
        successes: number;
    }>;
}

export interface ScraperIntegrationStats {
    processedListingsCount: number;
    qualityScoreCacheSize: number;
    currentlyProcessingCount: number;
    cacheExpiryMs: number;
    minQualityScore: number;
    autoApplicationsTriggered: number;
    duplicatesSkipped: number;
    lastProcessingTime?: Date;
}

class AutoApplicationService {
    private baseUrl = '/auto-application';

    // Settings Management
    async getSettings(userId: string): Promise<ApiResponse<AutoApplicationSettings>> {
        return apiService.get(`${this.baseUrl}/settings/${userId}`);
    }

    async updateSettings(userId: string, settings: Partial<AutoApplicationSettings>): Promise<ApiResponse<AutoApplicationSettings>> {
        return apiService.put(`${this.baseUrl}/settings/${userId}`, settings);
    }

    async enableAutoApplication(userId: string, settings: Partial<AutoApplicationSettings>): Promise<ApiResponse<AutoApplicationSettings>> {
        return apiService.post(`${this.baseUrl}/enable`, { userId, settings });
    }

    async disableAutoApplication(userId: string): Promise<ApiResponse<{ success: boolean }>> {
        return apiService.post(`${this.baseUrl}/disable`, {});
    }

    // Queue Management
    async getQueue(userId: string): Promise<ApiResponse<ApplicationQueue[]>> {
        return apiService.get(`${this.baseUrl}/queue/${userId}`);
    }

    async addToQueue(userId: string, listingId: string, priority: number = 5): Promise<ApiResponse<ApplicationQueue>> {
        return apiService.post(`${this.baseUrl}/queue`, {
            userId,
            listingId,
            priority
        });
    }

    async removeFromQueue(queueId: string): Promise<ApiResponse<{ success: boolean }>> {
        return apiService.delete(`${this.baseUrl}/queue/${queueId}`, {});
    }

    async updateQueuePriority(queueId: string, priority: number): Promise<ApiResponse<ApplicationQueue>> {
        return apiService.put(`${this.baseUrl}/queue/${queueId}/priority`, { priority });
    }

    async pauseQueue(userId: string): Promise<ApiResponse<{ success: boolean }>> {
        return apiService.post(`${this.baseUrl}/queue/${userId}/pause`);
    }

    async resumeQueue(userId: string): Promise<ApiResponse<{ success: boolean }>> {
        return apiService.post(`${this.baseUrl}/queue/${userId}/resume`);
    }

    // Application Results
    async getResults(userId: string, page: number = 1, limit: number = 20): Promise<ApiResponse<{
        results: ApplicationResult[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>> {
        return apiService.get(`${this.baseUrl}/results/${userId}`, { page, limit });
    }

    async getResult(resultId: string): Promise<ApiResponse<ApplicationResult>> {
        return apiService.get(`${this.baseUrl}/results/detail/${resultId}`);
    }

    async retryFailedApplication(resultId: string): Promise<ApiResponse<ApplicationQueue>> {
        return apiService.post(`${this.baseUrl}/results/${resultId}/retry`);
    }

    // Statistics and Analytics
    async getStats(userId: string): Promise<ApiResponse<AutoApplicationStats>> {
        return apiService.get(`${this.baseUrl}/stats/${userId}`);
    }

    async getGlobalStats(): Promise<ApiResponse<{
        totalUsers: number;
        totalApplications: number;
        averageSuccessRate: number;
        systemUptime: number;
    }>> {
        return apiService.get(`${this.baseUrl}/stats/global`);
    }

    // Manual Application Submission
    async submitManualApplication(
        userId: string,
        listingId: string,
        applicationData: Record<string, any>
    ): Promise<ApiResponse<ApplicationResult>> {
        return apiService.post(`${this.baseUrl}/submit/manual`, {
            userId,
            listingId,
            applicationData
        });
    }

    // Test and Validation
    async testCriteria(userId: string, listing: Listing): Promise<ApiResponse<{
        matches: boolean;
        score: number;
        reasons: string[];
        recommendations: string[];
    }>> {
        return apiService.post(`${this.baseUrl}/test-criteria`, {
            userId,
            listing
        });
    }

    async validateSettings(settings: Partial<AutoApplicationSettings>): Promise<ApiResponse<{
        valid: boolean;
        errors: string[];
        warnings: string[];
    }>> {
        return apiService.post(`${this.baseUrl}/validate-settings`, settings);
    }

    // Document Management
    async uploadDocument(
        userId: string,
        file: File | Blob,
        type: 'id' | 'income' | 'employment' | 'bank' | 'reference' | 'other',
        name: string
    ): Promise<ApiResponse<{
        url: string;
        type: string;
        name: string;
        uploadedAt: Date;
    }>> {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('type', type);
        formData.append('name', name);
        formData.append('userId', userId);

        return apiService.post(`${this.baseUrl}/documents/upload`, formData);
    }

    async deleteDocument(userId: string, documentUrl: string): Promise<ApiResponse<{ success: boolean }>> {
        return apiService.delete(`${this.baseUrl}/documents`, { userId, documentUrl });
    }

    // Real-time Updates and WebSocket Integration
    async subscribeToUpdates(userId: string, callback: (update: any) => void): Promise<() => void> {
        // This would integrate with WebSocket service
        // For now, we'll use polling as fallback
        const interval = setInterval(async () => {
            try {
                const queueResponse = await this.getQueue(userId);
                const statsResponse = await this.getStats(userId);

                if (queueResponse.success && statsResponse.success) {
                    callback({
                        type: 'status_update',
                        queue: queueResponse.data,
                        stats: statsResponse.data,
                        timestamp: new Date()
                    });
                }
            } catch (error) {
                console.error('Error polling for updates:', error);
            }
        }, 30000); // Poll every 30 seconds

        return () => clearInterval(interval);
    }

    // Scraper Integration
    async getScraperIntegrationStats(): Promise<ApiResponse<ScraperIntegrationStats>> {
        return apiService.get(`${this.baseUrl}/scraper/stats`);
    }

    async triggerScraperIntegration(): Promise<ApiResponse<{ success: boolean; message: string }>> {
        return apiService.post(`${this.baseUrl}/scraper/trigger`);
    }

    async clearScraperCache(): Promise<ApiResponse<{ success: boolean }>> {
        return apiService.post(`${this.baseUrl}/scraper/clear-cache`);
    }

    // Learning and Optimization
    async getOptimizationSuggestions(userId: string): Promise<ApiResponse<{
        suggestions: Array<{
            type: 'criteria' | 'timing' | 'template' | 'documents';
            title: string;
            description: string;
            impact: 'low' | 'medium' | 'high';
            implementation: string;
        }>;
        currentPerformance: {
            successRate: number;
            averageResponseTime: number;
            competitiveness: number;
        };
    }>> {
        return apiService.get(`${this.baseUrl}/optimization/${userId}`);
    }

    async applyOptimization(userId: string, optimizationType: string): Promise<ApiResponse<{ success: boolean }>> {
        return apiService.post(`${this.baseUrl}/optimization/${userId}/apply`, {
            type: optimizationType
        });
    }

    // Emergency Controls
    async emergencyStop(userId: string, reason: string): Promise<ApiResponse<{ success: boolean }>> {
        return apiService.post(`${this.baseUrl}/emergency-stop`, {
            userId,
            reason
        });
    }

    async getEmergencyStatus(userId: string): Promise<ApiResponse<{
        isStopped: boolean;
        reason?: string;
        stoppedAt?: Date;
        canResume: boolean;
    }>> {
        return apiService.get(`${this.baseUrl}/emergency-status/${userId}`);
    }

    // Health and Monitoring
    async getSystemHealth(): Promise<ApiResponse<{
        status: 'healthy' | 'degraded' | 'unhealthy';
        components: {
            queue: 'operational' | 'degraded' | 'down';
            scraper: 'operational' | 'degraded' | 'down';
            browser: 'operational' | 'degraded' | 'down';
            database: 'operational' | 'degraded' | 'down';
        };
        metrics: {
            queueSize: number;
            processingRate: number;
            errorRate: number;
            averageProcessingTime: number;
        };
        lastHealthCheck: Date;
    }>> {
        return apiService.get(`${this.baseUrl}/health`);
    }

    // Utility Methods
    formatApplicationResult(result: ApplicationResult): {
        statusText: string;
        statusColor: string;
        timeAgo: string;
        successRate: string;
    } {
        const statusMap = {
            success: { text: 'Successfully Submitted', color: '#10b981' },
            failed: { text: 'Submission Failed', color: '#ef4444' },
            cancelled: { text: 'Cancelled', color: '#6b7280' }
        };

        const status = statusMap[result.status] || { text: 'Unknown', color: '#6b7280' };

        const timeAgo = this.getTimeAgo(result.submittedAt);
        const successRate = result.metrics?.successProbability
            ? `${Math.round(result.metrics.successProbability)}%`
            : 'N/A';

        return {
            statusText: status.text,
            statusColor: status.color,
            timeAgo,
            successRate
        };
    }

    private getTimeAgo(date: Date): string {
        const now = new Date();
        const diffMs = now.getTime() - new Date(date).getTime();
        const diffMins = Math.floor(diffMs / 60000);
        const diffHours = Math.floor(diffMs / 3600000);
        const diffDays = Math.floor(diffMs / 86400000);

        if (diffMins < 1) return 'Just now';
        if (diffMins < 60) return `${diffMins}m ago`;
        if (diffHours < 24) return `${diffHours}h ago`;
        return `${diffDays}d ago`;
    }

    calculateQualityScore(listing: Listing, userCriteria: AutoApplicationSettings['criteria']): number {
        let score = 0.5; // Base score

        // Price match (30%)
        if (userCriteria.maxPrice && listing.price != null) {
            const price = typeof listing.price === 'number' ? listing.price : Number(listing.price);
            if (!isNaN(price) && price <= userCriteria.maxPrice) {
                score += 0.3;
            } else if (!isNaN(price) && price <= userCriteria.maxPrice * 1.1) {
                score += 0.15; // Partial credit for close matches
            }
        }

        // Room count match (20%)
        if (userCriteria.minRooms && userCriteria.maxRooms && listing.bedrooms != null) {
            const rooms = typeof listing.bedrooms === 'number' ? listing.bedrooms : Number(listing.bedrooms);
            if (!isNaN(rooms) && rooms >= userCriteria.minRooms && rooms <= userCriteria.maxRooms) {
                score += 0.2;
            }
        }

        // Location match (20%)
        if (userCriteria.locations.length > 0 && listing.location) {
            const listingLocation = typeof listing.location === 'string' 
                ? listing.location.toLowerCase()
                : listing.location.address?.toLowerCase() || '';
            const hasLocationMatch = userCriteria.locations.some(loc =>
                listingLocation.includes(loc.toLowerCase())
            );
            if (hasLocationMatch) {
                score += 0.2;
            }
        }

        // Property type match (15%)
        if (userCriteria.propertyTypes.length > 0 && listing.propertyType) {
            const hasTypeMatch = userCriteria.propertyTypes.some(type =>
                listing.propertyType?.toLowerCase().includes(type.toLowerCase())
            );
            if (hasTypeMatch) {
                score += 0.15;
            }
        }

        // Size match (10%)
        if (userCriteria.minSize && userCriteria.maxSize && listing.area != null) {
            const area = typeof listing.area === 'number' ? listing.area : Number(listing.area);
            if (!isNaN(area) && area >= userCriteria.minSize && area <= userCriteria.maxSize) {
                score += 0.1;
            }
        }

        // Keyword exclusion check (penalty)
        if (userCriteria.excludeKeywords.length > 0 && listing.description) {
            const description = listing.description.toLowerCase();
            const hasExcludedKeyword = userCriteria.excludeKeywords.some(keyword =>
                description.includes(keyword.toLowerCase())
            );
            if (hasExcludedKeyword) {
                score -= 0.2;
            }
        }

        // Keyword inclusion bonus (5%)
        if (userCriteria.includeKeywords.length > 0 && listing.description) {
            const description = listing.description.toLowerCase();
            const hasIncludedKeyword = userCriteria.includeKeywords.some(keyword =>
                description.includes(keyword.toLowerCase())
            );
            if (hasIncludedKeyword) {
                score += 0.05;
            }
        }

        return Math.max(0, Math.min(1, score)); // Clamp between 0 and 1
    }
}

export const autoApplicationService = new AutoApplicationService();
export default autoApplicationService;