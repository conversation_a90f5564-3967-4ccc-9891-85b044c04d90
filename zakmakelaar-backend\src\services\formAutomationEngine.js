const {
  browserPool,
  setupPageStealth,
  getRandomDelay,
} = require("./scraperUtils");
const { loggers } = require("./logger");
const ErrorHandlingService = require("./errorHandlingService");
const logger = loggers.app;

/**
 * FormAutomationEngine - Handles automated form detection, analysis, and filling for Funda applications
 *
 * This engine provides capabilities to:
 * - Detect different types of Funda application forms (native vs external)
 * - Analyze form fields dynamically and map them to user data
 * - Fill forms with validation and error handling
 * - Upload documents automatically
 * - Handle multi-step application processes
 */
class FormAutomationEngine {
  constructor(config = {}) {
    this.config = {
      timeout: 30000,
      retries: 3,
      delayMin: 1000,
      delayMax: 3000,
      screenshotOnError: true,
      ...config,
    };

    this.errorHandlingService = new ErrorHandlingService();

    // Form type detection patterns
    this.formPatterns = {
      fundaNative: {
        indicators: [
          'form[action*="funda.nl"]',
          ".application-form",
          "#funda-application-form",
          '[data-testid*="application"]',
        ],
        type: "native",
      },
      external: {
        indicators: [
          'form[action*="rentals"]',
          'form[action*="application"]',
          ".external-form",
          'iframe[src*="application"]',
        ],
        type: "external",
      },
    };

    // Common field mappings for different form types
    this.fieldMappings = {
      personal: {
        firstName: [
          'input[name*="first"], input[name*="voornaam"], input[id*="first"]',
        ],
        lastName: [
          'input[name*="last"], input[name*="achternaam"], input[id*="last"]',
        ],
        email: [
          'input[type="email"], input[name*="email"], input[name*="e-mail"]',
        ],
        phone: [
          'input[type="tel"], input[name*="phone"], input[name*="telefoon"]',
        ],
        dateOfBirth: [
          'input[type="date"], input[name*="birth"], input[name*="geboorte"]',
        ],
      },
      financial: {
        monthlyIncome: [
          'input[name*="income"], input[name*="inkomen"], input[name*="salary"]',
        ],
        employer: [
          'input[name*="employer"], input[name*="werkgever"], input[name*="company"]',
        ],
        occupation: [
          'input[name*="occupation"], input[name*="beroep"], input[name*="job"]',
        ],
      },
      housing: {
        moveInDate: [
          'input[name*="move"], input[name*="start"], input[name*="datum"]',
        ],
        numberOfOccupants: [
          'input[name*="occupants"], input[name*="bewoners"], select[name*="persons"]',
        ],
        leaseDuration: [
          'select[name*="duration"], input[name*="period"], select[name*="termijn"]',
        ],
      },
      documents: {
        fileUpload: [
          'input[type="file"]',
          '[data-testid*="upload"]',
          ".file-upload",
        ],
      },
      message: {
        applicationLetter: [
          'textarea[name*="message"], textarea[name*="letter"], textarea[name*="motivatie"]',
        ],
      },
    };
  }

  /**
   * Detect the type of form on the current page
   * @param {Object} page - Puppeteer page object
   * @returns {Object} Form type information
   */
  async detectFormType(page) {
    try {
      logger.info("Detecting form type on page");

      const formInfo = {
        type: "unknown",
        confidence: 0,
        indicators: [],
        forms: [],
      };

      // Check for each form pattern
      for (const [patternName, pattern] of Object.entries(this.formPatterns)) {
        for (const indicator of pattern.indicators) {
          try {
            const elements = await page.$$(indicator);
            if (elements.length > 0) {
              formInfo.indicators.push({
                pattern: patternName,
                selector: indicator,
                count: elements.length,
              });

              if (formInfo.confidence < 0.8) {
                formInfo.type = pattern.type;
                formInfo.confidence += 0.3;
              }
            }
          } catch (error) {
            logger.warn(
              `Error checking indicator ${indicator}:`,
              error.message
            );
          }
        }
      }

      // Get all forms on the page for analysis
      const forms = await page.evaluate(() => {
        const formElements = document.querySelectorAll("form");
        return Array.from(formElements).map((form, index) => ({
          index,
          action: form.action || "",
          method: form.method || "GET",
          fieldCount: form.querySelectorAll("input, select, textarea").length,
          hasFileUpload: form.querySelectorAll('input[type="file"]').length > 0,
          id: form.id || "",
          className: form.className || "",
        }));
      });

      formInfo.forms = forms;

      // If no specific pattern matched, try to infer from form characteristics
      if (formInfo.type === "unknown" && forms.length > 0) {
        const mainForm = forms.reduce((prev, current) =>
          current.fieldCount > prev.fieldCount ? current : prev
        );

        if (mainForm.action.includes("funda.nl")) {
          formInfo.type = "native";
          formInfo.confidence = 0.6;
        } else if (mainForm.fieldCount > 5) {
          formInfo.type = "external";
          formInfo.confidence = 0.5;
        }
      }

      logger.info(
        `Form detection completed: ${formInfo.type} (confidence: ${formInfo.confidence})`
      );
      return formInfo;
    } catch (error) {
      logger.error("Error detecting form type:", error);
      throw new Error(`Form detection failed: ${error.message}`);
    }
  }

  /**
   * Analyze form fields and create mapping to user data
   * @param {Object} page - Puppeteer page object
   * @param {Object} formInfo - Form type information from detectFormType
   * @returns {Object} Field mapping information
   */
  async analyzeFormFields(page, formInfo = null) {
    try {
      logger.info("Analyzing form fields");

      if (!formInfo) {
        formInfo = await this.detectFormType(page);
      }

      const fieldAnalysis = {
        formType: formInfo.type,
        mappedFields: {},
        unmappedFields: [],
        requiredFields: [],
        validationRules: {},
      };

      // Get all form fields
      const fields = await page.evaluate(() => {
        const inputs = document.querySelectorAll("input, select, textarea");
        return Array.from(inputs).map((field) => ({
          type: field.type || field.tagName.toLowerCase(),
          name: field.name || "",
          id: field.id || "",
          placeholder: field.placeholder || "",
          required: field.required || field.hasAttribute("required"),
          className: field.className || "",
          value: field.value || "",
          options:
            field.tagName.toLowerCase() === "select"
              ? Array.from(field.options).map((opt) => ({
                  value: opt.value,
                  text: opt.text,
                }))
              : null,
        }));
      });

      // Map fields to user data categories
      for (const field of fields) {
        let mapped = false;

        for (const [category, mappings] of Object.entries(this.fieldMappings)) {
          for (const [dataField, selectors] of Object.entries(mappings)) {
            // Check if field matches any selector pattern
            const fieldIdentifiers = [
              field.name,
              field.id,
              field.placeholder,
            ].filter(Boolean);

            for (const selector of selectors) {
              // Extract the key parts from selector for matching
              const selectorParts = selector.toLowerCase().match(/\w+/g) || [];

              if (
                fieldIdentifiers.some((identifier) =>
                  selectorParts.some((part) =>
                    identifier.toLowerCase().includes(part)
                  )
                )
              ) {
                if (!fieldAnalysis.mappedFields[category]) {
                  fieldAnalysis.mappedFields[category] = {};
                }
                fieldAnalysis.mappedFields[category][dataField] = {
                  ...field,
                  selector: this.generateFieldSelector(field),
                };
                mapped = true;
                break;
              }
            }
            if (mapped) break;
          }
          if (mapped) break;
        }

        if (!mapped) {
          fieldAnalysis.unmappedFields.push(field);
        }

        if (field.required) {
          fieldAnalysis.requiredFields.push(field);
        }
      }

      logger.info(
        `Field analysis completed: ${
          Object.keys(fieldAnalysis.mappedFields).length
        } categories mapped`
      );
      return fieldAnalysis;
    } catch (error) {
      logger.error("Error analyzing form fields:", error);
      throw new Error(`Field analysis failed: ${error.message}`);
    }
  }

  /**
   * Generate a reliable selector for a form field
   * @param {Object} field - Field information
   * @returns {string} CSS selector
   */
  generateFieldSelector(field) {
    // Priority order: id, name, type+placeholder, type+class
    if (field.id) {
      return `#${field.id}`;
    }
    if (field.name) {
      return `[name="${field.name}"]`;
    }
    if (field.placeholder) {
      return `[placeholder="${field.placeholder}"]`;
    }
    if (field.className) {
      return `.${field.className.split(" ")[0]}`;
    }
    return `${field.type}`;
  }
  /**
   * Fill application form with user data
   * @param {Object} page - Puppeteer page object
   * @param {Object} applicationData - User application data
   * @param {Object} fieldAnalysis - Field mapping from analyzeFormFields
   * @returns {Object} Fill result with success status and errors
   */
  async fillApplicationForm(page, applicationData, fieldAnalysis = null) {
    try {
      logger.info("Starting form filling process");

      if (!fieldAnalysis) {
        fieldAnalysis = await this.analyzeFormFields(page);
      }

      const fillResult = {
        success: false,
        filledFields: [],
        errors: [],
        skippedFields: [],
      };

      // Fill personal information
      if (fieldAnalysis.mappedFields.personal) {
        await this.fillFieldCategory(
          page,
          "personal",
          fieldAnalysis.mappedFields.personal,
          applicationData.personalInfo,
          fillResult
        );
      }

      // Fill financial information
      if (fieldAnalysis.mappedFields.financial) {
        await this.fillFieldCategory(
          page,
          "financial",
          fieldAnalysis.mappedFields.financial,
          applicationData.personalInfo,
          fillResult
        );
      }

      // Fill housing preferences
      if (fieldAnalysis.mappedFields.housing) {
        await this.fillFieldCategory(
          page,
          "housing",
          fieldAnalysis.mappedFields.housing,
          applicationData.personalInfo,
          fillResult
        );
      }

      // Fill application message/letter
      if (
        fieldAnalysis.mappedFields.message &&
        applicationData.generatedContent
      ) {
        await this.fillFieldCategory(
          page,
          "message",
          fieldAnalysis.mappedFields.message,
          { applicationLetter: applicationData.generatedContent.message },
          fillResult
        );
      }

      fillResult.success = fillResult.errors.length === 0;

      logger.info(
        `Form filling completed: ${fillResult.filledFields.length} fields filled, ${fillResult.errors.length} errors`
      );
      return fillResult;
    } catch (error) {
      logger.error("Error filling form:", error);
      throw new Error(`Form filling failed: ${error.message}`);
    }
  }

  /**
   * Fill a category of form fields
   * @param {Object} page - Puppeteer page object
   * @param {string} category - Field category name
   * @param {Object} fieldMappings - Mapped fields for this category
   * @param {Object} userData - User data for this category
   * @param {Object} fillResult - Result object to update
   */
  async fillFieldCategory(page, category, fieldMappings, userData, fillResult) {
    for (const [dataField, fieldInfo] of Object.entries(fieldMappings)) {
      try {
        const value = userData[dataField];
        if (value === undefined || value === null) {
          fillResult.skippedFields.push({
            category,
            field: dataField,
            reason: "No data available",
          });
          continue;
        }

        await this.fillSingleField(page, fieldInfo, value);
        fillResult.filledFields.push({
          category,
          field: dataField,
          selector: fieldInfo.selector,
        });

        // Add small delay between fields to mimic human behavior
        await getRandomDelay(200, 800);
      } catch (error) {
        logger.warn(`Error filling field ${dataField}:`, error.message);
        fillResult.errors.push({
          category,
          field: dataField,
          error: error.message,
          selector: fieldInfo.selector,
        });
      }
    }
  }

  /**
   * Fill a single form field with appropriate method based on field type
   * @param {Object} page - Puppeteer page object
   * @param {Object} fieldInfo - Field information including selector
   * @param {*} value - Value to fill
   */
  async fillSingleField(page, fieldInfo, value) {
    const selector = fieldInfo.selector;

    // Wait for field to be available
    await page.waitForSelector(selector, { timeout: 5000 });

    // Handle different field types
    switch (fieldInfo.type) {
      case "select":
        await this.fillSelectField(page, selector, value, fieldInfo.options);
        break;

      case "textarea":
        await this.fillTextareaField(page, selector, value);
        break;

      case "checkbox":
        await this.fillCheckboxField(page, selector, value);
        break;

      case "radio":
        await this.fillRadioField(page, selector, value);
        break;

      case "date":
        await this.fillDateField(page, selector, value);
        break;

      default:
        await this.fillTextField(page, selector, value);
        break;
    }
  }

  /**
   * Fill text input field
   */
  async fillTextField(page, selector, value) {
    await page.focus(selector);
    await page.evaluate(
      (sel) => (document.querySelector(sel).value = ""),
      selector
    );
    await page.type(selector, String(value), { delay: 50 });
  }

  /**
   * Fill textarea field
   */
  async fillTextareaField(page, selector, value) {
    await page.focus(selector);
    await page.evaluate(
      (sel) => (document.querySelector(sel).value = ""),
      selector
    );
    await page.type(selector, String(value), { delay: 30 });
  }

  /**
   * Fill select dropdown field
   */
  async fillSelectField(page, selector, value, options) {
    // Try to match value with available options
    let optionValue = value;

    if (options && options.length > 0) {
      // Try exact match first
      let matchedOption = options.find(
        (opt) => opt.value === String(value) || opt.text === String(value)
      );

      // Try partial match if exact match fails
      if (!matchedOption) {
        matchedOption = options.find(
          (opt) =>
            opt.text.toLowerCase().includes(String(value).toLowerCase()) ||
            String(value).toLowerCase().includes(opt.text.toLowerCase())
        );
      }

      if (matchedOption) {
        optionValue = matchedOption.value;
      }
    }

    await page.select(selector, optionValue);
  }

  /**
   * Fill checkbox field
   */
  async fillCheckboxField(page, selector, value) {
    const isChecked = await page.$eval(selector, (el) => el.checked);
    const shouldBeChecked = Boolean(value);

    if (isChecked !== shouldBeChecked) {
      await page.click(selector);
    }
  }

  /**
   * Fill radio button field
   */
  async fillRadioField(page, selector, value) {
    // For radio buttons, we need to find the specific option
    const radioSelector = `${selector}[value="${value}"]`;
    try {
      await page.click(radioSelector);
    } catch (error) {
      // If exact value match fails, try to find by label
      const radioButtons = await page.$$(selector);
      for (const radio of radioButtons) {
        const label = await page.evaluate((el) => {
          const labelEl = document.querySelector(`label[for="${el.id}"]`);
          return labelEl ? labelEl.textContent.trim() : "";
        }, radio);

        if (label.toLowerCase().includes(String(value).toLowerCase())) {
          await radio.click();
          break;
        }
      }
    }
  }

  /**
   * Fill date field
   */
  async fillDateField(page, selector, value) {
    let dateValue = value;

    // Convert date to appropriate format if needed
    if (value instanceof Date) {
      dateValue = value.toISOString().split("T")[0]; // YYYY-MM-DD format
    } else if (
      typeof value === "string" &&
      !value.match(/^\d{4}-\d{2}-\d{2}$/)
    ) {
      // Try to parse and convert to ISO format
      const parsedDate = new Date(value);
      if (!isNaN(parsedDate.getTime())) {
        dateValue = parsedDate.toISOString().split("T")[0];
      }
    }

    await page.focus(selector);
    await page.evaluate(
      (sel) => (document.querySelector(sel).value = ""),
      selector
    );
    await page.type(selector, dateValue);
  }

  /**
   * Upload documents to form
   * @param {Object} page - Puppeteer page object
   * @param {Array} documents - Array of document objects with path and type
   * @param {Object} fieldAnalysis - Field analysis containing upload fields
   * @returns {Object} Upload result
   */
  async uploadDocuments(page, documents, fieldAnalysis = null) {
    try {
      logger.info("Starting document upload process");

      if (!fieldAnalysis) {
        fieldAnalysis = await this.analyzeFormFields(page);
      }

      const uploadResult = {
        success: false,
        uploadedDocuments: [],
        errors: [],
        skippedDocuments: [],
      };

      // Find file upload fields
      const uploadFields = fieldAnalysis.mappedFields.documents?.fileUpload;
      if (!uploadFields) {
        // Try to find upload fields directly
        const fileInputs = await page.$$('input[type="file"]');
        if (fileInputs.length === 0) {
          uploadResult.errors.push("No file upload fields found on form");
          return uploadResult;
        }
      }

      // Get all file input elements
      const fileInputs = await page.$$('input[type="file"]');

      for (let i = 0; i < documents.length && i < fileInputs.length; i++) {
        const document = documents[i];
        const fileInput = fileInputs[i];

        try {
          // Validate document exists and is accessible
          if (!document.path || !require("fs").existsSync(document.path)) {
            uploadResult.errors.push(`Document not found: ${document.path}`);
            continue;
          }

          // Upload the file
          await fileInput.uploadFile(document.path);

          uploadResult.uploadedDocuments.push({
            type: document.type,
            path: document.path,
            fieldIndex: i,
          });

          logger.info(`Uploaded document: ${document.type}`);

          // Wait a bit between uploads
          await getRandomDelay(1000, 2000);
        } catch (error) {
          logger.warn(
            `Error uploading document ${document.type}:`,
            error.message
          );
          uploadResult.errors.push({
            document: document.type,
            error: error.message,
          });
        }
      }

      // Handle case where there are more documents than upload fields
      if (documents.length > fileInputs.length) {
        const skippedCount = documents.length - fileInputs.length;
        uploadResult.skippedDocuments = documents.slice(fileInputs.length);
        logger.warn(
          `${skippedCount} documents skipped - not enough upload fields`
        );
      }

      uploadResult.success = uploadResult.errors.length === 0;

      logger.info(
        `Document upload completed: ${uploadResult.uploadedDocuments.length} uploaded, ${uploadResult.errors.length} errors`
      );
      return uploadResult;
    } catch (error) {
      logger.error("Error uploading documents:", error);
      throw new Error(`Document upload failed: ${error.message}`);
    }
  }
  /**
   * Submit the application form
   * @param {Object} page - Puppeteer page object
   * @param {Object} options - Submission options
   * @returns {Object} Submission result
   */
  async submitForm(page, options = {}) {
    try {
      logger.info("Starting form submission");

      const submitResult = {
        success: false,
        confirmationData: null,
        redirectUrl: null,
        errors: [],
        screenshots: [],
      };

      // Take screenshot before submission if enabled
      if (this.config.screenshotOnError) {
        const screenshotPath = `screenshots/pre-submit-${Date.now()}.png`;
        await page.screenshot({ path: screenshotPath, fullPage: true });
        submitResult.screenshots.push(screenshotPath);
      }

      // Find submit button
      const submitButton = await this.findSubmitButton(page);
      if (!submitButton) {
        throw new Error("Submit button not found");
      }

      // Validate form before submission if requested
      if (options.validateBeforeSubmit !== false) {
        const validationResult = await this.validateForm(page);
        if (!validationResult.isValid) {
          submitResult.errors = validationResult.errors;
          return submitResult;
        }
      }

      // Click submit button
      const navigationPromise = page
        .waitForNavigation({
          waitUntil: "networkidle0",
          timeout: this.config.timeout,
        })
        .catch(() => null); // Don't fail if no navigation occurs

      await submitButton.click();

      // Wait for either navigation or form response
      await Promise.race([
        navigationPromise,
        await new Promise((resolve) => setTimeout(resolve, 3000)), // Wait at least 3 seconds for response
      ]);

      // Check for success indicators
      const currentUrl = page.url();
      submitResult.redirectUrl = currentUrl;

      // Look for confirmation messages or success indicators
      const confirmationData = await this.extractConfirmationData(page);
      submitResult.confirmationData = confirmationData;

      // Check for error messages
      const errorMessages = await this.extractErrorMessages(page);
      if (errorMessages.length > 0) {
        submitResult.errors = errorMessages;
      } else {
        submitResult.success = true;
      }

      // Take screenshot after submission
      if (this.config.screenshotOnError || submitResult.success) {
        const screenshotPath = `screenshots/post-submit-${Date.now()}.png`;
        await page.screenshot({ path: screenshotPath, fullPage: true });
        submitResult.screenshots.push(screenshotPath);
      }

      logger.info(
        `Form submission completed: ${
          submitResult.success ? "SUCCESS" : "FAILED"
        }`
      );
      return submitResult;
    } catch (error) {
      logger.error("Error submitting form:", error);
      throw new Error(`Form submission failed: ${error.message}`);
    }
  }

  /**
   * Find the submit button on the form
   * @param {Object} page - Puppeteer page object
   * @returns {Object} Submit button element
   */
  async findSubmitButton(page) {
    const submitSelectors = [
      'input[type="submit"]',
      'button[type="submit"]',
      'button:contains("Submit")',
      'button:contains("Verstuur")',
      'button:contains("Aanmelden")',
      'button:contains("Apply")',
      ".submit-button",
      ".btn-submit",
      '[data-testid*="submit"]',
    ];

    for (const selector of submitSelectors) {
      try {
        const element = await page.$(selector);
        if (element) {
          // Check if button is visible and enabled
          const isVisible = await page.evaluate((el) => {
            const style = window.getComputedStyle(el);
            return (
              style.display !== "none" &&
              style.visibility !== "hidden" &&
              !el.disabled
            );
          }, element);

          if (isVisible) {
            return element;
          }
        }
      } catch (error) {
        // Continue to next selector
      }
    }

    // If no standard submit button found, look for any clickable button with submit-like text
    const buttons = await page.$$('button, input[type="button"]');
    for (const button of buttons) {
      const text = await page.evaluate(
        (el) => el.textContent || el.value || "",
        button
      );
      if (text.toLowerCase().match(/(submit|verstuur|aanmelden|apply|send)/)) {
        return button;
      }
    }

    return null;
  }

  /**
   * Validate form before submission
   * @param {Object} page - Puppeteer page object
   * @returns {Object} Validation result
   */
  async validateForm(page) {
    try {
      const validationResult = {
        isValid: true,
        errors: [],
        warnings: [],
      };

      // Check for required fields that are empty
      const requiredFields = await page.$$("[required], .required");
      for (const field of requiredFields) {
        const value = await page.evaluate(
          (el) => el.value || el.textContent || "",
          field
        );
        const fieldName = await page.evaluate(
          (el) => el.name || el.id || el.placeholder || "Unknown field",
          field
        );

        if (!value.trim()) {
          validationResult.errors.push(`Required field is empty: ${fieldName}`);
          validationResult.isValid = false;
        }
      }

      // Check for client-side validation errors
      const errorElements = await page.$$(
        '.error, .invalid, [aria-invalid="true"]'
      );
      for (const errorEl of errorElements) {
        const errorText = await page.evaluate(
          (el) => el.textContent || el.title || "",
          errorEl
        );
        if (errorText.trim()) {
          validationResult.errors.push(`Validation error: ${errorText}`);
          validationResult.isValid = false;
        }
      }

      // Check for email format validation
      const emailFields = await page.$$('input[type="email"]');
      for (const emailField of emailFields) {
        const email = await page.evaluate((el) => el.value, emailField);
        if (email && !this.isValidEmail(email)) {
          validationResult.errors.push(`Invalid email format: ${email}`);
          validationResult.isValid = false;
        }
      }

      return validationResult;
    } catch (error) {
      logger.warn("Error during form validation:", error.message);
      return {
        isValid: false,
        errors: [`Validation check failed: ${error.message}`],
        warnings: [],
      };
    }
  }

  /**
   * Extract confirmation data after form submission
   * @param {Object} page - Puppeteer page object
   * @returns {Object} Confirmation data
   */
  async extractConfirmationData(page) {
    try {
      const confirmationData = {
        message: null,
        confirmationNumber: null,
        nextSteps: [],
        contactInfo: null,
      };

      // Look for success messages
      const successSelectors = [
        ".success",
        ".confirmation",
        ".thank-you",
        '[class*="success"]',
        '[class*="confirm"]',
        "h1, h2, h3",
        ".message",
      ];

      for (const selector of successSelectors) {
        try {
          const elements = await page.$$(selector);
          for (const element of elements) {
            const text = await page.evaluate(
              (el) => el.textContent.trim(),
              element
            );
            if (
              text &&
              text
                .toLowerCase()
                .match(/(success|bedankt|thank|confirm|ontvangen)/)
            ) {
              confirmationData.message = text;
              break;
            }
          }
          if (confirmationData.message) break;
        } catch (error) {
          // Continue to next selector
        }
      }

      // Look for confirmation numbers
      const confirmationNumberRegex =
        /(?:confirmation|reference|nummer|number)[\s:]*([A-Z0-9-]+)/i;
      const pageText = await page.evaluate(() => document.body.textContent);
      const numberMatch = pageText.match(confirmationNumberRegex);
      if (numberMatch) {
        confirmationData.confirmationNumber = numberMatch[1];
      }

      // Look for next steps or instructions
      const instructionSelectors = [
        ".next-steps",
        ".instructions",
        ".what-happens-next",
      ];
      for (const selector of instructionSelectors) {
        try {
          const element = await page.$(selector);
          if (element) {
            const text = await page.evaluate(
              (el) => el.textContent.trim(),
              element
            );
            confirmationData.nextSteps.push(text);
          }
        } catch (error) {
          // Continue
        }
      }

      return confirmationData;
    } catch (error) {
      logger.warn("Error extracting confirmation data:", error.message);
      return null;
    }
  }

  /**
   * Extract error messages from the page
   * @param {Object} page - Puppeteer page object
   * @returns {Array} Array of error messages
   */
  async extractErrorMessages(page) {
    const errors = [];

    try {
      const errorSelectors = [
        ".error",
        ".alert-error",
        ".danger",
        ".invalid",
        '[class*="error"]',
        '[class*="invalid"]',
        '[role="alert"]',
      ];

      for (const selector of errorSelectors) {
        try {
          const elements = await page.$$(selector);
          for (const element of elements) {
            const text = await page.evaluate(
              (el) => el.textContent.trim(),
              element
            );
            if (text && text.length > 0) {
              errors.push(text);
            }
          }
        } catch (error) {
          // Continue to next selector
        }
      }
    } catch (error) {
      logger.warn("Error extracting error messages:", error.message);
    }

    return errors;
  }

  /**
   * Handle form errors and attempt recovery
   * @param {Object} page - Puppeteer page object
   * @param {Array} errors - Array of error messages
   * @returns {Object} Recovery result
   */
  async handleFormErrors(page, errors) {
    try {
      logger.info("Attempting to handle form errors");

      const recoveryResult = {
        recovered: false,
        actions: [],
        remainingErrors: [...errors],
      };

      for (const error of errors) {
        const errorLower = error.toLowerCase();

        // Handle common error types
        if (
          errorLower.includes("required") ||
          errorLower.includes("verplicht")
        ) {
          // Try to find and highlight missing required fields
          const requiredFields = await page.$$(
            "[required]:invalid, .required:empty"
          );
          for (const field of requiredFields) {
            await page.evaluate((el) => {
              el.style.border = "2px solid red";
              el.focus();
            }, field);
          }
          recoveryResult.actions.push("Highlighted missing required fields");
        }

        if (errorLower.includes("email") || errorLower.includes("e-mail")) {
          // Try to fix email format issues
          const emailFields = await page.$$('input[type="email"]');
          for (const field of emailFields) {
            const value = await page.evaluate((el) => el.value, field);
            if (value && !this.isValidEmail(value)) {
              // Could attempt to fix common email issues here
              recoveryResult.actions.push(`Found invalid email: ${value}`);
            }
          }
        }

        if (
          errorLower.includes("captcha") ||
          errorLower.includes("verification")
        ) {
          recoveryResult.actions.push(
            "CAPTCHA detected - manual intervention required"
          );
          // This would need to be handled by the calling service
        }
      }

      // Check if errors are resolved
      await new Promise((resolve) => setTimeout(resolve, 1000));
      const newErrors = await this.extractErrorMessages(page);
      recoveryResult.remainingErrors = newErrors;
      recoveryResult.recovered = newErrors.length < errors.length;

      return recoveryResult;
    } catch (error) {
      logger.error("Error during form error handling:", error);
      return {
        recovered: false,
        actions: [`Error handling failed: ${error.message}`],
        remainingErrors: errors,
      };
    }
  }

  /**
   * Validate email format
   * @param {string} email - Email to validate
   * @returns {boolean} Is valid email
   */
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Take screenshot for debugging purposes
   * @param {Object} page - Puppeteer page object
   * @param {string} prefix - Screenshot filename prefix
   * @returns {string} Screenshot path
   */
  async takeScreenshot(page, prefix = "form-automation") {
    try {
      const timestamp = Date.now();
      const screenshotPath = `screenshots/${prefix}-${timestamp}.png`;
      await page.screenshot({ path: screenshotPath, fullPage: true });
      return screenshotPath;
    } catch (error) {
      logger.warn("Failed to take screenshot:", error.message);
      return null;
    }
  }

  /**
   * Handle multi-step application processes
   * @param {Object} page - Puppeteer page object
   * @param {Object} applicationData - Application data
   * @param {Object} options - Processing options
   * @returns {Object} Multi-step processing result
   */
  async handleMultiStepProcess(page, applicationData, options = {}) {
    try {
      logger.info("Starting multi-step application process");

      const processResult = {
        success: false,
        completedSteps: [],
        currentStep: 1,
        totalSteps: 0,
        errors: [],
        screenshots: [],
      };

      // Detect if this is a multi-step process
      const stepInfo = await this.detectSteps(page);
      processResult.totalSteps = stepInfo.totalSteps;

      if (stepInfo.totalSteps <= 1) {
        // Single step process, handle normally
        return await this.processSingleStep(page, applicationData, options);
      }

      // Process each step
      for (let step = 1; step <= stepInfo.totalSteps; step++) {
        try {
          logger.info(`Processing step ${step} of ${stepInfo.totalSteps}`);
          processResult.currentStep = step;

          // Take screenshot before processing step
          if (this.config.screenshotOnError) {
            const screenshot = await this.takeScreenshot(
              page,
              `step-${step}-before`
            );
            if (screenshot) processResult.screenshots.push(screenshot);
          }

          // Process current step
          const stepResult = await this.processStep(
            page,
            step,
            applicationData,
            options
          );

          if (!stepResult.success) {
            processResult.errors.push(...stepResult.errors);
            break;
          }

          processResult.completedSteps.push({
            step,
            result: stepResult,
          });

          // Check if there's a next step
          const hasNextStep = await this.hasNextStep(
            page,
            step,
            stepInfo.totalSteps
          );
          if (!hasNextStep) {
            break;
          }

          // Navigate to next step
          const navigationResult = await this.navigateToNextStep(page, step);
          if (!navigationResult.success) {
            processResult.errors.push(
              `Failed to navigate to step ${step + 1}: ${
                navigationResult.error
              }`
            );
            break;
          }

          // Wait for next step to load
          await new Promise((resolve) => setTimeout(resolve, 2000));
        } catch (error) {
          logger.error(`Error processing step ${step}:`, error);
          processResult.errors.push(`Step ${step} failed: ${error.message}`);
          break;
        }
      }

      processResult.success =
        processResult.completedSteps.length === stepInfo.totalSteps &&
        processResult.errors.length === 0;

      logger.info(
        `Multi-step process completed: ${
          processResult.success ? "SUCCESS" : "FAILED"
        }`
      );
      return processResult;
    } catch (error) {
      logger.error("Error in multi-step process:", error);
      throw new Error(`Multi-step process failed: ${error.message}`);
    }
  }

  /**
   * Detect steps in a multi-step form
   * @param {Object} page - Puppeteer page object
   * @returns {Object} Step information
   */
  async detectSteps(page) {
    try {
      const stepInfo = await page.evaluate(() => {
        // Look for common step indicators
        const stepIndicators = [
          ".step-indicator",
          ".progress-bar",
          ".wizard-steps",
          '[class*="step"]',
          '[class*="progress"]',
          ".breadcrumb",
        ];

        let totalSteps = 1;
        let currentStep = 1;
        let stepLabels = [];

        for (const selector of stepIndicators) {
          const elements = document.querySelectorAll(selector);
          if (elements.length > 0) {
            // Try to extract step information
            elements.forEach((el) => {
              const text = el.textContent || "";

              // Look for "Step X of Y" patterns
              const stepMatch = text.match(/step\s+(\d+)\s+of\s+(\d+)/i);
              if (stepMatch) {
                currentStep = parseInt(stepMatch[1]);
                totalSteps = parseInt(stepMatch[2]);
              }

              // Look for numbered steps
              const numberedSteps = el.querySelectorAll('[class*="step"], li');
              if (numberedSteps.length > totalSteps) {
                totalSteps = numberedSteps.length;
              }

              // Extract step labels
              numberedSteps.forEach((step, index) => {
                const label = step.textContent.trim();
                if (label && !stepLabels.includes(label)) {
                  stepLabels.push(label);
                }
              });
            });
          }
        }

        return {
          totalSteps,
          currentStep,
          stepLabels,
        };
      });

      return stepInfo;
    } catch (error) {
      logger.warn("Error detecting steps:", error.message);
      return { totalSteps: 1, currentStep: 1, stepLabels: [] };
    }
  }

  /**
   * Process a single step in multi-step form
   * @param {Object} page - Puppeteer page object
   * @param {number} stepNumber - Current step number
   * @param {Object} applicationData - Application data
   * @param {Object} options - Processing options
   * @returns {Object} Step processing result
   */
  async processStep(page, stepNumber, applicationData, options) {
    try {
      logger.info(`Processing step ${stepNumber}`);

      // Analyze fields for this step
      const fieldAnalysis = await this.analyzeFormFields(page);

      // Fill fields based on step content
      const fillResult = await this.fillApplicationForm(
        page,
        applicationData,
        fieldAnalysis
      );

      // Upload documents if this step has file uploads
      let uploadResult = { success: true, uploadedDocuments: [], errors: [] };
      if (fieldAnalysis.mappedFields.documents && applicationData.documents) {
        uploadResult = await this.uploadDocuments(
          page,
          applicationData.documents,
          fieldAnalysis
        );
      }

      return {
        success: fillResult.success && uploadResult.success,
        fillResult,
        uploadResult,
        errors: [...fillResult.errors, ...uploadResult.errors],
      };
    } catch (error) {
      logger.error(`Error processing step ${stepNumber}:`, error);
      return {
        success: false,
        errors: [error.message],
      };
    }
  }

  /**
   * Check if there's a next step available
   * @param {Object} page - Puppeteer page object
   * @param {number} currentStep - Current step number
   * @param {number} totalSteps - Total number of steps
   * @returns {boolean} Has next step
   */
  async hasNextStep(page, currentStep, totalSteps) {
    if (currentStep >= totalSteps) {
      return false;
    }

    try {
      // Look for next button or step navigation
      const nextSelectors = [
        'button:contains("Next")',
        'button:contains("Volgende")',
        ".next-button",
        ".btn-next",
        '[data-testid*="next"]',
        'input[value*="Next"]',
        'input[value*="Volgende"]',
      ];

      for (const selector of nextSelectors) {
        try {
          const element = await page.$(selector);
          if (element) {
            const isVisible = await page.evaluate((el) => {
              const style = window.getComputedStyle(el);
              return (
                style.display !== "none" &&
                style.visibility !== "hidden" &&
                !el.disabled
              );
            }, element);

            if (isVisible) {
              return true;
            }
          }
        } catch (error) {
          // Continue checking other selectors
        }
      }

      return false;
    } catch (error) {
      logger.warn("Error checking for next step:", error.message);
      return false;
    }
  }

  /**
   * Navigate to the next step
   * @param {Object} page - Puppeteer page object
   * @param {number} currentStep - Current step number
   * @returns {Object} Navigation result
   */
  async navigateToNextStep(page, currentStep) {
    try {
      // Find and click next button
      const nextSelectors = [
        'button:contains("Next")',
        'button:contains("Volgende")',
        ".next-button",
        ".btn-next",
        '[data-testid*="next"]',
        'input[value*="Next"]',
        'input[value*="Volgende"]',
      ];

      for (const selector of nextSelectors) {
        try {
          const element = await page.$(selector);
          if (element) {
            const isVisible = await page.evaluate((el) => {
              const style = window.getComputedStyle(el);
              return (
                style.display !== "none" &&
                style.visibility !== "hidden" &&
                !el.disabled
              );
            }, element);

            if (isVisible) {
              await element.click();

              // Wait for navigation or content change
              await Promise.race([
                page.waitForNavigation({
                  waitUntil: "networkidle0",
                  timeout: 10000,
                }),
                await new Promise((resolve) => setTimeout(resolve, 3000)),
              ]);

              return { success: true };
            }
          }
        } catch (error) {
          // Continue to next selector
        }
      }

      return {
        success: false,
        error: "Next button not found or not clickable",
      };
    } catch (error) {
      logger.error("Error navigating to next step:", error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Process single step (non-multi-step) application
   * @param {Object} page - Puppeteer page object
   * @param {Object} applicationData - Application data
   * @param {Object} options - Processing options
   * @returns {Object} Processing result
   */
  async processSingleStep(page, applicationData, options) {
    try {
      // Analyze form fields
      const fieldAnalysis = await this.analyzeFormFields(page);

      // Fill form fields
      const fillResult = await this.fillApplicationForm(
        page,
        applicationData,
        fieldAnalysis
      );

      // Upload documents if available
      let uploadResult = { success: true, uploadedDocuments: [], errors: [] };
      if (applicationData.documents && applicationData.documents.length > 0) {
        uploadResult = await this.uploadDocuments(
          page,
          applicationData.documents,
          fieldAnalysis
        );
      }

      // Submit form if requested
      let submitResult = { success: true };
      if (options.autoSubmit !== false) {
        submitResult = await this.submitForm(page, options);
      }

      return {
        success:
          fillResult.success && uploadResult.success && submitResult.success,
        completedSteps: [
          {
            step: 1,
            result: {
              fillResult,
              uploadResult,
              submitResult,
            },
          },
        ],
        currentStep: 1,
        totalSteps: 1,
        errors: [
          ...fillResult.errors,
          ...uploadResult.errors,
          ...(submitResult.errors || []),
        ],
        screenshots: submitResult.screenshots || [],
      };
    } catch (error) {
      logger.error("Error in single step process:", error);
      throw new Error(`Single step process failed: ${error.message}`);
    }
  }

  /**
   * Create a new browser page with stealth configuration
   * @param {Object} browser - Browser instance from browser pool
   * @returns {Object} Configured page
   */
  async createStealthPage(browser) {
    try {
      const page = await browser.newPage();
      await setupPageStealth(page);

      // Set additional form automation specific configurations
      await page.setDefaultTimeout(this.config.timeout);

      // Add form automation specific event listeners
      page.on("dialog", async (dialog) => {
        logger.info(`Dialog detected: ${dialog.message()}`);
        await dialog.accept();
      });

      page.on("console", (msg) => {
        if (msg.type() === "error") {
          logger.warn(`Browser console error: ${msg.text()}`);
        }
      });

      return page;
    } catch (error) {
      logger.error("Error creating stealth page:", error);
      throw new Error(`Failed to create stealth page: ${error.message}`);
    }
  }

  /**
   * Test form automation without actually submitting
   * @param {string} url - The URL of the page with the form
   * @param {Object} options - Configuration options
   * @returns {Promise<Object>} Test result
   */
  async testFormAutomation(url, options = {}) {
    const { dryRun = true, userId } = options;

    logger.info("Starting form automation test", {
      url,
      dryRun,
      userId,
    });

    let browser = null;
    let page = null;
    const testResult = {
      success: false,
      platform: null,
      formType: null,
      fieldsFound: 0,
      formFields: [],
      screenshots: [],
      logs: [],
      errors: [],
      successProbability: 0,
    };

    try {
      // Get browser from pool
      browser = await browserPool.getBrowser();
      page = await this.createStealthPage(browser);

      testResult.logs.push("Browser launched successfully");

      // Navigate to the page
      await page.goto(url, { waitUntil: "networkidle2", timeout: 30000 });
      testResult.logs.push("Page loaded successfully");

      // Determine platform
      const urlObj = new URL(url);
      const domain = urlObj.hostname.toLowerCase();

      if (domain.includes("funda.nl")) {
        testResult.platform = "funda";

        // Detect form type
        const formInfo = await this.detectFormType(page);
        testResult.formType = formInfo.type;
        testResult.logs.push(`Form type detected: ${formInfo.type}`);

        // Analyze form fields
        const fieldAnalysis = await this.analyzeFormFields(page, formInfo);
        testResult.formFields = Object.entries(
          fieldAnalysis.fieldMappings || {}
        )
          .map(([category, fields]) => {
            return Object.entries(fields).map(([fieldName, fieldInfo]) => ({
              name: fieldName,
              type: fieldInfo.type || "unknown",
              selector: fieldInfo.selector,
              required: fieldInfo.required || false,
              found: true,
            }));
          })
          .flat();

        testResult.fieldsFound = testResult.formFields.length;
        testResult.successProbability = Math.min(
          90,
          testResult.fieldsFound * 10
        );

        testResult.logs.push(`Found ${testResult.fieldsFound} form fields`);

        // Take screenshot
        const screenshot = await this.takeScreenshot(page, "test-form");
        if (screenshot) {
          testResult.screenshots.push({
            name: "form_analysis",
            path: screenshot,
          });
        }
      } else {
        testResult.platform = "generic";
        testResult.logs.push(
          "Generic platform detected - limited testing available"
        );
        testResult.successProbability = 30;
      }

      testResult.success = true;
      testResult.logs.push("Form automation test completed successfully");
    } catch (error) {
      testResult.errors.push(error.message);
      testResult.logs.push(`Test failed: ${error.message}`);
      logger.error("Form automation test failed:", error);
    } finally {
      if (page) {
        try {
          await page.close();
        } catch (error) {
          // Ignore close errors
        }
      }
      if (browser) {
        // Note: browserPool uses round-robin, no need to release
        // The browser will be reused by the pool
      }
    }

    return testResult;
  }

  /**
   * Complete browser automation workflow for form filling
   * @param {string} url - URL to navigate to
   * @param {Object} applicationData - Application data
   * @param {Object} options - Automation options
   * @returns {Object} Complete automation result
   */
  async automateFormFilling(url, applicationData, options = {}) {
    let browser = null;
    let page = null;

    try {
      logger.info(`Starting browser automation for: ${url}`);

      // Get browser from pool
      browser = await browserPool.getBrowser();
      page = await this.createStealthPage(browser);

      const automationResult = {
        success: false,
        url,
        formType: null,
        processResult: null,
        screenshots: [],
        errors: [],
        metrics: {
          startTime: Date.now(),
          endTime: null,
          duration: null,
          stepsCompleted: 0,
          fieldsFilledCount: 0,
          documentsUploadedCount: 0,
        },
      };

      // Navigate to the URL
      logger.info(`Navigating to: ${url}`);
      await page.goto(url, {
        waitUntil: "networkidle0",
        timeout: this.config.timeout,
      });

      // Add random delay to mimic human behavior
      await getRandomDelay(this.config.delayMin, this.config.delayMax);

      // Take initial screenshot
      if (this.config.screenshotOnError) {
        const screenshot = await this.takeScreenshot(page, "initial");
        if (screenshot) automationResult.screenshots.push(screenshot);
      }

      // Detect form type
      const formInfo = await this.detectFormType(page);
      automationResult.formType = formInfo.type;

      if (formInfo.type === "unknown") {
        throw new Error("Unable to detect application form on the page");
      }

      logger.info(
        `Detected form type: ${formInfo.type} (confidence: ${formInfo.confidence})`
      );

      // Handle multi-step or single-step process
      const processResult = await this.handleMultiStepProcess(
        page,
        applicationData,
        options
      );
      automationResult.processResult = processResult;

      if (processResult.screenshots) {
        automationResult.screenshots.push(...processResult.screenshots);
      }

      if (!processResult.success) {
        automationResult.errors.push(...processResult.errors);
      } else {
        automationResult.success = true;
      }

      // Calculate metrics
      automationResult.metrics.endTime = Date.now();
      automationResult.metrics.duration =
        automationResult.metrics.endTime - automationResult.metrics.startTime;
      automationResult.metrics.stepsCompleted =
        processResult.completedSteps.length;

      // Count filled fields and uploaded documents
      processResult.completedSteps.forEach((step) => {
        if (step.result.fillResult) {
          automationResult.metrics.fieldsFilledCount +=
            step.result.fillResult.filledFields.length;
        }
        if (step.result.uploadResult) {
          automationResult.metrics.documentsUploadedCount +=
            step.result.uploadResult.uploadedDocuments.length;
        }
      });

      logger.info(
        `Browser automation completed: ${
          automationResult.success ? "SUCCESS" : "FAILED"
        } (${automationResult.metrics.duration}ms)`
      );
      return automationResult;
    } catch (error) {
      logger.error("Error in browser automation:", error);

      // Take error screenshot
      if (page && this.config.screenshotOnError) {
        try {
          const screenshot = await this.takeScreenshot(page, "error");
          if (screenshot) {
            if (!automationResult) {
              automationResult = { screenshots: [] };
            }
            automationResult.screenshots.push(screenshot);
          }
        } catch (screenshotError) {
          logger.warn(
            "Failed to take error screenshot:",
            screenshotError.message
          );
        }
      }

      throw new Error(`Browser automation failed: ${error.message}`);
    } finally {
      // Clean up page
      if (page) {
        try {
          await page.close();
        } catch (error) {
          logger.warn("Error closing page:", error.message);
        }
      }
    }
  }
}

module.exports = FormAutomationEngine;
