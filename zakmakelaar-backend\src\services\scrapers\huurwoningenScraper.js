const cheerio = require("cheerio");
const Listing = require("../../models/Listing");
const { sendAlerts } = require("../alertService");
const {
  browserPool,
  validateAndNormalizeListing,
  setupPageStealth,
  autoScroll,
  getRandomDelay,
  scrapingMetrics,
  isRetryableError
} = require("../scraperUtils");
const { validateAndNormalizeListingEnhanced } = require("../transformationIntegration");

// Helper function to fetch detailed listing information
const fetchListingDetails = async (browser, url) => {
  let detailPage = null;
  try {
    detailPage = await browser.newPage();
    await setupPageStealth(detailPage);

    // Set cookies to avoid cookie banners
    await detailPage.setCookie({
      name: "cookie_consent",
      value: "accepted",
      domain: ".huurwoningen.nl",
      httpOnly: false,
      secure: true,
    });

    await detailPage.goto(url, {
      waitUntil: "networkidle2",
      timeout: 30000,
    });

    // Wait for content to load
    await new Promise((r) => setTimeout(r, 2000));

    // Scroll to load all content
    await autoScroll(detailPage);

    const detailHtml = await detailPage.content();
    const $ = cheerio.load(detailHtml);

    // Initialize all possible fields
    let price = null;
    let size = null;
    let bedrooms = null;
    let rooms = null;
    let description = null;
    let year = null;
    let interior = null;
    let propertyType = "woning";
    let energyLabel = null;
    let availableFrom = null;
    let garden = null;
    let balcony = null;
    let parking = null;
    let heating = null;
    let isolation = null;
    let images = [];

    // Extract price from the main price element
    const priceElement = $(".price");
    if (priceElement.length) {
      price = priceElement.text().trim();
      console.log(`Found price: ${price}`);
    }

    // Extract description - try multiple selectors for huurwoningen.nl
    const descriptionSelectors = [
      ".description",
      ".property-description",
      ".listing-description",
      ".object-description",
      ".detail-description",
      ".property-details .description",
      ".listing-details .description",
      ".property-info .description",
      "[class*='description']",
      ".property-text",
      ".listing-text",
      ".object-text",
      ".detail-text",
      ".property-content",
      ".listing-content",
      ".object-content",
      ".detail-content",
      // More specific selectors for huurwoningen.nl
      ".property-details p",
      ".listing-details p",
      ".object-details p",
      ".detail-section p",
      ".property-info p",
      ".listing-info p",
      // Generic paragraph selectors as fallback
      "main p",
      "article p",
      ".content p"
    ];

    for (const selector of descriptionSelectors) {
      const descriptionElement = $(selector);
      if (descriptionElement.length) {
        // Get the text content and clean it up
        let descText = descriptionElement.first().text().trim();

        // Skip if it's too short (likely not a description)
        if (descText.length < 50) continue;

        // Skip if it contains common non-description content
        if (descText.toLowerCase().includes('cookie') ||
          descText.toLowerCase().includes('privacy') ||
          descText.toLowerCase().includes('contact') ||
          descText.toLowerCase().includes('navigation') ||
          descText.toLowerCase().includes('menu')) {
          continue;
        }

        description = descText
          .replace(/\s+/g, ' ')
          .substring(0, 1000); // Limit description length
        console.log(`Found description with selector ${selector}: ${description.substring(0, 50)}...`);
        break; // Stop at first valid description found
      }
    }

    // If no description found with selectors, try to extract from structured data
    if (!description) {
      console.log("No description found with selectors, trying structured data...");
      const jsonLdScripts = $('script[type="application/ld+json"]');
      jsonLdScripts.each((i, script) => {
        try {
          const jsonData = JSON.parse($(script).html());
          if (jsonData.description && jsonData.description.length > 50) {
            description = jsonData.description.replace(/\s+/g, ' ').substring(0, 1000);
            console.log(`Found description in JSON-LD: ${description.substring(0, 50)}...`);
            return false; // Break the each loop
          }
        } catch (e) {
          // Invalid JSON, continue
        }
      });
    }

    // If still no description, try to extract from meta tags
    if (!description) {
      console.log("No description found in structured data, trying meta tags...");
      const metaDescription = $('meta[name="description"]').attr('content');
      if (metaDescription && metaDescription.length > 50) {
        description = metaDescription.replace(/\s+/g, ' ').substring(0, 1000);
        console.log(`Found description in meta tag: ${description.substring(0, 50)}...`);
      }
    }

    // Extract property details from specifications sections
    const detailSections = [
      ".property-features",
      ".property-info",
      ".property-details",
      ".listing-features",
      ".listing-details"
    ];

    for (const sectionSelector of detailSections) {
      const section = $(sectionSelector);
      if (!section.length) continue;

      // Look for detail rows/items in this section
      section.find("li, .feature-item, .detail-item, .property-feature").each((i, el) => {
        const text = $(el).text().trim();

        // Size
        const sizeMatch = text.match(/(\d+)\s*m²|woonoppervlakte:\s*(\d+)\s*m²/i);
        if (sizeMatch && !size) {
          size = `${sizeMatch[1] || sizeMatch[2]} m²`;
          console.log(`Found size: ${size}`);
        }

        // Rooms
        const roomMatch = text.match(/(\d+)\s*kamer|kamers:\s*(\d+)/i);
        if (roomMatch && !rooms) {
          rooms = roomMatch[1] || roomMatch[2];
          console.log(`Found rooms: ${rooms}`);
        }

        // Bedrooms
        const bedroomMatch = text.match(/(\d+)\s*slaapkamer|slaapkamers:\s*(\d+)/i);
        if (bedroomMatch && !bedrooms) {
          bedrooms = bedroomMatch[1] || bedroomMatch[2];
          console.log(`Found bedrooms: ${bedrooms}`);
        }

        // Construction year
        const yearMatch = text.match(/bouwjaar:\s*(\d{4})|\b(19|20)\d{2}\b/i);
        if (yearMatch && !year) {
          year = yearMatch[1] || yearMatch[2];
          console.log(`Found year: ${year}`);
        }

        // Interior
        if (!interior) {
          if (text.toLowerCase().includes("gemeubileerd") || text.toLowerCase().includes("furnished")) {
            interior = "Gemeubileerd";
            console.log(`Found interior: ${interior}`);
          } else if (text.toLowerCase().includes("gestoffeerd") || text.toLowerCase().includes("semi-furnished")) {
            interior = "Gestoffeerd";
            console.log(`Found interior: ${interior}`);
          } else if (text.toLowerCase().includes("kaal") || text.toLowerCase().includes("unfurnished")) {
            interior = "Kaal";
            console.log(`Found interior: ${interior}`);
          }
        }

        // Property type
        if (text.toLowerCase().includes("appartement")) {
          propertyType = "appartement";
          console.log(`Found property type: ${propertyType}`);
        } else if (text.toLowerCase().includes("huis") || text.toLowerCase().includes("woning")) {
          propertyType = "huis";
          console.log(`Found property type: ${propertyType}`);
        } else if (text.toLowerCase().includes("kamer") && !text.toLowerCase().includes("slaapkamer")) {
          propertyType = "kamer";
          console.log(`Found property type: ${propertyType}`);
        } else if (text.toLowerCase().includes("studio")) {
          propertyType = "studio";
          console.log(`Found property type: ${propertyType}`);
        }

        // Energy label
        const energyLabelMatch = text.match(/energielabel:\s*([A-G][+\-]?)/i);
        if (energyLabelMatch && !energyLabel) {
          energyLabel = energyLabelMatch[1];
          console.log(`Found energy label: ${energyLabel}`);
        }

        // Available from
        const availableMatch = text.match(/beschikbaar vanaf:\s*([\d\-\s\/\w]+)/i);
        if (availableMatch && !availableFrom) {
          availableFrom = availableMatch[1].trim();
          console.log(`Found availability: ${availableFrom}`);
        }

        // Garden
        if (text.toLowerCase().includes("tuin") && !garden) {
          garden = "Ja";
          console.log(`Found garden: ${garden}`);
        }

        // Balcony
        if (text.toLowerCase().includes("balkon") && !balcony) {
          balcony = "Ja";
          console.log(`Found balcony: ${balcony}`);
        }

        // Parking
        if ((text.toLowerCase().includes("parkeer") || text.toLowerCase().includes("garage")) && !parking) {
          parking = "Ja";
          console.log(`Found parking: ${parking}`);
        }

        // Heating
        const heatingMatch = text.match(/verwarming:\s*([\w\s\-]+)/i);
        if (heatingMatch && !heating) {
          heating = heatingMatch[1].trim();
          console.log(`Found heating: ${heating}`);
        }

        // Isolation
        const isolationMatch = text.match(/isolatie:\s*([\w\s\-,]+)/i);
        if (isolationMatch && !isolation) {
          isolation = isolationMatch[1].trim();
          console.log(`Found isolation: ${isolation}`);
        }
      });
    }

    // Extract images
    console.log("Extracting images from Huurwoningen listing...");

    // First try the carrousel__track class as specified
    const carrouselTrack = $('.carrousel__track');
    if (carrouselTrack.length) {
      console.log(`Found carrousel__track element, extracting images...`);
      carrouselTrack.find('img').each((i, img) => {
        // Try to get the highest resolution version of the image
        let imgSrc = $(img).attr('data-lazy-srcset') ||
          $(img).attr('srcset') ||
          $(img).attr('data-srcset') ||
          $(img).attr('data-lazy-src') ||
          $(img).attr('data-src') ||
          $(img).attr('src');

        // If we have a srcset, extract the largest image URL
        if (imgSrc && imgSrc.includes(',')) {
          const srcsetParts = imgSrc.split(',');
          // Get the last part which usually has the highest resolution
          const lastPart = srcsetParts[srcsetParts.length - 1].trim();
          // Extract the URL part before the size descriptor
          imgSrc = lastPart.split(' ')[0];
        }

        if (imgSrc && !images.includes(imgSrc)) {
          // Make sure the URL is absolute
          if (imgSrc.startsWith('/')) {
            imgSrc = 'https://www.huurwoningen.nl' + imgSrc;
          }

          // Skip tiny images, icons, and logos
          if (!imgSrc.includes('icon') && !imgSrc.includes('logo')) {
            images.push(imgSrc);
            console.log(`Found image in carrousel__track: ${imgSrc}`);
          }
        }
      });
    }

    // If no images found in carrousel__track, try other selectors as fallback
    if (images.length === 0) {
      const imageSelectors = [
        // Main image gallery
        '.media-viewer img',
        '.slider-wrapper img',
        '.property-photos img',
        '.property-gallery img',
        '.property-slider img',
        '.listing-photos img',
        '.listing-gallery img',
        '.photo-gallery img',
        // Thumbnail gallery
        '.thumbnails img',
        '.property-thumbnails img',
        '.gallery-thumbnails img',
        // Generic selectors as fallback
        '.property-media img',
        '.listing-media img',
        '.property img[src*="media"]',
        '.listing img[src*="media"]',
        // Very generic fallback
        'img[src*="huurwoningen"][src*="media"]',
        'img[data-src*="huurwoningen"][data-src*="media"]'
      ];

      for (const selector of imageSelectors) {
        const imageElements = $(selector);
        if (imageElements.length) {
          imageElements.each((i, img) => {
            // Try to get the highest resolution version of the image
            let imgSrc = $(img).attr('data-lazy-srcset') ||
              $(img).attr('srcset') ||
              $(img).attr('data-srcset') ||
              $(img).attr('data-lazy-src') ||
              $(img).attr('data-src') ||
              $(img).attr('src');

            // If we have a srcset, extract the largest image URL
            if (imgSrc && imgSrc.includes(',')) {
              const srcsetParts = imgSrc.split(',');
              // Get the last part which usually has the highest resolution
              const lastPart = srcsetParts[srcsetParts.length - 1].trim();
              // Extract the URL part before the size descriptor
              imgSrc = lastPart.split(' ')[0];
            }

            if (imgSrc && !images.includes(imgSrc)) {
              // Make sure the URL is absolute
              if (imgSrc.startsWith('/')) {
                imgSrc = 'https://www.huurwoningen.nl' + imgSrc;
              }

              // Skip tiny images, icons, and logos
              if (!imgSrc.includes('icon') && !imgSrc.includes('logo')) {
                images.push(imgSrc);
              }
            }
          });

          if (images.length > 0) {
            console.log(`Found ${images.length} images with selector ${selector}`);
            break;
          }
        }
      }
    }

    // If no images found with selectors, try to extract from JSON-LD data
    if (images.length === 0) {
      const jsonLdScripts = $('script[type="application/ld+json"]');
      jsonLdScripts.each((i, script) => {
        try {
          const jsonData = JSON.parse($(script).html());

          // Look for images in various JSON-LD properties
          if (jsonData.image) {
            if (Array.isArray(jsonData.image)) {
              jsonData.image.forEach(img => {
                if (typeof img === 'string' && !images.includes(img)) {
                  images.push(img);
                }
              });
            } else if (typeof jsonData.image === 'string' && !images.includes(jsonData.image)) {
              images.push(jsonData.image);
            }
          }

          // Check for images in other common properties
          if (jsonData.photo && Array.isArray(jsonData.photo)) {
            jsonData.photo.forEach(photo => {
              if (photo.contentUrl && !images.includes(photo.contentUrl)) {
                images.push(photo.contentUrl);
              }
            });
          }
        } catch (e) {
          // Invalid JSON, continue
        }
      });

      if (images.length > 0) {
        console.log(`Found ${images.length} images in JSON-LD data`);
      }
    }

    // Limit the number of images to prevent excessive data
    if (images.length > 10) {
      console.log(`Limiting images from ${images.length} to 10`);
      images = images.slice(0, 10);
    }

    return {
      price,
      size,
      bedrooms,
      rooms,
      description,
      year,
      interior,
      propertyType,
      energyLabel,
      availableFrom,
      garden,
      balcony,
      parking,
      heating,
      isolation,
      images
    };
  } catch (error) {
    console.log(`Error fetching details for ${url}:`, error.message);
    return {
      price: null,
      size: null,
      bedrooms: null,
      rooms: null,
      description: null,
      year: null,
      interior: null,
      images: []
    };
  } finally {
    if (detailPage) {
      await detailPage.close();
    }
  }
};

const scrapeHuurwoningen = async (retryCount = 0, maxRetries = 3) => {
  if (retryCount === 0) {
    scrapingMetrics.recordScrapeStart();
  }

  let browser = null;
  let page = null;
  let listingsSaved = 0;
  let duplicatesSkipped = 0;

  try {
    browser = await browserPool.getBrowser();
    page = await browser.newPage();

    await setupPageStealth(page);

    // Set cookies to avoid cookie banners
    await page.setCookie({
      name: "cookie_consent",
      value: "accepted",
      domain: ".huurwoningen.nl",
      httpOnly: false,
      secure: true,
    });

    const listings = [];

    // Scrape multiple cities for better coverage
    const searchUrls = [
      "https://www.huurwoningen.nl/in/amsterdam/",
      "https://www.huurwoningen.nl/in/rotterdam/",
      "https://www.huurwoningen.nl/in/den-haag/",
      "https://www.huurwoningen.nl/in/utrecht/",
      "https://www.huurwoningen.nl/in/eindhoven/",
      "https://www.huurwoningen.nl/in/groningen/",
    ];

    for (const searchUrl of searchUrls) {
      try {
        console.log(`Scraping Huurwoningen: ${searchUrl}`);

        await page.goto(searchUrl, {
          waitUntil: "networkidle2",
          timeout: 60000,
        });

        // Add random delay
        await new Promise((resolve) =>
          setTimeout(resolve, getRandomDelay(2000, 4000))
        );

        // Scroll to load more content
        await autoScroll(page);

        const html = await page.content();
        const $ = cheerio.load(html);

        // Extract listings from the page
        const cityListings = [];
        console.log("Analyzing page content for listings...");

        // Try multiple selectors to find listing containers
        // Updated selectors based on actual Huurwoningen structure
        const listingSelectors = [
          '.search-list .search-list__item',        // Main search results
          '.property-list .property-item',          // Property list items
          '.listing-item',                          // Generic listing item
          '.rental-item',                           // Specific to rentals
          'article.listing',                        // Article-based listings
          '.card.property-card',                    // Property cards
          '[data-listing-id]',                      // Elements with listing IDs
          '[data-property-id]',                     // Elements with property IDs
          'a[href*="/huren/"][href*="/\d+/"]'      // Direct links with property IDs
        ];

        let foundListings = false;
        let listingElements = [];

        // Try each selector until we find listings
        for (const selector of listingSelectors) {
          console.log(`Trying selector: ${selector}`);
          const elements = $(selector);
          if (elements.length > 0) {
            console.log(`Found ${elements.length} potential listings with selector: ${selector}`);
            listingElements = elements;
            foundListings = true;
            break;
          }
        }

        // If no listings found with specific selectors, try a more generic approach
        if (!foundListings) {
          console.log("No listings found with specific selectors, trying generic approach...");
          // Look for any elements that might contain rental information
          $('a[href*="/huren/"]').each((i, el) => {
            // Find the closest container that might represent a listing
            const container = $(el).closest('div, article, section, li');
            if (container.length && !listingElements.includes(container[0])) {
              listingElements.push(container[0]);
            }
          });
          console.log(`Found ${listingElements.length} potential listings with generic approach`);
        }

        // Process each listing element
        $(listingElements).each((index, element) => {
          const $element = $(element);

          // Find the link to the listing detail page
          const linkElement = $element.is('a') ?
            $element :
            $element.find('a[href*="/huren/"]').first();

          if (!linkElement.length) return;

          const href = linkElement.attr("href");
          if (!href) return;

          // Skip error pages and invalid URLs
          if (href.includes('meld-een-probleem') ||
            href.includes('error') ||
            href.includes('404') ||
            href.includes('contact') ||
            href.includes('help') ||
            href.includes('support')) {
            console.log(`Skipping error/support page: ${href}`);
            return;
          }

          // Extract title from the link text or nearby heading
          let title = linkElement.text().trim();
          if (!title || title.length < 3) {
            title = $element.find("h1, h2, h3, h4, .title, .property-title").first().text().trim();
          }
          if (!title || title.length < 3) return;

          // Skip invalid titles
          if (title.toLowerCase().includes('laat het ons weten') ||
            title.toLowerCase().includes('meld een probleem') ||
            title.toLowerCase().includes('error') ||
            title.toLowerCase().includes('not found') ||
            title.length < 5) {
            console.log(`Skipping invalid title: ${title}`);
            return;
          }

          console.log(`Found listing: ${title}`);

          // Extract location using multiple approaches
          let location = "";

          // First, look for elements with location-specific classes
          const locationElements = $element.find(".location, .address, .property-address, .listing-address");
          if (locationElements.length) {
            location = locationElements.first().text().trim();
          }

          // If not found, try to find postal code patterns
          if (!location) {
            $element.find("*").each((i, el) => {
              const text = $(el).text().trim();
              // Look for postal code patterns (4 digits + 2 letters + city name)
              const locationMatch = text.match(/(\d{4}\s*[A-Z]{2})\s+([A-Za-z\s\-]+)/);
              if (locationMatch) {
                location = `${locationMatch[1]} ${locationMatch[2]}`.trim();
                return false; // Break the each loop
              }

              // Also look for city names
              const cityMatch = text.match(/\b(Amsterdam|Rotterdam|Utrecht|Den Haag|Eindhoven|Groningen)\b/);
              if (cityMatch && !location) {
                location = cityMatch[1];
                return false; // Break the each loop
              }
            });
          }

          // If still no location, extract from URL or title as last resort
          if (!location) {
            const urlMatch = href.match(/\/in\/([^\/]+)\//i);
            if (urlMatch) {
              location = urlMatch[1].replace(/-/g, ' ');
              location = location.charAt(0).toUpperCase() + location.slice(1); // Capitalize first letter
            } else if (title.includes(' in ')) {
              const titleMatch = title.match(/ in ([A-Za-z\s\-]+)/);
              if (titleMatch) {
                location = titleMatch[1].trim();
              }
            }
          }

          // Extract price using multiple approaches
          let price = "";

          // First, look for elements with price-specific classes
          const priceElements = $element.find(".price, .rental-price, .property-price, .listing-price");
          if (priceElements.length) {
            price = priceElements.first().text().trim();
          }

          // If not found, try to find price patterns
          if (!price) {
            $element.find("*").each((i, el) => {
              const text = $(el).text().trim();
              // Look for euro price patterns
              const priceMatch = text.match(/€\s*[\d.,]+\s*(per\s*maand|p\.m\.|p\/m|per month)?/i);
              if (priceMatch) {
                price = priceMatch[0];
                return false; // Break the each loop
              }
            });
          }

          // If still no price, set as "Price on request"
          if (!price) {
            price = "Prijs op aanvraag";
          }

          // Extract additional details using multiple approaches
          let size = null;
          let rooms = null;
          let year = null;
          let interior = null;

          // First, look for elements with specific classes for each detail
          const sizeElements = $element.find(".size, .property-size, .surface, .area");
          if (sizeElements.length) {
            const sizeText = sizeElements.first().text().trim();
            const sizeMatch = sizeText.match(/(\d+)\s*m²/);
            if (sizeMatch) {
              size = `${sizeMatch[1]} m²`;
            }
          }

          const roomElements = $element.find(".rooms, .property-rooms, .bedrooms");
          if (roomElements.length) {
            const roomText = roomElements.first().text().trim();
            const roomMatch = roomText.match(/(\d+)\s*kamer/);
            if (roomMatch) {
              rooms = `${roomMatch[1]} kamers`;
            }
          }

          // If specific elements not found, try to find patterns in all elements
          $element.find("*").each((i, el) => {
            const text = $(el).text().trim();

            // Size in m²
            if (!size) {
              const sizeMatch = text.match(/(\d+)\s*m²/);
              if (sizeMatch) {
                size = `${sizeMatch[1]} m²`;
              }
            }

            // Number of rooms/bedrooms
            if (!rooms) {
              const roomMatch = text.match(/(\d+)\s*kamer/);
              if (roomMatch) {
                rooms = `${roomMatch[1]} kamers`;
              }
            }

            // Build year
            if (!year) {
              const yearMatch = text.match(/\b(19|20)\d{2}\b/);
              if (yearMatch) {
                year = yearMatch[0];
              }
            }

            // Interior type
            if (!interior) {
              if (text.includes("Kaal")) interior = "Kaal";
              else if (text.includes("Gestoffeerd")) interior = "Gestoffeerd";
              else if (text.includes("Gemeubileerd")) interior = "Gemeubileerd";
            }
          });

          // Build full URL
          const url = href.startsWith("http")
            ? href
            : `https://www.huurwoningen.nl${href}`;

          // Determine property type from title and URL
          let propertyType = "woning";
          const titleLower = title.toLowerCase();
          if (titleLower.includes("appartement") || url.includes("/appartement/"))
            propertyType = "appartement";
          else if (titleLower.includes("huis") || url.includes("/huis/"))
            propertyType = "huis";
          else if (titleLower.includes("kamer") || url.includes("/kamer/"))
            propertyType = "kamer";
          else if (titleLower.includes("studio") || url.includes("/studio/"))
            propertyType = "studio";

          // Only create a listing if we have the minimum required data
          if (title && url) {
            // If location is still empty, use the city from the search URL
            if (!location) {
              const cityMatch = searchUrl.match(/\/in\/([^\/]+)\//i);
              if (cityMatch) {
                location = cityMatch[1].replace(/-/g, ' ');
                location = location.charAt(0).toUpperCase() + location.slice(1); // Capitalize
              } else {
                location = "Unknown";
              }
            }

            const listingData = validateAndNormalizeListing({
              title,
              price: price || "Prijs op aanvraag",
              location,
              url,
              propertyType,
              size,
              rooms,
              year,
              interior,
              source: "huurwoningen.nl",
            });

            if (listingData) {
              console.log(
                `Huurwoningen found: ${listingData.title} - ${listingData.price} - ${listingData.location}`
              );
              cityListings.push(listingData);
            }
          }
        });

        listings.push(...cityListings);
        console.log(`Found ${cityListings.length} listings from ${searchUrl}`);

        // If we didn't find any listings with our selectors, try a more aggressive approach
        if (cityListings.length === 0) {
          console.log("No listings found with standard selectors, trying direct page evaluation...");

          // Use browser evaluation to extract listings directly
          const directListings = await page.evaluate(() => {
            const results = [];
            // Look for any links that might be rental listings
            const links = Array.from(document.querySelectorAll('a[href*="/huren/"]'));

            links.forEach(link => {
              // Try to find the listing container
              const container = link.closest('div[class*="item"], div[class*="card"], article, li');
              if (!container) return;

              // Extract basic information
              const title = link.innerText || container.querySelector('h1, h2, h3, h4')?.innerText;
              if (!title) return;

              const url = link.href;

              // Look for price
              let price = '';
              const priceElement = container.querySelector('[class*="price"]');
              if (priceElement) price = priceElement.innerText;

              // Look for location
              let location = '';
              const locationElement = container.querySelector('[class*="location"], [class*="address"]');
              if (locationElement) location = locationElement.innerText;

              results.push({ title, url, price, location });
            });

            return results;
          });

          console.log(`Found ${directListings.length} listings using direct page evaluation`);

          // Process these listings
          for (const item of directListings) {
            // Check if this URL already exists in our listings to prevent duplicates
            const url = item.url;
            const isDuplicate = listings.some(listing => listing.url === url);

            if (!isDuplicate) {
              const listingData = validateAndNormalizeListing({
                title: item.title,
                price: item.price || "Prijs op aanvraag",
                location: item.location || searchUrl.match(/\/in\/([^\/]+)\//i)?.[1]?.replace(/-/g, ' ') || "Unknown",
                url: item.url,
                propertyType: "woning", // Default
                source: "huurwoningen.nl",
              });

              if (listingData) {
                console.log(`Direct listing found: ${listingData.title} - ${listingData.price}`);
                listings.push(listingData);
              }
            } else {
              console.log(`Skipping duplicate listing with URL: ${url}`);
            }
          }
        }

        // Add delay between cities
        await new Promise((resolve) =>
          setTimeout(resolve, getRandomDelay(3000, 6000))
        );
      } catch (cityError) {
        console.error(`Error scraping ${searchUrl}:`, cityError.message);
        // Continue with other cities even if one fails
      }
    }

    console.log(`Found ${listings.length} total listings from Huurwoningen.`);

    // Deduplicate listings by URL before enrichment
    console.log(`Total listings before deduplication: ${listings.length}`);
    const uniqueUrls = new Set();
    const uniqueListings = [];

    for (const listing of listings) {
      if (!uniqueUrls.has(listing.url)) {
        uniqueUrls.add(listing.url);
        uniqueListings.push(listing);
      } else {
        console.log(`Skipping duplicate listing: ${listing.title}`);
      }
    }

    console.log(`Unique listings after deduplication: ${uniqueListings.length}`);

    // Enrich listings with detailed information
    const enrichedListings = [];
    for (const listingData of uniqueListings) {
      try {
        console.log(`Fetching details for: ${listingData.title} (${listingData.url})`);

        // Add random delay between requests
        await new Promise(resolve => setTimeout(resolve, getRandomDelay(1500, 3000)));

        // Fetch detailed information
        const details = await fetchListingDetails(browser, listingData.url);

        // Merge details with basic listing data
        if (details.price) listingData.price = details.price;
        if (details.size) listingData.size = details.size;
        if (details.bedrooms) listingData.bedrooms = details.bedrooms;
        if (details.rooms) listingData.rooms = details.rooms;
        if (details.description) listingData.description = details.description;
        if (details.year) listingData.year = details.year;
        if (details.interior) listingData.interior = details.interior;
        if (details.propertyType) listingData.propertyType = details.propertyType;
        if (details.images && details.images.length > 0) listingData.images = details.images;

        // Store extended details as JSON
        const extendedDetails = {};
        if (details.energyLabel) extendedDetails.energyLabel = details.energyLabel;
        if (details.availableFrom) extendedDetails.availableFrom = details.availableFrom;
        if (details.garden) extendedDetails.garden = details.garden;
        if (details.balcony) extendedDetails.balcony = details.balcony;
        if (details.parking) extendedDetails.parking = details.parking;
        if (details.heating) extendedDetails.heating = details.heating;
        if (details.isolation) extendedDetails.isolation = details.isolation;

        // Only add extendedDetails if we have any
        if (Object.keys(extendedDetails).length > 0) {
          listingData.extendedDetails = JSON.stringify(extendedDetails);
        }

        // Validate and normalize the enriched listing
        const enrichedListing = validateAndNormalizeListing(listingData);
        if (enrichedListing) {
          console.log(`Enriched listing: ${enrichedListing.title}`);
          enrichedListings.push(enrichedListing);
        }
      } catch (detailError) {
        console.error(`Error enriching listing ${listingData.title}:`, detailError.message);
        // Still add the basic listing if we couldn't get details
        enrichedListings.push(listingData);
      }
    }

    console.log(`Enriched ${enrichedListings.length} listings with details.`);

    // Process listings with better error handling
    for (const listingData of enrichedListings) {
      try {
        // Check if this listing already exists in the database
        const existingListing = await Listing.findOne({ url: listingData.url });

        if (existingListing) {
          console.log(`Listing already exists in database: ${listingData.title}`);
          duplicatesSkipped++;

          // Update the existing listing with any new information
          const updatedFields = {};

          // Only update fields that have changed
          if (listingData.price && listingData.price !== existingListing.price) {
            updatedFields.price = listingData.price;
          }

          if (listingData.images && listingData.images.length > 0 &&
            (!existingListing.images || existingListing.images.length === 0)) {
            updatedFields.images = listingData.images;
          }

          if (Object.keys(updatedFields).length > 0) {
            await Listing.updateOne({ _id: existingListing._id }, { $set: updatedFields });
            console.log(`Updated existing listing: ${existingListing.title}`);
          }

        } else {
          // Transform the listing data using the unified schema
          const transformedData = await validateAndNormalizeListingEnhanced(listingData);

          if (!transformedData) {
            console.log(`Skipping invalid listing: ${listingData.title || 'Unknown'}`);
            continue;
          }

          // Save as a new listing
          const newListing = new Listing(transformedData);
          await newListing.save();
          console.log(`Saved new listing: ${newListing.title}`);
          listingsSaved++;
          sendAlerts(newListing);
        }
      } catch (error) {
        if (error.code === 11000) {
          // Duplicate key error
          console.log(`Skipping duplicate listing: ${listingData.title}`);
          duplicatesSkipped++;
        } else {
          console.error(`Error saving listing ${listingData.title}:`, error);
        }
      }
    }

    scrapingMetrics.recordScrapeSuccess(
      listings.length,
      listingsSaved,
      duplicatesSkipped
    );
    return listings;
  } catch (error) {
    console.error(
      `Error during Huurwoningen scraping (attempt ${retryCount + 1}/${maxRetries + 1
      }):`,
      error
    );

    // Record failure only on first attempt
    if (retryCount === 0) {
      scrapingMetrics.recordScrapeFailure(error);
    }

    // Retry logic for transient errors
    if (retryCount < maxRetries && isRetryableError(error)) {
      console.log(
        `Retrying Huurwoningen scraping in ${(retryCount + 1) * 5} seconds...`
      );
      await new Promise((resolve) =>
        setTimeout(resolve, (retryCount + 1) * 5000)
      );
      return scrapeHuurwoningen(retryCount + 1, maxRetries);
    }

    console.log(
      `Huurwoningen scraping failed after ${maxRetries + 1
      } attempts. Saved: ${listingsSaved}, Duplicates: ${duplicatesSkipped}`
    );
    return [];
  } finally {
    // Close the page to free up resources
    if (page) {
      try {
        await page.close();
      } catch (closeError) {
        console.error("Error closing Huurwoningen page:", closeError);
      }
    }
    console.log(
      `Huurwoningen scraping completed. Saved: ${listingsSaved}, Duplicates: ${duplicatesSkipped}`
    );
  }
};

module.exports = {
  scrapeHuurwoningen,
  fetchListingDetails
};
