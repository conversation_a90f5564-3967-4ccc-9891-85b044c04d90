const express = require("express");
const router = express.Router();
const aiService = require("../services/aiService");
const { auth } = require("../middleware/auth");
const { logHelpers } = require("../services/logger");
const Listing = require("../models/Listing");

/**
 * @swagger
 * /api/ai/match:
 *   post:
 *     summary: AI-powered listing matching
 *     tags: [AI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - listing
 *               - userPreferences
 *             properties:
 *               listing:
 *                 type: object
 *                 properties:
 *                   title: { type: string }
 *                   price: { type: string }
 *                   location: { type: string }
 *                   size: { type: string }
 *                   rooms: { type: string }
 *                   propertyType: { type: string }
 *               userPreferences:
 *                 type: object
 *                 properties:
 *                   location: { type: string }
 *                   budget: { type: number }
 *                   rooms: { type: number }
 *                   propertyType: { type: string }
 *     responses:
 *       200:
 *         description: AI matching result
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 score: { type: number }
 *                 matchReasoning: { type: string }
 *                 keyHighlights: { type: array }
 *                 potentialConcerns: { type: array }
 *                 recommendation: { type: string }
 */
router.post("/match", auth, async (req, res) => {
  try {
    const { userPreferences } = req.body;
    const startTime = Date.now();

    // Log incoming payload
    console.log(
      "[AI MATCH] Incoming payload:",
      JSON.stringify({ userPreferences }, null, 2)
    );

    // Fetch only the 10 most recent listings from the database
    const listings = await Listing.find({}).sort({ dateAdded: -1 }).limit(10);
    if (!listings.length) {
      return res
        .status(404)
        .json({ success: false, error: "No listings found" });
    }

    // For each listing, get the AI match score
    let bestMatch = null;
    let bestScore = -1;
    let bestResult = null;
    for (const listing of listings) {
      try {
        const result = await aiService.matchListingToUser(
          listing,
          userPreferences
        );
        if (
          result &&
          typeof result.score === "number" &&
          result.score > bestScore
        ) {
          bestScore = result.score;
          bestMatch = listing;
          bestResult = result;
        }
      } catch (err) {
        console.error("[AI MATCH] Error matching listing:", listing._id, err);
      }
    }

    if (!bestResult) {
      return res
        .status(404)
        .json({ success: false, error: "No suitable match found" });
    }

    // Log AI service result
    console.log(
      "[AI MATCH] AI service result:",
      JSON.stringify(bestResult, null, 2)
    );

    const duration = Date.now() - startTime;
    logHelpers.logAiPerformance("listing_matching", duration, "claude-3-haiku");

    res.json({
      success: true,
      data: bestResult,
      bestListing: bestMatch,
      performance: {
        duration: `${duration}ms`,
      },
    });
  } catch (error) {
    // Log error
    console.error("[AI MATCH] Error:", error);
    logHelpers.logAiOperation("listing_matching", "error", error.message);
    res.status(500).json({
      success: false,
      error: "AI matching failed",
      details: error.message,
    });
  }
});

/**
 * @swagger
 * /api/ai/contract/analyze:
 *   post:
 *     summary: Analyze rental contract for legal compliance
 *     tags: [AI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - contractText
 *             properties:
 *               contractText: { type: string }
 *               language: { type: string, default: "dutch" }
 *     responses:
 *       200:
 *         description: Contract analysis result
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 riskLevel: { type: string }
 *                 complianceScore: { type: number }
 *                 keyClauses: { type: array }
 *                 potentialIssues: { type: array }
 *                 recommendations: { type: array }
 *                 summary: { type: string }
 *                 legalAdvice: { type: string }
 */
router.post("/contract/analyze", auth, async (req, res) => {
  try {
    const { contractText, language = "dutch" } = req.body;
    const startTime = Date.now();

    const result = await aiService.analyzeContract(contractText, language);

    const duration = Date.now() - startTime;
    logHelpers.logAiPerformance("contract_analysis", duration, "gpt-4o-mini");

    res.json({
      success: true,
      data: result,
      performance: {
        duration: `${duration}ms`,
      },
    });
  } catch (error) {
    logHelpers.logAiOperation("contract_analysis", "error", error.message);
    res.status(500).json({
      success: false,
      error: "Contract analysis failed",
      details: error.message,
    });
  }
});

/**
 * @swagger
 * /api/ai/application/generate:
 *   post:
 *     summary: Generate personalized application message
 *     tags: [AI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - listing
 *               - userProfile
 *             properties:
 *               listing:
 *                 type: object
 *                 properties:
 *                   title: { type: string }
 *                   location: { type: string }
 *                   price: { type: string }
 *               userProfile:
 *                 type: object
 *                 properties:
 *                   name: { type: string }
 *                   income: { type: number }
 *                   occupation: { type: string }
 *               template: { type: string, default: "professional" }
 *     responses:
 *       200:
 *         description: Generated application message
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message: { type: string }
 *                 template: { type: string }
 *                 generatedAt: { type: string }
 */
router.post("/application/generate", auth, async (req, res) => {
  try {
    const { listing, userProfile, template = "professional" } = req.body;
    const startTime = Date.now();

    // Validate required fields
    if (!listing || !userProfile) {
      return res.status(400).json({
        success: false,
        error: "Missing required fields",
        details: "Both listing and userProfile are required",
      });
    }

    let result;
    try {
      result = await aiService.generateApplicationMessage(
        listing,
        userProfile,
        template
      );
    } catch (aiError) {
      console.log("AI service failed, using fallback:", aiError.message);
      
      // Fallback response when AI service is not available
      result = {
        message: `Dear Landlord,

I am writing to express my strong interest in your ${template} rental property: ${listing.title || 'property'}.

As a ${userProfile.occupation || 'professional'} with a stable income${userProfile.income ? ` of €${userProfile.income}` : ''}, I believe I would be an excellent tenant for your property. I am looking for a ${template === 'student' ? 'quiet place to study' : template === 'expat' ? 'comfortable home as I settle in the Netherlands' : 'long-term rental'} and am committed to maintaining the property in excellent condition.

${listing.price ? `The asking price of €${listing.price} fits well within my budget, ` : ''}and I am prepared to provide all necessary documentation including proof of income, references, and a security deposit.

I would be delighted to schedule a viewing at your earliest convenience and am available for any questions you may have.

Thank you for your consideration.

Best regards,
${userProfile.name || 'Applicant'}`,
        subject: `Application for ${listing.title || 'Rental Property'}`,
        template,
        personalizedElements: ["Property title", "User occupation", "Budget alignment"],
        tips: [
          "Follow up within 24-48 hours if no response",
          "Be prepared with all required documents",
          "Show genuine interest during viewing"
        ],
        generatedAt: new Date().toISOString(),
        fallback: true,
      };
    }

    const duration = Date.now() - startTime;
    logHelpers.logAiPerformance(
      "application_generation",
      duration,
      result.fallback ? "fallback" : "ai-service"
    );

    res.json({
      status: "success",
      data: result,
      performance: {
        duration: `${duration}ms`,
      },
    });
  } catch (error) {
    console.error("Application generation error:", error);
    logHelpers.logAiOperation("application_generation", "error", error.message);
    res.status(500).json({
      status: "error",
      error: "Application generation failed",
      details: error.message,
    });
  }
});

/**
 * @swagger
 * /api/ai/market/analyze:
 *   post:
 *     summary: Analyze market trends and provide predictions
 *     tags: [AI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - location
 *               - propertyType
 *             properties:
 *               location: { type: string }
 *               propertyType: { type: string }
 *               historicalData: { type: object }
 *     responses:
 *       200:
 *         description: Market analysis result
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 marketTrend: { type: string }
 *                 pricePrediction: { type: string }
 *                 demandLevel: { type: string }
 *                 keyInsights: { type: array }
 *                 recommendations: { type: array }
 *                 confidenceScore: { type: number }
 */
router.post("/market/analyze", auth, async (req, res) => {
  try {
    const { location, propertyType, historicalData = {} } = req.body;
    const startTime = Date.now();

    const result = await aiService.analyzeMarketTrends(
      location,
      propertyType,
      historicalData
    );

    const duration = Date.now() - startTime;
    logHelpers.logAiPerformance("market_analysis", duration, "gpt-4o-mini");

    res.json({
      success: true,
      data: result,
      performance: {
        duration: `${duration}ms`,
      },
    });
  } catch (error) {
    logHelpers.logAiOperation("market_analysis", "error", error.message);
    res.status(500).json({
      success: false,
      error: "Market analysis failed",
      details: error.message,
    });
  }
});

/**
 * @swagger
 * /api/ai/listing/summarize:
 *   post:
 *     summary: Generate smart listing summary
 *     tags: [AI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - listing
 *             properties:
 *               listing:
 *                 type: object
 *                 properties:
 *                   title: { type: string }
 *                   price: { type: string }
 *                   location: { type: string }
 *                   size: { type: string }
 *                   rooms: { type: string }
 *                   propertyType: { type: string }
 *               language: { type: string, default: "english" }
 *     responses:
 *       200:
 *         description: Listing summary
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 summary: { type: string }
 *                 language: { type: string }
 *                 generatedAt: { type: string }
 */
router.post("/listing/summarize", auth, async (req, res) => {
  try {
    const { listing, language = "english" } = req.body;
    const startTime = Date.now();

    const result = await aiService.summarizeListing(listing, language);

    const duration = Date.now() - startTime;
    logHelpers.logAiPerformance(
      "listing_summarization",
      duration,
      "llama-3.1-8b"
    );

    res.json({
      success: true,
      data: result,
      performance: {
        duration: `${duration}ms`,
      },
    });
  } catch (error) {
    logHelpers.logAiOperation("listing_summarization", "error", error.message);
    res.status(500).json({
      success: false,
      error: "Listing summarization failed",
      details: error.message,
    });
  }
});

/**
 * @swagger
 * /api/ai/application/submit:
 *   post:
 *     summary: Submit rental application
 *     tags: [AI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - applicationId
 *               - method
 *             properties:
 *               applicationId: { type: string }
 *               method: { type: string, enum: [manual, autonomous] }
 *               applicationData:
 *                 type: object
 *                 properties:
 *                   listingId: { type: string }
 *                   message: { type: string }
 *                   subject: { type: string }
 *                   contactInfo: { type: object }
 *     responses:
 *       200:
 *         description: Application submitted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success: { type: boolean }
 *                 submissionId: { type: string }
 *                 message: { type: string }
 *                 submittedAt: { type: string }
 */
router.post("/application/submit", auth, async (req, res) => {
  try {
    const { applicationId, method, applicationData } = req.body;
    const startTime = Date.now();

    // Validate required fields
    if (!applicationId || !method) {
      return res.status(400).json({
        success: false,
        error: "Missing required fields",
        details: "applicationId and method are required",
      });
    }

    if (!['manual', 'autonomous'].includes(method)) {
      return res.status(400).json({
        success: false,
        error: "Invalid submission method",
        details: "Method must be either 'manual' or 'autonomous'",
      });
    }

    if (!applicationData || !applicationData.listingId) {
      return res.status(400).json({
        success: false,
        error: "Missing application data",
        details: "listingId is required in applicationData",
      });
    }

    // Import the Application model
    const Application = require("../models/Application");
    const Listing = require("../models/Listing");

    // Generate a unique submission ID
    const submissionId = `sub_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const submittedAt = new Date();

    // Validate and get the listing details for the application
    console.log('[APPLICATION SUBMIT] Looking for listing:', applicationData.listingId);
    
    let listing;
    
    // For demo/testing purposes, if no real listing is found, create a mock one
    try {
      // Try to find by ObjectId first
      if (applicationData.listingId.length === 24) {
        listing = await Listing.findById(applicationData.listingId);
      }
      
      // If not found by ObjectId, try other searches
      if (!listing) {
        listing = await Listing.findOne({
          $or: [
            { id: applicationData.listingId },
            { url: { $regex: applicationData.listingId, $options: 'i' } }
          ]
        });
      }
      
    } catch (objectIdError) {
      console.log('[APPLICATION SUBMIT] ObjectId error:', objectIdError.message);
    }
    
    // If still no listing found, create a mock listing for demo purposes
    if (!listing) {
      console.log('[APPLICATION SUBMIT] No listing found, creating mock listing for demo');
      listing = {
        _id: applicationData.listingId,
        title: applicationData.propertyTitle || 'Demo Property',
        location: 'Amsterdam',
        price: 1800,
        propertyType: 'apartment'
      };
    }
    
    console.log('[APPLICATION SUBMIT] Using listing:', listing.title);

    // Debug logging
    console.log('[APPLICATION SUBMIT] User data:', {
      userId: req.user._id,
      name: req.user.name,
      firstName: req.user.firstName,
      lastName: req.user.lastName,
      email: req.user.email
    });
    
    console.log('[APPLICATION SUBMIT] Listing data:', {
      listingId: listing._id,
      title: listing.title,
      location: listing.location,
      price: listing.price
    });

    // Safely construct user name
    let userName = 'Unknown User';
    if (req.user.name) {
      userName = req.user.name;
    } else if (req.user.firstName && req.user.lastName) {
      userName = `${req.user.firstName} ${req.user.lastName}`;
    } else if (req.user.firstName) {
      userName = req.user.firstName;
    }

    // Safely extract tenant score - handle both number and object formats
    let tenantScore = 0;
    if (req.user.tenantScore) {
      if (typeof req.user.tenantScore === 'number') {
        tenantScore = req.user.tenantScore;
      } else if (typeof req.user.tenantScore === 'object' && req.user.tenantScore.overallScore !== undefined) {
        tenantScore = req.user.tenantScore.overallScore || 0;
      }
    }

    // Safely extract profile completeness - handle both number and object formats
    let profileCompleteness = 0;
    if (req.user.profileCompleteness) {
      if (typeof req.user.profileCompleteness === 'number') {
        profileCompleteness = req.user.profileCompleteness;
      } else if (typeof req.user.profileCompleteness === 'object' && req.user.profileCompleteness.overallScore !== undefined) {
        profileCompleteness = req.user.profileCompleteness.overallScore || 0;
      }
    }

    console.log('[APPLICATION SUBMIT] Processed user data:', {
      userName,
      tenantScore,
      profileCompleteness,
      originalTenantScore: typeof req.user.tenantScore,
      originalProfileCompleteness: typeof req.user.profileCompleteness
    });

    // Create the application document in the database
    const application = new Application({
      applicant: {
        userId: req.user._id,
        snapshot: {
          name: userName,
          email: req.user.email || '<EMAIL>',
          phone: req.user.phone || '',
          tenantScore: tenantScore,
          tenantScoreGrade: req.user.tenantScoreGrade || 'N/A',
          profileCompleteness: profileCompleteness
        }
      },
      
      property: {
        propertyId: listing._id,
        snapshot: {
          title: listing.title || 'Untitled Property',
          address: listing.location || listing.address || 'Address not specified',
          rent: typeof listing.price === 'number' ? listing.price : (parseInt(listing.price) || 0),
          propertyType: listing.propertyType || listing.type || 'Unknown'
        }
      },
      
      applicationData: {
        moveInDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // Default to 30 days from now
        numberOfOccupants: 1,
        personalMessage: applicationData.message || '',
        employment: {
          monthlyIncome: req.user.income || 0,
          employer: req.user.employer || '',
          occupation: req.user.occupation || '',
          contractType: req.user.contractType || 'permanent'
        }
      },
      
      status: 'submitted',
      submittedAt: submittedAt,
      
      metadata: {
        source: method === 'manual' ? 'manual_submission' : 'autonomous_submission',
        userAgent: req.headers['user-agent'] || 'Unknown',
        ipAddress: req.ip || req.connection.remoteAddress || 'Unknown',
        applicationVersion: '1.0.0'
      }
    });

    // Save the application to the database
    console.log('[APPLICATION SUBMIT] Attempting to save application...');
    let savedApplication;
    try {
      savedApplication = await application.save();
      console.log('[APPLICATION SUBMIT] Application saved successfully with ID:', savedApplication._id);
    } catch (saveError) {
      console.error('[APPLICATION SUBMIT] Database save error:', saveError);
      return res.status(500).json({
        success: false,
        error: "Failed to save application to database",
        details: saveError.message,
      });
    }

    console.log(`[APPLICATION SUBMIT] ${method} submission saved to database:`, {
      applicationId: savedApplication._id,
      submissionId,
      listingId: applicationData.listingId,
      userId: req.user._id,
      submittedAt: submittedAt.toISOString(),
    });

    const result = {
      success: true,
      submissionId,
      applicationDbId: savedApplication._id,
      message: method === 'manual' 
        ? 'Application submitted successfully! You will receive a confirmation email shortly.'
        : 'Application queued for autonomous submission. Our AI will submit it at the optimal time.',
      submittedAt: submittedAt.toISOString(),
      method,
      applicationData: {
        listingId: applicationData.listingId,
        subject: applicationData.subject,
        messageLength: applicationData.message?.length || 0,
        propertyTitle: listing.title,
        applicantName: req.user.name || req.user.firstName + ' ' + req.user.lastName || 'Unknown'
      },
    };

    const duration = Date.now() - startTime;
    logHelpers.logAiPerformance("application_submission", duration, "manual");

    res.json({
      success: true,
      data: result,
      performance: {
        duration: `${duration}ms`,
      },
    });
  } catch (error) {
    console.error("Application submission error:", error);
    console.error("Error stack:", error.stack);
    
    // Log more details about the error
    console.error("Error details:", {
      name: error.name,
      message: error.message,
      code: error.code,
      stack: error.stack
    });
    
    logHelpers.logAiOperation("application_submission", "error", error.message);
    res.status(500).json({
      success: false,
      error: "Application submission failed",
      details: error.message,
      errorType: error.name,
      errorCode: error.code
    });
  }
});

/**
 * @swagger
 * /api/ai/application/test:
 *   post:
 *     summary: Test application submission (simplified)
 *     tags: [AI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               applicationId: { type: string }
 *               method: { type: string }
 *               applicationData: { type: object }
 *     responses:
 *       200:
 *         description: Test submission successful
 */
router.post("/application/test", auth, async (req, res) => {
  try {
    const { applicationId, method, applicationData } = req.body;
    
    console.log('[APPLICATION TEST] Received request:', {
      applicationId,
      method,
      applicationData,
      user: req.user ? req.user._id : 'No user'
    });

    // Simple validation
    if (!applicationId || !method) {
      return res.status(400).json({
        success: false,
        error: "Missing required fields",
        details: "applicationId and method are required",
      });
    }

    // Just return success without database operations
    const result = {
      success: true,
      submissionId: `test_${Date.now()}`,
      message: 'Test submission successful - no database operations performed',
      receivedData: {
        applicationId,
        method,
        applicationData,
        userId: req.user?._id,
        userName: req.user?.name || req.user?.firstName
      }
    };

    res.json({
      success: true,
      data: result
    });

  } catch (error) {
    console.error("Test submission error:", error);
    res.status(500).json({
      success: false,
      error: "Test submission failed",
      details: error.message,
    });
  }
});

/**
 * @swagger
 * /api/ai/applications:
 *   get:
 *     summary: Get user's applications
 *     tags: [AI]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [draft, submitted, under_review, approved, rejected]
 *         description: Filter by application status
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of applications to return
 *     responses:
 *       200:
 *         description: List of user applications
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success: { type: boolean }
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       _id: { type: string }
 *                       status: { type: string }
 *                       submittedAt: { type: string }
 *                       property: { type: object }
 *                       applicationData: { type: object }
 */
router.get("/applications", auth, async (req, res) => {
  try {
    const { status, limit = 10 } = req.query;
    const Application = require("../models/Application");

    // Build query
    const query = { 'applicant.userId': req.user._id };
    if (status) {
      query.status = status;
    }

    // Get applications
    const applications = await Application.find(query)
      .sort({ createdAt: -1 })
      .limit(parseInt(limit))
      .populate('property.propertyId', 'title location price')
      .lean();

    console.log(`[GET APPLICATIONS] Found ${applications.length} applications for user ${req.user._id}`);

    res.json({
      success: true,
      data: applications,
      total: applications.length,
    });
  } catch (error) {
    console.error("Get applications error:", error);
    res.status(500).json({
      success: false,
      error: "Failed to retrieve applications",
      details: error.message,
    });
  }
});

/**
 * @swagger
 * /api/ai/translate:
 *   post:
 *     summary: Translate real estate content
 *     tags: [AI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - text
 *               - targetLanguage
 *             properties:
 *               text: 
 *                 type: string
 *                 description: The text content to translate
 *               targetLanguage: 
 *                 type: string
 *                 enum: [en, nl, de, fr, es, english, dutch, german, french, spanish]
 *                 description: The target language for translation (supports both ISO codes and language names)
 *               sourceLanguage: 
 *                 type: string
 *                 description: The source language (optional, auto-detected if not provided). Supports both ISO codes and language names
 *     responses:
 *       200:
 *         description: Translated content
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success: { type: boolean }
 *                 data:
 *                   type: object
 *                   properties:
 *                     translatedText: { type: string }
 *                     sourceLanguage: { type: string }
 *                     targetLanguage: { type: string }
 *                     confidence: { type: number }
 *                     original: { type: string }
 *                     translatedAt: { type: string }
 *       400:
 *         description: Missing required parameters
 *       500:
 *         description: Translation failed
 */
router.post("/translate", auth, async (req, res) => {
  try {
    const { text, targetLanguage, sourceLanguage } = req.body;
    
    // Support both old and new parameter formats for backward compatibility
    const content = text || req.body.content;
    const toLanguage = targetLanguage || req.body.toLanguage;
    const fromLanguage = sourceLanguage || req.body.fromLanguage || 'auto';
    
    if (!content || !toLanguage) {
      return res.status(400).json({
        success: false,
        error: "Missing required parameters",
        details: "Both 'text' and 'targetLanguage' are required"
      });
    }
    
    // Normalize language codes to ISO format
    const normalizeLanguage = (lang) => {
      const langMap = {
        'dutch': 'nl',
        'english': 'en',
        'german': 'de',
        'french': 'fr',
        'spanish': 'es',
        'nl': 'nl',
        'en': 'en',
        'de': 'de',
        'fr': 'fr',
        'es': 'es',
        'auto': 'auto'
      };
      return langMap[lang.toLowerCase()] || lang;
    };
    
    const normalizedFromLanguage = normalizeLanguage(fromLanguage);
    const normalizedToLanguage = normalizeLanguage(toLanguage);
    
    const startTime = Date.now();

    const result = await aiService.translateContent(
      content,
      normalizedFromLanguage,
      normalizedToLanguage
    );

    const duration = Date.now() - startTime;
    logHelpers.logAiPerformance(
      "content_translation",
      duration,
      "gemini-flash-1.5"
    );

    // Format response to match frontend expectations
    const formattedResult = {
      translatedText: result.translation,
      sourceLanguage: result.fromLanguage,
      targetLanguage: result.toLanguage,
      confidence: 0.95, // Default confidence score
      original: result.original,
      translatedAt: result.translatedAt
    };

    res.json({
      success: true,
      data: formattedResult,
      performance: {
        duration: `${duration}ms`,
      },
    });
  } catch (error) {
    logHelpers.logAiOperation("content_translation", "error", error.message);
    res.status(500).json({
      success: false,
      error: "Translation failed",
      details: error.message,
    });
  }
});

/**
 * @swagger
 * /api/ai/market-analysis:
 *   post:
 *     summary: Get market analysis data
 *     tags: [AI]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               city:
 *                 type: string
 *                 description: City name for market analysis
 *               propertyType:
 *                 type: string
 *                 description: Property type filter
 *               userPreferences:
 *                 type: object
 *                 description: User preferences for enhanced analysis
 *     responses:
 *       200:
 *         description: Market analysis data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 averagePrice:
 *                   type: number
 *                 priceRange:
 *                   type: object
 *                   properties:
 *                     min: { type: number }
 *                     max: { type: number }
 *                 marketTrends:
 *                   type: array
 *                   items:
 *                     type: object
 *                 insights:
 *                   type: array
 *                   items:
 *                     type: string
 *       400:
 *         description: Bad request
 *       401:
 *         description: Unauthorized
 */
router.post("/market-analysis", async (req, res) => {
  try {
    console.log("Market Analysis Request Body:", JSON.stringify(req.body, null, 2));
    const { city, location, propertyType, userPreferences, priceRange: requestPriceRange, timeframe } = req.body;
    const startTime = Date.now();

    console.log('AI Market Analysis Request:', {
      location: location || city,
      propertyType,
      priceRange: requestPriceRange,
      timeframe
    });
    
    // Add detailed logging to help diagnose issues
    console.log('Starting market analysis with parameters:', {
      searchLocation: location || city,
      propertyType,
      hasUserPreferences: !!userPreferences,
      requestedTimeframe: timeframe || 'default'
    });

    // Use location field if provided, otherwise fall back to city
    const searchLocation = location || city;

    // Build query filter
    const filter = {};
    if (searchLocation) {
      filter.location = new RegExp(searchLocation, 'i');
    }
    if (propertyType) {
      filter.propertyType = new RegExp(propertyType, 'i');
    }

    // Get listings for analysis
    const listings = await Listing.find(filter).limit(1000);

    if (listings.length === 0) {
      return res.status(200).json({
        averagePrice: 0,
        priceRange: { min: 0, max: 0 },
        marketTrends: [],
        insights: ["No data available for the specified criteria"],
        totalListings: 0
      });
    }

    // Calculate market metrics
    const prices = listings
      .map(listing => {
        // Try to extract numeric price from price string
        if (!listing.price) return 0;

        try {
          // Handle different price formats
          // Extract numbers with dots or commas as thousand separators (€3.950 or €3,950)
          const priceMatch = listing.price.match(/€\s*([\d.,]+)/);
          if (priceMatch) {
            const extractedPrice = priceMatch[1].trim();
            
            // Case 1: Format with dots as thousands separators (e.g., 3.950)
            if (extractedPrice.includes('.') && !extractedPrice.includes(',')) {
              const cleanPrice = extractedPrice.replace(/\./g, '');
              return parseFloat(cleanPrice);
            }
            
            // Case 2: Format with commas as thousands separators (e.g., 3,950)
            if (extractedPrice.includes(',') && !extractedPrice.includes('.')) {
              const cleanPrice = extractedPrice.replace(/,/g, '');
              return parseFloat(cleanPrice);
            }
            
            // Case 3: Format with both dots and commas (e.g., 1.234,56)
            if (extractedPrice.includes('.') && extractedPrice.includes(',')) {
              const cleanPrice = extractedPrice.replace(/\./g, '').replace(',', '.');
              return parseFloat(cleanPrice);
            }
            
            // Case 4: Simple number
            return parseFloat(extractedPrice.replace(',', '.'));
          }
          
          // Fallback to simple number extraction
          const simpleMatch = listing.price.match(/\d+/);
          return simpleMatch ? parseInt(simpleMatch[0]) : 0;
        } catch (parseError) {
          console.error('Error parsing price:', listing.price, parseError);
          return 0;
        }
      })
      .filter(price => price > 0);

    // Add safety checks for price calculations
    let averagePrice = 0;
    let priceRange = { min: 0, max: 0 };

    try {
      if (prices.length > 0) {
        // Calculate average price with safety checks
        const sum = prices.reduce((acc, price) => acc + (isNaN(price) ? 0 : price), 0);
        averagePrice = Math.round(sum / prices.length);

        // Calculate price range with safety checks
        priceRange = {
          min: Math.min(...prices.filter(p => !isNaN(p))),
          max: Math.max(...prices.filter(p => !isNaN(p)))
        };

        // Additional validation
        if (isNaN(averagePrice)) averagePrice = 0;
        if (isNaN(priceRange.min)) priceRange.min = 0;
        if (isNaN(priceRange.max)) priceRange.max = 0;
      }
    } catch (calcError) {
      console.error('Error calculating price metrics:', calcError);
      averagePrice = 0;
      priceRange = { min: 0, max: 0 };
    }

    // Generate market insights
    const insights = [];
    if (prices.length > 0) {
      insights.push(`Average price: €${averagePrice.toLocaleString()}`);
      insights.push(`Price range: €${priceRange.min.toLocaleString()} - €${priceRange.max.toLocaleString()}`);
      insights.push(`Total listings analyzed: ${listings.length}`);

      if (city) {
        insights.push(`Market data for ${city}`);
      }
      if (propertyType) {
        insights.push(`Property type: ${propertyType}`);
      }
    }

    // Simple market trends (could be enhanced with time-series data)
    const marketTrends = [
      { period: "current", averagePrice, listingCount: listings.length }
    ];

    const response = {
      success: true,
      data: {
        location: searchLocation || "Unknown",
        averagePrice,
        priceRange: {
          min: priceRange.min,
          max: priceRange.max
        },
        marketTrend: "stable", // Default value
        pricePrediction: "Prices are expected to remain stable",
        demandLevel: "medium",
        keyInsights: insights,
        recommendations: [
          "Set a budget based on your financial situation",
          "Consider multiple neighborhoods to increase options"
        ],
        confidenceScore: 70,
        dataPoints: listings.length
      },
      marketTrends,
      insights,
      totalListings: listings.length
    };

    console.log(`Market analysis completed in ${Date.now() - startTime}ms`);
    console.log('Market analysis response structure:', Object.keys(response));
    res.json(response);

  } catch (error) {
    console.error('AI Market Analysis Error:', error);
    console.error('Error stack:', error.stack);
    
    // Log more details about the error context
    console.error('Error context:', {
      errorName: error.name,
      errorCode: error.code,
      errorType: typeof error
    });
    
    // Provide more detailed error information
    res.status(500).json({
      success: false,
      error: "Internal server error",
      message: "Failed to generate market analysis",
      details: error.message || "Unknown error occurred",
      code: error.code || "MARKET_ANALYSIS_ERROR"
    });
  }
});

// AI-powered property matching endpoint
router.post("/match", auth, async (req, res) => {
  try {
    const { userProfile, preferences, maxResults = 50 } = req.body;
    const startTime = Date.now();

    logHelpers.logInfo(req, "AI Property Matching Request", {
      userId: req.user?.id,
      preferences: {
        locations: preferences?.preferredLocations?.length || 0,
        propertyTypes: preferences?.propertyTypes?.length || 0,
        priceRange: `${preferences?.minPrice || 0}-${preferences?.maxPrice || 'unlimited'}`,
        rooms: `${preferences?.minRooms || 0}-${preferences?.maxRooms || 'unlimited'}`
      },
      maxResults
    });

    // Validate required fields
    if (!preferences) {
      return res.status(400).json({
        status: "error",
        error: "User preferences are required",
        message: "Please provide user preferences for matching"
      });
    }

    // Get available listings from database
    const query = {};

    // Apply basic filters based on preferences
    if (preferences.preferredLocations && preferences.preferredLocations.length > 0) {
      query.location = { $in: preferences.preferredLocations.map(loc => new RegExp(loc, 'i')) };
    }

    if (preferences.propertyTypes && preferences.propertyTypes.length > 0) {
      query.propertyType = { $in: preferences.propertyTypes };
    }

    if (preferences.minPrice || preferences.maxPrice) {
      query.price = {};
      if (preferences.minPrice) query.price.$gte = preferences.minPrice;
      if (preferences.maxPrice) query.price.$lte = preferences.maxPrice;
    }

    if (preferences.minRooms || preferences.maxRooms) {
      query.rooms = {};
      if (preferences.minRooms) query.rooms.$gte = preferences.minRooms;
      if (preferences.maxRooms) query.rooms.$lte = preferences.maxRooms;
    }

    // Fetch listings from database
    const listings = await Listing.find(query)
      .limit(maxResults * 2) // Get more than needed for better matching
      .sort({ createdAt: -1 })
      .lean();

    logHelpers.logInfo(req, "Listings fetched for matching", {
      totalFound: listings.length,
      query: JSON.stringify(query)
    });

    // Use AI service to score and rank matches
    const matchingRequest = {
      userProfile,
      preferences,
      listings,
      maxResults
    };

    const aiResult = await aiService.getPropertyMatches(matchingRequest);

    // Format response
    const response = {
      status: "success",
      data: {
        matches: aiResult.matches || [],
        totalAnalyzed: aiResult.totalAnalyzed || listings.length,
        averageScore: aiResult.averageScore || 0,
        recommendations: aiResult.recommendations || []
      },
      message: `Found ${aiResult.matches?.length || 0} property matches`,
      processingTime: Date.now() - startTime
    };

    logHelpers.logInfo(req, "AI Property Matching Completed", {
      matchesFound: aiResult.matches?.length || 0,
      averageScore: aiResult.averageScore || 0,
      processingTime: response.processingTime
    });

    res.json(response);

  } catch (error) {
    console.error('AI Property Matching Error:', error);
    res.status(500).json({
      status: "error",
      error: "Internal server error",
      message: "Failed to generate property matches"
    });
  }
});

module.exports = router;
