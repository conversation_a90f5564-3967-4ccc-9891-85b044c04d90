/**
 * Tests for Unified Property Schema
 */

const {
  validateProperty,
  validatePropertyStrict,
  createMinimalProperty,
  getSchema,
  getValidationOptions
} = require('../schemas/unifiedPropertySchema');

describe('Unified Property Schema', () => {
  describe('validateProperty', () => {
    test('should validate a minimal valid property', () => {
      const property = {
        title: 'Test Property',
        source: 'funda.nl',
        url: 'https://www.funda.nl/test',
        location: 'Amsterdam',
        price: '€ 1500 per maand'
      };

      const result = validateProperty(property);
      expect(result.error).toBeUndefined();
      expect(result.value).toBeDefined();
      expect(result.value.title).toBe('Test Property');
      expect(result.value.source).toBe('funda.nl');
    });

    test('should validate property with all fields', () => {
      const property = {
        title: 'Luxury Apartment',
        description: 'Beautiful apartment in city center',
        source: 'pararius.nl',
        url: 'https://www.pararius.nl/test',
        location: 'Rotterdam',
        propertyType: 'apartment',
        size: '85 m²',
        area: 85,
        rooms: 3,
        bedrooms: 2,
        bathrooms: 1,
        year: '2020',
        price: 1800,
        interior: 'Gemeubileerd',
        furnished: true,
        pets: false,
        smoking: false,
        garden: true,
        balcony: true,
        parking: false,
        energyLabel: 'A',
        images: ['https://example.com/image1.jpg', 'https://example.com/image2.jpg'],
        isActive: true,
        features: ['balcony', 'garden'],
        deposit: 3600,
        utilities: 150,
        dateAvailable: '2024-01-01T00:00:00.000Z',
        contactInfo: {
          name: 'John Doe',
          phone: '+31612345678',
          email: '<EMAIL>'
        }
      };

      const result = validateProperty(property);
      expect(result.error).toBeUndefined();
      expect(result.value).toBeDefined();
      expect(result.value.title).toBe('Luxury Apartment');
      expect(result.value.propertyType).toBe('apartment');
      expect(result.value.furnished).toBe(true);
    });

    test('should fail validation for missing required fields', () => {
      const property = {
        title: 'Test Property'
        // Missing source, url, location, price
      };

      const result = validateProperty(property);
      expect(result.error).toBeDefined();
      expect(result.error.details).toHaveLength(4); // 4 required fields missing
    });

    test('should fail validation for invalid source', () => {
      const property = {
        title: 'Test Property',
        source: 'invalid-source.com',
        url: 'https://example.com',
        location: 'Amsterdam',
        price: 1500
      };

      const result = validateProperty(property);
      expect(result.error).toBeDefined();
      expect(result.error.details[0].path).toContain('source');
    });

    test('should fail validation for invalid URL', () => {
      const property = {
        title: 'Test Property',
        source: 'funda.nl',
        url: 'not-a-valid-url',
        location: 'Amsterdam',
        price: 1500
      };

      const result = validateProperty(property);
      expect(result.error).toBeDefined();
      expect(result.error.details[0].path).toContain('url');
    });

    test('should validate structured location object', () => {
      const property = {
        title: 'Test Property',
        source: 'huurwoningen.nl',
        url: 'https://www.huurwoningen.nl/test',
        location: {
          _unified: {
            address: {
              street: 'Damrak',
              houseNumber: '123',
              postalCode: '1012 LG',
              city: 'Amsterdam',
              province: 'Noord-Holland',
              country: 'Netherlands'
            },
            coordinates: {
              lat: 52.3676,
              lng: 4.9041
            }
          },
          _legacy: 'Amsterdam'
        },
        price: '€ 2000'
      };

      const result = validateProperty(property);
      expect(result.error).toBeUndefined();
      expect(result.value.location._unified.address.city).toBe('Amsterdam');
    });

    test('should validate Dutch property types', () => {
      const dutchTypes = ['appartement', 'huis', 'kamer', 'woning'];
      
      dutchTypes.forEach(type => {
        const property = {
          title: 'Test Property',
          source: 'funda.nl',
          url: 'https://www.funda.nl/test',
          location: 'Amsterdam',
          price: 1500,
          propertyType: type
        };

        const result = validateProperty(property);
        expect(result.error).toBeUndefined();
        expect(result.value.propertyType).toBe(type);
      });
    });

    test('should validate Dutch interior types', () => {
      const dutchInteriors = ['Kaal', 'Gestoffeerd', 'Gemeubileerd'];
      
      dutchInteriors.forEach(interior => {
        const property = {
          title: 'Test Property',
          source: 'funda.nl',
          url: 'https://www.funda.nl/test',
          location: 'Amsterdam',
          price: 1500,
          interior: interior
        };

        const result = validateProperty(property);
        expect(result.error).toBeUndefined();
        expect(result.value.interior).toBe(interior);
      });
    });

    test('should validate energy labels', () => {
      const energyLabels = ['A+++', 'A++', 'A+', 'A', 'B', 'C', 'D', 'E', 'F', 'G'];
      
      energyLabels.forEach(label => {
        const property = {
          title: 'Test Property',
          source: 'funda.nl',
          url: 'https://www.funda.nl/test',
          location: 'Amsterdam',
          price: 1500,
          energyLabel: label
        };

        const result = validateProperty(property);
        expect(result.error).toBeUndefined();
        expect(result.value.energyLabel).toBe(label);
      });
    });

    test('should handle both string and number for rooms/bedrooms', () => {
      const propertyWithStringRooms = {
        title: 'Test Property',
        source: 'funda.nl',
        url: 'https://www.funda.nl/test',
        location: 'Amsterdam',
        price: 1500,
        rooms: '3',
        bedrooms: '2'
      };

      const propertyWithNumberRooms = {
        title: 'Test Property',
        source: 'funda.nl',
        url: 'https://www.funda.nl/test',
        location: 'Amsterdam',
        price: 1500,
        rooms: 3,
        bedrooms: 2
      };

      const result1 = validateProperty(propertyWithStringRooms);
      const result2 = validateProperty(propertyWithNumberRooms);

      expect(result1.error).toBeUndefined();
      expect(result2.error).toBeUndefined();
      expect(result1.value.rooms).toBe('3');
      expect(result2.value.rooms).toBe(3);
    });

    test('should handle both string and number prices', () => {
      const propertyWithStringPrice = {
        title: 'Test Property',
        source: 'funda.nl',
        url: 'https://www.funda.nl/test',
        location: 'Amsterdam',
        price: '€ 1500 per maand'
      };

      const propertyWithNumberPrice = {
        title: 'Test Property',
        source: 'funda.nl',
        url: 'https://www.funda.nl/test',
        location: 'Amsterdam',
        price: 1500
      };

      const result1 = validateProperty(propertyWithStringPrice);
      const result2 = validateProperty(propertyWithNumberPrice);

      expect(result1.error).toBeUndefined();
      expect(result2.error).toBeUndefined();
      expect(result1.value.price).toBe('€ 1500 per maand');
      expect(result2.value.price).toBe(1500);
    });

    test('should set default values', () => {
      const property = {
        title: 'Test Property',
        source: 'funda.nl',
        url: 'https://www.funda.nl/test',
        location: 'Amsterdam',
        price: 1500
      };

      const result = validateProperty(property);
      expect(result.error).toBeUndefined();
      expect(result.value.propertyType).toBe('woning'); // default
      expect(result.value.bathrooms).toBe('1'); // default
      expect(result.value.furnished).toBe(false); // default
      expect(result.value.isActive).toBe(true); // default
      expect(result.value.images).toEqual([]); // default
      expect(result.value.features).toEqual([]); // default
    });
  });

  describe('validatePropertyStrict', () => {
    test('should require all fields in strict mode', () => {
      const property = {
        title: 'Test Property',
        source: 'funda.nl',
        url: 'https://www.funda.nl/test',
        location: 'Amsterdam',
        price: 1500
      };

      const result = validatePropertyStrict(property);
      // Strict mode should require more fields
      expect(result.error).toBeDefined();
    });
  });

  describe('createMinimalProperty', () => {
    test('should create minimal valid property with defaults', () => {
      const minimal = createMinimalProperty();
      const result = validateProperty(minimal);
      
      expect(result.error).toBeUndefined();
      expect(result.value.title).toBe('Untitled Property');
      expect(result.value.source).toBe('funda.nl');
      expect(result.value.propertyType).toBe('woning');
    });

    test('should create minimal property with provided data', () => {
      const basicData = {
        title: 'My Property',
        source: 'funda.nl',
        price: 1800
      };

      const minimal = createMinimalProperty(basicData);
      const result = validateProperty(minimal);
      
      expect(result.error).toBeUndefined();
      expect(result.value.title).toBe('My Property');
      expect(result.value.source).toBe('funda.nl');
      expect(result.value.price).toBe(1800);
    });
  });

  describe('utility functions', () => {
    test('should return schema object', () => {
      const schema = getSchema();
      expect(schema).toBeDefined();
      expect(typeof schema.validate).toBe('function');
    });

    test('should return validation options', () => {
      const normalOptions = getValidationOptions();
      const strictOptions = getValidationOptions(true);
      
      expect(normalOptions).toBeDefined();
      expect(strictOptions).toBeDefined();
      expect(normalOptions.presence).toBe('optional');
      expect(strictOptions.presence).toBe('required');
    });
  });
});