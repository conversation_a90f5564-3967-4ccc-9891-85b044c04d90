import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  TextInput,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useRouter } from 'expo-router';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import Animated, { FadeInUp, FadeInDown } from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';
import { Picker } from '@react-native-picker/picker';

import { useAuthStore } from '../store/authStore';
import { userProfileService } from '../services/userProfileService';

// Hide the default navigation header
export const options = {
  headerShown: false,
};

// Theme colors
const THEME = {
  primary: '#4361ee',
  secondary: '#7209b7',
  accent: '#f72585',
  dark: '#0a0a18',
  light: '#ffffff',
  gray: '#6b7280',
  lightGray: '#f3f4f6',
  success: '#10b981',
  warning: '#f59e0b',
  danger: '#ef4444',
};

// Header Component
const Header = () => {
  const router = useRouter();
  const insets = useSafeAreaInsets();

  return (
    <LinearGradient
      colors={[THEME.primary, THEME.secondary]}
      style={[styles.header, { paddingTop: Math.max(insets.top, 16) }]}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
    >
      <Animated.View
        style={styles.headerContent}
        entering={FadeInUp.duration(600)}
      >
        <TouchableOpacity
          onPress={() => {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            router.back();
          }}
          style={styles.backButton}
          activeOpacity={0.8}
        >
          <View style={styles.backButtonInner}>
            <Ionicons name="chevron-back" size={24} color={THEME.primary} />
          </View>
        </TouchableOpacity>

        <View style={styles.headerCenter}>
          <View style={styles.logoContainer}>
            <Text style={styles.logoText}>ZM</Text>
          </View>
          <Text style={styles.headerTitle}>Edit Profile</Text>
        </View>

        <View style={styles.headerSpacer} />
      </Animated.View>
    </LinearGradient>
  );
};

// Input Field Component
const InputField = ({
  label,
  value,
  onChangeText,
  placeholder,
  keyboardType = 'default',
  multiline = false,
  numberOfLines = 1,
  editable = true,
}: {
  label: string;
  value: string;
  onChangeText: (text: string) => void;
  placeholder?: string;
  keyboardType?: any;
  multiline?: boolean;
  numberOfLines?: number;
  editable?: boolean;
}) => (
  <View style={styles.inputContainer}>
    <Text style={styles.inputLabel}>{label}</Text>
    <TextInput
      style={[
        styles.textInput,
        multiline && styles.multilineInput,
        !editable && styles.disabledInput,
      ]}
      value={value}
      onChangeText={onChangeText}
      placeholder={placeholder}
      placeholderTextColor={THEME.gray}
      keyboardType={keyboardType}
      multiline={multiline}
      numberOfLines={numberOfLines}
      editable={editable}
    />
  </View>
);

// Picker Field Component
const PickerField = ({
  label,
  value,
  onValueChange,
  items,
}: {
  label: string;
  value: string;
  onValueChange: (value: string) => void;
  items: { label: string; value: string }[];
}) => (
  <View style={styles.inputContainer}>
    <Text style={styles.inputLabel}>{label}</Text>
    <View style={styles.pickerContainer}>
      <Picker
        selectedValue={value}
        onValueChange={onValueChange}
        style={styles.picker}
      >
        <Picker.Item label="Select..." value="" />
        {items.map((item) => (
          <Picker.Item key={item.value} label={item.label} value={item.value} />
        ))}
      </Picker>
    </View>
  </View>
);

export default function EditProfileScreen() {
  const router = useRouter();
  const { user, updateUser } = useAuthStore();
  
  // Form state
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [profile, setProfile] = useState({
    firstName: '',
    lastName: '',
    phoneNumber: '',
    dateOfBirth: '',
    nationality: '',
    userType: [] as string[],
    employment: {
      occupation: '',
      employmentType: '',
      contractType: '',
      employer: '',
      workLocation: '',
      monthlyIncome: '',
    },
  });

  const [preferences, setPreferences] = useState({
    minPrice: '',
    maxPrice: '',
    minRooms: '',
    maxRooms: '',
    propertyTypes: [] as string[],
    preferredLocations: [] as string[],
  });

  const [notifications, setNotifications] = useState({
    email: {
      newListings: true,
      priceChanges: true,
      applicationUpdates: true,
    },
    push: {
      newMatches: true,
      messages: true,
      systemUpdates: false,
    },
  });

  // Load current profile data
  useEffect(() => {
    loadProfile();
  }, []);

  const loadProfile = async () => {
    try {
      setLoading(true);
      const response = await userProfileService.getProfile();
      
      if (response.success && response.data) {
        const userData = response.data;
        
        // Set profile data
        setProfile({
          firstName: userData.profile?.firstName || '',
          lastName: userData.profile?.lastName || '',
          phoneNumber: userData.profile?.phoneNumber || '',
          dateOfBirth: userData.profile?.dateOfBirth ? new Date(userData.profile.dateOfBirth).toISOString().split('T')[0] : '',
          nationality: userData.profile?.nationality || '',
          userType: userData.profile?.userType || [],
          employment: {
            occupation: userData.profile?.employment?.occupation || '',
            employmentType: userData.profile?.employment?.employmentType || '',
            contractType: userData.profile?.employment?.contractType || '',
            employer: userData.profile?.employment?.employer || '',
            workLocation: userData.profile?.employment?.workLocation || '',
            monthlyIncome: userData.profile?.employment?.monthlyIncome?.toString() || '',
          },
        });

        // Set preferences
        setPreferences({
          minPrice: userData.preferences?.minPrice?.toString() || '',
          maxPrice: userData.preferences?.maxPrice?.toString() || '',
          minRooms: userData.preferences?.minRooms?.toString() || '',
          maxRooms: userData.preferences?.maxRooms?.toString() || '',
          propertyTypes: userData.preferences?.propertyTypes || [],
          preferredLocations: userData.preferences?.preferredLocations || [],
        });

        // Set notifications
        setNotifications({
          email: {
            newListings: userData.notifications?.email?.newListings ?? true,
            priceChanges: userData.notifications?.email?.priceChanges ?? true,
            applicationUpdates: userData.notifications?.email?.applicationUpdates ?? true,
          },
          push: {
            newMatches: userData.notifications?.push?.newMatches ?? true,
            messages: userData.notifications?.push?.messages ?? true,
            systemUpdates: userData.notifications?.push?.systemUpdates ?? false,
          },
        });
      }
    } catch (error) {
      console.error('Error loading profile:', error);
      Alert.alert('Error', 'Failed to load profile data');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      
      // Prepare data for API
      const updateData = {
        profile: {
          ...profile,
          dateOfBirth: profile.dateOfBirth ? new Date(profile.dateOfBirth) : undefined,
          employment: {
            ...profile.employment,
            monthlyIncome: profile.employment.monthlyIncome ? parseFloat(profile.employment.monthlyIncome) : undefined,
          },
        },
        preferences: {
          ...preferences,
          minPrice: preferences.minPrice ? parseFloat(preferences.minPrice) : undefined,
          maxPrice: preferences.maxPrice ? parseFloat(preferences.maxPrice) : undefined,
          minRooms: preferences.minRooms ? parseInt(preferences.minRooms) : undefined,
          maxRooms: preferences.maxRooms ? parseInt(preferences.maxRooms) : undefined,
        },
        notifications,
      };

      const response = await userProfileService.updateProfile(updateData);
      
      if (response.success) {
        // Update the auth store with new user data
        updateUser(response.data);
        
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        Alert.alert('Success', 'Profile updated successfully', [
          { text: 'OK', onPress: () => router.back() }
        ]);
      } else {
        throw new Error(response.error || 'Failed to update profile');
      }
    } catch (error: any) {
      console.error('Error saving profile:', error);
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      Alert.alert('Error', error.message || 'Failed to update profile');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <Header />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={THEME.primary} />
          <Text style={styles.loadingText}>Loading profile...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <Header />
      
      <KeyboardAvoidingView 
        style={styles.keyboardAvoid}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          style={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
        >
          {/* Personal Information Section */}
          <Animated.View
            style={styles.section}
            entering={FadeInUp.duration(600).delay(200)}
          >
            <Text style={styles.sectionTitle}>Personal Information</Text>
            
            <InputField
              label="First Name"
              value={profile.firstName}
              onChangeText={(text) => setProfile({ ...profile, firstName: text })}
              placeholder="Enter your first name"
            />
            
            <InputField
              label="Last Name"
              value={profile.lastName}
              onChangeText={(text) => setProfile({ ...profile, lastName: text })}
              placeholder="Enter your last name"
            />
            
            <InputField
              label="Phone Number"
              value={profile.phoneNumber}
              onChangeText={(text) => setProfile({ ...profile, phoneNumber: text })}
              placeholder="+31 6 12345678"
              keyboardType="phone-pad"
            />
            
            <InputField
              label="Date of Birth"
              value={profile.dateOfBirth}
              onChangeText={(text) => setProfile({ ...profile, dateOfBirth: text })}
              placeholder="YYYY-MM-DD"
            />
            
            <InputField
              label="Nationality"
              value={profile.nationality}
              onChangeText={(text) => setProfile({ ...profile, nationality: text })}
              placeholder="e.g., Dutch, German, American"
            />
          </Animated.View>

          {/* Employment Section */}
          <Animated.View
            style={styles.section}
            entering={FadeInUp.duration(600).delay(400)}
          >
            <Text style={styles.sectionTitle}>Employment</Text>
            
            <InputField
              label="Occupation"
              value={profile.employment.occupation}
              onChangeText={(text) => setProfile({
                ...profile,
                employment: { ...profile.employment, occupation: text }
              })}
              placeholder="e.g., Software Engineer, Student"
            />
            
            <PickerField
              label="Employment Type"
              value={profile.employment.employmentType}
              onValueChange={(value) => setProfile({
                ...profile,
                employment: { ...profile.employment, employmentType: value }
              })}
              items={[
                { label: 'Full-time', value: 'full-time' },
                { label: 'Part-time', value: 'part-time' },
                { label: 'Student', value: 'student' },
                { label: 'Freelancer', value: 'freelancer' },
                { label: 'Unemployed', value: 'unemployed' },
              ]}
            />
            
            <PickerField
              label="Contract Type"
              value={profile.employment.contractType}
              onValueChange={(value) => setProfile({
                ...profile,
                employment: { ...profile.employment, contractType: value }
              })}
              items={[
                { label: 'Permanent', value: 'permanent' },
                { label: 'Temporary', value: 'temporary' },
                { label: 'Student', value: 'student' },
                { label: 'Freelancer', value: 'freelancer' },
              ]}
            />
            
            <InputField
              label="Employer"
              value={profile.employment.employer}
              onChangeText={(text) => setProfile({
                ...profile,
                employment: { ...profile.employment, employer: text }
              })}
              placeholder="Company name"
            />
            
            <InputField
              label="Work Location"
              value={profile.employment.workLocation}
              onChangeText={(text) => setProfile({
                ...profile,
                employment: { ...profile.employment, workLocation: text }
              })}
              placeholder="City or remote"
            />
            
            <InputField
              label="Monthly Income (€)"
              value={profile.employment.monthlyIncome}
              onChangeText={(text) => setProfile({
                ...profile,
                employment: { ...profile.employment, monthlyIncome: text }
              })}
              placeholder="3000"
              keyboardType="numeric"
            />
          </Animated.View>

          {/* Housing Preferences Section */}
          <Animated.View
            style={styles.section}
            entering={FadeInUp.duration(600).delay(600)}
          >
            <Text style={styles.sectionTitle}>Housing Preferences</Text>
            
            <View style={styles.row}>
              <View style={styles.halfWidth}>
                <InputField
                  label="Min Price (€)"
                  value={preferences.minPrice}
                  onChangeText={(text) => setPreferences({ ...preferences, minPrice: text })}
                  placeholder="800"
                  keyboardType="numeric"
                />
              </View>
              <View style={styles.halfWidth}>
                <InputField
                  label="Max Price (€)"
                  value={preferences.maxPrice}
                  onChangeText={(text) => setPreferences({ ...preferences, maxPrice: text })}
                  placeholder="1500"
                  keyboardType="numeric"
                />
              </View>
            </View>
            
            <View style={styles.row}>
              <View style={styles.halfWidth}>
                <InputField
                  label="Min Rooms"
                  value={preferences.minRooms}
                  onChangeText={(text) => setPreferences({ ...preferences, minRooms: text })}
                  placeholder="1"
                  keyboardType="numeric"
                />
              </View>
              <View style={styles.halfWidth}>
                <InputField
                  label="Max Rooms"
                  value={preferences.maxRooms}
                  onChangeText={(text) => setPreferences({ ...preferences, maxRooms: text })}
                  placeholder="3"
                  keyboardType="numeric"
                />
              </View>
            </View>
          </Animated.View>

          {/* Save Button */}
          <Animated.View
            style={styles.saveButtonContainer}
            entering={FadeInDown.duration(600).delay(800)}
          >
            <TouchableOpacity
              style={[styles.saveButton, saving && styles.saveButtonDisabled]}
              onPress={handleSave}
              disabled={saving}
              activeOpacity={0.8}
            >
              <LinearGradient
                colors={saving ? [THEME.gray, THEME.gray] : [THEME.primary, THEME.secondary]}
                style={styles.saveButtonGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
              >
                {saving ? (
                  <ActivityIndicator size="small" color={THEME.light} />
                ) : (
                  <Ionicons name="checkmark" size={20} color={THEME.light} />
                )}
                <Text style={styles.saveButtonText}>
                  {saving ? 'Saving...' : 'Save Profile'}
                </Text>
              </LinearGradient>
            </TouchableOpacity>
          </Animated.View>

          {/* Bottom Spacing */}
          <View style={styles.bottomSpacing} />
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: THEME.lightGray,
  },
  keyboardAvoid: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    marginRight: 16,
  },
  backButtonInner: {
    width: 40,
    height: 40,
    backgroundColor: THEME.light,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: THEME.dark,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  headerCenter: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  logoContainer: {
    width: 40,
    height: 40,
    backgroundColor: THEME.light,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    shadowColor: THEME.dark,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  logoText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: THEME.primary,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: THEME.light,
  },
  headerSpacer: {
    width: 40,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: THEME.gray,
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
  },
  section: {
    backgroundColor: THEME.light,
    borderRadius: 20,
    padding: 20,
    marginBottom: 20,
    shadowColor: THEME.dark,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 5,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: THEME.dark,
    marginBottom: 20,
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: THEME.dark,
    marginBottom: 8,
  },
  textInput: {
    backgroundColor: THEME.lightGray,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: THEME.dark,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  multilineInput: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
  disabledInput: {
    backgroundColor: '#f0f0f0',
    color: THEME.gray,
  },
  pickerContainer: {
    backgroundColor: THEME.lightGray,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  picker: {
    height: 50,
  },
  row: {
    flexDirection: 'row',
    gap: 12,
  },
  halfWidth: {
    flex: 1,
  },
  saveButtonContainer: {
    marginTop: 20,
  },
  saveButton: {
    borderRadius: 25,
    overflow: 'hidden',
    shadowColor: THEME.dark,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 5,
  },
  saveButtonDisabled: {
    opacity: 0.6,
  },
  saveButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 32,
    paddingVertical: 16,
    gap: 8,
  },
  saveButtonText: {
    color: THEME.light,
    fontSize: 18,
    fontWeight: 'bold',
  },
  bottomSpacing: {
    height: 40,
  },
});