/**
 * Frontend Compatibility Layer
 * 
 * This module ensures that unified property schema data is compatible with frontend expectations.
 * It provides conversion functions to transform unified property data into formats expected by
 * the frontend components, maintaining backward compatibility while leveraging the enhanced schema.
 */

/**
 * Convert a unified property to frontend-compatible format
 * @param {Object} unifiedProperty - Property data in unified schema format
 * @returns {Object} Frontend-compatible property object
 */
function convertToFrontendFormat(unifiedProperty) {
  if (!unifiedProperty) return null;
  
  // Create a new object with frontend-expected fields and formats
  return {
    // Basic identification
    _id: unifiedProperty._id || unifiedProperty.id,
    id: unifiedProperty._id || unifiedProperty.id, // Alias for compatibility
    
    // Basic information
    title: unifiedProperty.title,
    description: unifiedProperty.description,
    
    // Price handling - frontend formatPrice utility handles both string and number
    price: typeof unifiedProperty.price === 'number' 
      ? formatPrice(unifiedProperty.price) 
      : unifiedProperty.price,
    
    // Location - support both string and object formats
    location: formatLocation(unifiedProperty.location),
    
    // Property details in frontend-expected formats
    propertyType: unifiedProperty.propertyType,
    rooms: unifiedProperty.rooms,
    bedrooms: unifiedProperty.bedrooms,
    bathrooms: unifiedProperty.bathrooms,
    
    // Size information
    area: unifiedProperty.area,
    size: unifiedProperty.size,
    year: unifiedProperty.year,
    
    // Features
    interior: unifiedProperty.interior,
    furnished: unifiedProperty.furnished,
    pets: unifiedProperty.pets,
    smoking: unifiedProperty.smoking,
    garden: unifiedProperty.garden,
    balcony: unifiedProperty.balcony,
    parking: unifiedProperty.parking,
    energyLabel: unifiedProperty.energyLabel,
    
    // Media
    images: unifiedProperty.images || [],
    
    // Source and dates
    source: unifiedProperty.source,
    url: unifiedProperty.url,
    dateAdded: unifiedProperty.dateAdded,
    dateAvailable: unifiedProperty.dateAvailable,
    
    // Additional frontend fields
    isActive: unifiedProperty.isActive !== undefined ? unifiedProperty.isActive : true,
    features: unifiedProperty.features || [],
    deposit: unifiedProperty.deposit,
    utilities: unifiedProperty.utilities,
    contactInfo: unifiedProperty.contactInfo || {}
  };
}

/**
 * Convert a legacy listing to unified format
 * @param {Object} legacyListing - Legacy listing model data
 * @returns {Object} Unified format property
 */
function convertFromLegacyListing(legacyListing) {
  if (!legacyListing) return null;
  
  return {
    _id: legacyListing._id,
    id: legacyListing._id,
    title: legacyListing.title,
    description: legacyListing.description,
    price: legacyListing.price,
    location: legacyListing.location,
    propertyType: legacyListing.propertyType,
    rooms: legacyListing.rooms,
    bedrooms: legacyListing.bedrooms,
    size: legacyListing.size,
    year: legacyListing.year,
    interior: legacyListing.interior,
    source: legacyListing.source,
    url: legacyListing.url,
    images: legacyListing.images || [],
    dateAdded: legacyListing.dateAdded || legacyListing.timestamp,
    
    // Set reasonable defaults for new fields
    bathrooms: "1",
    area: extractNumericSize(legacyListing.size),
    furnished: inferFurnishedStatus(legacyListing.interior),
    pets: false,
    smoking: false,
    garden: false,
    balcony: false,
    parking: false,
    isActive: true,
    features: [],
    
    _internal: {
      sourceMetadata: {
        website: legacyListing.source,
        scrapedAt: legacyListing.dateAdded || legacyListing.timestamp,
        lastUpdated: new Date()
      },
      rawData: {
        original: legacyListing,
        processed: {},
        metadata: { migrated: true }
      }
    }
  };
}

/**
 * Format location data for frontend compatibility
 * @param {Object|String} location - Location data from unified schema
 * @returns {String} Frontend-compatible location string
 */
function formatLocation(location) {
  if (!location) return '';
  
  // If location is already a string, return it
  if (typeof location === 'string') {
    return location;
  }
  
  // If location has a legacy format, use it
  if (location._legacy) {
    return location._legacy;
  }
  
  // If location has a toString method, use it
  if (typeof location.toString === 'function' && location.toString !== Object.prototype.toString) {
    return location.toString();
  }
  
  // If location has unified address data, format it
  if (location._unified && location._unified.address) {
    const address = location._unified.address;
    const parts = [];
    
    if (address.street) parts.push(address.street);
    if (address.houseNumber) parts.push(address.houseNumber);
    if (parts.length === 0 && address.city) {
      return address.city;
    }
    
    if (address.city) {
      if (parts.length > 0) {
        // Join street and house number with a space, then add city with comma
        return `${parts.join(' ')}, ${address.city}`;
      } else {
        return address.city;
      }
    }
    
    return parts.join(' ');
  }
  
  // Fallback to empty string if no valid location format is found
  return '';
}

/**
 * Format price for frontend display
 * @param {Number|String} price - Price value
 * @returns {String} Formatted price string
 */
function formatPrice(price) {
  if (typeof price === 'string') {
    return price;
  }
  
  if (typeof price === 'number') {
    // Format as Dutch price with Euro symbol
    if (price > 0) {
      const formattedPrice = `€ ${Math.round(price).toLocaleString('nl-NL')}`;
      // Add "per maand" for rental prices (assumed to be less than 10000)
      if (price < 10000) {
        return `${formattedPrice} per maand`;
      }
      return formattedPrice;
    }
  }
  
  return 'Prijs op aanvraag';
}

/**
 * Extract numeric size from size string
 * @param {String} sizeString - Size string (e.g., "85 m²")
 * @returns {Number|null} Numeric size or null if not found
 */
function extractNumericSize(sizeString) {
  if (!sizeString) return null;
  const match = sizeString.match(/(\d+)/);
  return match ? parseInt(match[1]) : null;
}

/**
 * Infer furnished status from interior description
 * @param {String} interior - Interior description
 * @returns {Boolean} Whether the property is furnished
 */
function inferFurnishedStatus(interior) {
  if (!interior) return false;
  const lowerInterior = interior.toLowerCase();
  
  // First check for unfurnished indicators (these take precedence)
  if (lowerInterior === 'unfurnished' || 
      lowerInterior === 'kaal' || 
      lowerInterior === 'ongemeubileerd') {
    return false;
  }
  
  // Then check for furnished indicators
  if (lowerInterior.includes('gemeubileerd') || 
      lowerInterior.includes('furnished') || 
      lowerInterior.includes('meubil')) {
    return true;
  }
  
  // Default to false for other cases
  return false;
}

/**
 * Normalize room count to frontend-expected format
 * @param {String|Number} rooms - Room count
 * @returns {String} Normalized room count
 */
function normalizeRooms(rooms) {
  if (rooms === null || rooms === undefined) return null;
  
  // If already a string, return it
  if (typeof rooms === 'string') {
    return rooms;
  }
  
  // Convert number to string
  if (typeof rooms === 'number') {
    return rooms.toString();
  }
  
  return null;
}

module.exports = {
  convertToFrontendFormat,
  convertFromLegacyListing,
  formatLocation,
  formatPrice,
  extractNumericSize,
  inferFurnishedStatus,
  normalizeRooms
};