const User = require('../models/User');
const cacheService = require('./cacheService');
const { loggers } = require('./logger');

/**
 * TenantScoringService - Calculates and manages tenant scores for rental applications
 * 
 * This service implements algorithms to evaluate tenant reliability based on:
 * - Income stability and employment status
 * - Rental history and references
 * - Credit worthiness indicators
 * - Employment verification
 * - Reference quality
 */
class TenantScoringService {
  constructor() {
    this.CACHE_TTL = 1800; // 30 minutes cache for scores
    this.SCORE_WEIGHTS = {
      incomeStability: 0.30,    // 30% weight
      rentalHistory: 0.25,      // 25% weight
      creditworthiness: 0.20,   // 20% weight
      employment: 0.15,         // 15% weight
      references: 0.10          // 10% weight
    };
  }

  /**
   * Calculate comprehensive tenant score for a user
   * @param {string} userId - User ID to calculate score for
   * @returns {Promise<Object>} Score breakdown and overall score
   */
  async calculateTenantScore(userId) {
    try {
      loggers.app.info(`Calculating tenant score for user: ${userId}`);

      // Check cache first
      const cachedScore = await this._getCachedScore(userId);
      if (cachedScore) {
        loggers.app.info(`Returning cached score for user: ${userId}`);
        return cachedScore;
      }

      // Get user data
      const user = await User.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Calculate individual component scores
      const incomeStability = this._calculateIncomeStability(user.profile.employment);
      const rentalHistory = this._calculateRentalHistory(user.profile.rentalHistory);
      const creditworthiness = this._calculateCreditworthiness(user.profile.rentalHistory);
      const employment = this._calculateEmploymentScore(user.profile.employment);
      const references = this._calculateReferencesScore(user.profile.rentalHistory);

      // Calculate weighted overall score
      const overallScore = Math.round(
        (incomeStability * this.SCORE_WEIGHTS.incomeStability) +
        (rentalHistory * this.SCORE_WEIGHTS.rentalHistory) +
        (creditworthiness * this.SCORE_WEIGHTS.creditworthiness) +
        (employment * this.SCORE_WEIGHTS.employment) +
        (references * this.SCORE_WEIGHTS.references)
      );

      // Determine verification level
      const verificationLevel = this._determineVerificationLevel(user);

      const scoreData = {
        overallScore,
        components: {
          incomeStability,
          rentalHistory,
          creditworthiness,
          employment,
          references
        },
        verificationLevel,
        lastCalculated: new Date(),
        userId
      };

      // Cache the score
      await this._cacheScore(userId, scoreData);

      loggers.app.info(`Calculated tenant score for user ${userId}: ${overallScore}`);
      return scoreData;

    } catch (error) {
      loggers.app.error(`Error calculating tenant score for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Update tenant score when profile data changes
   * @param {string} userId - User ID to update score for
   * @param {Object} newData - Updated profile data
   * @returns {Promise<Object>} Updated score data
   */
  async updateScore(userId, newData) {
    try {
      loggers.app.info(`Updating tenant score for user: ${userId}`);

      // Clear cached score
      await this._clearCachedScore(userId);

      // Recalculate score with new data
      const updatedScore = await this.calculateTenantScore(userId);

      // Update user document with new score
      await User.findByIdAndUpdate(userId, {
        tenantScore: {
          overallScore: updatedScore.overallScore,
          components: updatedScore.components,
          lastCalculated: updatedScore.lastCalculated,
          verificationLevel: updatedScore.verificationLevel
        }
      });

      loggers.app.info(`Updated tenant score for user ${userId}: ${updatedScore.overallScore}`);
      return updatedScore;

    } catch (error) {
      loggers.app.error(`Error updating tenant score for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Get detailed score breakdown for a user
   * @param {string} userId - User ID to get breakdown for
   * @returns {Promise<Object>} Detailed score breakdown with explanations
   */
  async getScoreBreakdown(userId) {
    try {
      const user = await User.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      const scoreData = await this.calculateTenantScore(userId);
      
      return {
        ...scoreData,
        breakdown: {
          incomeStability: {
            score: scoreData.components.incomeStability,
            weight: this.SCORE_WEIGHTS.incomeStability,
            contribution: Math.round(scoreData.components.incomeStability * this.SCORE_WEIGHTS.incomeStability),
            factors: this._getIncomeStabilityFactors(user.profile.employment)
          },
          rentalHistory: {
            score: scoreData.components.rentalHistory,
            weight: this.SCORE_WEIGHTS.rentalHistory,
            contribution: Math.round(scoreData.components.rentalHistory * this.SCORE_WEIGHTS.rentalHistory),
            factors: this._getRentalHistoryFactors(user.profile.rentalHistory)
          },
          creditworthiness: {
            score: scoreData.components.creditworthiness,
            weight: this.SCORE_WEIGHTS.creditworthiness,
            contribution: Math.round(scoreData.components.creditworthiness * this.SCORE_WEIGHTS.creditworthiness),
            factors: this._getCreditworthinessFactors(user.profile.rentalHistory)
          },
          employment: {
            score: scoreData.components.employment,
            weight: this.SCORE_WEIGHTS.employment,
            contribution: Math.round(scoreData.components.employment * this.SCORE_WEIGHTS.employment),
            factors: this._getEmploymentFactors(user.profile.employment)
          },
          references: {
            score: scoreData.components.references,
            weight: this.SCORE_WEIGHTS.references,
            contribution: Math.round(scoreData.components.references * this.SCORE_WEIGHTS.references),
            factors: this._getReferencesFactors(user.profile.rentalHistory)
          }
        },
        grade: this._getScoreGrade(scoreData.overallScore),
        recommendations: this._getScoreRecommendations(scoreData)
      };

    } catch (error) {
      loggers.app.error(`Error getting score breakdown for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Verify a document and update score if needed
   * @param {string} userId - User ID
   * @param {string} documentId - Document ID to verify
   * @returns {Promise<Object>} Updated score if verification affects it
   */
  async verifyDocument(userId, documentId) {
    try {
      loggers.app.info(`Verifying document ${documentId} for user: ${userId}`);

      const user = await User.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Find and mark document as verified
      const document = user.documents.find(doc => doc.id === documentId || doc._id?.toString() === documentId);
      if (!document) {
        throw new Error('Document not found');
      }

      document.verified = true;
      await user.save();

      // Check if verification affects score (income proof, employment contract, etc.)
      const affectsScore = ['income_proof', 'employment_contract', 'bank_statement'].includes(document.type);
      
      if (affectsScore) {
        // Recalculate score with verified document
        const updatedScore = await this.updateScore(userId);
        loggers.app.info(`Document verification updated score for user ${userId}`);
        return updatedScore;
      }

      return null;

    } catch (error) {
      loggers.app.error(`Error verifying document ${documentId} for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Generate a comprehensive score report for a user
   * @param {string} userId - User ID to generate report for
   * @returns {Promise<Object>} Comprehensive score report
   */
  async generateScoreReport(userId) {
    try {
      const breakdown = await this.getScoreBreakdown(userId);
      const user = await User.findById(userId);

      return {
        userId,
        userName: user.fullName || user.email,
        generatedAt: new Date(),
        score: breakdown.overallScore,
        grade: breakdown.grade,
        verificationLevel: breakdown.verificationLevel,
        components: breakdown.breakdown,
        recommendations: breakdown.recommendations,
        summary: {
          strengths: this._identifyStrengths(breakdown),
          weaknesses: this._identifyWeaknesses(breakdown),
          improvementAreas: this._getImprovementAreas(breakdown)
        }
      };

    } catch (error) {
      loggers.app.error(`Error generating score report for user ${userId}:`, error);
      throw error;
    }
  }

  // Private methods for score calculations

  /**
   * Calculate income stability score based on employment data
   * @private
   */
  _calculateIncomeStability(employment) {
    if (!employment) return 0;

    let score = 0;
    
    // Base score for having employment info
    if (employment.occupation) score += 20;
    
    // Employment type scoring
    const employmentTypeScores = {
      'full-time': 40,
      'part-time': 25,
      'freelancer': 15,
      'student': 10,
      'unemployed': 0
    };
    score += employmentTypeScores[employment.employmentType] || 0;

    // Contract type scoring
    const contractTypeScores = {
      'permanent': 25,
      'temporary': 15,
      'freelancer': 10,
      'student': 5
    };
    score += contractTypeScores[employment.contractType] || 0;

    // Income verification bonus
    if (employment.incomeVerified) score += 15;

    return Math.min(score, 100);
  }

  /**
   * Calculate rental history score
   * @private
   */
  _calculateRentalHistory(rentalHistory) {
    if (!rentalHistory) return 50; // Neutral score for no history

    let score = 50; // Base score

    // Previous addresses boost score
    if (rentalHistory.previousAddresses && rentalHistory.previousAddresses.length > 0) {
      score += Math.min(rentalHistory.previousAddresses.length * 10, 30);
    }

    // Negative factors
    if (rentalHistory.evictions) score -= 40;
    if (rentalHistory.paymentIssues) score -= 25;

    // Credit score impact
    const creditScoreImpact = {
      'excellent': 20,
      'good': 10,
      'fair': 0,
      'poor': -15
    };
    score += creditScoreImpact[rentalHistory.creditScore] || 0;

    return Math.max(0, Math.min(score, 100));
  }

  /**
   * Calculate creditworthiness score
   * @private
   */
  _calculateCreditworthiness(rentalHistory) {
    if (!rentalHistory) return 50;

    let score = 50; // Base score

    // Credit score is the primary factor
    const creditScoreValues = {
      'excellent': 100,
      'good': 80,
      'fair': 60,
      'poor': 30
    };
    
    if (rentalHistory.creditScore) {
      score = creditScoreValues[rentalHistory.creditScore];
    }

    // Adjust based on payment history
    if (rentalHistory.paymentIssues) score -= 20;
    if (rentalHistory.evictions) score -= 30;

    return Math.max(0, Math.min(score, 100));
  }

  /**
   * Calculate employment score
   * @private
   */
  _calculateEmploymentScore(employment) {
    if (!employment) return 0;

    let score = 0;

    // Basic employment info
    if (employment.occupation) score += 25;
    if (employment.employer) score += 20;
    if (employment.workLocation) score += 10;

    // Employment stability
    const stabilityScores = {
      'full-time': 30,
      'part-time': 20,
      'freelancer': 10,
      'student': 5,
      'unemployed': 0
    };
    score += stabilityScores[employment.employmentType] || 0;

    // Income information
    if (employment.monthlyIncome) score += 10;
    if (employment.incomeVerified) score += 5;

    return Math.min(score, 100);
  }

  /**
   * Calculate references score
   * @private
   */
  _calculateReferencesScore(rentalHistory) {
    if (!rentalHistory || !rentalHistory.previousAddresses) return 0;

    let score = 0;
    let referenceCount = 0;

    rentalHistory.previousAddresses.forEach(address => {
      if (address.landlordName) referenceCount++;
      if (address.landlordContact) referenceCount++;
      if (address.reference) referenceCount++;
    });

    // Score based on number of references
    score = Math.min(referenceCount * 20, 100);

    return score;
  }

  /**
   * Determine verification level based on user data
   * @private
   */
  _determineVerificationLevel(user) {
    let verifiedCount = 0;
    let totalChecks = 0;

    // Check employment verification
    totalChecks++;
    if (user.profile.employment && user.profile.employment.incomeVerified) {
      verifiedCount++;
    }

    // Check document verification
    if (user.documents && user.documents.length > 0) {
      user.documents.forEach(doc => {
        totalChecks++;
        if (doc.verified) verifiedCount++;
      });
    }

    const verificationRatio = totalChecks > 0 ? verifiedCount / totalChecks : 0;

    if (verificationRatio >= 0.8) return 'verified';
    if (verificationRatio >= 0.4) return 'partial';
    return 'unverified';
  }

  /**
   * Get score grade (A-F)
   * @private
   */
  _getScoreGrade(score) {
    if (score >= 90) return 'A';
    if (score >= 80) return 'B';
    if (score >= 70) return 'C';
    if (score >= 60) return 'D';
    return 'F';
  }

  // Cache management methods

  /**
   * Get cached score for user
   * @private
   */
  async _getCachedScore(userId) {
    try {
      return await cacheService.get(`tenant_score:${userId}`);
    } catch (error) {
      loggers.app.error(`Error getting cached score for user ${userId}:`, error);
      return null;
    }
  }

  /**
   * Cache score for user
   * @private
   */
  async _cacheScore(userId, scoreData) {
    try {
      await cacheService.set(`tenant_score:${userId}`, scoreData, this.CACHE_TTL);
    } catch (error) {
      loggers.app.error(`Error caching score for user ${userId}:`, error);
    }
  }

  /**
   * Clear cached score for user
   * @private
   */
  async _clearCachedScore(userId) {
    try {
      await cacheService.del(`tenant_score:${userId}`);
    } catch (error) {
      loggers.app.error(`Error clearing cached score for user ${userId}:`, error);
    }
  }

  // Helper methods for detailed breakdowns

  _getIncomeStabilityFactors(employment) {
    const factors = [];
    if (employment?.occupation) factors.push('Has occupation listed');
    if (employment?.employmentType === 'full-time') factors.push('Full-time employment');
    if (employment?.contractType === 'permanent') factors.push('Permanent contract');
    if (employment?.incomeVerified) factors.push('Income verified');
    return factors;
  }

  _getRentalHistoryFactors(rentalHistory) {
    const factors = [];
    if (rentalHistory?.previousAddresses?.length > 0) factors.push(`${rentalHistory.previousAddresses.length} previous rental(s)`);
    if (rentalHistory?.creditScore === 'excellent') factors.push('Excellent credit score');
    if (rentalHistory?.evictions) factors.push('Previous evictions (negative)');
    if (rentalHistory?.paymentIssues) factors.push('Payment issues (negative)');
    return factors;
  }

  _getCreditworthinessFactors(rentalHistory) {
    const factors = [];
    if (rentalHistory?.creditScore) factors.push(`Credit score: ${rentalHistory.creditScore}`);
    if (!rentalHistory?.paymentIssues) factors.push('No payment issues');
    if (!rentalHistory?.evictions) factors.push('No evictions');
    return factors;
  }

  _getEmploymentFactors(employment) {
    const factors = [];
    if (employment?.employer) factors.push('Employer information provided');
    if (employment?.workLocation) factors.push('Work location specified');
    if (employment?.monthlyIncome) factors.push('Income information provided');
    return factors;
  }

  _getReferencesFactors(rentalHistory) {
    const factors = [];
    if (rentalHistory?.previousAddresses) {
      const withReferences = rentalHistory.previousAddresses.filter(addr => addr.landlordName || addr.reference);
      if (withReferences.length > 0) factors.push(`${withReferences.length} landlord reference(s)`);
    }
    return factors;
  }

  _getScoreRecommendations(scoreData) {
    const recommendations = [];
    
    if (scoreData.components.incomeStability < 70) {
      recommendations.push('Verify your income with official documents to improve your score');
    }
    
    if (scoreData.components.employment < 70) {
      recommendations.push('Complete your employment information including employer details');
    }
    
    if (scoreData.components.references < 50) {
      recommendations.push('Add previous landlord references to strengthen your application');
    }
    
    if (scoreData.verificationLevel === 'unverified') {
      recommendations.push('Upload and verify supporting documents to increase credibility');
    }

    return recommendations;
  }

  _identifyStrengths(breakdown) {
    const strengths = [];
    Object.entries(breakdown.breakdown).forEach(([key, data]) => {
      if (data.score >= 80) {
        strengths.push(`Strong ${key.replace(/([A-Z])/g, ' $1').toLowerCase()}`);
      }
    });
    return strengths;
  }

  _identifyWeaknesses(breakdown) {
    const weaknesses = [];
    Object.entries(breakdown.breakdown).forEach(([key, data]) => {
      if (data.score < 50) {
        weaknesses.push(`Weak ${key.replace(/([A-Z])/g, ' $1').toLowerCase()}`);
      }
    });
    return weaknesses;
  }

  _getImprovementAreas(breakdown) {
    const areas = [];
    Object.entries(breakdown.breakdown).forEach(([key, data]) => {
      if (data.score < 70) {
        areas.push({
          area: key.replace(/([A-Z])/g, ' $1').toLowerCase(),
          currentScore: data.score,
          suggestions: data.factors.length === 0 ? ['Provide more information in this area'] : []
        });
      }
    });
    return areas;
  }
}

module.exports = new TenantScoringService();