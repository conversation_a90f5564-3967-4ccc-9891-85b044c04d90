import { Ionicons } from "@expo/vector-icons";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { LinearGradient } from "expo-linear-gradient";
import { useRouter } from "expo-router";
import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  SafeAreaView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import Animated, { FadeInUp } from "react-native-reanimated";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { PreferencesOnboarding } from "../components/PreferencesOnboarding";
import { SmartPreferencesWizard } from "../components/SmartPreferencesWizard";
import { UserPreferences } from "../services/authService";
import { debugLogger } from "../services/debugLogger";
import { LogService } from "../services/logService";
import { redirectBlockerService } from "../services/redirectBlockerService";
import { useAIStore } from "../store/aiStore";
import { useAuthStore } from "../store/authStore";

// Define theme colors to match dashboard
const THEME = {
  primary: "#4361ee",
  secondary: "#7209b7",
  accent: "#f72585",
  dark: "#0a0a18",
  light: "#ffffff",
  gray: "#6b7280",
  lightGray: "#f3f4f6",
  success: "#10b981",
  warning: "#f59e0b",
  danger: "#ef4444",
};

// Define specific error types for better error handling
class PreferencesSaveError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "PreferencesSaveError";
  }
}

class NavigationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "NavigationError";
  }
}

class PropertyMatchingError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "PropertyMatchingError";
  }
}

// Enhanced Header Component to match dashboard
const Header = ({
  showBackButton = false,
  onBack,
}: {
  showBackButton?: boolean;
  onBack?: () => void;
}) => {
  const insets = useSafeAreaInsets();

  return (
    <LinearGradient
      colors={[THEME.primary, THEME.secondary]}
      style={[styles.header, { paddingTop: Math.max(insets.top, 16) }]}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
    >
      <Animated.View
        style={styles.headerContent}
        entering={FadeInUp.duration(600)}
      >
        {showBackButton ? (
          <TouchableOpacity onPress={onBack} style={styles.backButton}>
            <View style={styles.backButtonContainer}>
              <Ionicons name="arrow-back" size={24} color={THEME.light} />
            </View>
          </TouchableOpacity>
        ) : (
          <View style={styles.placeholder} />
        )}

        <View style={styles.headerCenter}>
          <View style={styles.logoContainer}>
            <Text style={styles.logoText}>ZM</Text>
          </View>
          <View style={styles.headerTextContainer}>
            <Text style={styles.headerTitle}>Preferences</Text>
            <Text style={styles.headerSubtitle}>Customize your experience</Text>
          </View>
        </View>

        <View style={styles.placeholder} />
      </Animated.View>
    </LinearGradient>
  );
};

export default function PreferencesSetupScreen() {
  const router = useRouter();
  const { user, updatePreferences, isLoading } = useAuthStore();
  const { requestPropertyMatching } = useAIStore();
  const [isSaving, setIsSaving] = useState(false);

  const [savingStage, setSavingStage] = useState<
    "idle" | "saving" | "matching" | "navigating"
  >("idle");
  const [saveAttemptCount, setSaveAttemptCount] = useState(0);

  // Log component mount and store navigation data
  useEffect(() => {
    const logNavigation = async () => {
      // Track that we navigated to preferences
      const timestamp = new Date().getTime();
      const navigationEvent = {
        screen: "preferences",
        timestamp,
        hasPreferences: !!user?.preferences,
      };

      // Store this navigation event with AsyncStorage
      try {
        await AsyncStorage.setItem(
          "lastNavigation",
          JSON.stringify(navigationEvent)
        );
      } catch (storageError) {
        // Only log critical errors
        LogService.error(
          "PreferencesScreen",
          "Failed to store navigation data",
          storageError
        );
      }
    };

    logNavigation();
  }, []);

  // Handle save preferences
  const handleSavePreferences = async (preferences: UserPreferences) => {
    debugLogger.log("PREFERENCES", "handleSavePreferences called", {
      hasPreferences: !!preferences,
    });

    // SIMPLE APPROACH: Just navigate to dashboard, don't save again
    // (SmartPreferencesWizard already saved the preferences)
    console.log(
      "✅ Preferences already saved by wizard, navigating to dashboard"
    );
    debugLogger.log(
      "PREFERENCES",
      "Preferences already saved by wizard, just navigating"
    );

    // BLOCK ALL REDIRECTS for 30 seconds to prevent loops
    redirectBlockerService.blockRedirects(30000);
    debugLogger.log("PREFERENCES", "Redirect blocker activated for 30 seconds");

    // Show saving overlay during navigation to cover the wizard until the route switches
    setIsSaving(true);
    setSavingStage("navigating");

    console.log("🚀 Navigating to dashboard immediately");
    debugLogger.log("PREFERENCES", "Calling router.replace(/dashboard)");
    router.replace("/dashboard");
    debugLogger.log("PREFERENCES", "router.replace(/dashboard) completed");
  };

  return (
    <SafeAreaView style={styles.container}>
      <Header showBackButton={true} onBack={() => router.back()} />

      {isSaving ? (
        <Animated.View
          style={styles.loadingContainer}
          entering={FadeInUp.duration(600)}
        >
          <LinearGradient
            colors={["rgba(67, 97, 238, 0.1)", "rgba(114, 9, 183, 0.1)"]}
            style={styles.loadingCard}
          >
            <View style={styles.loadingIconContainer}>
              <ActivityIndicator size="large" color={THEME.primary} />
            </View>
            <Text style={styles.loadingText}>
              {savingStage === "saving"
                ? "Saving your preferences..."
                : savingStage === "matching"
                ? "Finding matching properties..."
                : savingStage === "navigating"
                ? "Redirecting to dashboard..."
                : "Processing your preferences..."}
            </Text>
            {saveAttemptCount > 1 && (
              <Text style={styles.loadingSubtext}>
                Attempt {saveAttemptCount} of 3
              </Text>
            )}
          </LinearGradient>
        </Animated.View>
      ) : (
        <Animated.View
          style={styles.contentContainer}
          entering={FadeInUp.duration(600).delay(200)}
        >
          <PreferencesOnboarding isOnboarding={true}>
            <SmartPreferencesWizard
              onComplete={handleSavePreferences}
              initialPreferences={user?.preferences}
              onValidationChange={() => {}}
            />
          </PreferencesOnboarding>
        </Animated.View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: THEME.lightGray,
  },
  header: {
    paddingHorizontal: 20,
    paddingBottom: 20,
    shadowColor: THEME.dark,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  headerContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    width: "100%",
  },
  backButton: {
    padding: 8,
  },
  backButtonContainer: {
    width: 40,
    height: 40,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  placeholder: {
    width: 40,
  },
  headerCenter: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
    justifyContent: "center",
  },
  logoContainer: {
    width: 40,
    height: 40,
    backgroundColor: THEME.light,
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
    shadowColor: THEME.dark,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  logoText: {
    fontSize: 18,
    fontWeight: "bold",
    color: THEME.primary,
  },
  headerTextContainer: {
    alignItems: "center",
  },
  headerTitle: {
    fontSize: 22,
    fontWeight: "bold",
    color: THEME.light,
    marginBottom: 2,
  },
  headerSubtitle: {
    fontSize: 14,
    color: "rgba(255, 255, 255, 0.8)",
    fontWeight: "500",
  },
  contentContainer: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 24,
  },
  loadingCard: {
    backgroundColor: THEME.light,
    borderRadius: 20,
    padding: 32,
    alignItems: "center",
    shadowColor: THEME.dark,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
    minWidth: 280,
  },
  loadingIconContainer: {
    marginBottom: 20,
  },
  loadingText: {
    fontSize: 18,
    color: THEME.dark,
    textAlign: "center",
    fontWeight: "600",
    marginBottom: 8,
  },
  loadingSubtext: {
    fontSize: 14,
    color: THEME.gray,
    textAlign: "center",
    fontWeight: "500",
  },
});
