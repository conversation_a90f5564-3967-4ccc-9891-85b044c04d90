const applicationSubmissionWorkflow = require('../../services/applicationSubmissionWorkflow');
const ApplicationQueue = require('../../models/ApplicationQueue');
const ApplicationResult = require('../../models/ApplicationResult');
const AutoApplicationSettings = require('../../models/AutoApplicationSettings');

// Mock all dependencies
jest.mock('../../models/ApplicationQueue');
jest.mock('../../models/ApplicationResult');
jest.mock('../../models/AutoApplicationSettings');
jest.mock('../../services/formAutomationEngine');
jest.mock('../../services/antiDetectionSystem');
jest.mock('../../services/applicationMonitor');
jest.mock('../../services/scraperUtils');

describe('ApplicationSubmissionWorkflow Unit Tests', () => {
  let mockQueueItem;
  let mockUserSettings;
  let mockPage;

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock queue item
    mockQueueItem = {
      _id: 'queue123',
      userId: 'user123',
      listingId: 'listing123',
      listingUrl: 'https://www.funda.nl/test',
      applicationData: {
        personalInfo: {
          fullName: 'Test User',
          email: '<EMAIL>',
          phone: '+31612345678'
        },
        documents: [
          { type: 'id', filename: 'id.pdf', required: true, uploaded: true }
        ]
      },
      generatedContent: {
        subject: 'Application',
        message: 'Test message'
      },
      attempts: 0,
      maxAttempts: 3,
      createdAt: new Date(),
      updateStatus: jest.fn().mockResolvedValue(undefined),
      save: jest.fn().mockResolvedValue(undefined),
      errors: []
    };

    // Mock user settings
    mockUserSettings = {
      userId: 'user123',
      enabled: true,
      settings: {
        maxApplicationsPerDay: 5,
        applicationTemplate: 'professional'
      },
      personalInfo: {
        fullName: 'Test User',
        email: '<EMAIL>',
        phone: '+31612345678',
        dateOfBirth: new Date('1990-01-01'),
        nationality: 'Dutch',
        occupation: 'Developer',
        monthlyIncome: 5000
      },
      documents: [
        { type: 'id', filename: 'id.pdf', required: true, uploaded: true },
        { type: 'income', filename: 'income.pdf', required: true, uploaded: true }
      ]
    };

    // Mock page
    mockPage = {
      content: jest.fn().mockResolvedValue('<html><body>Success</body></html>'),
      evaluate: jest.fn().mockResolvedValue('bedankt voor je aanmelding'),
      screenshot: jest.fn().mockResolvedValue(undefined),
      url: jest.fn().mockReturnValue('https://www.funda.nl/confirmation')
    };

    // Setup default mocks
    ApplicationQueue.findById.mockResolvedValue(mockQueueItem);
    AutoApplicationSettings.findOne.mockResolvedValue(mockUserSettings);
    ApplicationResult.countDocuments.mockResolvedValue(0);
    ApplicationResult.findOne.mockResolvedValue(null);
    global.fetch = jest.fn().mockResolvedValue({ ok: true, status: 200 });
  });

  describe('performPreSubmissionValidation', () => {
    it('should pass validation with complete data', async () => {
      // Act
      const result = await applicationSubmissionWorkflow.performPreSubmissionValidation(mockQueueItem);

      // Assert
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.validatedAt).toBeInstanceOf(Date);
    });

    it('should fail validation when auto-application is disabled', async () => {
      // Arrange
      mockUserSettings.enabled = false;
      AutoApplicationSettings.findOne.mockResolvedValue(mockUserSettings);

      // Act
      const result = await applicationSubmissionWorkflow.performPreSubmissionValidation(mockQueueItem);

      // Assert
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Auto-application is not enabled for user');
    });

    it('should fail validation with incomplete personal information', async () => {
      // Arrange
      mockUserSettings.personalInfo.fullName = '';
      AutoApplicationSettings.findOne.mockResolvedValue(mockUserSettings);

      // Act
      const result = await applicationSubmissionWorkflow.performPreSubmissionValidation(mockQueueItem);

      // Assert
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Incomplete personal information in user settings');
    });

    it('should fail validation when required documents are missing', async () => {
      // Arrange
      mockUserSettings.documents[0].uploaded = false;
      AutoApplicationSettings.findOne.mockResolvedValue(mockUserSettings);

      // Act
      const result = await applicationSubmissionWorkflow.performPreSubmissionValidation(mockQueueItem);

      // Assert
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Required documents are missing');
    });

    it('should fail validation when daily limit is reached', async () => {
      // Arrange
      ApplicationResult.countDocuments.mockResolvedValue(5);

      // Act
      const result = await applicationSubmissionWorkflow.performPreSubmissionValidation(mockQueueItem);

      // Assert
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Daily application limit reached');
    });

    it('should fail validation for duplicate applications', async () => {
      // Arrange
      ApplicationResult.findOne.mockResolvedValue({
        _id: 'existing123',
        status: 'submitted'
      });

      // Act
      const result = await applicationSubmissionWorkflow.performPreSubmissionValidation(mockQueueItem);

      // Assert
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Application already submitted for this listing');
    });

    it('should fail validation when listing URL is not accessible', async () => {
      // Arrange
      global.fetch = jest.fn().mockResolvedValue({ ok: false, status: 404 });

      // Act
      const result = await applicationSubmissionWorkflow.performPreSubmissionValidation(mockQueueItem);

      // Assert
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Listing URL not accessible: 404');
    });

    it('should fail validation when application data is missing', async () => {
      // Arrange
      mockQueueItem.applicationData = null;

      // Act
      const result = await applicationSubmissionWorkflow.performPreSubmissionValidation(mockQueueItem);

      // Assert
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Application data or generated content is missing');
    });

    it('should handle validation errors gracefully', async () => {
      // Arrange
      AutoApplicationSettings.findOne.mockRejectedValue(new Error('Database error'));

      // Act
      const result = await applicationSubmissionWorkflow.performPreSubmissionValidation(mockQueueItem);

      // Assert
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Validation error: Database error');
    });
  });

  describe('performPostSubmissionVerification', () => {
    it('should detect successful submission with Dutch success message', async () => {
      // Arrange
      mockPage.evaluate.mockResolvedValue('bedankt voor je aanmelding');

      // Act
      const result = await applicationSubmissionWorkflow.performPostSubmissionVerification(
        mockPage, 
        { submittedAt: new Date() }
      );

      // Assert
      expect(result.success).toBe(true);
      expect(result.status).toBe('success');
      expect(result.indicators.successFound).toBe(true);
      expect(result.indicators.errorFound).toBe(false);
    });

    it('should detect successful submission with English success message', async () => {
      // Arrange
      mockPage.evaluate.mockResolvedValue('thank you for your application');

      // Act
      const result = await applicationSubmissionWorkflow.performPostSubmissionVerification(
        mockPage, 
        { submittedAt: new Date() }
      );

      // Assert
      expect(result.success).toBe(true);
      expect(result.status).toBe('success');
      expect(result.indicators.successFound).toBe(true);
    });

    it('should detect failed submission with error message', async () => {
      // Arrange
      mockPage.evaluate.mockResolvedValue('error occurred, please try again');

      // Act
      const result = await applicationSubmissionWorkflow.performPostSubmissionVerification(
        mockPage, 
        { submittedAt: new Date() }
      );

      // Assert
      expect(result.success).toBe(false);
      expect(result.status).toBe('error');
      expect(result.indicators.errorFound).toBe(true);
    });

    it('should extract confirmation number from page content', async () => {
      // Arrange
      mockPage.evaluate.mockResolvedValue('Your confirmation number is: ABC123456');

      // Act
      const result = await applicationSubmissionWorkflow.performPostSubmissionVerification(
        mockPage, 
        { submittedAt: new Date() }
      );

      // Assert
      expect(result.success).toBe(true);
      expect(result.confirmationNumber).toBe('ABC123456');
      expect(result.indicators.confirmationNumberFound).toBe(true);
    });

    it('should detect confirmation email mention', async () => {
      // Arrange
      mockPage.evaluate.mockResolvedValue('a confirmation email has been sent to you');

      // Act
      const result = await applicationSubmissionWorkflow.performPostSubmissionVerification(
        mockPage, 
        { submittedAt: new Date() }
      );

      // Assert
      expect(result.confirmationEmail).toBe(true);
    });

    it('should handle verification errors gracefully', async () => {
      // Arrange
      mockPage.evaluate.mockRejectedValue(new Error('Page evaluation failed'));

      // Act
      const result = await applicationSubmissionWorkflow.performPostSubmissionVerification(
        mockPage, 
        { submittedAt: new Date() }
      );

      // Assert
      expect(result.success).toBe(false);
      expect(result.status).toBe('verification_failed');
      expect(result.error).toBe('Page evaluation failed');
    });

    it('should take screenshot during verification', async () => {
      // Act
      await applicationSubmissionWorkflow.performPostSubmissionVerification(
        mockPage, 
        { submittedAt: new Date() }
      );

      // Assert
      expect(mockPage.screenshot).toHaveBeenCalledWith(
        expect.objectContaining({
          path: expect.stringContaining('confirmation_'),
          fullPage: true
        })
      );
    });
  });

  describe('createApplicationResult', () => {
    it('should create successful application result', async () => {
      // Arrange
      const submissionResult = {
        submittedAt: new Date(),
        formComplexity: 'medium',
        successProbability: 0.8
      };
      const verificationResult = {
        success: true,
        confirmationNumber: 'ABC123',
        confirmationEmail: true,
        redirectUrl: 'https://www.funda.nl/confirmation',
        screenshot: 'screenshot.png'
      };

      const mockSave = jest.fn().mockResolvedValue(undefined);
      const mockApplicationResult = {
        _id: 'result123',
        save: mockSave
      };
      ApplicationResult.mockImplementation(() => mockApplicationResult);

      // Act
      const result = await applicationSubmissionWorkflow.createApplicationResult(
        mockQueueItem,
        submissionResult,
        verificationResult
      );

      // Assert
      expect(result._id).toBe('result123');
      expect(mockSave).toHaveBeenCalled();
      expect(ApplicationResult).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: mockQueueItem.userId,
          listingId: mockQueueItem.listingId,
          queueItemId: mockQueueItem._id,
          status: 'submitted',
          confirmationNumber: 'ABC123',
          confirmationEmail: true
        })
      );
    });

    it('should create failed application result with error', async () => {
      // Arrange
      const error = new Error('Submission failed');
      const mockSave = jest.fn().mockResolvedValue(undefined);
      const mockApplicationResult = {
        _id: 'result123',
        save: mockSave
      };
      ApplicationResult.mockImplementation(() => mockApplicationResult);

      // Act
      const result = await applicationSubmissionWorkflow.createApplicationResult(
        mockQueueItem,
        null,
        null,
        error
      );

      // Assert
      expect(result._id).toBe('result123');
      expect(ApplicationResult).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 'failed',
          response: expect.objectContaining({
            success: false,
            message: 'Submission failed'
          })
        })
      );
    });
  });

  describe('handleSubmissionError', () => {
    it('should set retry for network errors', async () => {
      // Arrange
      const error = new Error('Network connection failed');

      // Act
      const result = await applicationSubmissionWorkflow.handleSubmissionError(
        error,
        mockQueueItem
      );

      // Assert
      expect(result.shouldRetry).toBe(true);
      expect(result.retryDelay).toBeGreaterThan(0);
      expect(result.errorType).toBe('network');
      expect(mockQueueItem.updateStatus).toHaveBeenCalledWith('retrying');
    });

    it('should not retry for blocking errors', async () => {
      // Arrange
      const error = new Error('Access blocked by server');

      // Act
      const result = await applicationSubmissionWorkflow.handleSubmissionError(
        error,
        mockQueueItem
      );

      // Assert
      expect(result.shouldRetry).toBe(false);
      expect(result.errorType).toBe('blocked');
      expect(mockQueueItem.updateStatus).toHaveBeenCalledWith('failed');
    });

    it('should not retry for validation errors', async () => {
      // Arrange
      const error = new Error('Validation failed: required field missing');

      // Act
      const result = await applicationSubmissionWorkflow.handleSubmissionError(
        error,
        mockQueueItem
      );

      // Assert
      expect(result.shouldRetry).toBe(false);
      expect(result.errorType).toBe('validation');
    });

    it('should limit retries for form changes', async () => {
      // Arrange
      const error = new Error('Form selector not found');
      mockQueueItem.attempts = 2;

      // Act
      const result = await applicationSubmissionWorkflow.handleSubmissionError(
        error,
        mockQueueItem
      );

      // Assert
      expect(result.shouldRetry).toBe(false); // Should not retry after 2 attempts for form changes
      expect(result.errorType).toBe('form_changed');
    });

    it('should implement exponential backoff for retries', async () => {
      // Arrange
      const error = new Error('Timeout occurred');
      mockQueueItem.attempts = 1;

      // Act
      const result = await applicationSubmissionWorkflow.handleSubmissionError(
        error,
        mockQueueItem
      );

      // Assert
      expect(result.shouldRetry).toBe(true);
      expect(result.retryDelay).toBeGreaterThan(5 * 60 * 1000); // Should be more than base delay
    });

    it('should stop retrying after max attempts', async () => {
      // Arrange
      const error = new Error('Network timeout');
      mockQueueItem.attempts = 3;
      mockQueueItem.maxAttempts = 3;

      // Act
      const result = await applicationSubmissionWorkflow.handleSubmissionError(
        error,
        mockQueueItem
      );

      // Assert
      expect(result.shouldRetry).toBe(false);
      expect(mockQueueItem.updateStatus).toHaveBeenCalledWith('failed');
    });
  });

  describe('Helper Methods', () => {
    describe('isPersonalInfoComplete', () => {
      it('should return true for complete personal info', () => {
        const personalInfo = {
          fullName: 'John Doe',
          email: '<EMAIL>',
          phone: '+31612345678',
          dateOfBirth: new Date('1990-01-01'),
          nationality: 'Dutch',
          occupation: 'Developer',
          monthlyIncome: 5000
        };

        const result = applicationSubmissionWorkflow.isPersonalInfoComplete(personalInfo);
        expect(result).toBe(true);
      });

      it('should return false for incomplete personal info', () => {
        const personalInfo = {
          fullName: 'John Doe',
          email: '', // Missing email
          phone: '+31612345678',
          dateOfBirth: new Date('1990-01-01'),
          nationality: 'Dutch',
          occupation: 'Developer',
          monthlyIncome: 5000
        };

        const result = applicationSubmissionWorkflow.isPersonalInfoComplete(personalInfo);
        expect(result).toBe(false);
      });

      it('should return false for null personal info', () => {
        const result = applicationSubmissionWorkflow.isPersonalInfoComplete(null);
        expect(result).toBe(false);
      });
    });

    describe('areRequiredDocumentsUploaded', () => {
      it('should return true when all required documents are uploaded', () => {
        const documents = [
          { type: 'id', required: true, uploaded: true },
          { type: 'income', required: true, uploaded: true },
          { type: 'optional', required: false, uploaded: false }
        ];

        const result = applicationSubmissionWorkflow.areRequiredDocumentsUploaded(documents);
        expect(result).toBe(true);
      });

      it('should return false when required documents are missing', () => {
        const documents = [
          { type: 'id', required: true, uploaded: true },
          { type: 'income', required: true, uploaded: false }
        ];

        const result = applicationSubmissionWorkflow.areRequiredDocumentsUploaded(documents);
        expect(result).toBe(false);
      });

      it('should return false for null or invalid documents array', () => {
        expect(applicationSubmissionWorkflow.areRequiredDocumentsUploaded(null)).toBe(false);
        expect(applicationSubmissionWorkflow.areRequiredDocumentsUploaded(undefined)).toBe(false);
        expect(applicationSubmissionWorkflow.areRequiredDocumentsUploaded('invalid')).toBe(false);
      });
    });

    describe('calculateFormComplexity', () => {
      it('should return simple for few fields', () => {
        const formFields = [
          { name: 'name' },
          { name: 'email' },
          { name: 'message' }
        ];

        const result = applicationSubmissionWorkflow.calculateFormComplexity(formFields);
        expect(result).toBe('simple');
      });

      it('should return medium for moderate number of fields', () => {
        const formFields = Array(10).fill().map((_, i) => ({ name: `field${i}` }));

        const result = applicationSubmissionWorkflow.calculateFormComplexity(formFields);
        expect(result).toBe('medium');
      });

      it('should return complex for many fields', () => {
        const formFields = Array(20).fill().map((_, i) => ({ name: `field${i}` }));

        const result = applicationSubmissionWorkflow.calculateFormComplexity(formFields);
        expect(result).toBe('complex');
      });
    });

    describe('calculateSuccessProbability', () => {
      it('should calculate higher probability for native forms', () => {
        const fillResult = { fieldsFilledCount: 9, totalFieldsCount: 10 };
        const formType = { type: 'native' };

        const result = applicationSubmissionWorkflow.calculateSuccessProbability(fillResult, formType);
        expect(result).toBeGreaterThan(0.8);
      });

      it('should calculate lower probability with validation errors', () => {
        const fillResult = { 
          fieldsFilledCount: 9, 
          totalFieldsCount: 10,
          validationErrors: ['Error 1', 'Error 2']
        };
        const formType = { type: 'external' };

        const result = applicationSubmissionWorkflow.calculateSuccessProbability(fillResult, formType);
        expect(result).toBeLessThan(0.8);
      });

      it('should ensure probability stays within 0-1 range', () => {
        const fillResult = { 
          fieldsFilledCount: 10, 
          totalFieldsCount: 10,
          validationErrors: []
        };
        const formType = { type: 'native' };

        const result = applicationSubmissionWorkflow.calculateSuccessProbability(fillResult, formType);
        expect(result).toBeGreaterThanOrEqual(0);
        expect(result).toBeLessThanOrEqual(1);
      });
    });

    describe('extractConfirmationNumber', () => {
      it('should extract confirmation number from Dutch text', async () => {
        // Arrange
        mockPage.evaluate.mockResolvedValue('Uw aanmelding nummer: ABC123456');

        // Act
        const result = await applicationSubmissionWorkflow.extractConfirmationNumber(mockPage);

        // Assert
        expect(result).toBe('ABC123456');
      });

      it('should extract confirmation number from English text', async () => {
        // Arrange
        mockPage.evaluate.mockResolvedValue('Your confirmation number: XYZ789012');

        // Act
        const result = await applicationSubmissionWorkflow.extractConfirmationNumber(mockPage);

        // Assert
        expect(result).toBe('XYZ789012');
      });

      it('should return null when no confirmation number found', async () => {
        // Arrange
        mockPage.evaluate.mockResolvedValue('Thank you for your application');

        // Act
        const result = await applicationSubmissionWorkflow.extractConfirmationNumber(mockPage);

        // Assert
        expect(result).toBeNull();
      });

      it('should handle extraction errors gracefully', async () => {
        // Arrange
        mockPage.evaluate.mockRejectedValue(new Error('Page error'));

        // Act
        const result = await applicationSubmissionWorkflow.extractConfirmationNumber(mockPage);

        // Assert
        expect(result).toBeNull();
      });
    });

    describe('categorizeError', () => {
      it('should categorize timeout errors', () => {
        const error = new Error('Navigation timeout exceeded');
        const result = applicationSubmissionWorkflow.categorizeError(error);
        expect(result).toBe('timeout');
      });

      it('should categorize network errors', () => {
        const error = new Error('Network connection failed');
        const result = applicationSubmissionWorkflow.categorizeError(error);
        expect(result).toBe('network');
      });

      it('should categorize blocking errors', () => {
        const error = new Error('Access blocked by server');
        const result = applicationSubmissionWorkflow.categorizeError(error);
        expect(result).toBe('blocked');
      });

      it('should categorize CAPTCHA errors', () => {
        const error = new Error('CAPTCHA verification required');
        const result = applicationSubmissionWorkflow.categorizeError(error);
        expect(result).toBe('captcha');
      });

      it('should categorize form change errors', () => {
        const error = new Error('Form selector not found');
        const result = applicationSubmissionWorkflow.categorizeError(error);
        expect(result).toBe('form_changed');
      });

      it('should categorize validation errors', () => {
        const error = new Error('Validation failed: required field missing');
        const result = applicationSubmissionWorkflow.categorizeError(error);
        expect(result).toBe('validation');
      });

      it('should categorize rate limit errors', () => {
        const error = new Error('Rate limit exceeded');
        const result = applicationSubmissionWorkflow.categorizeError(error);
        expect(result).toBe('rate_limit');
      });

      it('should categorize unknown errors', () => {
        const error = new Error('Some unexpected error');
        const result = applicationSubmissionWorkflow.categorizeError(error);
        expect(result).toBe('unknown');
      });
    });
  });

  describe('getStatus', () => {
    it('should return current status information', () => {
      const status = applicationSubmissionWorkflow.getStatus();

      expect(status).toHaveProperty('isProcessing');
      expect(status).toHaveProperty('activeSubmissions');
      expect(status).toHaveProperty('activeSubmissionIds');
      expect(typeof status.isProcessing).toBe('boolean');
      expect(typeof status.activeSubmissions).toBe('number');
      expect(Array.isArray(status.activeSubmissionIds)).toBe(true);
    });
  });
});