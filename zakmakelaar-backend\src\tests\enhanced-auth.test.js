/**
 * Tests for Enhanced Authentication Controller Methods - Task 2 Implementation
 */

const userController = require('../controllers/userController');
const validation = require('../middleware/validation');
const fs = require('fs');
const path = require('path');

describe('Enhanced Authentication Implementation - Task 2', () => {
  describe('Controller Methods', () => {
    test('completeProfile method exists and is a function', () => {
      expect(userController.completeProfile).toBeDefined();
      expect(typeof userController.completeProfile).toBe('function');
    });

    test('updateUserType method exists and is a function', () => {
      expect(userController.updateUserType).toBeDefined();
      expect(typeof userController.updateUserType).toBe('function');
    });

    test('setLanguagePreference method exists and is a function', () => {
      expect(userController.setLanguagePreference).toBeDefined();
      expect(typeof userController.setLanguagePreference).toBe('function');
    });

    test('uploadProfilePicture method exists', () => {
      expect(userController.uploadProfilePicture).toBeDefined();
    });

    test('updateProfilePicture method exists and is a function', () => {
      expect(userController.updateProfilePicture).toBeDefined();
      expect(typeof userController.updateProfilePicture).toBe('function');
    });

    test('getProfileStatus method exists and is a function', () => {
      expect(userController.getProfileStatus).toBeDefined();
      expect(typeof userController.getProfileStatus).toBe('function');
    });

    test('updateEmployment method exists and is a function', () => {
      expect(userController.updateEmployment).toBeDefined();
      expect(typeof userController.updateEmployment).toBe('function');
    });

    test('updateSocialPreferences method exists and is a function', () => {
      expect(userController.updateSocialPreferences).toBeDefined();
      expect(typeof userController.updateSocialPreferences).toBe('function');
    });
  });

  describe('Validation Middleware', () => {
    test('validateProfileCompletion exists and is an array', () => {
      expect(validation.validateProfileCompletion).toBeDefined();
      expect(Array.isArray(validation.validateProfileCompletion)).toBe(true);
      expect(validation.validateProfileCompletion.length).toBeGreaterThan(0);
    });

    test('validateUserType exists and is an array', () => {
      expect(validation.validateUserType).toBeDefined();
      expect(Array.isArray(validation.validateUserType)).toBe(true);
      expect(validation.validateUserType.length).toBeGreaterThan(0);
    });

    test('validateLanguagePreference exists and is an array', () => {
      expect(validation.validateLanguagePreference).toBeDefined();
      expect(Array.isArray(validation.validateLanguagePreference)).toBe(true);
      expect(validation.validateLanguagePreference.length).toBeGreaterThan(0);
    });

    test('validateEmployment exists and is an array', () => {
      expect(validation.validateEmployment).toBeDefined();
      expect(Array.isArray(validation.validateEmployment)).toBe(true);
      expect(validation.validateEmployment.length).toBeGreaterThan(0);
    });

    test('validateSocialPreferences exists and is an array', () => {
      expect(validation.validateSocialPreferences).toBeDefined();
      expect(Array.isArray(validation.validateSocialPreferences)).toBe(true);
      expect(validation.validateSocialPreferences.length).toBeGreaterThan(0);
    });
  });

  describe('Route Integration', () => {
    test('auth routes include new endpoints', () => {
      const authRoutesPath = path.join(__dirname, '../routes/auth.js');
      const authRoutesContent = fs.readFileSync(authRoutesPath, 'utf8');

      expect(authRoutesContent).toContain('/profile/complete');
      expect(authRoutesContent).toContain('/user-type');
      expect(authRoutesContent).toContain('/language');
      expect(authRoutesContent).toContain('/profile-picture');
      expect(authRoutesContent).toContain('/employment');
      expect(authRoutesContent).toContain('/social-preferences');
    });

    test('controller methods are used in routes', () => {
      const authRoutesPath = path.join(__dirname, '../routes/auth.js');
      const authRoutesContent = fs.readFileSync(authRoutesPath, 'utf8');

      expect(authRoutesContent).toContain('userController.completeProfile');
      expect(authRoutesContent).toContain('userController.updateUserType');
      expect(authRoutesContent).toContain('userController.setLanguagePreference');
      expect(authRoutesContent).toContain('userController.updateProfilePicture');
      expect(authRoutesContent).toContain('userController.updateEmployment');
      expect(authRoutesContent).toContain('userController.updateSocialPreferences');
    });
  });

  describe('File Upload Configuration', () => {
    test('uploads directory exists', () => {
      const uploadsDir = path.join(__dirname, '../../uploads/profile-pictures');
      expect(fs.existsSync(uploadsDir)).toBe(true);
    });

    test('multer configuration exists in controller', () => {
      const controllerPath = path.join(__dirname, '../controllers/userController.js');
      const controllerContent = fs.readFileSync(controllerPath, 'utf8');

      expect(controllerContent).toContain('multer.diskStorage');
      expect(controllerContent).toContain('fileFilter');
      expect(controllerContent).toContain('5 * 1024 * 1024'); // 5MB limit
    });
  });

  describe('Requirements Coverage', () => {
    test('Requirement 1.1 - User registration and profile completion', () => {
      expect(userController.completeProfile).toBeDefined();
      expect(userController.getProfileStatus).toBeDefined();
      expect(validation.validateProfileCompletion).toBeDefined();
    });

    test('Requirements 3.1, 3.2 - User type management', () => {
      expect(userController.updateUserType).toBeDefined();
      expect(validation.validateUserType).toBeDefined();
    });

    test('Requirements 4.1, 4.2 - Language preference', () => {
      expect(userController.setLanguagePreference).toBeDefined();
      expect(validation.validateLanguagePreference).toBeDefined();
    });

    test('Requirements 5.1, 5.2 - Profile management and file upload', () => {
      expect(userController.updateProfilePicture).toBeDefined();
      expect(userController.uploadProfilePicture).toBeDefined();
      expect(userController.updateEmployment).toBeDefined();
      expect(userController.updateSocialPreferences).toBeDefined();
      expect(validation.validateEmployment).toBeDefined();
      expect(validation.validateSocialPreferences).toBeDefined();
    });
  });
});