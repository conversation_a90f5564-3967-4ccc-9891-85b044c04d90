/**
 * Test Component for LocationSelector
 * Tests the location dropdown functionality
 */

import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { LocationSelector } from '../zakmakelaar-frontend/components/LocationSelector';
import { listingsService } from '../zakmakelaar-frontend/services/listingsService';

// Mock the listings service
jest.mock('../zakmakelaar-frontend/services/listingsService');

// Mock cities data
const mockCities = [
  'Amsterdam',
  'Rotterdam',
  'Den Haag',
  'Utrecht',
  'Eindhoven',
  'Tilburg',
  'Groningen',
  'Almere',
  'Breda',
  'Nijmegen',
  'Enschede',
  'Haarlem',
  'Arnhem',
  'Zaanstad',
  'Amersfoort'
];

describe('LocationSelector', () => {
  const mockOnSelectionChange = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    
    (listingsService.getAvailableCities as jest.Mock).mockResolvedValue({
      success: true,
      data: mockCities
    });
  });

  it('renders with placeholder text when no locations selected', () => {
    const { getByText } = render(
      <LocationSelector
        selectedLocations={[]}
        onSelectionChange={mockOnSelectionChange}
        placeholder="Select cities..."
      />
    );

    expect(getByText('Select cities...')).toBeTruthy();
  });

  it('displays selected locations as chips in chip mode', () => {
    const selectedLocations = ['Amsterdam', 'Utrecht'];
    
    const { getByText } = render(
      <LocationSelector
        selectedLocations={selectedLocations}
        onSelectionChange={mockOnSelectionChange}
        displayMode="chips"
      />
    );

    expect(getByText('Amsterdam')).toBeTruthy();
    expect(getByText('Utrecht')).toBeTruthy();
    expect(getByText('2')).toBeTruthy(); // Count badge
  });

  it('displays selected locations as text in text mode', () => {
    const selectedLocations = ['Amsterdam', 'Utrecht'];
    
    const { getByText } = render(
      <LocationSelector
        selectedLocations={selectedLocations}
        onSelectionChange={mockOnSelectionChange}
        displayMode="text"
      />
    );

    expect(getByText('Amsterdam, Utrecht')).toBeTruthy();
  });

  it('allows removing individual chips in chip mode', () => {
    const selectedLocations = ['Amsterdam', 'Utrecht', 'Rotterdam'];
    
    const { getAllByTestId } = render(
      <LocationSelector
        selectedLocations={selectedLocations}
        onSelectionChange={mockOnSelectionChange}
        displayMode="chips"
      />
    );

    // Find and press the remove button for Amsterdam chip
    // This would need proper testID implementation in the component
    // For now, we test the handler function directly
    expect(selectedLocations).toContain('Amsterdam');
  });

  it('shows add more button in chip mode', () => {
    const selectedLocations = ['Amsterdam', 'Utrecht'];
    
    const { getByText } = render(
      <LocationSelector
        selectedLocations={selectedLocations}
        onSelectionChange={mockOnSelectionChange}
        displayMode="chips"
      />
    );

    expect(getByText('Add')).toBeTruthy();
  });

  it('shows count badge when locations are selected in text mode', () => {
    const selectedLocations = ['Amsterdam', 'Utrecht', 'Rotterdam'];
    
    const { getByText } = render(
      <LocationSelector
        selectedLocations={selectedLocations}
        onSelectionChange={mockOnSelectionChange}
        displayMode="text"
      />
    );

    // Should show "Amsterdam, Utrecht +1 more" format
    expect(getByText('Amsterdam, Utrecht +1 more')).toBeTruthy();
  });

  it('opens modal when selector is pressed', async () => {
    const { getByText, getByPlaceholderText } = render(
      <LocationSelector
        selectedLocations={[]}
        onSelectionChange={mockOnSelectionChange}
      />
    );

    // Press the selector button
    fireEvent.press(getByText('Select locations...'));

    // Wait for modal to appear
    await waitFor(() => {
      expect(getByText('Select Locations')).toBeTruthy();
      expect(getByPlaceholderText('Search cities...')).toBeTruthy();
    });
  });

  it('loads and displays available cities', async () => {
    const { getByText } = render(
      <LocationSelector
        selectedLocations={[]}
        onSelectionChange={mockOnSelectionChange}
      />
    );

    // Open modal
    fireEvent.press(getByText('Select locations...'));

    // Wait for cities to load
    await waitFor(() => {
      expect(getByText('Amsterdam')).toBeTruthy();
      expect(getByText('Rotterdam')).toBeTruthy();
      expect(getByText('Utrecht')).toBeTruthy();
    });

    expect(listingsService.getAvailableCities).toHaveBeenCalled();
  });

  it('filters cities based on search query', async () => {
    const { getByText, getByPlaceholderText, queryByText } = render(
      <LocationSelector
        selectedLocations={[]}
        onSelectionChange={mockOnSelectionChange}
      />
    );

    // Open modal
    fireEvent.press(getByText('Select locations...'));

    await waitFor(() => {
      expect(getByText('Amsterdam')).toBeTruthy();
    });

    // Search for "Amst"
    const searchInput = getByPlaceholderText('Search cities...');
    fireEvent.changeText(searchInput, 'Amst');

    await waitFor(() => {
      expect(getByText('Amsterdam')).toBeTruthy();
      expect(queryByText('Rotterdam')).toBeNull(); // Should be filtered out
    });
  });

  it('allows selecting and deselecting cities', async () => {
    const { getByText } = render(
      <LocationSelector
        selectedLocations={[]}
        onSelectionChange={mockOnSelectionChange}
      />
    );

    // Open modal
    fireEvent.press(getByText('Select locations...'));

    await waitFor(() => {
      expect(getByText('Amsterdam')).toBeTruthy();
    });

    // Select Amsterdam
    fireEvent.press(getByText('Amsterdam'));

    expect(mockOnSelectionChange).toHaveBeenCalledWith(['Amsterdam']);
  });

  it('prevents selection beyond max limit', async () => {
    const selectedLocations = ['Amsterdam', 'Utrecht']; // Already 2 selected
    
    const { getByText } = render(
      <LocationSelector
        selectedLocations={selectedLocations}
        onSelectionChange={mockOnSelectionChange}
        maxSelections={2}
      />
    );

    // Open modal
    fireEvent.press(getByText('Amsterdam, Utrecht'));

    await waitFor(() => {
      expect(getByText('Rotterdam')).toBeTruthy();
    });

    // Try to select Rotterdam (should show alert)
    fireEvent.press(getByText('Rotterdam'));

    // Should not call onSelectionChange since limit is reached
    expect(mockOnSelectionChange).not.toHaveBeenCalled();
  });

  it('shows selection count and allows select all', async () => {
    const { getByText } = render(
      <LocationSelector
        selectedLocations={['Amsterdam']}
        onSelectionChange={mockOnSelectionChange}
        maxSelections={20}
      />
    );

    // Open modal
    fireEvent.press(getByText('Amsterdam'));

    await waitFor(() => {
      expect(getByText('1 of 20 locations selected')).toBeTruthy();
      expect(getByText('Select All')).toBeTruthy();
      expect(getByText('Clear All')).toBeTruthy();
    });
  });

  it('allows clearing all selections', async () => {
    const { getByText } = render(
      <LocationSelector
        selectedLocations={['Amsterdam', 'Utrecht']}
        onSelectionChange={mockOnSelectionChange}
      />
    );

    // Open modal
    fireEvent.press(getByText('Amsterdam, Utrecht'));

    await waitFor(() => {
      expect(getByText('Clear All')).toBeTruthy();
    });

    // Clear all selections
    fireEvent.press(getByText('Clear All'));

    expect(mockOnSelectionChange).toHaveBeenCalledWith([]);
  });

  it('handles API error gracefully with fallback cities', async () => {
    // Mock API failure
    (listingsService.getAvailableCities as jest.Mock).mockRejectedValue(
      new Error('API Error')
    );

    const { getByText } = render(
      <LocationSelector
        selectedLocations={[]}
        onSelectionChange={mockOnSelectionChange}
      />
    );

    // Open modal
    fireEvent.press(getByText('Select locations...'));

    // Should still show fallback cities
    await waitFor(() => {
      expect(getByText('Amsterdam')).toBeTruthy();
      expect(getByText('Rotterdam')).toBeTruthy();
    });
  });

  it('closes modal when done button is pressed', async () => {
    const { getByText, queryByText } = render(
      <LocationSelector
        selectedLocations={[]}
        onSelectionChange={mockOnSelectionChange}
      />
    );

    // Open modal
    fireEvent.press(getByText('Select locations...'));

    await waitFor(() => {
      expect(getByText('Select Locations')).toBeTruthy();
    });

    // Press done button
    fireEvent.press(getByText('Done'));

    await waitFor(() => {
      expect(queryByText('Select Locations')).toBeNull();
    });
  });

  it('handles chip removal correctly', () => {
    const selectedLocations = ['Amsterdam', 'Utrecht', 'Rotterdam'];
    
    const { getByText } = render(
      <LocationSelector
        selectedLocations={selectedLocations}
        onSelectionChange={mockOnSelectionChange}
        displayMode="chips"
      />
    );

    // Verify chips are displayed
    expect(getByText('Amsterdam')).toBeTruthy();
    expect(getByText('Utrecht')).toBeTruthy();
    expect(getByText('Rotterdam')).toBeTruthy();

    // Test would require finding the remove button and pressing it
    // This tests the component structure for now
  });
});

// Integration test with auto-application settings
describe('LocationSelector Integration', () => {
  it('integrates correctly with auto-application settings', async () => {
    const mockSettings = {
      criteria: {
        locations: ['Amsterdam', 'Utrecht']
      }
    };

    const mockUpdateSetting = jest.fn();

    const { getByText } = render(
      <LocationSelector
        selectedLocations={mockSettings.criteria.locations}
        onSelectionChange={(locations) => mockUpdateSetting('criteria.locations', locations)}
      />
    );

    // Should display current selections
    expect(getByText('Amsterdam, Utrecht')).toBeTruthy();

    // Open modal and add Rotterdam
    fireEvent.press(getByText('Amsterdam, Utrecht'));

    await waitFor(() => {
      expect(getByText('Rotterdam')).toBeTruthy();
    });

    fireEvent.press(getByText('Rotterdam'));

    // Should update settings with new location
    expect(mockUpdateSetting).toHaveBeenCalledWith(
      'criteria.locations',
      ['Amsterdam', 'Utrecht', 'Rotterdam']
    );
  });
});

export default LocationSelector;