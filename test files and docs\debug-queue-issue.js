/**
 * Comprehensive Debug Script for Queue Management Issue
 * This script will help identify the exact cause of the 500 error
 */

const axios = require('axios');

const BACKEND_URL = 'http://localhost:3000';

async function debugQueueIssue() {
  console.log('🔍 Comprehensive Queue Management Debug');
  console.log('='.repeat(50));

  try {
    // Step 1: Test basic backend connectivity
    console.log('\n1️⃣ Testing Backend Connectivity...');
    try {
      const healthResponse = await axios.get(`${BACKEND_URL}/health`);
      console.log('✅ Backend is accessible');
      console.log(`   Status: ${healthResponse.data.status}`);
      console.log(`   Database: ${healthResponse.data.database}`);
    } catch (error) {
      console.log('❌ Backend not accessible');
      console.log(`   Error: ${error.message}`);
      return;
    }

    // Step 2: Test authentication
    console.log('\n2️⃣ Testing Authentication...');
    let token, userId;
    try {
      const loginResponse = await axios.post(`${BACKEND_URL}/api/auth/login`, {
        email: '<EMAIL>',
        password: 'Testpassword123'
      });

      if (loginResponse.data.token) {
        token = loginResponse.data.token;
        const user = loginResponse.data.data.user;
        userId = user.id || user._id;
        console.log('✅ Authentication successful');
        console.log(`   User ID: ${userId}`);
        console.log(`   Token: ${token.substring(0, 20)}...`);
      } else {
        throw new Error('No token received');
      }
    } catch (error) {
      console.log('❌ Authentication failed');
      console.log(`   Error: ${error.response?.data || error.message}`);
      return;
    }

    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };

    // Step 3: Test debug endpoint
    console.log('\n3️⃣ Testing Debug Endpoint...');
    try {
      const debugResponse = await axios.get(
        `${BACKEND_URL}/api/auto-application/queue/debug/${userId}`,
        { headers }
      );

      if (debugResponse.data.status === 'success') {
        const debug = debugResponse.data.data;
        console.log('✅ Debug endpoint accessible');
        console.log(`   Queue Manager Exists: ${debug.queueManagerExists}`);
        console.log(`   Pause Method: ${debug.pauseQueueMethod}`);
        console.log(`   Resume Method: ${debug.resumeQueueMethod}`);
        console.log(`   User ID Type: ${debug.userIdType}`);
        console.log(`   Queue Items: ${debug.queueItemsCount}`);
        
        if (debug.databaseError) {
          console.log(`   ⚠️ Database Error: ${debug.databaseError}`);
        }

        // Check if methods are available
        if (!debug.queueManagerExists) {
          console.log('❌ Queue Manager not initialized!');
          return;
        }
        
        if (debug.pauseQueueMethod !== 'function' || debug.resumeQueueMethod !== 'function') {
          console.log('❌ Queue methods not available!');
          console.log(`   Pause method type: ${debug.pauseQueueMethod}`);
          console.log(`   Resume method type: ${debug.resumeQueueMethod}`);
          return;
        }
      } else {
        console.log('❌ Debug endpoint failed');
        console.log(`   Response: ${JSON.stringify(debugResponse.data, null, 2)}`);
      }
    } catch (error) {
      console.log('❌ Debug endpoint error');
      console.log(`   Status: ${error.response?.status}`);
      console.log(`   Error: ${JSON.stringify(error.response?.data, null, 2)}`);
    }

    // Step 4: Test pause functionality with detailed logging
    console.log('\n4️⃣ Testing Pause Queue (Detailed)...');
    try {
      const pauseResponse = await axios.post(
        `${BACKEND_URL}/api/auto-application/queue/${userId}/pause`,
        {},
        { headers }
      );

      console.log('✅ Pause request completed');
      console.log(`   Status: ${pauseResponse.status}`);
      console.log(`   Response: ${JSON.stringify(pauseResponse.data, null, 2)}`);
    } catch (error) {
      console.log('❌ Pause request failed');
      console.log(`   Status: ${error.response?.status}`);
      console.log(`   Status Text: ${error.response?.statusText}`);
      console.log(`   Response Headers: ${JSON.stringify(error.response?.headers, null, 2)}`);
      console.log(`   Response Data: ${JSON.stringify(error.response?.data, null, 2)}`);
      console.log(`   Error Message: ${error.message}`);
      
      // Check if it's the generic error
      if (error.response?.data?.message === 'Something went wrong!') {
        console.log('🚨 This is the generic error from error handler middleware!');
        console.log('   The actual error is being caught by the global error handler.');
        console.log('   Check the backend console logs for the real error.');
      }
    }

    // Step 5: Test resume functionality with detailed logging
    console.log('\n5️⃣ Testing Resume Queue (Detailed)...');
    try {
      const resumeResponse = await axios.post(
        `${BACKEND_URL}/api/auto-application/queue/${userId}/resume`,
        {},
        { headers }
      );

      console.log('✅ Resume request completed');
      console.log(`   Status: ${resumeResponse.status}`);
      console.log(`   Response: ${JSON.stringify(resumeResponse.data, null, 2)}`);
    } catch (error) {
      console.log('❌ Resume request failed');
      console.log(`   Status: ${error.response?.status}`);
      console.log(`   Status Text: ${error.response?.statusText}`);
      console.log(`   Response Headers: ${JSON.stringify(error.response?.headers, null, 2)}`);
      console.log(`   Response Data: ${JSON.stringify(error.response?.data, null, 2)}`);
      console.log(`   Error Message: ${error.message}`);
      
      // Check if it's the generic error
      if (error.response?.data?.message === 'Something went wrong!') {
        console.log('🚨 This is the generic error from error handler middleware!');
        console.log('   The actual error is being caught by the global error handler.');
        console.log('   Check the backend console logs for the real error.');
      }
    }

    // Step 6: Test with different user ID formats
    console.log('\n6️⃣ Testing Different User ID Formats...');
    
    const testUserIds = [
      userId,
      userId.toString(),
      `"${userId}"`,
      userId.replace(/[^a-f0-9]/gi, ''), // Remove non-hex characters if any
    ];

    for (const testId of testUserIds) {
      if (testId === userId) continue; // Skip the original, already tested
      
      console.log(`\n   Testing with User ID: ${testId}`);
      try {
        const testResponse = await axios.post(
          `${BACKEND_URL}/api/auto-application/queue/${testId}/resume`,
          {},
          { headers }
        );
        console.log(`   ✅ Success with ID format: ${testId}`);
      } catch (error) {
        console.log(`   ❌ Failed with ID format: ${testId}`);
        console.log(`      Status: ${error.response?.status}`);
        console.log(`      Message: ${error.response?.data?.message}`);
      }
    }

    console.log('\n📋 Debug Summary');
    console.log('='.repeat(30));
    console.log('If you see "Something went wrong!" errors:');
    console.log('1. Check the backend console for actual error details');
    console.log('2. The error is being caught by the global error handler');
    console.log('3. Look for console.log statements in the route handlers');
    console.log('4. Check if the ApplicationQueue model is properly connected');
    console.log('5. Verify MongoDB connection and permissions');

  } catch (error) {
    console.log('\n💥 Unexpected error during debug:');
    console.log(`   Error: ${error.message}`);
    console.log(`   Stack: ${error.stack}`);
  }
}

// Helper function to check backend logs
function checkBackendLogs() {
  console.log('\n📝 Backend Log Checklist:');
  console.log('Look for these log messages in your backend console:');
  console.log('1. "Attempting to pause/resume queue for user: [userId]"');
  console.log('2. "Request user ID: [id], Target user ID: [id]"');
  console.log('3. "QueueManager or pauseQueue/resumeQueue method not available"');
  console.log('4. "Queue manager error: [error details]"');
  console.log('5. "Error in pause/resume queue route: [error details]"');
  console.log('6. Any MongoDB connection errors');
  console.log('7. Any ApplicationQueue model errors');
}

// Run the debug
if (require.main === module) {
  debugQueueIssue()
    .then(() => checkBackendLogs())
    .catch(console.error);
}

module.exports = { debugQueueIssue, checkBackendLogs };