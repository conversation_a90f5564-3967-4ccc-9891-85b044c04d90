const auditLogService = require('../../services/auditLogService');
const mongoose = require('mongoose');

// Mock the logger
jest.mock('../../services/logger', () => ({
  loggers: {
    app: {
      info: jest.fn(),
      error: jest.fn(),
      debug: jest.fn()
    },
    audit: {
      info: jest.fn()
    },
    security: {
      error: jest.fn()
    }
  }
}));

describe('AuditLogService', () => {
  const testUserId = new mongoose.Types.ObjectId();
  const testSessionId = 'test-session-123';
  const testMetadata = {
    ipAddress: '***********',
    userAgent: 'Mozilla/5.0 Test Browser',
    requestId: 'req-123',
    endpoint: '/api/test',
    method: 'POST'
  };

  beforeAll(async () => {
    // Connect to test database
    await mongoose.connect(process.env.MONGO_URI || 'mongodb://localhost:27017/zakmakelaar-test');
  });

  afterAll(async () => {
    // Clean up and close connection
    await mongoose.connection.dropDatabase();
    await mongoose.connection.close();
  });

  beforeEach(async () => {
    // Clear audit logs before each test
    const AuditLog = mongoose.model('AuditLog');
    await AuditLog.deleteMany({});
  });

  describe('Basic Logging', () => {
    test('should log audit events successfully', async () => {
      const logData = {
        userId: testUserId,
        sessionId: testSessionId,
        action: 'auto_application_enabled',
        category: 'auto_application',
        details: { maxApplicationsPerDay: 5 },
        ...testMetadata
      };

      await auditLogService.log(logData);

      // Verify log was created
      const AuditLog = mongoose.model('AuditLog');
      const logs = await AuditLog.find({ userId: testUserId });
      
      expect(logs).toHaveLength(1);
      expect(logs[0].action).toBe('auto_application_enabled');
      expect(logs[0].category).toBe('auto_application');
      expect(logs[0].details.maxApplicationsPerDay).toBe(5);
      expect(logs[0].metadata.ipAddress).toBe('***********');
    });

    test('should auto-categorize actions when category not provided', async () => {
      const logData = {
        userId: testUserId,
        action: 'login_failed',
        ...testMetadata
      };

      await auditLogService.log(logData);

      const AuditLog = mongoose.model('AuditLog');
      const logs = await AuditLog.find({ userId: testUserId });
      
      expect(logs[0].category).toBe('security');
    });

    test('should auto-determine severity when not provided', async () => {
      const logData = {
        userId: testUserId,
        action: 'suspicious_activity',
        ...testMetadata
      };

      await auditLogService.log(logData);

      const AuditLog = mongoose.model('AuditLog');
      const logs = await AuditLog.find({ userId: testUserId });
      
      expect(logs[0].severity).toBe('critical');
    });

    test('should handle logging errors gracefully', async () => {
      // Test with invalid data that might cause database error
      const logData = {
        userId: 'invalid-user-id', // Invalid ObjectId
        action: 'test_action'
      };

      // Should not throw error
      await expect(auditLogService.log(logData)).resolves.not.toThrow();
    });
  });

  describe('Specialized Logging Methods', () => {
    test('should log auto-application events', async () => {
      await auditLogService.logAutoApplication(
        testUserId,
        'application_submitted',
        { listingId: 'listing-123', success: true },
        testMetadata
      );

      const AuditLog = mongoose.model('AuditLog');
      const logs = await AuditLog.find({ userId: testUserId });
      
      expect(logs[0].category).toBe('auto_application');
      expect(logs[0].action).toBe('application_submitted');
      expect(logs[0].details.listingId).toBe('listing-123');
    });

    test('should log security events with appropriate severity', async () => {
      await auditLogService.logSecurity(
        testUserId,
        'login_failed',
        { consecutiveFailures: 4 },
        testMetadata
      );

      const AuditLog = mongoose.model('AuditLog');
      const logs = await AuditLog.find({ userId: testUserId });
      
      expect(logs[0].category).toBe('security');
      expect(logs[0].severity).toBe('critical'); // Due to consecutive failures > 3
    });

    test('should log privacy events as high severity', async () => {
      await auditLogService.logPrivacy(
        testUserId,
        'data_export_requested',
        { format: 'json' },
        testMetadata
      );

      const AuditLog = mongoose.model('AuditLog');
      const logs = await AuditLog.find({ userId: testUserId });
      
      expect(logs[0].category).toBe('privacy');
      expect(logs[0].severity).toBe('high');
    });
  });

  describe('Log Retrieval', () => {
    beforeEach(async () => {
      // Create test logs
      const testLogs = [
        {
          userId: testUserId,
          action: 'auto_application_enabled',
          category: 'auto_application',
          severity: 'medium',
          timestamp: new Date('2024-01-01T10:00:00Z')
        },
        {
          userId: testUserId,
          action: 'login_success',
          category: 'security',
          severity: 'low',
          timestamp: new Date('2024-01-01T11:00:00Z')
        },
        {
          userId: testUserId,
          action: 'data_export_requested',
          category: 'privacy',
          severity: 'high',
          timestamp: new Date('2024-01-01T12:00:00Z')
        }
      ];

      await auditLogService.log(testLogs[0]);
      await auditLogService.log(testLogs[1]);
      await auditLogService.log(testLogs[2]);
    });

    test('should retrieve user audit logs', async () => {
      const logs = await auditLogService.getUserAuditLogs(testUserId);
      
      expect(logs).toHaveLength(3);
      expect(logs[0].action).toBe('data_export_requested'); // Most recent first
      expect(logs[1].action).toBe('login_success');
      expect(logs[2].action).toBe('auto_application_enabled');
    });

    test('should filter logs by date range', async () => {
      const logs = await auditLogService.getUserAuditLogs(testUserId, {
        startDate: '2024-01-01T10:30:00Z',
        endDate: '2024-01-01T11:30:00Z'
      });
      
      expect(logs).toHaveLength(1);
      expect(logs[0].action).toBe('login_success');
    });

    test('should filter logs by actions', async () => {
      const logs = await auditLogService.getUserAuditLogs(testUserId, {
        actions: ['auto_application_enabled', 'data_export_requested']
      });
      
      expect(logs).toHaveLength(2);
      expect(logs.map(l => l.action)).toContain('auto_application_enabled');
      expect(logs.map(l => l.action)).toContain('data_export_requested');
    });

    test('should filter logs by categories', async () => {
      const logs = await auditLogService.getUserAuditLogs(testUserId, {
        categories: ['security', 'privacy']
      });
      
      expect(logs).toHaveLength(2);
      expect(logs.map(l => l.category)).toContain('security');
      expect(logs.map(l => l.category)).toContain('privacy');
    });

    test('should respect limit and offset', async () => {
      const logs = await auditLogService.getUserAuditLogs(testUserId, {
        limit: 2,
        offset: 1
      });
      
      expect(logs).toHaveLength(2);
      expect(logs[0].action).toBe('login_success'); // Skip first (most recent)
    });
  });

  describe('System-wide Audit Logs', () => {
    test('should retrieve system audit logs with pagination', async () => {
      // Create logs for multiple users
      const user2Id = new mongoose.Types.ObjectId();
      
      await auditLogService.log({
        userId: testUserId,
        action: 'test_action_1',
        category: 'user_action'
      });
      
      await auditLogService.log({
        userId: user2Id,
        action: 'test_action_2',
        category: 'user_action'
      });

      const result = await auditLogService.getSystemAuditLogs({
        limit: 10,
        offset: 0
      });
      
      expect(result.logs).toHaveLength(2);
      expect(result.pagination.total).toBe(2);
      expect(result.pagination.hasMore).toBe(false);
    });

    test('should filter system logs by multiple criteria', async () => {
      await auditLogService.log({
        userId: testUserId,
        action: 'security_event',
        category: 'security',
        severity: 'high'
      });

      const result = await auditLogService.getSystemAuditLogs({
        categories: ['security'],
        severities: ['high'],
        userIds: [testUserId.toString()]
      });
      
      expect(result.logs).toHaveLength(1);
      expect(result.logs[0].action).toBe('security_event');
    });
  });

  describe('Compliance Reporting', () => {
    beforeEach(async () => {
      // Create diverse test data for compliance reporting
      const testData = [
        { action: 'auto_application_enabled', category: 'auto_application', result: 'success' },
        { action: 'application_submitted', category: 'auto_application', result: 'success' },
        { action: 'application_failed', category: 'auto_application', result: 'failure' },
        { action: 'login_success', category: 'security', result: 'success' },
        { action: 'login_failed', category: 'security', result: 'failure' },
        { action: 'data_export_requested', category: 'privacy', result: 'success' }
      ];

      for (const data of testData) {
        await auditLogService.log({
          userId: testUserId,
          ...data,
          timestamp: new Date()
        });
      }
    });

    test('should generate compliance report', async () => {
      const report = await auditLogService.generateComplianceReport({
        userId: testUserId
      });
      
      expect(report.userId).toEqual(testUserId);
      expect(report.summary.totalEvents).toBe(6);
      expect(report.summary.securityEvents).toBe(2);
      expect(report.summary.privacyEvents).toBe(1);
      expect(report.summary.autoApplicationEvents).toBe(3);
      expect(report.summary.failedEvents).toBe(2);
      
      expect(report.categories).toHaveProperty('auto_application');
      expect(report.categories).toHaveProperty('security');
      expect(report.categories).toHaveProperty('privacy');
    });

    test('should generate system-wide compliance report', async () => {
      const report = await auditLogService.generateComplianceReport();
      
      expect(report.userId).toBeUndefined();
      expect(report.summary.totalEvents).toBeGreaterThan(0);
    });
  });

  describe('Log Cleanup', () => {
    test('should clean up expired logs', async () => {
      // Create a log with past retention date
      const AuditLog = mongoose.model('AuditLog');
      await new AuditLog({
        userId: testUserId,
        action: 'old_action',
        category: 'user_action',
        severity: 'low',
        retentionDate: new Date(Date.now() - 24 * 60 * 60 * 1000) // Yesterday
      }).save();

      // Create a log that should not be deleted
      await new AuditLog({
        userId: testUserId,
        action: 'recent_action',
        category: 'user_action',
        severity: 'low',
        retentionDate: new Date(Date.now() + 24 * 60 * 60 * 1000) // Tomorrow
      }).save();

      const deletedCount = await auditLogService.cleanupExpiredLogs();
      
      expect(deletedCount).toBe(1);
      
      const remainingLogs = await AuditLog.find({});
      expect(remainingLogs).toHaveLength(1);
      expect(remainingLogs[0].action).toBe('recent_action');
    });
  });

  describe('Retention Policies', () => {
    test('should set appropriate retention dates based on category and severity', async () => {
      const testCases = [
        { category: 'security', severity: 'critical', expectedDays: 2555 }, // 7 years
        { category: 'security', severity: 'high', expectedDays: 1095 }, // 3 years
        { category: 'privacy', severity: 'high', expectedDays: 2555 }, // 7 years
        { category: 'auto_application', severity: 'medium', expectedDays: 1095 }, // 3 years
        { category: 'system', severity: 'low', expectedDays: 365 }, // 1 year
        { category: 'user_action', severity: 'low', expectedDays: 730 } // 2 years default
      ];

      for (const testCase of testCases) {
        await auditLogService.log({
          userId: testUserId,
          action: 'test_action',
          category: testCase.category,
          severity: testCase.severity
        });
      }

      const AuditLog = mongoose.model('AuditLog');
      const logs = await AuditLog.find({ userId: testUserId }).sort({ timestamp: 1 });
      
      logs.forEach((log, index) => {
        const testCase = testCases[index];
        const expectedRetentionDate = new Date(log.timestamp.getTime() + (testCase.expectedDays * 24 * 60 * 60 * 1000));
        const actualRetentionDate = log.retentionDate;
        
        // Allow for small time differences (within 1 minute)
        const timeDiff = Math.abs(expectedRetentionDate.getTime() - actualRetentionDate.getTime());
        expect(timeDiff).toBeLessThan(60000); // Less than 1 minute difference
      });
    });
  });

  describe('Performance and Batching', () => {
    test('should handle high-volume logging without blocking', async () => {
      const startTime = Date.now();
      const promises = [];
      
      // Create 50 log entries
      for (let i = 0; i < 50; i++) {
        promises.push(auditLogService.log({
          userId: testUserId,
          action: `test_action_${i}`,
          category: 'user_action',
          severity: 'low'
        }));
      }
      
      await Promise.all(promises);
      const endTime = Date.now();
      
      // Should complete within reasonable time (less than 5 seconds)
      expect(endTime - startTime).toBeLessThan(5000);
    });
  });
});