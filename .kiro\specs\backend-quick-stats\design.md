# Design Document

## Overview

This design implements a backend API endpoint to provide quick statistics for the dashboard, including total listings count, average price, and new listings added today. The solution leverages MongoDB aggregation pipelines for efficient data processing and integrates with the existing Express.js backend architecture.

## Architecture

The quick stats feature will be implemented as a new endpoint in the existing listing routes, utilizing the current MVC architecture:

- **Route**: `/api/listings/quick-stats` (GET)
- **Controller**: New method in `listingController.js`
- **Service**: New service method for stats calculation
- **Model**: Existing `Listing` model
- **Caching**: Redis caching for performance optimization

## Components and Interfaces

### API Endpoint

**Endpoint**: `GET /api/listings/quick-stats`

**Response Format**:
```json
{
  "status": "success",
  "data": {
    "stats": {
      "totalListings": 1250,
      "averagePrice": 2850,
      "newToday": 15
    }
  },
  "cached": true,
  "timestamp": "2025-01-23T10:30:00.000Z"
}
```

### Controller Method

**Location**: `src/controllers/listingController.js`

```javascript
exports.getQuickStats = catchAsync(async (req, res, next) => {
  const stats = await searchService.getQuickStats();
  
  res.status(200).json({
    status: "success",
    data: {
      stats
    },
    cached: stats.cached || false,
    timestamp: new Date().toISOString()
  });
});
```

### Service Implementation

**Location**: `src/services/searchService.js`

The service will implement a `getQuickStats()` method that uses MongoDB aggregation pipeline for efficient calculation:

```javascript
async getQuickStats() {
  // Check cache first
  const cacheKey = 'quick-stats';
  const cached = await this.getCachedStats(cacheKey);
  if (cached) return { ...cached, cached: true };

  // Calculate stats using aggregation pipeline
  const pipeline = [
    {
      $facet: {
        totalCount: [{ $count: "count" }],
        avgPrice: [
          { $addFields: { numericPrice: /* price extraction logic */ } },
          { $match: { numericPrice: { $gt: 0 } } },
          { $group: { _id: null, avgPrice: { $avg: "$numericPrice" } } }
        ],
        newToday: [
          { $match: { dateAdded: { $gte: startOfToday } } },
          { $count: "count" }
        ]
      }
    }
  ];

  const results = await Listing.aggregate(pipeline);
  const stats = this.processStatsResults(results);
  
  // Cache results for 5 minutes
  await this.cacheStats(cacheKey, stats, 300);
  
  return { ...stats, cached: false };
}
```

### Route Registration

**Location**: `src/routes/listing.js`

```javascript
router.get(
  "/listings/quick-stats",
  cacheConfigs.short, // 5-minute cache
  listingController.getQuickStats
);
```

## Data Models

### Existing Listing Model

The implementation will use the existing `Listing` model with these relevant fields:

- `price`: String field containing price information (e.g., "€ 2.850 per maand")
- `dateAdded`: Date field for tracking when listing was added
- `timestamp`: Date field for last update

### Price Extraction Logic

Since prices are stored as strings in various formats, the service will implement price normalization:

```javascript
extractNumericPrice(priceString) {
  if (!priceString) return 0;
  
  // Match patterns like "€ 2.850", "€2,950", "€ 3.500 per maand"
  const match = priceString.match(/€\s*([\d.,]+)/);
  if (!match) return 0;
  
  // Handle European number format (dots as thousands separator, comma as decimal)
  let numericValue = match[1];
  
  // If contains both dot and comma, treat dot as thousands separator
  if (numericValue.includes('.') && numericValue.includes(',')) {
    numericValue = numericValue.replace(/\./g, '').replace(',', '.');
  }
  // If only dots and multiple of them, treat as thousands separator
  else if ((numericValue.match(/\./g) || []).length > 1) {
    numericValue = numericValue.replace(/\./g, '');
  }
  // If single dot with 3+ digits after, treat as thousands separator
  else if (numericValue.match(/\.\d{3,}/)) {
    numericValue = numericValue.replace('.', '');
  }
  
  return parseFloat(numericValue) || 0;
}
```

## Error Handling

### Error Scenarios

1. **Database Connection Issues**: Return cached data if available, otherwise return default values
2. **Invalid Price Data**: Skip invalid entries in average calculation
3. **Cache Failures**: Continue with database calculation, log warning
4. **Aggregation Errors**: Return partial stats with error indicators

### Error Response Format

```json
{
  "status": "error",
  "message": "Failed to calculate quick stats",
  "data": {
    "stats": {
      "totalListings": 0,
      "averagePrice": 0,
      "newToday": 0
    }
  },
  "error": "Database aggregation failed"
}
```

## Testing Strategy

### Unit Tests

**Location**: `src/tests/services/searchService.test.js`

1. **Price Extraction Tests**:
   - Test various European price formats
   - Test invalid price strings
   - Test edge cases (null, empty, malformed)

2. **Stats Calculation Tests**:
   - Test with sample data sets
   - Test empty database scenarios
   - Test date boundary conditions for "newToday"

3. **Caching Tests**:
   - Test cache hit/miss scenarios
   - Test cache expiration
   - Test cache failure fallback

### Integration Tests

**Location**: `src/tests/controllers/listingController.test.js`

1. **API Endpoint Tests**:
   - Test successful response format
   - Test error handling
   - Test caching headers
   - Test performance under load

### Performance Tests

1. **Database Performance**:
   - Test aggregation pipeline performance with large datasets
   - Verify query execution time stays under 500ms
   - Test concurrent request handling

2. **Cache Performance**:
   - Verify cache reduces database load
   - Test cache invalidation strategies

## Performance Optimizations

### Database Optimizations

1. **Indexes**: Ensure proper indexes exist on:
   - `dateAdded` field for "newToday" calculation
   - Compound index on frequently queried fields

2. **Aggregation Pipeline**: Use `$facet` to calculate all stats in single database round-trip

### Caching Strategy

1. **Cache Duration**: 5 minutes for quick stats (balances freshness with performance)
2. **Cache Key**: Simple key `quick-stats` since data is global
3. **Cache Invalidation**: Time-based expiration (no manual invalidation needed)

### Response Optimization

1. **Gzip Compression**: Enable for API responses
2. **HTTP Caching**: Set appropriate cache headers
3. **Connection Pooling**: Leverage existing MongoDB connection pool

## Security Considerations

1. **Rate Limiting**: Apply existing rate limiting middleware
2. **Input Validation**: No user input required, but validate any future query parameters
3. **Error Information**: Don't expose internal database errors to clients
4. **Authentication**: Consider if endpoint should require authentication (currently public)

## Monitoring and Logging

1. **Performance Metrics**: Log execution time for stats calculation
2. **Error Tracking**: Log aggregation failures and cache misses
3. **Usage Analytics**: Track endpoint usage patterns
4. **Health Checks**: Include stats endpoint in health monitoring

## Future Enhancements

1. **Real-time Updates**: WebSocket notifications for stats changes
2. **Historical Data**: Track stats over time for trending
3. **Filtering**: Allow stats filtering by location, property type, etc.
4. **Advanced Metrics**: Add median price, price distribution, etc.