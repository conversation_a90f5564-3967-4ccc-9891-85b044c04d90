const ErrorHandlingService = require('../../services/errorHandlingService');
const AutoApplicationService = require('../../services/autoApplicationService');
const ApplicationQueueManager = require('../../services/applicationQueueManager');
const FormAutomationEngine = require('../../services/formAutomationEngine');
const AutoApplicationSettings = require('../../models/AutoApplicationSettings');
const ApplicationQueue = require('../../models/ApplicationQueue');
const ApplicationResult = require('../../models/ApplicationResult');
const User = require('../../models/User');
const mongoose = require('mongoose');

// Mock external dependencies
jest.mock('../../services/websocketService');
jest.mock('../../services/logger', () => ({
  loggers: {
    app: {
      info: jest.fn(),
      warn: jest.fn(),
      error: jest.fn()
    },
    errorHandling: {
      info: jest.fn(),
      warn: jest.fn(),
      error: jest.fn()
    }
  }
}));

describe('Error Handling Integration Tests', () => {
  let errorHandlingService;
  let autoApplicationService;
  let queueManager;
  let formEngine;
  let testUser;
  let testSettings;

  beforeAll(async () => {
    // Connect to test database
    if (mongoose.connection.readyState === 0) {
      await mongoose.connect(process.env.MONGODB_TEST_URI || 'mongodb://localhost:27017/zakmakelaar_test');
    }
  });

  afterAll(async () => {
    await mongoose.connection.close();
  });

  beforeEach(async () => {
    // Clear test data
    await User.deleteMany({});
    await AutoApplicationSettings.deleteMany({});
    await ApplicationQueue.deleteMany({});
    await ApplicationResult.deleteMany({});

    // Create test user
    testUser = new User({
      email: '<EMAIL>',
      profile: {
        fullName: 'Test User',
        employment: {
          monthlyIncome: 3000,
          occupation: 'Software Developer'
        }
      }
    });
    await testUser.save();

    // Create test settings
    testSettings = new AutoApplicationSettings({
      userId: testUser._id,
      enabled: true,
      settings: {
        maxApplicationsPerDay: 5,
        applicationTemplate: 'professional'
      },
      criteria: {
        maxPrice: 2000,
        minRooms: 2
      },
      personalInfo: {
        fullName: 'Test User',
        monthlyIncome: 3000
      },
      status: {
        isActive: true
      }
    });
    await testSettings.save();

    // Initialize services
    errorHandlingService = new ErrorHandlingService();
    autoApplicationService = new AutoApplicationService();
    queueManager = new ApplicationQueueManager();
    formEngine = new FormAutomationEngine();
  });

  afterEach(async () => {
    // Clean up
    if (queueManager) {
      await queueManager.shutdown();
    }
    if (autoApplicationService) {
      autoApplicationService.shutdown();
    }
  });

  describe('Network Error Recovery', () => {
    test('should handle network errors with automatic retry', async () => {
      const networkError = new Error('ECONNRESET: Connection reset by peer');
      const context = {
        service: 'FormAutomationEngine',
        queueItemId: null,
        userId: testUser._id.toString(),
        attemptNumber: 1
      };

      const result = await errorHandlingService.handleError(networkError, context);

      expect(result.success).toBe(true);
      expect(result.action).toBe('RETRY_SCHEDULED');
      expect(result.retryScheduled).toBe(true);
      expect(result.retryAttempt).toBe(2);
      expect(result.retryDelay).toBeGreaterThan(0);
    });

    test('should stop retrying after max attempts for network errors', async () => {
      const networkError = new Error('ETIMEDOUT: Connection timed out');
      const context = {
        service: 'FormAutomationEngine',
        queueItemId: null,
        userId: testUser._id.toString(),
        attemptNumber: 4 // Exceeds max retries
      };

      const result = await errorHandlingService.handleError(networkError, context);

      expect(result.success).toBe(false);
      expect(result.action).toBe('MAX_RETRIES_EXCEEDED');
      expect(result.retryScheduled).toBe(false);
    });
  });

  describe('Form Error Recovery', () => {
    test('should handle form errors with adaptation strategy', async () => {
      const formError = new Error('selector not found: input[name="email"]');
      const context = {
        service: 'FormAutomationEngine',
        formType: 'funda_native',
        userId: testUser._id.toString(),
        attemptNumber: 1
      };

      const result = await errorHandlingService.handleError(formError, context);

      expect(result.success).toBe(true);
      expect(result.action).toBe('RETRY_WITH_ADAPTATION');
      expect(result.adaptation).toBeDefined();
      expect(result.retryScheduled).toBe(true);
    });
  });

  describe('Detection Error Recovery', () => {
    test('should pause user when bot detection occurs', async () => {
      const detectionError = new Error('Bot activity detected');
      const context = {
        service: 'FormAutomationEngine',
        userId: testUser._id.toString()
      };

      const result = await errorHandlingService.handleError(detectionError, context);

      expect(result.success).toBe(true);
      expect(result.action).toBe('USER_PAUSED_MANUAL_INTERVENTION');
      expect(result.manualInterventionRequired).toBe(true);

      // Verify user was actually paused
      const updatedSettings = await AutoApplicationSettings.findByUserId(testUser._id);
      expect(updatedSettings.status.isActive).toBe(false);
      expect(updatedSettings.status.pausedReason).toContain('Detection system triggered');
    });

    test('should handle CAPTCHA errors by requiring manual intervention', async () => {
      const captchaError = new Error('CAPTCHA verification required');
      const context = {
        service: 'FormAutomationEngine',
        userId: testUser._id.toString(),
        url: 'https://funda.nl/property/123'
      };

      const result = await errorHandlingService.handleError(captchaError, context);

      expect(result.success).toBe(true);
      expect(result.action).toBe('CAPTCHA_MANUAL_INTERVENTION');
      expect(result.manualInterventionRequired).toBe(true);
    });
  });

  describe('Data Error Recovery', () => {
    test('should handle missing data errors by notifying user', async () => {
      const dataError = new Error('Missing required field: monthlyIncome');
      const context = {
        service: 'AutoApplicationService',
        userId: testUser._id.toString(),
        missingData: ['monthlyIncome']
      };

      const result = await errorHandlingService.handleError(dataError, context);

      expect(result.success).toBe(true);
      expect(result.action).toBe('USER_NOTIFIED_PAUSED');
      expect(result.manualInterventionRequired).toBe(true);

      // Verify user was paused
      const updatedSettings = await AutoApplicationSettings.findByUserId(testUser._id);
      expect(updatedSettings.status.isActive).toBe(false);
      expect(updatedSettings.status.pausedReason).toContain('Missing or invalid data');
    });
  });

  describe('Rate Limit Error Recovery', () => {
    test('should handle rate limit errors with intelligent backoff', async () => {
      const rateLimitError = new Error('Too many requests - rate limit exceeded');
      const context = {
        service: 'FormAutomationEngine',
        userId: testUser._id.toString(),
        rateLimitInfo: { retryAfter: 300000 }, // 5 minutes
        attemptNumber: 1
      };

      const result = await errorHandlingService.handleError(rateLimitError, context);

      expect(result.success).toBe(true);
      expect(result.action).toBe('RATE_LIMIT_BACKOFF');
      expect(result.retryDelay).toBe(300000);
      expect(result.retryScheduled).toBe(true);

      // Verify user was temporarily paused
      const updatedSettings = await AutoApplicationSettings.findByUserId(testUser._id);
      expect(updatedSettings.status.isActive).toBe(false);
      expect(updatedSettings.status.pausedReason).toContain('Rate limit exceeded');
      expect(updatedSettings.status.pausedUntil).toBeInstanceOf(Date);
    });
  });

  describe('System Error Recovery', () => {
    test('should handle isolated system errors with retry', async () => {
      const systemError = new Error('Database connection failed');
      const context = {
        service: 'AutoApplicationService',
        attemptNumber: 1
      };

      const result = await errorHandlingService.handleError(systemError, context);

      expect(result.success).toBe(true);
      expect(result.action).toBe('SYSTEM_RETRY_SCHEDULED');
      expect(result.retryScheduled).toBe(true);
      expect(result.retryDelay).toBeGreaterThan(0);
    });

    test('should implement graceful degradation for system-wide failures', async () => {
      // Simulate multiple system failures
      errorHandlingService.healthMetrics.systemFailures = 3;
      errorHandlingService.healthMetrics.lastSystemFailure = new Date();

      const systemError = new Error('Critical system failure');
      const context = {
        service: 'core',
        attemptNumber: 1
      };

      const result = await errorHandlingService.handleError(systemError, context);

      expect(result.success).toBe(true);
      expect(result.action).toBe('GRACEFUL_DEGRADATION');
      expect(result.degradationMode).toBe(true);
      expect(result.manualInterventionRequired).toBe(true);
    });
  });

  describe('Queue Item Error Recovery', () => {
    test('should handle queue item errors with proper retry scheduling', async () => {
      // Create a test queue item
      const queueItem = new ApplicationQueue({
        userId: testUser._id,
        listingId: new mongoose.Types.ObjectId(),
        listingUrl: 'https://funda.nl/test-property',
        priority: 50,
        status: 'processing',
        attempts: 1,
        maxAttempts: 3,
        applicationData: {
          personalInfo: testSettings.personalInfo
        }
      });
      await queueItem.save();

      const processingError = new Error('Form submission failed');
      const context = {
        service: 'ApplicationQueueManager',
        queueItemId: queueItem._id.toString(),
        userId: testUser._id.toString(),
        attemptNumber: 1
      };

      const result = await errorHandlingService.handleError(processingError, context);

      expect(result.success).toBe(true);
      expect(result.retryScheduled).toBe(true);

      // Verify queue item was updated for retry
      const updatedQueueItem = await ApplicationQueue.findById(queueItem._id);
      expect(updatedQueueItem.status).toBe('retrying');
      expect(updatedQueueItem.attempts).toBe(2);
      expect(updatedQueueItem.scheduledAt).toBeInstanceOf(Date);
      expect(updatedQueueItem.delayUntil).toBeInstanceOf(Date);
    });

    test('should mark queue item for manual intervention when appropriate', async () => {
      const queueItem = new ApplicationQueue({
        userId: testUser._id,
        listingId: new mongoose.Types.ObjectId(),
        listingUrl: 'https://funda.nl/test-property',
        priority: 50,
        status: 'processing',
        attempts: 1,
        maxAttempts: 3,
        applicationData: {
          personalInfo: testSettings.personalInfo
        }
      });
      await queueItem.save();

      const captchaError = new Error('CAPTCHA verification required');
      const context = {
        service: 'FormAutomationEngine',
        queueItemId: queueItem._id.toString(),
        userId: testUser._id.toString()
      };

      const result = await errorHandlingService.handleError(captchaError, context);

      expect(result.manualInterventionRequired).toBe(true);

      // Verify queue item was marked for manual intervention
      const updatedQueueItem = await ApplicationQueue.findById(queueItem._id);
      expect(updatedQueueItem.status).toBe('manual_intervention_required');
      expect(updatedQueueItem.metadata.manualInterventionReason).toBeDefined();
    });
  });

  describe('Error Metrics and Health Monitoring', () => {
    test('should track error metrics correctly', async () => {
      const initialStats = errorHandlingService.getErrorStatistics();
      const initialTotal = initialStats.totalErrors;

      // Generate some errors
      await errorHandlingService.handleError(new Error('ECONNRESET'), {
        service: 'FormAutomationEngine',
        userId: testUser._id.toString()
      });

      await errorHandlingService.handleError(new Error('Form validation failed'), {
        service: 'FormAutomationEngine',
        userId: testUser._id.toString()
      });

      await errorHandlingService.handleError(new Error('Database error'), {
        service: 'AutoApplicationService'
      });

      const updatedStats = errorHandlingService.getErrorStatistics();

      expect(updatedStats.totalErrors).toBe(initialTotal + 3);
      expect(updatedStats.errorsByCategory.NETWORK).toBeGreaterThan(0);
      expect(updatedStats.errorsByCategory.FORM).toBeGreaterThan(0);
      expect(updatedStats.errorsByCategory.SYSTEM).toBeGreaterThan(0);
      expect(updatedStats.errorsByUser[testUser._id.toString()]).toBe(2);
    });

    test('should perform health checks and detect issues', async () => {
      // Create some stuck queue items
      const stuckItem = new ApplicationQueue({
        userId: testUser._id,
        listingId: new mongoose.Types.ObjectId(),
        listingUrl: 'https://funda.nl/stuck-item',
        status: 'processing',
        updatedAt: new Date(Date.now() - 2000000) // 33+ minutes ago
      });
      await stuckItem.save();

      await errorHandlingService.performHealthCheck();

      // Health check should have logged the stuck item
      // (We can't easily test the logging, but we can verify the method runs without error)
      expect(true).toBe(true); // Placeholder assertion
    });

    test('should reset error statistics', () => {
      // Add some errors first
      errorHandlingService.healthMetrics.totalErrors = 10;
      errorHandlingService.healthMetrics.systemFailures = 2;

      errorHandlingService.resetErrorStatistics();

      const stats = errorHandlingService.getErrorStatistics();
      expect(stats.totalErrors).toBe(0);
      expect(stats.systemFailures).toBe(0);
      expect(stats.lastSystemFailure).toBeNull();
    });
  });

  describe('Service Integration', () => {
    test('should integrate with AutoApplicationService error handling', async () => {
      // Test that AutoApplicationService uses error handling service
      const service = new AutoApplicationService();
      expect(service.errorHandlingService).toBeInstanceOf(ErrorHandlingService);
    });

    test('should integrate with ApplicationQueueManager error handling', async () => {
      // Test that ApplicationQueueManager uses error handling service
      const manager = new ApplicationQueueManager();
      expect(manager.errorHandlingService).toBeInstanceOf(ErrorHandlingService);
    });

    test('should integrate with FormAutomationEngine error handling', async () => {
      // Test that FormAutomationEngine uses error handling service
      const engine = new FormAutomationEngine();
      expect(engine.errorHandlingService).toBeInstanceOf(ErrorHandlingService);
    });
  });

  describe('Backoff Algorithm Validation', () => {
    test('should calculate exponential backoff with jitter correctly', () => {
      const categoryConfig = errorHandlingService.errorCategories.NETWORK;
      
      // Test multiple attempts to verify exponential growth
      const delay1 = errorHandlingService.calculateBackoffDelay('NETWORK', 1, categoryConfig);
      const delay2 = errorHandlingService.calculateBackoffDelay('NETWORK', 2, categoryConfig);
      const delay3 = errorHandlingService.calculateBackoffDelay('NETWORK', 3, categoryConfig);

      // Delays should generally increase (allowing for jitter)
      expect(delay2).toBeGreaterThan(delay1 * 0.8); // Allow for jitter
      expect(delay3).toBeGreaterThan(delay2 * 0.8);

      // Should not exceed maximum delay
      expect(delay1).toBeLessThanOrEqual(categoryConfig.maxDelay);
      expect(delay2).toBeLessThanOrEqual(categoryConfig.maxDelay);
      expect(delay3).toBeLessThanOrEqual(categoryConfig.maxDelay);
    });

    test('should respect maximum delay limits for high attempt numbers', () => {
      const categoryConfig = errorHandlingService.errorCategories.NETWORK;
      
      const delay = errorHandlingService.calculateBackoffDelay('NETWORK', 10, categoryConfig);
      
      expect(delay).toBeLessThanOrEqual(categoryConfig.maxDelay);
      expect(delay).toBeGreaterThan(0);
    });
  });

  describe('Error Categorization Accuracy', () => {
    test('should correctly categorize various error types', () => {
      const testCases = [
        { error: new Error('ECONNRESET'), expectedCategory: 'NETWORK' },
        { error: new Error('selector not found'), expectedCategory: 'FORM' },
        { error: new Error('CAPTCHA required'), expectedCategory: 'DETECTION' },
        { error: new Error('User not found'), expectedCategory: 'DATA' },
        { error: new Error('Database connection failed'), expectedCategory: 'SYSTEM' },
        { error: new Error('Rate limit exceeded'), expectedCategory: 'RATE_LIMIT' },
        { error: new Error('Authentication failed'), expectedCategory: 'AUTHENTICATION' }
      ];

      testCases.forEach(({ error, expectedCategory }) => {
        const result = errorHandlingService.categorizeError(error, {});
        expect(result.category).toBe(expectedCategory);
      });
    });

    test('should use context information for better categorization', () => {
      const error = new Error('timeout');
      
      // Without context
      const result1 = errorHandlingService.categorizeError(error, {});
      
      // With context indicating network issue
      const result2 = errorHandlingService.categorizeError(error, {
        service: 'BrowserAutomation',
        statusCode: 408
      });

      // Context should improve categorization confidence
      expect(result2.confidence).toBeGreaterThanOrEqual(result1.confidence);
    });
  });
});