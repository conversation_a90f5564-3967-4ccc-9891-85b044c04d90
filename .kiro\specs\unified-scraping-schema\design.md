# Design Document

## Overview

The unified scraping schema system will standardize property data extraction across all scrapers (Funda, Huurwoningen, Pararius) by implementing a centralized data transformation layer. This design introduces a comprehensive schema definition, field mapping system, and validation framework that ensures consistent data structure regardless of the source website.

The solution maintains backward compatibility with the existing Listing model while providing a migration path to a more robust Property model. It implements a plugin-based architecture where each scraper can define its own field mappings while adhering to a common output schema.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    A[Raw Scraper Data] --> B[Schema Transformer]
    B --> C[Validation Engine]
    C --> D[Normalized Property Data]
    D --> E[Database Storage]
    
    F[Field Mapping Registry] --> B
    G[Schema Definition] --> C
    H[Migration Service] --> E
    
    subgraph "Scrapers"
        I[Funda Scraper]
        J[Huurwoningen Scraper]
        K[Pararius Scraper]
    end
    
    I --> A
    J --> A
    K --> A
```

### Component Architecture

```mermaid
graph LR
    A[Scraper] --> B[Raw Data Extractor]
    B --> C[Field Mapper]
    C --> D[Schema Transformer]
    D --> E[Validator]
    E --> F[Property Model]
    
    G[Mapping Registry] --> C
    H[Schema Registry] --> D
    I[Validation Rules] --> E
```

## Components and Interfaces

### 1. Unified Property Schema

**Core Schema Structure (Frontend-Compatible):**
```javascript
const UnifiedPropertySchema = {
  // Basic Information (Frontend Required)
  _id: String, // MongoDB ObjectId as string
  id: String,  // Alias for _id for compatibility
  title: String,
  description: String,
  
  // Source Information
  source: String, // 'funda.nl', 'huurwoningen.nl', 'pararius.nl' (simplified for frontend)
  url: String,
  dateAdded: String, // ISO date string (frontend expects string)
  
  // Location Data (Frontend Compatible)
  location: {
    // Support both string and object formats for backward compatibility
    _unified: {
      address: {
        street: String,
        houseNumber: String,
        postalCode: String,
        city: String,
        province: String,
        country: String
      },
      coordinates: {
        lat: Number,
        lng: Number
      }
    },
    // Legacy format for backward compatibility
    _legacy: String, // Simple location string
    // Dynamic getter that returns appropriate format based on frontend needs
    toString: Function // Returns formatted address string
  },
  
  // Property Classification (Frontend Compatible)
  propertyType: String, // 'apartment', 'house', 'studio', 'room' (direct frontend field)
  
  // Physical Attributes (Frontend Compatible Format)
  // Size information
  size: String,        // Formatted size string (e.g., "85 m²") - frontend expects string
  area: Number,        // Numeric area in square meters - frontend fallback
  
  // Room information (Frontend expects string or number)
  rooms: String | Number,     // Total rooms - frontend uses both formats
  bedrooms: String | Number,  // Bedrooms - frontend uses both formats  
  bathrooms: String | Number, // Bathrooms - frontend uses both formats
  
  // Build year
  year: String,        // Build year as string - frontend expects string
  
  // Financial Information (Frontend Compatible)
  price: String | Number, // Frontend expects both formats, has formatPrice utility
  
  // Features and Amenities (Frontend Compatible)
  interior: String,    // 'Kaal', 'Gestoffeerd', 'Gemeubileerd' - Dutch terms
  furnished: Boolean,  // Frontend fallback boolean
  pets: Boolean,       // Frontend filter field
  smoking: Boolean,    // Frontend filter field
  garden: Boolean,     // Frontend filter field
  balcony: Boolean,    // Frontend filter field
  parking: Boolean,    // Frontend filter field
  energyLabel: String, // Frontend expects string
  
  // Media (Frontend Compatible)
  images: [String],    // Array of image URLs - frontend expects simple string array
  
  // Additional Frontend Fields
  isActive: Boolean,   // Frontend uses this field
  features: [String],  // Frontend expects array of feature strings
  deposit: Number,     // Frontend expects numeric deposit
  utilities: Number,   // Frontend expects numeric utilities cost
  dateAvailable: String, // Frontend expects date string
  
  // Contact Information (Frontend Compatible)
  contactInfo: {
    name: String,
    phone: String,
    email: String
  },
  
  // Internal Processing Data (Hidden from Frontend)
  _internal: {
    sourceMetadata: {
      website: String,
      externalId: String,
      scrapedAt: Date,
      lastUpdated: Date,
      version: Number
    },
    rawData: {
      original: Object,
      processed: Object,
      metadata: Object
    },
    dataQuality: {
      completeness: Number,
      accuracy: Number,
      lastValidated: Date,
      validationErrors: [String]
    }
  }
}
```

### 2. Field Mapping Registry

**Interface:**
```javascript
class FieldMappingRegistry {
  constructor() {
    this.mappings = new Map();
  }
  
  registerMapping(source, fieldMappings) {
    // Register field mappings for a specific source
  }
  
  getMapping(source, field) {
    // Get mapping for a specific field from a source
  }
  
  getAllMappings(source) {
    // Get all mappings for a source
  }
  
  validateMapping(mapping) {
    // Validate mapping configuration
  }
}
```

**Mapping Configuration Example (Frontend-Compatible):**
```javascript
const fundaMappings = {
  // Direct field mappings
  'title': 'title',
  'description': 'description',
  'url': 'url',
  'source': { value: 'funda.nl' },
  'propertyType': {
    path: 'propertyType',
    transform: 'normalizePropertyType'
  },
  
  // Price handling (frontend expects string or number)
  'price': {
    path: 'price',
    transform: 'normalizePrice'
  },
  
  // Location (support both formats)
  'location': {
    path: 'location',
    transform: 'normalizeLocation'
  },
  
  // Size information (frontend expects both string and number)
  'size': {
    path: 'size',
    transform: 'formatSizeString' // "85 m²"
  },
  'area': {
    path: 'size', 
    transform: 'extractNumericSize' // 85
  },
  
  // Room information (frontend expects string or number)
  'rooms': {
    path: 'rooms',
    transform: 'normalizeRooms'
  },
  'bedrooms': {
    path: 'bedrooms',
    transform: 'normalizeRooms'
  },
  'bathrooms': {
    path: 'bathrooms',
    transform: 'normalizeRooms',
    default: "1"
  },
  
  // Build year (frontend expects string)
  'year': {
    path: 'year',
    transform: 'normalizeYear'
  },
  
  // Interior (Dutch terms for frontend)
  'interior': {
    path: 'interior',
    transform: 'normalizeInterior'
  },
  
  // Boolean features
  'furnished': {
    path: 'interior',
    transform: 'inferFurnishedStatus'
  },
  'garden': {
    path: 'garden',
    transform: 'normalizeBoolean'
  },
  'balcony': {
    path: 'balcony', 
    transform: 'normalizeBoolean'
  },
  'parking': {
    path: 'parking',
    transform: 'normalizeBoolean'
  },
  
  // Media
  'images': {
    path: 'images',
    transform: 'normalizeImageArray'
  },
  
  // Dates (frontend expects ISO strings)
  'dateAdded': {
    transform: 'getCurrentISOString'
  },
  
  // Additional frontend fields with defaults
  'isActive': { value: true },
  'pets': { value: false },
  'smoking': { value: false },
  'features': { value: [] }
};
```

### 3. Schema Transformer

**Interface:**
```javascript
class SchemaTransformer {
  constructor(mappingRegistry, transformationRules) {
    this.mappingRegistry = mappingRegistry;
    this.transformationRules = transformationRules;
  }
  
  async transform(rawData, source) {
    // Transform raw scraper data to unified schema
  }
  
  applyFieldMapping(data, mappings) {
    // Apply field mappings to raw data
  }
  
  executeTransformations(data, transformations) {
    // Execute transformation functions
  }
  
  preserveRawData(originalData, processedData) {
    // Preserve original data for debugging
  }
}
```

### 4. Validation Engine

**Interface:**
```javascript
class ValidationEngine {
  constructor(schemaDefinition) {
    this.schema = schemaDefinition;
    this.validators = new Map();
  }
  
  validate(data) {
    // Validate data against schema
  }
  
  validateField(fieldName, value) {
    // Validate individual field
  }
  
  getValidationErrors(data) {
    // Get detailed validation errors
  }
  
  applyDefaults(data) {
    // Apply default values for missing fields
  }
}
```

### 5. Migration Service

**Interface:**
```javascript
class MigrationService {
  constructor(sourceModel, targetModel) {
    this.sourceModel = sourceModel;
    this.targetModel = targetModel;
  }
  
  async migrateExistingData() {
    // Migrate existing Listing records to unified schema
  }
  
  async migrateBatch(records) {
    // Migrate a batch of records
  }
  
  createMigrationMapping() {
    // Create mapping from old to new schema
  }
  
  validateMigration(originalRecord, migratedRecord) {
    // Validate migration accuracy
  }
}
```

## Data Models

### Enhanced Property Model

The existing Property model will be enhanced to support the unified schema:

```javascript
const enhancedPropertySchema = {
  // Extend existing Property model with unified schema fields
  unifiedData: {
    type: Object,
    default: {}
  },
  
  // Source tracking
  sourceMetadata: {
    website: String,
    externalId: String,
    scrapedAt: Date,
    lastUpdated: Date,
    version: Number
  },
  
  // Data quality indicators
  dataQuality: {
    completeness: Number, // 0-100%
    accuracy: Number,     // 0-100%
    lastValidated: Date,
    validationErrors: [String]
  },
  
  // Processing metadata
  processingMetadata: {
    transformationVersion: String,
    processingTime: Number,
    errors: [Object],
    warnings: [Object]
  }
};
```

### Frontend Compatibility Layer

```javascript
class FrontendCompatibilityLayer {
  static convertToFrontendFormat(unifiedProperty) {
    // Convert unified property to frontend-expected format
    return {
      _id: unifiedProperty._id,
      id: unifiedProperty._id, // Alias for compatibility
      title: unifiedProperty.title,
      description: unifiedProperty.description,
      
      // Price handling - frontend formatPrice utility handles both string and number
      price: unifiedProperty.price,
      
      // Location - support both string and object formats
      location: typeof unifiedProperty.location === 'string' 
        ? unifiedProperty.location 
        : unifiedProperty.location._unified?.address?.city || unifiedProperty.location._legacy,
      
      // Property details in frontend-expected formats
      propertyType: unifiedProperty.propertyType,
      rooms: unifiedProperty.rooms,
      bedrooms: unifiedProperty.bedrooms,
      bathrooms: unifiedProperty.bathrooms,
      area: unifiedProperty.area,
      size: unifiedProperty.size,
      year: unifiedProperty.year,
      
      // Features
      interior: unifiedProperty.interior,
      furnished: unifiedProperty.furnished,
      pets: unifiedProperty.pets,
      smoking: unifiedProperty.smoking,
      garden: unifiedProperty.garden,
      balcony: unifiedProperty.balcony,
      parking: unifiedProperty.parking,
      energyLabel: unifiedProperty.energyLabel,
      
      // Media
      images: unifiedProperty.images || [],
      
      // Source and dates
      source: unifiedProperty.source,
      url: unifiedProperty.url,
      dateAdded: unifiedProperty.dateAdded,
      dateAvailable: unifiedProperty.dateAvailable,
      
      // Additional frontend fields
      isActive: unifiedProperty.isActive,
      features: unifiedProperty.features || [],
      deposit: unifiedProperty.deposit,
      utilities: unifiedProperty.utilities,
      contactInfo: unifiedProperty.contactInfo
    };
  }
  
  static convertFromLegacyListing(legacyListing) {
    // Convert legacy Listing model to unified format
    return {
      _id: legacyListing._id,
      title: legacyListing.title,
      description: legacyListing.description,
      price: legacyListing.price,
      location: legacyListing.location,
      propertyType: legacyListing.propertyType,
      rooms: legacyListing.rooms,
      bedrooms: legacyListing.bedrooms,
      size: legacyListing.size,
      year: legacyListing.year,
      interior: legacyListing.interior,
      source: legacyListing.source,
      url: legacyListing.url,
      images: legacyListing.images || [],
      dateAdded: legacyListing.dateAdded || legacyListing.timestamp,
      
      // Set reasonable defaults for new fields
      bathrooms: "1",
      area: this.extractNumericSize(legacyListing.size),
      furnished: this.inferFurnishedStatus(legacyListing.interior),
      pets: false,
      smoking: false,
      garden: false,
      balcony: false,
      parking: false,
      isActive: true,
      features: [],
      
      _internal: {
        sourceMetadata: {
          website: legacyListing.source,
          scrapedAt: legacyListing.dateAdded,
          lastUpdated: new Date()
        },
        rawData: {
          original: legacyListing,
          processed: {},
          metadata: { migrated: true }
        }
      }
    };
  }
  
  static extractNumericSize(sizeString) {
    if (!sizeString) return null;
    const match = sizeString.match(/(\d+)/);
    return match ? parseInt(match[1]) : null;
  }
  
  static inferFurnishedStatus(interior) {
    if (!interior) return false;
    return interior.toLowerCase().includes('gemeubileerd') || 
           interior.toLowerCase().includes('furnished');
  }
}
```

## Error Handling

### Error Classification System

```javascript
const ErrorTypes = {
  TRANSFORMATION_ERROR: 'TRANSFORMATION_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  MAPPING_ERROR: 'MAPPING_ERROR',
  DATA_QUALITY_ERROR: 'DATA_QUALITY_ERROR'
};

class SchemaError extends Error {
  constructor(type, message, context = {}) {
    super(message);
    this.type = type;
    this.context = context;
    this.timestamp = new Date();
  }
}
```

### Error Recovery Strategies

1. **Graceful Degradation**: Continue processing with partial data
2. **Default Value Application**: Use sensible defaults for missing fields
3. **Raw Data Preservation**: Always preserve original data for reprocessing
4. **Retry Mechanisms**: Retry failed transformations with different strategies

## Testing Strategy

### Unit Testing

1. **Field Mapping Tests**: Verify correct field mappings for each source
2. **Transformation Tests**: Test individual transformation functions
3. **Validation Tests**: Verify schema validation rules
4. **Migration Tests**: Test data migration accuracy

### Integration Testing

1. **End-to-End Pipeline Tests**: Test complete data flow from scraping to storage
2. **Cross-Source Consistency Tests**: Verify consistent output across different sources
3. **Performance Tests**: Ensure transformation performance meets requirements
4. **Backward Compatibility Tests**: Verify existing functionality continues to work

### Test Data Strategy

```javascript
const testDataSets = {
  funda: {
    minimal: { /* minimal valid data */ },
    complete: { /* complete data with all fields */ },
    edge_cases: { /* edge cases and unusual formats */ },
    invalid: { /* invalid data for error testing */ }
  },
  huurwoningen: { /* similar structure */ },
  pararius: { /* similar structure */ }
};
```

### Performance Testing

- **Transformation Speed**: < 100ms per property
- **Memory Usage**: < 500MB for batch processing
- **Concurrent Processing**: Support for multiple scrapers
- **Database Performance**: Efficient storage and retrieval

## Implementation Phases

### Phase 1: Core Infrastructure
- Implement unified schema definition
- Create field mapping registry
- Build basic transformation engine
- Set up validation framework

### Phase 2: Scraper Integration
- Integrate with existing scrapers
- Implement source-specific mappings
- Add error handling and logging
- Create backward compatibility layer

### Phase 3: Migration and Optimization
- Implement data migration service
- Optimize performance
- Add comprehensive monitoring
- Complete testing suite

### Phase 4: Advanced Features
- Add data quality scoring
- Implement advanced validation rules
- Create schema evolution support
- Add analytics and reporting