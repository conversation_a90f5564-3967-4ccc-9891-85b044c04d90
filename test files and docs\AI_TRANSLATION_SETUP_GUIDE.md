# AI Translation Setup Guide

## 🤖 Overview
The ZakMakelaar app now uses **AI-powered translation** instead of free translation APIs. This provides:
- **Higher Quality**: Professional-grade translations using advanced AI models
- **Better Context**: Specialized real estate terminology understanding
- **Reliability**: Consistent performance without rate limits
- **Flexibility**: Handles any text length without chunking

## 🔧 Setup Instructions

### Step 1: Get an API Key

#### Option A: OpenRouter (Recommended)
1. Visit [OpenRouter.ai](https://openrouter.ai)
2. Sign up for a free account
3. Get your API key from the dashboard
4. **Benefit**: Access to multiple AI models including free options

#### Option B: OpenAI
1. Visit [OpenAI Platform](https://platform.openai.com)
2. Sign up and add billing information
3. Get your API key from the API section
4. **Benefit**: Direct access to GPT models

### Step 2: Configure Environment Variables

#### For Development
Add to your `.env` file in the `zakmakelaar-backend` directory:

```bash
# Option A: OpenRouter (Recommended)
OPENROUTER_API_KEY=your_openrouter_key_here

# Option B: OpenAI
OPENAI_API_KEY=your_openai_key_here

# Optional: Specify models
OPENROUTER_TRANSLATION_MODEL=deepseek/deepseek-r1-0528:free
```

#### For Production
Set environment variables in your deployment platform:
- **Heroku**: `heroku config:set OPENROUTER_API_KEY=your_key`
- **Vercel**: Add to environment variables in dashboard
- **Docker**: Add to docker-compose.yml or Dockerfile

### Step 3: Restart the Backend
```bash
cd zakmakelaar-backend
npm start
```

## 🎯 AI Translation Features

### Professional Quality
- **Real Estate Specialized**: Understands property terminology
- **Context Aware**: Maintains meaning and technical accuracy
- **Natural Language**: Produces fluent, readable translations

### Supported Languages
- **Dutch** ↔ **English** (primary focus)
- **German**, **French**, **Spanish** (additional support)
- **Auto-detection**: Automatically identifies source language

### Performance
- **Speed**: 2-5 seconds for typical property descriptions
- **Accuracy**: Professional-grade translation quality
- **Reliability**: No rate limits with proper API keys

## 🧪 Testing the Setup

Once configured, test the translation:

1. **Frontend**: Use the translate button in property listings
2. **API**: Send POST request to `/api/ai/translate`

### Example API Request
```bash
curl -X POST http://localhost:3000/api/ai/translate \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "text": "Modern appartement met 2 slaapkamers en balkon",
    "targetLanguage": "en",
    "sourceLanguage": "nl"
  }'
```

### Expected Response
```json
{
  "success": true,
  "data": {
    "translatedText": "Modern apartment with 2 bedrooms and balcony",
    "sourceLanguage": "nl",
    "targetLanguage": "en",
    "confidence": 0.95,
    "original": "Modern appartement met 2 slaapkamers en balkon",
    "translatedAt": "2025-01-11T23:26:14.438Z"
  },
  "performance": {
    "duration": "2341ms"
  }
}
```

## 💰 Cost Considerations

### OpenRouter (Recommended)
- **Free Tier**: Available with some models
- **Pay-per-use**: Only pay for what you use
- **Cost**: ~$0.001-0.01 per translation
- **Models**: Access to multiple AI providers

### OpenAI
- **Pay-per-token**: Based on input/output tokens
- **Cost**: ~$0.002-0.02 per translation
- **Quality**: High-quality GPT models

### Estimated Monthly Costs
- **Small app** (100 translations/day): $3-10/month
- **Medium app** (500 translations/day): $15-50/month
- **Large app** (2000 translations/day): $60-200/month

## 🔒 Security Best Practices

### API Key Management
- **Never commit** API keys to version control
- **Use environment variables** for all deployments
- **Rotate keys** regularly for security
- **Monitor usage** to detect unauthorized access

### Rate Limiting
- **Built-in protection**: AI services have their own limits
- **Monitor costs**: Set up billing alerts
- **Cache translations**: Reduce duplicate requests

## 🚀 Production Deployment

### Environment Setup
```bash
# Production environment variables
OPENROUTER_API_KEY=prod_key_here
NODE_ENV=production
OPENROUTER_TRANSLATION_MODEL=gpt-4o-mini
```

### Monitoring
- **API Usage**: Monitor translation requests
- **Error Rates**: Track failed translations
- **Performance**: Monitor response times
- **Costs**: Set up billing alerts

## 🎉 Benefits of AI Translation

### vs Free APIs
- ✅ **No Rate Limits**: Unlimited translations with API key
- ✅ **Higher Quality**: Professional-grade results
- ✅ **Better Context**: Real estate terminology understanding
- ✅ **Reliability**: Consistent availability
- ✅ **No Chunking**: Handles long texts seamlessly

### User Experience
- **Instant Results**: Fast, accurate translations
- **Professional Quality**: Natural, readable output
- **Specialized Terms**: Proper real estate vocabulary
- **Any Length**: No text length restrictions

## 🔧 Troubleshooting

### Common Issues

#### "Authentication Required" Error
- **Cause**: Missing or invalid API key
- **Solution**: Check environment variables and restart server

#### "Rate Limit Exceeded" Error
- **Cause**: Too many requests or insufficient credits
- **Solution**: Check API usage and billing

#### "Translation Failed" Error
- **Cause**: Network issues or API service down
- **Solution**: Check internet connection and API status

### Support
- **OpenRouter**: [Documentation](https://openrouter.ai/docs)
- **OpenAI**: [API Documentation](https://platform.openai.com/docs)
- **Issues**: Check backend logs for detailed error messages

## 📈 Next Steps

1. **Configure API Keys**: Set up OpenRouter or OpenAI credentials
2. **Test Translation**: Verify functionality with sample texts
3. **Monitor Usage**: Track API calls and costs
4. **Optimize**: Fine-tune models and caching as needed

The AI translation system is now ready to provide professional-quality translations for your real estate platform! 🏠✨