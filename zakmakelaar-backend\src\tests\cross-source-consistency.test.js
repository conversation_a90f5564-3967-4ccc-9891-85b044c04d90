/**
 * Cross-Source Consistency Tests
 * 
 * This file contains tests to verify that the transformation pipeline produces
 * consistent output across different scrapers (Funda, Huurwoningen, Pararius).
 */

const { SchemaTransformer } = require('../services/schemaTransformer');
const { FieldMappingRegistry } = require('../services/fieldMappingRegistry');
const { MappingConfigLoader } = require('../services/mappingConfigLoader');
const { ValidationEngine } = require('../services/validationEngine');
const { testDataSets, rawScraperData } = require('./testData/unifiedSchemaTestData');

describe('Cross-Source Consistency', () => {
  let registry;
  let transformer;
  let validationEngine;
  let transformedProperties;
  
  beforeAll(async () => {
    // Set up the transformation pipeline components
    registry = new FieldMappingRegistry();
    const loader = new MappingConfigLoader(registry);
    
    // Load mappings for all sources
    loader.loadFromObject('funda.nl', {
      'title': 'title',
      'description': 'description',
      'url': 'url',
      'source': { value: 'funda.nl' },
      'location': {
        path: 'location',
        transform: 'normalizeLocation'
      },
      'price': {
        path: 'price',
        transform: 'normalizePrice'
      },
      'propertyType': {
        path: 'propertyType',
        transform: 'normalizePropertyType'
      },
      'size': {
        path: 'size',
        transform: 'normalizeSize'
      },
      'area': {
        path: 'size',
        transform: 'extractNumericSize'
      },
      'rooms': {
        path: 'rooms',
        transform: 'normalizeRooms'
      },
      'bedrooms': {
        path: 'bedrooms',
        transform: 'normalizeRooms'
      },
      'year': {
        path: 'year',
        transform: 'normalizeYear'
      },
      'interior': {
        path: 'interior',
        transform: 'normalizeInterior'
      },
      'furnished': {
        path: 'interior',
        transform: 'inferFurnishedStatus'
      },
      'images': {
        path: 'images',
        transform: 'normalizeImageArray'
      },
      'energyLabel': {
        path: 'energyLabel',
        transform: 'normalizeEnergyLabel'
      },
      'dateAdded': {
        transform: 'getCurrentISOString'
      }
    });
    
    loader.loadFromObject('huurwoningen.nl', {
      'title': 'title',
      'description': 'description',
      'url': 'url',
      'source': { value: 'huurwoningen.nl' },
      'location': {
        path: 'location',
        transform: 'normalizeLocation'
      },
      'price': {
        path: 'price',
        transform: 'normalizePrice'
      },
      'propertyType': {
        path: 'propertyType',
        transform: 'normalizePropertyType'
      },
      'size': {
        path: 'size',
        transform: 'normalizeSize'
      },
      'area': {
        path: 'oppervlakte',
        transform: 'extractNumericSize'
      },
      'rooms': {
        path: 'rooms',
        transform: 'normalizeRooms'
      },
      'bedrooms': {
        path: 'bedrooms',
        transform: 'normalizeRooms'
      },
      'year': {
        path: 'year',
        transform: 'normalizeYear'
      },
      'interior': {
        path: 'interior',
        transform: 'normalizeInterior'
      },
      'furnished': {
        path: 'interior',
        transform: 'inferFurnishedStatus'
      },
      'images': {
        path: 'images',
        transform: 'normalizeImageArray'
      },
      'dateAdded': {
        transform: 'getCurrentISOString'
      },
      'pets': {
        path: 'huisdierenToegstaan',
        transform: 'normalizeBoolean'
      },
      'smoking': {
        path: 'rokenToegstaan',
        transform: 'normalizeBoolean'
      },
      'deposit': {
        path: 'waarborgsom',
        transform: 'normalizePrice'
      }
    });
    
    loader.loadFromObject('pararius.nl', {
      'title': 'title',
      'description': 'description',
      'url': 'url',
      'source': { value: 'pararius.nl' },
      'location': {
        path: 'location',
        transform: 'normalizeLocation'
      },
      'price': {
        path: 'price',
        transform: 'normalizePrice'
      },
      'propertyType': {
        path: 'propertyType',
        transform: 'normalizePropertyType'
      },
      'size': {
        path: 'size',
        transform: 'normalizeSize'
      },
      'area': {
        path: 'woonoppervlakte',
        transform: 'extractNumericSize'
      },
      'rooms': {
        path: 'rooms',
        transform: 'normalizeRooms'
      },
      'bedrooms': {
        path: 'bedrooms',
        transform: 'normalizeRooms'
      },
      'bathrooms': {
        path: 'bathrooms',
        transform: 'normalizeRooms'
      },
      'year': {
        path: 'year',
        transform: 'normalizeYear'
      },
      'interior': {
        path: 'interior',
        transform: 'normalizeInterior'
      },
      'furnished': {
        path: 'interior',
        transform: 'inferFurnishedStatus'
      },
      'images': {
        path: 'images',
        transform: 'normalizeImageArray'
      },
      'dateAdded': {
        transform: 'getCurrentISOString'
      },
      'garden': {
        path: 'tuin',
        transform: 'normalizeBoolean'
      },
      'balcony': {
        path: 'balkon',
        transform: 'normalizeBoolean'
      },
      'parking': {
        path: 'parkeren',
        transform: 'normalizeBoolean'
      },
      'energyLabel': {
        path: 'energielabel',
        transform: 'normalizeEnergyLabel'
      }
    });
    
    transformer = new SchemaTransformer(registry);
    validationEngine = new ValidationEngine();
    
    // Transform properties from all sources
    transformedProperties = {
      funda: await transformer.transform(rawScraperData.funda, 'funda.nl'),
      huurwoningen: await transformer.transform(rawScraperData.huurwoningen, 'huurwoningen.nl'),
      pararius: await transformer.transform(rawScraperData.pararius, 'pararius.nl')
    };
  });
  
  test('all transformed properties should have the same schema structure', () => {
    // Get the keys from each transformed property
    const fundaKeys = Object.keys(transformedProperties.funda);
    const huurwoningenKeys = Object.keys(transformedProperties.huurwoningen);
    const parariusKeys = Object.keys(transformedProperties.pararius);
    
    // Get the common required fields from the schema
    const requiredFields = [
      'title', 'source', 'url', 'location', 'price', 'propertyType',
      'dateAdded', '_internal'
    ];
    
    // Verify that all required fields are present in all transformed properties
    for (const field of requiredFields) {
      expect(fundaKeys).toContain(field);
      expect(huurwoningenKeys).toContain(field);
      expect(parariusKeys).toContain(field);
    }
  });
  
  test('all transformed properties should have consistent data types', () => {
    // Check data types for common fields
    expect(typeof transformedProperties.funda.title).toBe('string');
    expect(typeof transformedProperties.huurwoningen.title).toBe('string');
    expect(typeof transformedProperties.pararius.title).toBe('string');
    
    expect(typeof transformedProperties.funda.source).toBe('string');
    expect(typeof transformedProperties.huurwoningen.source).toBe('string');
    expect(typeof transformedProperties.pararius.source).toBe('string');
    
    expect(typeof transformedProperties.funda.url).toBe('string');
    expect(typeof transformedProperties.huurwoningen.url).toBe('string');
    expect(typeof transformedProperties.pararius.url).toBe('string');
    
    // Location can be string or object, but should be consistent in structure
    if (typeof transformedProperties.funda.location === 'object') {
      expect(transformedProperties.funda.location).toHaveProperty('_legacy');
      expect(transformedProperties.funda.location).toHaveProperty('_unified');
    }
    
    if (typeof transformedProperties.huurwoningen.location === 'object') {
      expect(transformedProperties.huurwoningen.location).toHaveProperty('_legacy');
      expect(transformedProperties.huurwoningen.location).toHaveProperty('_unified');
    }
    
    if (typeof transformedProperties.pararius.location === 'object') {
      expect(transformedProperties.pararius.location).toHaveProperty('_legacy');
      expect(transformedProperties.pararius.location).toHaveProperty('_unified');
    }
    
    // Price can be string or number
    expect(['string', 'number']).toContain(typeof transformedProperties.funda.price);
    expect(['string', 'number']).toContain(typeof transformedProperties.huurwoningen.price);
    expect(['string', 'number']).toContain(typeof transformedProperties.pararius.price);
    
    expect(typeof transformedProperties.funda.propertyType).toBe('string');
    expect(typeof transformedProperties.huurwoningen.propertyType).toBe('string');
    expect(typeof transformedProperties.pararius.propertyType).toBe('string');
    
    expect(typeof transformedProperties.funda.dateAdded).toBe('string');
    expect(typeof transformedProperties.huurwoningen.dateAdded).toBe('string');
    expect(typeof transformedProperties.pararius.dateAdded).toBe('string');
    
    // Internal metadata should be objects
    expect(typeof transformedProperties.funda._internal).toBe('object');
    expect(typeof transformedProperties.huurwoningen._internal).toBe('object');
    expect(typeof transformedProperties.pararius._internal).toBe('object');
  });
  
  test('all transformed properties should pass validation', () => {
    // Validate each transformed property
    const fundaValidation = validationEngine.validate(transformedProperties.funda);
    const huurwoningenValidation = validationEngine.validate(transformedProperties.huurwoningen);
    const parariusValidation = validationEngine.validate(transformedProperties.pararius);
    
    // Verify that all properties pass validation
    expect(fundaValidation.valid).toBe(true);
    expect(huurwoningenValidation.valid).toBe(true);
    expect(parariusValidation.valid).toBe(true);
  });
  
  test('property types should be normalized consistently', () => {
    // Create test data with the same property type but different source formats
    const fundaData = { ...rawScraperData.funda, propertyType: 'appartement' };
    const huurwoningenData = { ...rawScraperData.huurwoningen, propertyType: 'apartment' };
    const parariusData = { ...rawScraperData.pararius, propertyType: 'appartement' };
    
    // Transform the test data
    return Promise.all([
      transformer.transform(fundaData, 'funda.nl'),
      transformer.transform(huurwoningenData, 'huurwoningen.nl'),
      transformer.transform(parariusData, 'pararius.nl')
    ]).then(([fundaResult, huurwoningenResult, parariusResult]) => {
      // Verify that all property types are normalized to the same value
      expect(fundaResult.propertyType).toBe('apartment');
      expect(huurwoningenResult.propertyType).toBe('apartment');
      expect(parariusResult.propertyType).toBe('apartment');
    });
  });
  
  test('prices should be normalized consistently', () => {
    // Create test data with the same price but different formats
    const fundaData = { ...rawScraperData.funda, price: '€ 1.500 per maand' };
    const huurwoningenData = { ...rawScraperData.huurwoningen, price: '€1500' };
    const parariusData = { ...rawScraperData.pararius, price: '1500' };
    
    // Transform the test data
    return Promise.all([
      transformer.transform(fundaData, 'funda.nl'),
      transformer.transform(huurwoningenData, 'huurwoningen.nl'),
      transformer.transform(parariusData, 'pararius.nl')
    ]).then(([fundaResult, huurwoningenResult, parariusResult]) => {
      // Verify that all prices are normalized to the same value
      expect(fundaResult.price).toBe(1500);
      expect(huurwoningenResult.price).toBe(1500);
      expect(parariusResult.price).toBe(1500);
    });
  });
  
  test('sizes should be normalized consistently', () => {
    // Create test data with the same size but different formats
    const fundaData = { ...rawScraperData.funda, size: '85 m²' };
    const huurwoningenData = { ...rawScraperData.huurwoningen, size: '85m²' };
    const parariusData = { ...rawScraperData.pararius, size: '85' };
    
    // Transform the test data
    return Promise.all([
      transformer.transform(fundaData, 'funda.nl'),
      transformer.transform(huurwoningenData, 'huurwoningen.nl'),
      transformer.transform(parariusData, 'pararius.nl')
    ]).then(([fundaResult, huurwoningenResult, parariusResult]) => {
      // Verify that all sizes are normalized to the same format
      expect(fundaResult.size).toBe('85 m²');
      expect(huurwoningenResult.size).toBe('85 m²');
      expect(parariusResult.size).toBe('85 m²');
      
      // Verify that all areas are normalized to the same numeric value
      expect(fundaResult.area).toBe(85);
      expect(huurwoningenResult.area).toBe(85);
      expect(parariusResult.area).toBe(85);
    });
  });
  
  test('rooms should be normalized consistently', () => {
    // Create test data with the same number of rooms but different formats
    const fundaData = { ...rawScraperData.funda, rooms: '3' };
    const huurwoningenData = { ...rawScraperData.huurwoningen, rooms: 3 };
    const parariusData = { ...rawScraperData.pararius, rooms: '3 kamers' };
    
    // Transform the test data
    return Promise.all([
      transformer.transform(fundaData, 'funda.nl'),
      transformer.transform(huurwoningenData, 'huurwoningen.nl'),
      transformer.transform(parariusData, 'pararius.nl')
    ]).then(([fundaResult, huurwoningenResult, parariusResult]) => {
      // Verify that all rooms are normalized consistently
      // Note: The exact format (string or number) may vary, but the value should be consistent
      expect(String(fundaResult.rooms)).toBe(String(huurwoningenResult.rooms));
      expect(String(huurwoningenResult.rooms)).toBe(String(parariusResult.rooms));
    });
  });
  
  test('interior types should be normalized consistently', () => {
    // Create test data with equivalent interior types but different formats
    const fundaData = { ...rawScraperData.funda, interior: 'Gemeubileerd' };
    const huurwoningenData = { ...rawScraperData.huurwoningen, interior: 'gemeubileerd' };
    const parariusData = { ...rawScraperData.pararius, interior: 'furnished' };
    
    // Transform the test data
    return Promise.all([
      transformer.transform(fundaData, 'funda.nl'),
      transformer.transform(huurwoningenData, 'huurwoningen.nl'),
      transformer.transform(parariusData, 'pararius.nl')
    ]).then(([fundaResult, huurwoningenResult, parariusResult]) => {
      // Verify that all interior types are normalized to the same value
      expect(fundaResult.interior).toBe('Gemeubileerd');
      expect(huurwoningenResult.interior).toBe('Gemeubileerd');
      expect(parariusResult.interior).toBe('Gemeubileerd');
      
      // Verify that furnished status is inferred consistently
      expect(fundaResult.furnished).toBe(true);
      expect(huurwoningenResult.furnished).toBe(true);
      expect(parariusResult.furnished).toBe(true);
    });
  });
  
  test('energy labels should be normalized consistently', () => {
    // Create test data with the same energy label but different formats
    const fundaData = { ...rawScraperData.funda, energyLabel: 'A' };
    const huurwoningenData = { ...rawScraperData.huurwoningen, energyLabel: 'a' };
    const parariusData = { ...rawScraperData.pararius, energielabel: 'A' };
    
    // Transform the test data
    return Promise.all([
      transformer.transform(fundaData, 'funda.nl'),
      transformer.transform(huurwoningenData, 'huurwoningen.nl'),
      transformer.transform(parariusData, 'pararius.nl')
    ]).then(([fundaResult, huurwoningenResult, parariusResult]) => {
      // Verify that all energy labels are normalized to the same value
      expect(fundaResult.energyLabel).toBe('A');
      expect(huurwoningenResult.energyLabel).toBe('A');
      expect(parariusResult.energyLabel).toBe('A');
    });
  });
  
  test('boolean features should be normalized consistently', () => {
    // Create test data with the same boolean features but different formats
    const fundaData = {
      ...rawScraperData.funda,
      garden: 'yes',
      balcony: 'ja',
      parking: true
    };
    
    const huurwoningenData = {
      ...rawScraperData.huurwoningen,
      garden: 1,
      balcony: '1',
      parking: 'true'
    };
    
    const parariusData = {
      ...rawScraperData.pararius,
      tuin: 'Ja',
      balkon: 'Ja',
      parkeren: 'Ja'
    };
    
    // Transform the test data
    return Promise.all([
      transformer.transform(fundaData, 'funda.nl'),
      transformer.transform(huurwoningenData, 'huurwoningen.nl'),
      transformer.transform(parariusData, 'pararius.nl')
    ]).then(([fundaResult, huurwoningenResult, parariusResult]) => {
      // Verify that all boolean features are normalized to the same value
      expect(fundaResult.garden).toBe(true);
      expect(huurwoningenResult.garden).toBe(true);
      expect(parariusResult.garden).toBe(true);
      
      expect(fundaResult.balcony).toBe(true);
      expect(huurwoningenResult.balcony).toBe(true);
      expect(parariusResult.balcony).toBe(true);
      
      expect(fundaResult.parking).toBe(true);
      expect(huurwoningenResult.parking).toBe(true);
      expect(parariusResult.parking).toBe(true);
    });
  });
});