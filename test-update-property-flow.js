// Test the complete update property flow
const fetch = require('node-fetch');

async function testUpdatePropertyFlow() {
  console.log('=== TESTING UPDATE PROPERTY FLOW ===\n');
  
  // Configuration
  const baseUrl = 'http://localhost:3001/api/property-owner';
  const propertyId = '507f1f77bcf86cd799439011'; // Mock property ID
  
  // Test data
  const updateData = {
    title: 'Updated Modern Apartment in Utrecht',
    description: 'Updated description with new features and amenities',
    address: {
      street: 'Updated Street',
      houseNumber: '456',
      postalCode: '3511CD',
      city: 'Utrecht',
      province: 'Utrecht'
    },
    propertyType: 'apartment',
    size: 85,
    rooms: 4,
    bedrooms: 3,
    bathrooms: 2,
    rent: {
      amount: 1600,
      currency: 'EUR',
      deposit: 3200,
      additionalCosts: {
        utilities: 175,
        serviceCharges: 75,
        parking: 0,
        other: 0
      }
    },
    features: {
      furnished: true,
      interior: 'gemeubileerd',
      parking: true,
      balcony: true,
      garden: false,
      elevator: true,
      energyLabel: 'A'
    },
    policies: {
      petsAllowed: false,
      smokingAllowed: false,
      studentsAllowed: true,
      expatFriendly: true,
      minimumIncome: 4800,
      maximumOccupants: 3
    },
    status: 'draft',
    images: [
      {
        url: 'https://example.com/image1.jpg',
        caption: 'Living room',
        isPrimary: true
      }
    ],
    availabilityDate: '2024-02-01'
  };
  
  try {
    console.log('1. Testing property update endpoint...');
    console.log(`URL: ${baseUrl}/properties/${propertyId}`);
    console.log('Method: PUT');
    console.log('Data:', JSON.stringify(updateData, null, 2));
    
    const response = await fetch(`${baseUrl}/properties/${propertyId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer mock-token' // Mock token for testing
      },
      body: JSON.stringify(updateData)
    });
    
    console.log('\n2. Response received:');
    console.log('Status:', response.status);
    console.log('Status Text:', response.statusText);
    console.log('Headers:', Object.fromEntries(response.headers.entries()));
    
    const responseText = await response.text();
    console.log('Raw Response:', responseText);
    
    let result;
    try {
      result = JSON.parse(responseText);
      console.log('Parsed Response:', JSON.stringify(result, null, 2));
    } catch (parseError) {
      console.error('Failed to parse response as JSON:', parseError.message);
      return;
    }
    
    if (response.ok) {
      console.log('\n✅ UPDATE SUCCESSFUL!');
      console.log('Message:', result.message);
      if (result.data) {
        console.log('Updated Property ID:', result.data._id || result.data.propertyId);
      }
    } else {
      console.log('\n❌ UPDATE FAILED!');
      console.log('Error:', result.message || result.error);
      
      // Common error scenarios
      if (response.status === 401) {
        console.log('💡 Suggestion: Check authentication token');
      } else if (response.status === 404) {
        console.log('💡 Suggestion: Check if property exists and property ID is correct');
      } else if (response.status === 403) {
        console.log('💡 Suggestion: Check if user owns this property');
      } else if (response.status === 400) {
        console.log('💡 Suggestion: Check validation errors in request data');
      } else if (response.status === 500) {
        console.log('💡 Suggestion: Check server logs for internal errors');
      }
    }
    
  } catch (error) {
    console.error('\n❌ NETWORK ERROR:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('💡 Suggestion: Make sure the backend server is running on port 3001');
    } else if (error.code === 'ENOTFOUND') {
      console.log('💡 Suggestion: Check the server URL and network connection');
    }
  }
  
  console.log('\n=== TEST COMPLETED ===');
}

// Additional test for validation
async function testValidationErrors() {
  console.log('\n=== TESTING VALIDATION ERRORS ===\n');
  
  const baseUrl = 'http://localhost:3001/api/property-owner';
  const propertyId = '507f1f77bcf86cd799439011';
  
  // Test with invalid data
  const invalidData = {
    title: 'A', // Too short
    rent: {
      amount: 50 // Too low
    },
    address: {
      postalCode: 'INVALID' // Invalid format
    }
  };
  
  try {
    const response = await fetch(`${baseUrl}/properties/${propertyId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer mock-token'
      },
      body: JSON.stringify(invalidData)
    });
    
    const result = await response.json();
    
    if (response.status === 400) {
      console.log('✅ Validation working correctly');
      console.log('Validation errors:', result.errors || result.message);
    } else {
      console.log('❌ Validation not working as expected');
      console.log('Response:', result);
    }
    
  } catch (error) {
    console.error('Validation test error:', error.message);
  }
}

// Run tests
async function runAllTests() {
  await testUpdatePropertyFlow();
  await testValidationErrors();
}

runAllTests();