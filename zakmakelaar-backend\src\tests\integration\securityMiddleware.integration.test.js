const request = require('supertest');
const express = require('express');
const mongoose = require('mongoose');
const jwt = require('jsonwebtoken');
const User = require('../../models/User');
const config = require('../../config/config');
const {
  authenticateWithAudit,
  requireAutoApplicationAccess,
  requireDocumentAccess,
  requireAdminWithAudit,
  requireConsent,
  strictRateLimit,
  standardRateLimit,
  securityHeaders,
  cors,
  detectSuspiciousActivity,
  securityErrorHandler
} = require('../../middleware/securityMiddleware');

// Mock services
jest.mock('../../services/auditLogService', () => ({
  logSecurity: jest.fn(),
  logAutoApplication: jest.fn(),
  logPrivacy: jest.fn()
}));

jest.mock('../../services/gdprComplianceService', () => ({
  hasAutoApplicationConsent: jest.fn(),
  validateConsentForOperation: jest.fn()
}));

jest.mock('../../services/logger', () => ({
  loggers: {
    app: {
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn()
    },
    security: {
      warn: jest.fn(),
      error: jest.fn()
    }
  }
}));

describe('Security Middleware Integration Tests', () => {
  let app;
  let testUser;
  let adminUser;
  let authToken;
  let adminToken;

  beforeAll(async () => {
    await mongoose.connect(process.env.MONGO_URI || 'mongodb://localhost:27017/zakmakelaar-test');
  });

  afterAll(async () => {
    await mongoose.connection.dropDatabase();
    await mongoose.connection.close();
  });

  beforeEach(async () => {
    // Clear database
    await User.deleteMany({});

    // Create test users
    testUser = new User({
      email: '<EMAIL>',
      password: 'hashedPassword',
      role: 'user',
      gdprConsent: {
        consents: new Map([
          ['data_processing', { granted: true, timestamp: new Date() }],
          ['auto_application', { granted: true, timestamp: new Date() }]
        ])
      }
    });
    await testUser.save();

    adminUser = new User({
      email: '<EMAIL>',
      password: 'hashedPassword',
      role: 'admin'
    });
    await adminUser.save();

    // Generate tokens
    authToken = jwt.sign(
      { _id: testUser._id, email: testUser.email, role: testUser.role },
      config.jwtSecret,
      { expiresIn: '1h' }
    );

    adminToken = jwt.sign(
      { _id: adminUser._id, email: adminUser.email, role: adminUser.role },
      config.jwtSecret,
      { expiresIn: '1h' }
    );

    // Create test app
    app = express();
    app.use(express.json());
    app.use(securityHeaders);
    app.use(cors);

    // Mock GDPR service responses
    const gdprComplianceService = require('../../services/gdprComplianceService');
    gdprComplianceService.hasAutoApplicationConsent.mockResolvedValue(true);
    gdprComplianceService.validateConsentForOperation.mockResolvedValue(true);
  });

  describe('Authentication Middleware', () => {
    beforeEach(() => {
      app.get('/test-auth', authenticateWithAudit, (req, res) => {
        res.json({ success: true, userId: req.user.id });
      });
    });

    test('should authenticate valid token', async () => {
      const response = await request(app)
        .get('/test-auth')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.userId).toBe(testUser._id.toString());
    });

    test('should reject missing token', async () => {
      await request(app)
        .get('/test-auth')
        .expect(401);
    });

    test('should reject invalid token', async () => {
      await request(app)
        .get('/test-auth')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);
    });

    test('should reject malformed authorization header', async () => {
      await request(app)
        .get('/test-auth')
        .set('Authorization', 'InvalidFormat token')
        .expect(401);
    });

    test('should log successful authentication', async () => {
      const auditLogService = require('../../services/auditLogService');
      
      await request(app)
        .get('/test-auth')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(auditLogService.logSecurity).toHaveBeenCalledWith(
        testUser._id.toString(),
        'login_success',
        expect.objectContaining({
          endpoint: '/test-auth',
          method: 'GET'
        }),
        expect.objectContaining({
          ipAddress: expect.any(String),
          userAgent: expect.any(String)
        })
      );
    });

    test('should log failed authentication', async () => {
      const auditLogService = require('../../services/auditLogService');
      
      await request(app)
        .get('/test-auth')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);

      expect(auditLogService.logSecurity).toHaveBeenCalledWith(
        null,
        'login_failed',
        expect.objectContaining({
          endpoint: '/test-auth',
          method: 'GET'
        }),
        expect.objectContaining({
          ipAddress: expect.any(String),
          userAgent: expect.any(String)
        })
      );
    });
  });

  describe('Auto-Application Access Control', () => {
    beforeEach(() => {
      app.get('/test-auto-app', authenticateWithAudit, requireAutoApplicationAccess, (req, res) => {
        res.json({ success: true });
      });
    });

    test('should allow access with valid consent', async () => {
      await request(app)
        .get('/test-auto-app')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);
    });

    test('should deny access without consent', async () => {
      const gdprComplianceService = require('../../services/gdprComplianceService');
      gdprComplianceService.hasAutoApplicationConsent.mockResolvedValueOnce(false);

      const response = await request(app)
        .get('/test-auto-app')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(403);

      expect(response.body.code).toBe('CONSENT_REQUIRED');
    });

    test('should require authentication first', async () => {
      await request(app)
        .get('/test-auto-app')
        .expect(401);
    });

    test('should log access granted', async () => {
      const auditLogService = require('../../services/auditLogService');
      
      await request(app)
        .get('/test-auto-app')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(auditLogService.logAutoApplication).toHaveBeenCalledWith(
        testUser._id.toString(),
        'auto_application_access_granted',
        expect.objectContaining({
          endpoint: '/test-auto-app',
          method: 'GET'
        }),
        expect.objectContaining({
          ipAddress: expect.any(String),
          userAgent: expect.any(String)
        })
      );
    });
  });

  describe('Admin Access Control', () => {
    beforeEach(() => {
      app.get('/test-admin', authenticateWithAudit, requireAdminWithAudit, (req, res) => {
        res.json({ success: true });
      });
    });

    test('should allow admin access', async () => {
      await request(app)
        .get('/test-admin')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);
    });

    test('should deny non-admin access', async () => {
      await request(app)
        .get('/test-admin')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(403);
    });

    test('should log admin access', async () => {
      const auditLogService = require('../../services/auditLogService');
      
      await request(app)
        .get('/test-admin')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(auditLogService.logSecurity).toHaveBeenCalledWith(
        adminUser._id.toString(),
        'admin_access',
        expect.objectContaining({
          endpoint: '/test-admin',
          method: 'GET'
        }),
        expect.any(Object)
      );
    });

    test('should log denied admin access', async () => {
      const auditLogService = require('../../services/auditLogService');
      
      await request(app)
        .get('/test-admin')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(403);

      expect(auditLogService.logSecurity).toHaveBeenCalledWith(
        testUser._id.toString(),
        'admin_access_denied',
        expect.objectContaining({
          endpoint: '/test-admin',
          method: 'GET',
          userRole: 'user'
        }),
        expect.any(Object)
      );
    });
  });

  describe('Consent Validation', () => {
    beforeEach(() => {
      app.post('/test-consent', authenticateWithAudit, requireConsent('auto_application'), (req, res) => {
        res.json({ success: true });
      });
    });

    test('should allow access with valid consent', async () => {
      await request(app)
        .post('/test-consent')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);
    });

    test('should deny access without consent', async () => {
      const gdprComplianceService = require('../../services/gdprComplianceService');
      gdprComplianceService.validateConsentForOperation.mockResolvedValueOnce(false);

      const response = await request(app)
        .post('/test-consent')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(403);

      expect(response.body.code).toBe('CONSENT_REQUIRED');
      expect(response.body.operation).toBe('auto_application');
    });

    test('should log consent validation failure', async () => {
      const gdprComplianceService = require('../../services/gdprComplianceService');
      const auditLogService = require('../../services/auditLogService');
      
      gdprComplianceService.validateConsentForOperation.mockResolvedValueOnce(false);

      await request(app)
        .post('/test-consent')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(403);

      expect(auditLogService.logPrivacy).toHaveBeenCalledWith(
        testUser._id.toString(),
        'consent_validation_failed',
        expect.objectContaining({
          operation: 'auto_application',
          endpoint: '/test-consent'
        }),
        expect.any(Object)
      );
    });
  });

  describe('Rate Limiting', () => {
    beforeEach(() => {
      app.get('/test-rate-limit', strictRateLimit, (req, res) => {
        res.json({ success: true });
      });
    });

    test('should allow requests within limit', async () => {
      // Make a few requests within the limit
      for (let i = 0; i < 3; i++) {
        await request(app)
          .get('/test-rate-limit')
          .expect(200);
      }
    });

    test('should block requests exceeding limit', async () => {
      // Make requests to exceed the limit (strict rate limit is 10 per 5 minutes)
      const promises = [];
      for (let i = 0; i < 12; i++) {
        promises.push(
          request(app)
            .get('/test-rate-limit')
        );
      }

      const responses = await Promise.all(promises);
      
      // Some requests should be blocked
      const blockedResponses = responses.filter(res => res.status === 429);
      expect(blockedResponses.length).toBeGreaterThan(0);
    });

    test('should include rate limit headers', async () => {
      const response = await request(app)
        .get('/test-rate-limit')
        .expect(200);

      expect(response.headers).toHaveProperty('x-ratelimit-limit');
      expect(response.headers).toHaveProperty('x-ratelimit-remaining');
    });
  });

  describe('Security Headers', () => {
    test('should include security headers', async () => {
      app.get('/test-headers', (req, res) => {
        res.json({ success: true });
      });

      const response = await request(app)
        .get('/test-headers')
        .expect(200);

      expect(response.headers).toHaveProperty('x-content-type-options');
      expect(response.headers).toHaveProperty('x-frame-options');
      expect(response.headers).toHaveProperty('x-xss-protection');
      expect(response.headers).toHaveProperty('strict-transport-security');
    });

    test('should include CSP header', async () => {
      app.get('/test-csp', (req, res) => {
        res.json({ success: true });
      });

      const response = await request(app)
        .get('/test-csp')
        .expect(200);

      expect(response.headers).toHaveProperty('content-security-policy');
      expect(response.headers['content-security-policy']).toContain("default-src 'self'");
    });
  });

  describe('CORS Configuration', () => {
    test('should handle CORS preflight requests', async () => {
      app.options('/test-cors', (req, res) => {
        res.json({ success: true });
      });

      const response = await request(app)
        .options('/test-cors')
        .set('Origin', 'http://localhost:3000')
        .set('Access-Control-Request-Method', 'POST')
        .expect(204);

      expect(response.headers).toHaveProperty('access-control-allow-origin');
      expect(response.headers).toHaveProperty('access-control-allow-methods');
    });

    test('should allow configured origins', async () => {
      app.get('/test-cors', (req, res) => {
        res.json({ success: true });
      });

      const response = await request(app)
        .get('/test-cors')
        .set('Origin', 'http://localhost:3000')
        .expect(200);

      expect(response.headers['access-control-allow-origin']).toBe('http://localhost:3000');
    });
  });

  describe('Suspicious Activity Detection', () => {
    beforeEach(() => {
      app.get('/test-suspicious', detectSuspiciousActivity, (req, res) => {
        res.json({ success: true });
      });
    });

    test('should allow normal request patterns', async () => {
      await request(app)
        .get('/test-suspicious')
        .expect(200);
    });

    test('should detect rapid request patterns', async () => {
      // Make many rapid requests to trigger suspicious activity detection
      const promises = [];
      for (let i = 0; i < 60; i++) {
        promises.push(
          request(app)
            .get('/test-suspicious')
        );
      }

      const responses = await Promise.all(promises);
      
      // Some requests should be blocked due to suspicious activity
      const blockedResponses = responses.filter(res => res.status === 429);
      expect(blockedResponses.length).toBeGreaterThan(0);
    });

    test('should log suspicious activity', async () => {
      const auditLogService = require('../../services/auditLogService');
      
      // Make many rapid requests
      const promises = [];
      for (let i = 0; i < 60; i++) {
        promises.push(
          request(app)
            .get('/test-suspicious')
        );
      }

      await Promise.all(promises);

      // Should have logged suspicious activity
      expect(auditLogService.logSecurity).toHaveBeenCalledWith(
        null,
        'suspicious_activity',
        expect.objectContaining({
          pattern: 'rapid_requests'
        }),
        expect.any(Object)
      );
    });
  });

  describe('Error Handling', () => {
    beforeEach(() => {
      app.get('/test-error', (req, res, next) => {
        const error = new Error('Test error');
        error.status = 500;
        next(error);
      });

      app.get('/test-auth-error', (req, res, next) => {
        const error = new Error('Authentication failed');
        error.name = 'UnauthorizedError';
        next(error);
      });

      app.use(securityErrorHandler);
    });

    test('should handle general errors', async () => {
      const response = await request(app)
        .get('/test-error')
        .expect(500);

      expect(response.body.status).toBe('error');
      expect(response.body.message).toBe('Test error');
    });

    test('should handle and log security errors', async () => {
      const auditLogService = require('../../services/auditLogService');
      
      await request(app)
        .get('/test-auth-error')
        .expect(500);

      expect(auditLogService.logSecurity).toHaveBeenCalledWith(
        null,
        'security_error',
        expect.objectContaining({
          error: 'Authentication failed',
          endpoint: '/test-auth-error',
          method: 'GET'
        }),
        expect.any(Object)
      );
    });

    test('should not expose error details in production', async () => {
      const originalEnv = config.nodeEnv;
      config.nodeEnv = 'production';

      const response = await request(app)
        .get('/test-error')
        .expect(500);

      expect(response.body.message).toBe('Internal server error');
      expect(response.body.stack).toBeUndefined();

      config.nodeEnv = originalEnv;
    });
  });

  describe('Document Access Control', () => {
    let testDocument;

    beforeEach(async () => {
      const Document = require('../../models/Document');
      testDocument = new Document({
        userId: testUser._id,
        filename: 'test-doc.pdf',
        originalName: 'test-document.pdf',
        type: 'income_proof',
        size: 1024,
        mimeType: 'application/pdf',
        encryptedPath: '/path/to/encrypted/file',
        encryptionKey: 'test-key'
      });
      await testDocument.save();

      app.get('/test-document/:documentId', authenticateWithAudit, requireDocumentAccess, (req, res) => {
        res.json({ success: true, document: req.document });
      });
    });

    test('should allow document owner access', async () => {
      const response = await request(app)
        .get(`/test-document/${testDocument._id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.document).toBeTruthy();
    });

    test('should allow admin access to any document', async () => {
      await request(app)
        .get(`/test-document/${testDocument._id}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);
    });

    test('should deny access to non-owner', async () => {
      const otherUser = new User({
        email: '<EMAIL>',
        password: 'hashedPassword',
        role: 'user'
      });
      await otherUser.save();

      const otherToken = jwt.sign(
        { _id: otherUser._id, email: otherUser.email, role: otherUser.role },
        config.jwtSecret,
        { expiresIn: '1h' }
      );

      await request(app)
        .get(`/test-document/${testDocument._id}`)
        .set('Authorization', `Bearer ${otherToken}`)
        .expect(403);
    });

    test('should return 404 for non-existent document', async () => {
      const nonExistentId = new mongoose.Types.ObjectId();
      
      await request(app)
        .get(`/test-document/${nonExistentId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);
    });

    test('should log document access denial', async () => {
      const otherUser = new User({
        email: '<EMAIL>',
        password: 'hashedPassword',
        role: 'user'
      });
      await otherUser.save();

      const otherToken = jwt.sign(
        { _id: otherUser._id, email: otherUser.email, role: otherUser.role },
        config.jwtSecret,
        { expiresIn: '1h' }
      );

      const auditLogService = require('../../services/auditLogService');

      await request(app)
        .get(`/test-document/${testDocument._id}`)
        .set('Authorization', `Bearer ${otherToken}`)
        .expect(403);

      expect(auditLogService.logSecurity).toHaveBeenCalledWith(
        otherUser._id.toString(),
        'document_access_denied',
        expect.objectContaining({
          documentId: testDocument._id.toString(),
          ownerId: testUser._id,
          reason: 'Insufficient permissions'
        }),
        expect.any(Object)
      );
    });
  });
});