/**
 * Field Mapping Registry System
 * 
 * This module provides a centralized registry for managing field mappings
 * between different scraper sources and the unified property schema.
 * It supports loading mappings from JSON files, validating mappings,
 * and retrieving mappings for transformation operations.
 */

const fs = require('fs').promises;
const path = require('path');
const Joi = require('joi');
const { getSchema } = require('../schemas/unifiedPropertySchema');

/**
 * Schema for validating mapping configurations
 */
const MappingConfigSchema = Joi.object({
  // Direct field mapping (source field -> target field)
  [Joi.string()]: Joi.alternatives().try(
    // Simple string mapping
    Joi.string(),
    // Complex mapping with transformation
    Joi.object({
      path: Joi.string().allow(''), // Source field path (can be empty for computed fields)
      transform: Joi.string().optional(), // Transformation function name
      value: Joi.any().optional(), // Static value
      default: Joi.any().optional(), // Default value if source is missing/invalid
      required: Joi.boolean().default(false), // Whether this field is required
      validate: Joi.string().optional() // Custom validation function name
    }),
    // Static value mapping
    Joi.object({
      value: Joi.any().required()
    })
  )
}).unknown(true);

/**
 * Field Mapping Registry Class
 * Manages field mappings for different scraper sources
 */
class FieldMappingRegistry {
  constructor() {
    this.mappings = new Map();
    this.transformationFunctions = new Map();
    this.validationFunctions = new Map();
    this.unifiedSchema = getSchema();
    this._initializeBuiltInTransformations();
  }

  /**
   * Initialize built-in transformation functions
   * @private
   */
  _initializeBuiltInTransformations() {
    // Price normalization functions
    this.transformationFunctions.set('normalizePrice', (value) => {
      if (!value) return null;
      if (typeof value === 'number') return value;
      
      const stringValue = String(value);
      
      // Handle special cases
      if (stringValue.toLowerCase().includes('op aanvraag') || 
          stringValue.toLowerCase().includes('n.o.t.k.')) {
        return stringValue;
      }
      
      // Extract numeric value from price strings
      const priceMatch = stringValue.match(/[\d.,]+/);
      if (priceMatch) {
        let numericString = priceMatch[0];
        
        // Handle Dutch number formats where dot is thousands separator and comma is decimal
        // Examples: 1.500,00 -> 1500.00, 2.850 -> 2850, 1,500 -> 1500
        if (numericString.includes('.') && numericString.includes(',')) {
          // Format: 1.500,00 (dot = thousands, comma = decimal)
          numericString = numericString.replace(/\./g, '').replace(',', '.');
        } else if (numericString.includes('.') && !numericString.includes(',')) {
          // Check if dot is thousands separator (e.g., 2.850) or decimal (e.g., 2.5)
          const parts = numericString.split('.');
          if (parts.length === 2 && parts[1].length === 3) {
            // Likely thousands separator: 2.850 -> 2850
            numericString = numericString.replace('.', '');
          }
          // Otherwise treat as decimal separator
        } else if (numericString.includes(',') && !numericString.includes('.')) {
          // Comma could be thousands separator (1,500) or decimal (1,5)
          const parts = numericString.split(',');
          if (parts.length === 2 && parts[1].length === 3) {
            // Likely thousands separator: 1,500 -> 1500
            numericString = numericString.replace(',', '');
          } else {
            // Treat as decimal separator: 1,5 -> 1.5
            numericString = numericString.replace(',', '.');
          }
        }
        
        const numericPrice = parseFloat(numericString);
        return isNaN(numericPrice) ? value : numericPrice;
      }
      return value;
    });

    // Size normalization functions
    this.transformationFunctions.set('formatSizeString', (value) => {
      if (!value) return null;
      const numericValue = this._extractNumericValue(value);
      return numericValue ? `${numericValue} m²` : value;
    });

    this.transformationFunctions.set('extractNumericSize', (value) => {
      return this._extractNumericValue(value);
    });

    // Room normalization
    this.transformationFunctions.set('normalizeRooms', (value) => {
      if (!value) return null;
      if (typeof value === 'number') return String(value); // Convert to string for frontend compatibility
      
      const numericValue = this._extractNumericValue(value);
      return numericValue ? String(numericValue) : value; // Return as string for frontend compatibility
    });

    // Year normalization
    this.transformationFunctions.set('normalizeYear', (value) => {
      if (!value) return null;
      const year = String(value).match(/\d{4}/);
      return year ? year[0] : null;
    });

    // Property type normalization
    this.transformationFunctions.set('normalizePropertyType', (value) => {
      if (!value) return 'woning';
      const type = String(value).toLowerCase();
      
      const typeMap = {
        'appartement': 'apartment',
        'apartment': 'apartment',
        'huis': 'house',
        'house': 'house',
        'studio': 'studio',
        'kamer': 'room',
        'room': 'room',
        'woning': 'woning'
      };
      
      return typeMap[type] || 'woning';
    });

    // Location normalization
    this.transformationFunctions.set('normalizeLocation', (value) => {
      if (!value) return null;
      if (typeof value === 'string') return value;
      
      // Handle structured location objects
      if (typeof value === 'object') {
        if (value.city) return value.city;
        if (value.address) return value.address;
      }
      
      return String(value);
    });

    // Enhanced location normalization with support for both string and object formats
    this.transformationFunctions.set('enhancedLocationNormalization', (value, rawData) => {
      if (!value) return null;
      
      // If it's already a properly formatted object, return it
      if (typeof value === 'object' && value._unified) {
        return value;
      }
      
      // Create a structured location object that supports both formats
      const locationObj = {
        _unified: {
          address: {
            street: null,
            houseNumber: null,
            postalCode: null,
            city: null,
            province: null,
            country: 'Netherlands'
          },
          coordinates: null
        },
        _legacy: null
      };
      
      // Extract location components
      if (typeof value === 'string') {
        // Store the original string in _legacy for backward compatibility
        locationObj._legacy = value;
        
        // Try to extract city from string (assuming format like "Amsterdam, Noord-Holland")
        const parts = value.split(',').map(part => part.trim());
        if (parts.length > 0) {
          locationObj._unified.address.city = parts[0];
        }
        if (parts.length > 1) {
          locationObj._unified.address.province = parts[1];
        }
      } else if (typeof value === 'object') {
        // Handle structured location object from scraper
        if (value.street) locationObj._unified.address.street = value.street;
        if (value.houseNumber) locationObj._unified.address.houseNumber = value.houseNumber;
        if (value.postalCode) locationObj._unified.address.postalCode = value.postalCode;
        if (value.city) locationObj._unified.address.city = value.city;
        if (value.province) locationObj._unified.address.province = value.province;
        
        // Handle coordinates if available
        if (value.lat && value.lng) {
          locationObj._unified.coordinates = {
            lat: parseFloat(value.lat),
            lng: parseFloat(value.lng)
          };
        }
        
        // Create legacy string format for backward compatibility
        const cityProvince = [value.city, value.province].filter(Boolean).join(', ');
        locationObj._legacy = cityProvince || String(value);
      }
      
      // Add toString method for easy conversion to string
      Object.defineProperty(locationObj, 'toString', {
        value: function() {
          return this._legacy || this._unified.address.city || 'Unknown Location';
        },
        enumerable: false
      });
      
      return locationObj;
    });

    // Interior normalization
    this.transformationFunctions.set('normalizeInterior', (value) => {
      if (!value) return null;
      const interior = String(value).toLowerCase();
      
      if (interior.includes('gemeubileerd') || interior.includes('furnished')) {
        return 'Gemeubileerd';
      } else if (interior.includes('gestoffeerd') || interior.includes('semi-furnished')) {
        return 'Gestoffeerd';
      } else if (interior.includes('kaal') || interior.includes('unfurnished')) {
        return 'Kaal';
      }
      
      return value;
    });

    // Boolean normalization
    this.transformationFunctions.set('normalizeBoolean', (value) => {
      if (typeof value === 'boolean') return value;
      if (!value) return false;
      
      const str = String(value).toLowerCase();
      return str === 'true' || str === 'yes' || str === 'ja' || str === '1';
    });

    // Infer furnished status from interior
    this.transformationFunctions.set('inferFurnishedStatus', (value) => {
      if (!value) return false;
      const interior = String(value).toLowerCase();
      return interior.includes('gemeubileerd') || interior.includes('furnished');
    });

    // Image array normalization
    this.transformationFunctions.set('normalizeImageArray', (value) => {
      if (!value) return [];
      if (Array.isArray(value)) return value.filter(img => img && typeof img === 'string');
      if (typeof value === 'string') return [value];
      return [];
    });

    // Current ISO string generator
    this.transformationFunctions.set('getCurrentISOString', () => {
      return new Date().toISOString();
    });
    
    // Date string normalization
    this.transformationFunctions.set('normalizeDateString', (value) => {
      if (!value) return null;
      
      // If it's already an ISO date string, return it
      if (typeof value === 'string' && /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/.test(value)) {
        return value;
      }
      
      // Try to parse the date
      try {
        const date = new Date(value);
        if (!isNaN(date.getTime())) {
          return date.toISOString();
        }
      } catch (e) {
        // Parsing failed, continue with other methods
      }
      
      // Try to extract date patterns (DD-MM-YYYY or similar)
      const dateMatch = String(value).match(/(\d{1,2})[-\/\.](\d{1,2})[-\/\.](\d{2,4})/);
      if (dateMatch) {
        try {
          // Assume European format (DD-MM-YYYY)
          const day = parseInt(dateMatch[1]);
          const month = parseInt(dateMatch[2]) - 1; // JS months are 0-indexed
          let year = parseInt(dateMatch[3]);
          
          // Handle 2-digit years
          if (year < 100) {
            year += year < 50 ? 2000 : 1900;
          }
          
          const date = new Date(year, month, day);
          if (!isNaN(date.getTime())) {
            return date.toISOString();
          }
        } catch (e) {
          // Parsing failed
        }
      }
      
      // Return the original value if parsing fails
      return value;
    });
    
    // Features normalization
    this.transformationFunctions.set('normalizeFeatures', (value) => {
      if (!value) return [];
      
      // If it's already an array, filter out non-strings
      if (Array.isArray(value)) {
        return value.filter(item => item && typeof item === 'string');
      }
      
      // If it's a string, split by commas or semicolons
      if (typeof value === 'string') {
        return value.split(/[,;]/).map(item => item.trim()).filter(Boolean);
      }
      
      // If it's an object, try to extract values
      if (typeof value === 'object') {
        return Object.values(value).filter(item => item && typeof item === 'string');
      }
      
      return [];
    });
    
    // Contact info normalization
    this.transformationFunctions.set('normalizeContactInfo', (value) => {
      if (!value) return {};
      
      // If it's already a properly formatted object, return it
      if (typeof value === 'object' && (value.name || value.phone || value.email)) {
        return {
          name: value.name || '',
          phone: value.phone || '',
          email: value.email || ''
        };
      }
      
      // If it's a string, assume it's a name
      if (typeof value === 'string') {
        return {
          name: value,
          phone: '',
          email: ''
        };
      }
      
      return {
        name: '',
        phone: '',
        email: ''
      };
    });
  }

  /**
   * Extract numeric value from string
   * @private
   */
  _extractNumericValue(value) {
    if (!value) return null;
    if (typeof value === 'number') return value;
    
    const match = String(value).match(/(\d+)/);
    return match ? parseInt(match[1]) : null;
  }

  /**
   * Register field mappings for a specific source
   * @param {string} source - Source identifier (e.g., 'funda', 'huurwoningen')
   * @param {Object} fieldMappings - Field mapping configuration
   * @throws {Error} If mappings are invalid
   */
  registerMapping(source, fieldMappings) {
    if (!source || typeof source !== 'string') {
      throw new Error('Source must be a non-empty string');
    }

    // Validate mapping configuration
    const validation = this.validateMapping(fieldMappings);
    if (validation.error) {
      throw new Error(`Invalid mapping configuration for ${source}: ${validation.error.message}`);
    }

    this.mappings.set(source, fieldMappings);
  }

  /**
   * Get mapping for a specific field from a source
   * @param {string} source - Source identifier
   * @param {string} field - Field name
   * @returns {*} Field mapping or null if not found
   */
  getMapping(source, field) {
    const sourceMappings = this.mappings.get(source);
    if (!sourceMappings) {
      return null;
    }

    return sourceMappings[field] || null;
  }

  /**
   * Get all mappings for a source
   * @param {string} source - Source identifier
   * @returns {Object|null} All mappings for the source or null if not found
   */
  getAllMappings(source) {
    return this.mappings.get(source) || null;
  }

  /**
   * Get all registered sources
   * @returns {string[]} Array of registered source identifiers
   */
  getRegisteredSources() {
    return Array.from(this.mappings.keys());
  }

  /**
   * Check if a source is registered
   * @param {string} source - Source identifier
   * @returns {boolean} True if source is registered
   */
  hasSource(source) {
    return this.mappings.has(source);
  }

  /**
   * Remove mappings for a source
   * @param {string} source - Source identifier
   * @returns {boolean} True if source was removed, false if it didn't exist
   */
  removeSource(source) {
    return this.mappings.delete(source);
  }

  /**
   * Clear all mappings
   */
  clearAll() {
    this.mappings.clear();
  }

  /**
   * Validate mapping configuration
   * @param {Object} mapping - Mapping configuration to validate
   * @returns {Object} Validation result with error property
   */
  validateMapping(mapping) {
    if (!mapping || typeof mapping !== 'object') {
      return { error: new Error('Mapping must be an object') };
    }

    // Validate against mapping schema
    const schemaValidation = MappingConfigSchema.validate(mapping);
    if (schemaValidation.error) {
      return schemaValidation;
    }

    // Validate that mapped fields exist in unified schema
    const unifiedSchemaKeys = this._extractSchemaKeys(this.unifiedSchema);
    const errors = [];

    for (const [targetField, mappingConfig] of Object.entries(mapping)) {
      // Skip internal fields and special cases
      if (targetField.startsWith('_') || targetField === 'id') {
        continue;
      }

      // Check if target field exists in unified schema
      if (!unifiedSchemaKeys.includes(targetField)) {
        errors.push(`Target field '${targetField}' does not exist in unified schema`);
      }

      // Validate transformation function exists if specified
      if (typeof mappingConfig === 'object' && mappingConfig.transform) {
        if (!this.transformationFunctions.has(mappingConfig.transform)) {
          errors.push(`Transformation function '${mappingConfig.transform}' not found for field '${targetField}'`);
        }
      }

      // Validate validation function exists if specified
      if (typeof mappingConfig === 'object' && mappingConfig.validate) {
        if (!this.validationFunctions.has(mappingConfig.validate)) {
          errors.push(`Validation function '${mappingConfig.validate}' not found for field '${targetField}'`);
        }
      }
    }

    if (errors.length > 0) {
      return { error: new Error(errors.join('; ')) };
    }

    return { error: null };
  }

  /**
   * Extract field keys from Joi schema
   * @private
   */
  _extractSchemaKeys(schema) {
    const keys = [];
    
    if (schema._ids && schema._ids._byKey) {
      // Extract keys from Joi schema
      for (const key of schema._ids._byKey.keys()) {
        keys.push(key);
      }
    }

    return keys;
  }

  /**
   * Register a custom transformation function
   * @param {string} name - Function name
   * @param {Function} func - Transformation function
   */
  registerTransformation(name, func) {
    if (typeof name !== 'string' || typeof func !== 'function') {
      throw new Error('Transformation name must be string and func must be function');
    }
    this.transformationFunctions.set(name, func);
  }

  /**
   * Register a custom validation function
   * @param {string} name - Function name
   * @param {Function} func - Validation function
   */
  registerValidation(name, func) {
    if (typeof name !== 'string' || typeof func !== 'function') {
      throw new Error('Validation name must be string and func must be function');
    }
    this.validationFunctions.set(name, func);
  }

  /**
   * Get transformation function
   * @param {string} name - Function name
   * @returns {Function|null} Transformation function or null if not found
   */
  getTransformation(name) {
    return this.transformationFunctions.get(name) || null;
  }

  /**
   * Get validation function
   * @param {string} name - Function name
   * @returns {Function|null} Validation function or null if not found
   */
  getValidation(name) {
    return this.validationFunctions.get(name) || null;
  }

  /**
   * Load mapping configuration from JSON file
   * @param {string} filePath - Path to JSON configuration file
   * @returns {Promise<Object>} Loaded mapping configuration
   */
  async loadMappingFromFile(filePath) {
    try {
      const fileContent = await fs.readFile(filePath, 'utf8');
      const mappingConfig = JSON.parse(fileContent);
      
      // Validate the loaded configuration
      const validation = this.validateMapping(mappingConfig);
      if (validation.error) {
        throw new Error(`Invalid mapping configuration in ${filePath}: ${validation.error.message}`);
      }

      return mappingConfig;
    } catch (error) {
      if (error.code === 'ENOENT') {
        throw new Error(`Mapping file not found: ${filePath}`);
      } else if (error instanceof SyntaxError) {
        throw new Error(`Invalid JSON in mapping file ${filePath}: ${error.message}`);
      }
      throw error;
    }
  }

  /**
   * Load and register mapping from JSON file
   * @param {string} source - Source identifier
   * @param {string} filePath - Path to JSON configuration file
   */
  async loadAndRegisterMapping(source, filePath) {
    const mappingConfig = await this.loadMappingFromFile(filePath);
    this.registerMapping(source, mappingConfig);
  }

  /**
   * Load mappings from a directory containing JSON files
   * @param {string} directoryPath - Path to directory containing mapping files
   * @param {string} filePattern - File pattern to match (default: '*.json')
   */
  async loadMappingsFromDirectory(directoryPath, filePattern = /\.json$/) {
    try {
      const files = await fs.readdir(directoryPath);
      const mappingFiles = files.filter(file => filePattern.test(file));

      for (const file of mappingFiles) {
        const filePath = path.join(directoryPath, file);
        const source = path.basename(file, '.json'); // Use filename as source identifier
        
        try {
          await this.loadAndRegisterMapping(source, filePath);
        } catch (error) {
          console.warn(`Failed to load mapping for ${source} from ${filePath}: ${error.message}`);
        }
      }
    } catch (error) {
      throw new Error(`Failed to load mappings from directory ${directoryPath}: ${error.message}`);
    }
  }

  /**
   * Export current mappings to JSON
   * @param {string} source - Source identifier (optional, exports all if not specified)
   * @returns {Object} Mapping configuration as JSON-serializable object
   */
  exportMappings(source = null) {
    if (source) {
      return this.getAllMappings(source);
    }

    const allMappings = {};
    for (const [src, mappings] of this.mappings.entries()) {
      allMappings[src] = mappings;
    }
    return allMappings;
  }

  /**
   * Get registry statistics
   * @returns {Object} Statistics about the registry
   */
  getStats() {
    const stats = {
      totalSources: this.mappings.size,
      sources: [],
      totalTransformations: this.transformationFunctions.size,
      totalValidations: this.validationFunctions.size
    };

    for (const [source, mappings] of this.mappings.entries()) {
      stats.sources.push({
        source,
        fieldCount: Object.keys(mappings).length
      });
    }

    return stats;
  }
}

module.exports = {
  FieldMappingRegistry,
  MappingConfigSchema
};