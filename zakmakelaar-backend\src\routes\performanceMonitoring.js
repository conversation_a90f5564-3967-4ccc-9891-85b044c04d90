/**
 * Performance Monitoring Routes
 * 
 * This module provides API endpoints for accessing performance metrics
 * from the unified schema transformation pipeline.
 */

const express = require('express');
const router = express.Router();
const { performanceMonitor } = require('../monitoring/performanceMonitor');
const { DatabaseOptimizer } = require('../services/databaseOptimizer');
const { OptimizedSchemaTransformer } = require('../services/transformationOptimizer');
const { loggers } = require('../services/logger');

// Get current performance metrics
router.get('/performance', async (req, res) => {
  try {
    const metrics = performanceMonitor.getMetrics();
    res.json(metrics);
  } catch (error) {
    loggers.error.error('Failed to get performance metrics', {
      error: error.message,
      stack: error.stack
    });
    res.status(500).json({ error: 'Failed to get performance metrics' });
  }
});

// Reset performance metrics
router.post('/performance/reset', (req, res) => {
  try {
    performanceMonitor.resetMetrics();
    res.json({ success: true, message: 'Performance metrics reset' });
  } catch (error) {
    loggers.error.error('Failed to reset performance metrics', {
      error: error.message,
      stack: error.stack
    });
    res.status(500).json({ error: 'Failed to reset performance metrics' });
  }
});

// Get database statistics
router.get('/database-stats', async (req, res) => {
  try {
    const Property = req.app.get('models').Property;
    const stats = await DatabaseOptimizer.getDatabaseStats(Property);
    res.json(stats);
  } catch (error) {
    loggers.error.error('Failed to get database statistics', {
      error: error.message,
      stack: error.stack
    });
    res.status(500).json({ error: 'Failed to get database statistics' });
  }
});

// Get transformer statistics
router.get('/transformer-stats', (req, res) => {
  try {
    const transformer = req.app.get('services').optimizedTransformer;
    
    if (!transformer || !(transformer instanceof OptimizedSchemaTransformer)) {
      return res.status(404).json({ error: 'Optimized transformer not found' });
    }
    
    const stats = transformer.getStats();
    res.json(stats);
  } catch (error) {
    loggers.error.error('Failed to get transformer statistics', {
      error: error.message,
      stack: error.stack
    });
    res.status(500).json({ error: 'Failed to get transformer statistics' });
  }
});

// Clear transformation cache
router.post('/transformer/clear-cache', (req, res) => {
  try {
    const transformer = req.app.get('services').optimizedTransformer;
    
    if (!transformer || !(transformer instanceof OptimizedSchemaTransformer)) {
      return res.status(404).json({ error: 'Optimized transformer not found' });
    }
    
    transformer.clearCache();
    res.json({ success: true, message: 'Transformation cache cleared' });
  } catch (error) {
    loggers.error.error('Failed to clear transformation cache', {
      error: error.message,
      stack: error.stack
    });
    res.status(500).json({ error: 'Failed to clear transformation cache' });
  }
});

// Get performance dashboard
router.get('/dashboard', (req, res) => {
  res.sendFile('performance-dashboard.html', { root: './public' });
});

module.exports = router;