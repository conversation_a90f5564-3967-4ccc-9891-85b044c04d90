import React from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';

const { width, height } = Dimensions.get('window');

interface FuturisticGridProps {
  cellSize?: number;
  lineColor?: string;
  lineWidth?: number;
}

export const FuturisticGrid: React.FC<FuturisticGridProps> = ({
  cellSize = 40,
  lineColor = 'rgba(73, 80, 170, 0.15)',
  lineWidth = 1,
}) => {
  // Calculate number of horizontal and vertical lines needed
  const horizontalLines = Math.ceil(height / cellSize) + 1;
  const verticalLines = Math.ceil(width / cellSize) + 1;
  
  return (
    <View style={styles.container}>
      {/* Horizontal lines */}
      {Array.from({ length: horizontalLines }).map((_, index) => (
        <View
          key={`h-${index}`}
          style={[
            styles.horizontalLine,
            {
              top: index * cellSize,
              borderTopWidth: lineWidth,
              borderColor: lineColor,
            },
          ]}
        />
      ))}
      
      {/* Vertical lines */}
      {Array.from({ length: verticalLines }).map((_, index) => (
        <View
          key={`v-${index}`}
          style={[
            styles.verticalLine,
            {
              left: index * cellSize,
              borderLeftWidth: lineWidth,
              borderColor: lineColor,
            },
          ]}
        />
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    ...StyleSheet.absoluteFillObject,
    overflow: 'hidden',
  },
  horizontalLine: {
    position: 'absolute',
    width: width,
    height: 0,
  },
  verticalLine: {
    position: 'absolute',
    width: 0,
    height: height,
  },
});