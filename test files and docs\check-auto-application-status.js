// Script to check auto-application status and debug why no applications are being created
const mongoose = require('mongoose');

// Connect to MongoDB (adjust connection string as needed)
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/zakmakelaar';

async function checkAutoApplicationStatus() {
  try {
    console.log('🔍 Checking Auto-Application Status\n');
    console.log('=' .repeat(60));

    // Connect to database
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Import models
    const AutoApplicationSettings = require('./zakmakelaar-backend/src/models/AutoApplicationSettings');
    const ApplicationQueue = require('./zakmakelaar-backend/src/models/ApplicationQueue');
    const User = require('./zakmakelaar-backend/src/models/User');
    const Listing = require('./zakmakelaar-backend/src/models/Listing');

    // Check 1: Are there any users with auto-application enabled?
    console.log('\n📋 Step 1: Checking users with auto-application enabled...');
    const enabledUsers = await AutoApplicationSettings.find({ enabled: true });
    console.log(`Found ${enabledUsers.length} users with auto-application enabled`);

    if (enabledUsers.length === 0) {
      console.log('❌ No users have auto-application enabled!');
      console.log('💡 This is why no autonomous applications are being created.');
      
      // Check if there are any auto-application settings at all
      const allSettings = await AutoApplicationSettings.find({});
      console.log(`Total auto-application settings records: ${allSettings.length}`);
      
      if (allSettings.length > 0) {
        console.log('\n📊 Auto-application settings found:');
        allSettings.forEach((setting, index) => {
          console.log(`${index + 1}. User: ${setting.userId}, Enabled: ${setting.enabled}`);
        });
      }
    } else {
      console.log('\n📊 Enabled users:');
      enabledUsers.forEach((setting, index) => {
        console.log(`${index + 1}. User: ${setting.userId}`);
        console.log(`   - Max applications per day: ${setting.settings?.maxApplicationsPerDay || 'Not set'}`);
        console.log(`   - Criteria: ${JSON.stringify(setting.criteria || {})}`);
        console.log(`   - Status: ${setting.status?.isActive ? 'Active' : 'Inactive'}`);
      });
    }

    // Check 2: Are there any items in the application queue?
    console.log('\n📋 Step 2: Checking application queue...');
    const queueItems = await ApplicationQueue.find({}).sort({ createdAt: -1 }).limit(10);
    console.log(`Found ${queueItems.length} items in application queue`);

    if (queueItems.length > 0) {
      console.log('\n📊 Recent queue items:');
      queueItems.forEach((item, index) => {
        console.log(`${index + 1}. Status: ${item.status}, User: ${item.userId}, Listing: ${item.listingId}`);
        console.log(`   - Scheduled: ${item.scheduledAt}`);
        console.log(`   - Attempts: ${item.attempts}/${item.maxAttempts}`);
      });
    } else {
      console.log('❌ No items in application queue!');
    }

    // Check 3: Are there recent listings that should trigger auto-applications?
    console.log('\n📋 Step 3: Checking recent listings...');
    const recentListings = await Listing.find({
      dateAdded: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) } // Last 24 hours
    }).sort({ dateAdded: -1 }).limit(5);
    
    console.log(`Found ${recentListings.length} listings added in the last 24 hours`);
    
    if (recentListings.length > 0) {
      console.log('\n📊 Recent listings:');
      recentListings.forEach((listing, index) => {
        console.log(`${index + 1}. ${listing.title} - €${listing.price} - ${listing.location}`);
        console.log(`   - Added: ${listing.dateAdded}`);
        console.log(`   - Source: ${listing.source}`);
      });
    }

    // Check 4: Total users in system
    console.log('\n📋 Step 4: Checking total users...');
    const totalUsers = await User.countDocuments();
    console.log(`Total users in system: ${totalUsers}`);

    // Summary and recommendations
    console.log('\n' + '='.repeat(60));
    console.log('🎯 DIAGNOSIS SUMMARY:');
    
    if (enabledUsers.length === 0) {
      console.log('❌ ROOT CAUSE: No users have auto-application enabled');
      console.log('\n💡 SOLUTIONS:');
      console.log('1. Enable auto-application for at least one user');
      console.log('2. Use the frontend to configure auto-application settings');
      console.log('3. Or use the API endpoint: POST /api/auto-application/enable');
    } else if (queueItems.length === 0) {
      console.log('❌ ISSUE: Users have auto-application enabled but no queue items');
      console.log('\n💡 POSSIBLE CAUSES:');
      console.log('1. User criteria are too restrictive (no listings match)');
      console.log('2. Auto-application service is not processing new listings');
      console.log('3. No new listings have been added recently');
    } else {
      console.log('✅ Auto-application system appears to be configured correctly');
      console.log('🔍 Check server logs for processing details');
    }

    console.log('\n🔧 NEXT STEPS:');
    console.log('1. Restart the backend server (auto-application service is now initialized)');
    console.log('2. Enable auto-application for a test user');
    console.log('3. Check server logs for auto-application processing messages');
    console.log('4. Monitor the application queue for new items');

  } catch (error) {
    console.error('❌ Error checking auto-application status:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n✅ Disconnected from MongoDB');
  }
}

// Run the check
checkAutoApplicationStatus().catch(console.error);