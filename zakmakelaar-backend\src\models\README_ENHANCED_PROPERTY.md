# Enhanced Property Model with Unified Schema Support

This document describes the enhanced Property model that includes unified schema support for standardized property data across all scrapers (Funda, Huurwoningen, Pararius).

## Overview

The enhanced Property model extends the existing Property model with additional fields to support:

1. Unified schema data storage
2. Source metadata tracking for scraping provenance
3. Data quality indicators and validation results
4. Processing metadata and error tracking
5. Raw data preservation for debugging and reprocessing

## Model Structure

### Unified Data Fields

```javascript
unifiedData: {
  type: Schema.Types.Mixed,
  default: {}
}
```

This field stores the complete unified property data as transformed by the schema transformer. It preserves the standardized format for all properties regardless of source.

### Source Metadata

```javascript
sourceMetadata: {
  website: { type: String }, // 'funda.nl', 'huurwoningen.nl', 'pararius.nl'
  externalId: { type: String },
  scraperId: { type: String },
  scrapedAt: { type: Date },
  lastUpdated: { type: Date },
  version: { type: Number, default: 1 }
}
```

These fields track the provenance of scraped data, including which website it came from, when it was scraped, and version information.

### Data Quality Indicators

```javascript
dataQuality: {
  completeness: { type: Number, min: 0, max: 100, default: 0 }, // Percentage of fields filled
  accuracy: { type: Number, min: 0, max: 100, default: 0 }, // Confidence score
  lastValidated: { type: Date },
  validationErrors: [{ type: String }],
  validationWarnings: [{ type: String }]
}
```

These fields provide metrics on the quality of the scraped data, including completeness scores, accuracy estimates, and validation results.

### Processing Metadata

```javascript
processingMetadata: {
  transformationVersion: { type: String },
  processingTime: { type: Number }, // in milliseconds
  processingDate: { type: Date, default: Date.now },
  errors: [{ 
    type: { type: String },
    message: { type: String },
    field: { type: String },
    timestamp: { type: Date, default: Date.now }
  }],
  warnings: [{
    type: { type: String },
    message: { type: String },
    field: { type: String },
    timestamp: { type: Date, default: Date.now }
  }]
}
```

These fields track information about the transformation process, including performance metrics and any errors or warnings encountered.

### Raw Data Preservation

```javascript
rawData: {
  original: { type: Schema.Types.Mixed },
  processed: { type: Schema.Types.Mixed },
  metadata: { type: Schema.Types.Mixed }
}
```

This field preserves the original scraped data for debugging and reprocessing purposes.

### Migration Tracking

```javascript
isLegacyMigrated: { type: Boolean, default: false },
originalListingId: { type: Schema.Types.ObjectId, ref: 'Listing' }
```

These fields track whether a property was migrated from a legacy Listing and maintain a reference to the original Listing document.

## Indexes

The enhanced Property model includes additional indexes for efficient querying:

```javascript
// Single field indexes
propertySchema.index({ 'sourceMetadata.website': 1 });
propertySchema.index({ 'sourceMetadata.externalId': 1, 'sourceMetadata.website': 1 }, { unique: true, sparse: true });
propertySchema.index({ 'dataQuality.completeness': 1 });
propertySchema.index({ 'dataQuality.accuracy': 1 });
propertySchema.index({ 'sourceMetadata.scrapedAt': -1 });
propertySchema.index({ 'processingMetadata.processingDate': -1 });
propertySchema.index({ isLegacyMigrated: 1 });

// Compound indexes
propertySchema.index({ 'sourceMetadata.website': 1, 'dataQuality.completeness': -1 });
propertySchema.index({ 'sourceMetadata.website': 1, 'sourceMetadata.scrapedAt': -1 });
```

## Methods

### Instance Methods

#### `updateFromUnifiedSchema(unifiedData)`

Updates the property document with data from the unified schema format.

```javascript
const property = await Property.findById(id);
property.updateFromUnifiedSchema(unifiedData);
await property.save();
```

### Static Methods

#### `createFromUnifiedSchema(unifiedData)`

Creates a new property document from unified schema data.

```javascript
const property = await Property.createFromUnifiedSchema(unifiedData);
await property.save();
```

#### `findBySourceMetadata(website, externalId)`

Finds a property by its source website and external ID.

```javascript
const property = await Property.findBySourceMetadata('funda.nl', '12345');
```

#### `findWithQualityIssues(minCompleteness, minAccuracy)`

Finds properties with data quality issues.

```javascript
const propertiesWithIssues = await Property.findWithQualityIssues(50, 50);
```

## Migration Scripts

Three migration scripts are provided to support the enhanced Property model:

1. `002-enhance-property-model.js` - Adds the new unified schema fields to existing Property documents
2. `003-migrate-listings-to-properties.js` - Migrates data from the legacy Listing model to the enhanced Property model

### Running Migrations

To run the migrations:

```bash
node migrations/002-enhance-property-model.js
node migrations/003-migrate-listings-to-properties.js
```

## Usage Examples

### Creating a Property from Unified Schema Data

```javascript
const { createMinimalProperty } = require('../schemas/unifiedPropertySchema');
const Property = require('./EnhancedProperty');

// Create unified data
const unifiedData = createMinimalProperty({
  title: 'Modern Apartment in Amsterdam',
  description: 'Beautiful apartment in the center of Amsterdam',
  price: '€1,500 per month',
  location: 'Amsterdam, Netherlands',
  source: 'funda.nl',
  url: 'https://www.funda.nl/apartment/123',
  propertyType: 'apartment',
  size: '85 m²',
  rooms: '3',
  bedrooms: '2'
});

// Create property from unified data
const property = await Property.createFromUnifiedSchema(unifiedData);
await property.save();
```

### Updating a Property with Unified Schema Data

```javascript
const Property = require('./EnhancedProperty');
const { SchemaTransformer } = require('../services/schemaTransformer');
const { FieldMappingRegistry } = require('../services/fieldMappingRegistry');

// Get property
const property = await Property.findById(id);

// Transform raw data to unified schema
const transformer = new SchemaTransformer(new FieldMappingRegistry());
const unifiedData = await transformer.transform(rawData, 'funda.nl');

// Update property with unified data
property.updateFromUnifiedSchema(unifiedData);
await property.save();
```

### Finding Properties by Source

```javascript
const Property = require('./EnhancedProperty');

// Find all properties from a specific source
const fundaProperties = await Property.find({
  'sourceMetadata.website': 'funda.nl'
});

// Find properties with high data quality
const highQualityProperties = await Property.find({
  'dataQuality.completeness': { $gte: 90 },
  'dataQuality.accuracy': { $gte: 80 }
});
```