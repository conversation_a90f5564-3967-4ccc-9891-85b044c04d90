/**
 * Setup test user via API calls
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3000';

const TEST_USER = {
  email: '<EMAIL>',
  password: 'Password123',
  firstName: 'Wellis',
  lastName: 'Hant',
  phone: '+31612345678'
};

async function setupTestUser() {
  try {
    console.log('🔧 Setting up test user via API...');

    // Try to register the user
    try {
      console.log('👤 Registering test user...');
      const registerResponse = await axios.post(`${API_BASE_URL}/api/auth/register`, {
        email: TEST_USER.email,
        password: TEST_USER.password,
        firstName: TEST_USER.firstName,
        lastName: TEST_USER.lastName,
        phone: TEST_USER.phone
      });

      if (registerResponse.data.status === 'success') {
        console.log('✅ User registered successfully');
      }
    } catch (error) {
      if (error.response?.data?.message?.includes('already exists')) {
        console.log('✅ User already exists');
      } else {
        console.error('❌ Registration failed:', error.response?.data || error.message);
        return false;
      }
    }

    // Login to get token
    console.log('🔐 Logging in...');
    const loginResponse = await axios.post(`${API_BASE_URL}/api/auth/login`, {
      email: TEST_USER.email,
      password: TEST_USER.password
    });

    if (loginResponse.data.status !== 'success') {
      console.error('❌ Login failed:', loginResponse.data);
      return false;
    }

    const token = loginResponse.data.token;
    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };

    console.log('✅ Login successful');

    // Enable auto application
    try {
      console.log('⚙️ Enabling auto application...');
      const autoAppResponse = await axios.post(`${API_BASE_URL}/api/auto-application/enable`, {
        settings: {
          maxApplicationsPerDay: 10,
          applicationTemplate: 'professional',
          autoSubmit: true,
          requireManualReview: false,
          language: 'dutch'
        },
        criteria: {
          maxPrice: 2000,
          minRooms: 1,
          maxRooms: 5,
          propertyTypes: ['woning', 'apartment'],
          locations: ['Utrecht', 'Amsterdam'],
          minSize: 20,
          maxSize: 150
        },
        personalInfo: {
          fullName: `${TEST_USER.firstName} ${TEST_USER.lastName}`,
          email: TEST_USER.email,
          phone: TEST_USER.phone,
          monthlyIncome: 3000,
          occupation: 'Software Developer',
          moveInDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
          numberOfOccupants: 1
        }
      }, { headers });

      if (autoAppResponse.data.status === 'success') {
        console.log('✅ Auto application enabled');
      } else {
        console.log('⚠️ Auto application enable response:', autoAppResponse.data);
      }
    } catch (error) {
      console.error('❌ Auto application setup failed:', error.response?.data || error.message);
    }

    console.log('\n🎉 Test user setup completed!');
    console.log(`📧 Email: ${TEST_USER.email}`);
    console.log(`🔑 Password: ${TEST_USER.password}`);
    console.log(`👤 Name: ${TEST_USER.firstName} ${TEST_USER.lastName}`);
    console.log(`📱 Phone: ${TEST_USER.phone}`);

    return true;

  } catch (error) {
    console.error('❌ Setup failed:', error.message);
    return false;
  }
}

// Run setup
if (require.main === module) {
  setupTestUser().catch(console.error);
}

module.exports = setupTestUser;
