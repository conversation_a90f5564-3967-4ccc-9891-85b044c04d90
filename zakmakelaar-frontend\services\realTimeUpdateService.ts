import { useAIStore, PropertyMatch } from '../store/aiStore';
import { useAuthStore } from '../store/authStore';
import { useNotificationStore } from '../store/notificationStore';
import { aiService } from './aiService';
import { UserPreferences } from './authService';

// Refresh interval in milliseconds
const DEFAULT_REFRESH_INTERVAL = 30 * 60 * 1000; // 30 minutes

// Interface for refresh strategy
export interface RefreshStrategy {
  interval: number; // in milliseconds
  enabled: boolean;
  lastRefresh: Date | null;
  nextScheduledRefresh: Date | null;
}

// Class for handling real-time updates
export class RealTimeUpdateService {
  private static instance: RealTimeUpdateService;
  private refreshInterval: NodeJS.Timeout | null = null;
  private refreshStrategy: RefreshStrategy = {
    interval: DEFAULT_REFRESH_INTERVAL,
    enabled: true,
    lastRefresh: null,
    nextScheduledRefresh: null,
  };
  
  // Private constructor for singleton pattern
  private constructor() {}
  
  // Get singleton instance
  public static getInstance(): RealTimeUpdateService {
    if (!RealTimeUpdateService.instance) {
      RealTimeUpdateService.instance = new RealTimeUpdateService();
    }
    return RealTimeUpdateService.instance;
  }
  
  // Start real-time updates
  public startRealTimeUpdates(interval: number = DEFAULT_REFRESH_INTERVAL): void {
    // Clear existing interval if any
    this.stopRealTimeUpdates();
    
    // Update refresh strategy
    this.refreshStrategy = {
      ...this.refreshStrategy,
      interval,
      enabled: true,
      nextScheduledRefresh: new Date(Date.now() + interval),
    };
    
    // Set new interval
    this.refreshInterval = setInterval(() => {
      this.refreshMatches();
    }, interval);
    
    console.log(`Real-time updates started with interval: ${interval / 1000} seconds`);
  }
  
  // Stop real-time updates
  public stopRealTimeUpdates(): void {
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval);
      this.refreshInterval = null;
      
      // Update refresh strategy
      this.refreshStrategy = {
        ...this.refreshStrategy,
        enabled: false,
        nextScheduledRefresh: null,
      };
      
      console.log('Real-time updates stopped');
    }
  }
  
  // Get current refresh strategy
  public getRefreshStrategy(): RefreshStrategy {
    return { ...this.refreshStrategy };
  }
  
  // Update refresh strategy
  public updateRefreshStrategy(strategy: Partial<RefreshStrategy>): void {
    this.refreshStrategy = {
      ...this.refreshStrategy,
      ...strategy,
    };
    
    // Restart updates if interval changed and enabled
    if (strategy.interval && strategy.enabled !== false && this.refreshInterval) {
      this.startRealTimeUpdates(strategy.interval);
    } else if (strategy.enabled === false) {
      this.stopRealTimeUpdates();
    } else if (strategy.enabled === true && !this.refreshInterval) {
      this.startRealTimeUpdates(this.refreshStrategy.interval);
    }
  }
  
  // Refresh matches immediately
  public async refreshMatches(): Promise<boolean> {
    try {
      const user = useAuthStore.getState().user;
      
      // Skip if no user or no preferences
      if (!user || !user.preferences) {
        return false;
      }
      
      // Get AI store state
      const aiStore = useAIStore.getState();
      
      // Update last refresh time
      this.refreshStrategy.lastRefresh = new Date();
      this.refreshStrategy.nextScheduledRefresh = new Date(Date.now() + this.refreshStrategy.interval);
      
      // Run property matching
      const response = await aiService.getPropertyMatches({
        userProfile: user,
        preferences: user.preferences,
        maxResults: 50,
      });
      
      if (response.success && response.data) {
        // Process new matches
        const newMatches: PropertyMatch[] = response.data.matches.map((match, index) => ({
          ...match,
          id: `match_${Date.now()}_${index}`,
          timestamp: new Date(),
          viewed: false,
          applied: false,
          saved: false,
        }));
        
        // Get existing matches
        const existingMatches = aiStore.matches;
        
        // Find truly new matches (not in existing matches)
        const trulyNewMatches = newMatches.filter(newMatch => 
          !existingMatches.some(existingMatch => 
            existingMatch.listing._id === newMatch.listing._id
          )
        );
        
        // Preserve user interactions (viewed, saved, applied) for existing matches
        const mergedMatches = newMatches.map(newMatch => {
          const existingMatch = existingMatches.find(
            match => match.listing._id === newMatch.listing._id
          );
          
          if (existingMatch) {
            return {
              ...newMatch,
              viewed: existingMatch.viewed,
              saved: existingMatch.saved,
              applied: existingMatch.applied,
            };
          }
          
          return newMatch;
        });
        
        // Update matches in store
        aiStore.matches = mergedMatches;
        aiStore.lastMatchUpdate = new Date();
        
        // Send notification for new matches
        if (trulyNewMatches.length > 0) {
          const notificationStore = useNotificationStore.getState();
          
          await notificationStore.scheduleLocalNotification({
            title: `${trulyNewMatches.length} New Property Matches`,
            body: `We found ${trulyNewMatches.length} new properties that match your preferences!`,
            data: { type: 'new_matches', count: trulyNewMatches.length },
          });
        }
        
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Failed to refresh matches:', error);
      return false;
    }
  }
  
  // Learn from user interactions and update preferences
  public async learnFromUserInteractions(): Promise<boolean> {
    try {
      const user = useAuthStore.getState().user;
      
      // Skip if no user or no preferences
      if (!user || !user.preferences) {
        return false;
      }
      
      // Get AI store state
      const aiStore = useAIStore.getState();
      
      // Get user interactions
      const viewedMatches = aiStore.matches.filter(match => match.viewed);
      const savedMatches = aiStore.matches.filter(match => match.saved);
      const appliedMatches = aiStore.matches.filter(match => match.applied);
      
      // Skip if not enough interactions
      if (viewedMatches.length < 5 && savedMatches.length < 2) {
        return false;
      }
      
      // Extract preferences from interactions
      const learnedPreferences = this.extractPreferencesFromInteractions(
        user.preferences,
        viewedMatches,
        savedMatches,
        appliedMatches
      );
      
      // Update user preferences if significant changes
      if (this.hasSignificantChanges(user.preferences, learnedPreferences)) {
        // Don't actually update preferences automatically, just suggest changes
        // This could be implemented as a notification or suggestion to the user
        
        const notificationStore = useNotificationStore.getState();
        
        await notificationStore.scheduleLocalNotification({
          title: 'Preference Suggestions Available',
          body: 'Based on your activity, we have suggestions to improve your property matches.',
          data: { 
            type: 'preference_suggestions',
            suggestions: this.formatPreferenceSuggestions(user.preferences, learnedPreferences)
          },
        });
      }
      
      return true;
    } catch (error) {
      console.error('Failed to learn from user interactions:', error);
      return false;
    }
  }
  
  // Extract preferences from user interactions
  private extractPreferencesFromInteractions(
    currentPreferences: UserPreferences,
    viewedMatches: PropertyMatch[],
    savedMatches: PropertyMatch[],
    appliedMatches: PropertyMatch[]
  ): Partial<UserPreferences> {
    // Initialize learned preferences with current preferences
    const learnedPreferences: Partial<UserPreferences> = { ...currentPreferences };
    
    // Prioritize applied > saved > viewed matches
    const prioritizedMatches = [
      ...appliedMatches.map(match => ({ match, weight: 3 })),
      ...savedMatches.map(match => ({ match, weight: 2 })),
      ...viewedMatches.map(match => ({ match, weight: 1 })),
    ];
    
    // Extract price preferences
    if (prioritizedMatches.length > 0) {
      const prices = prioritizedMatches.map(({ match, weight }) => ({
        price: typeof match.listing.price === 'string' 
          ? parseFloat(match.listing.price.replace(/[^0-9.]/g, ''))
          : match.listing.price,
        weight
      }));
      
      // Calculate weighted average min and max prices
      const validPrices = prices.filter(p => !isNaN(p.price));
      if (validPrices.length > 0) {
        const totalWeight = validPrices.reduce((sum, p) => sum + p.weight, 0);
        const weightedSum = validPrices.reduce((sum, p) => sum + p.price * p.weight, 0);
        const weightedAvg = weightedSum / totalWeight;
        
        // Set min and max prices based on weighted average
        learnedPreferences.minPrice = Math.max(
          Math.round(weightedAvg * 0.8),
          currentPreferences.minPrice * 0.9
        );
        learnedPreferences.maxPrice = Math.min(
          Math.round(weightedAvg * 1.2),
          currentPreferences.maxPrice * 1.1
        );
      }
    }
    
    // Extract location preferences
    const locationCounts: Record<string, number> = {};
    prioritizedMatches.forEach(({ match, weight }) => {
      const location = typeof match.listing.location === 'string'
        ? match.listing.location
        : match.listing.location.city || '';
      
      if (location) {
        locationCounts[location] = (locationCounts[location] || 0) + weight;
      }
    });
    
    // Get top locations
    const topLocations = Object.entries(locationCounts)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 3)
      .map(([location]) => location);
    
    if (topLocations.length > 0) {
      learnedPreferences.preferredLocations = [
        ...topLocations,
        ...currentPreferences.preferredLocations?.filter(loc => !topLocations.includes(loc)) || []
      ].slice(0, 5); // Limit to 5 locations
    }
    
    // Extract property type preferences
    const propertyTypeCounts: Record<string, number> = {};
    prioritizedMatches.forEach(({ match, weight }) => {
      const propertyType = match.listing.propertyType;
      if (propertyType) {
        propertyTypeCounts[propertyType] = (propertyTypeCounts[propertyType] || 0) + weight;
      }
    });
    
    // Get top property types
    const topPropertyTypes = Object.entries(propertyTypeCounts)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 3)
      .map(([type]) => type);
    
    if (topPropertyTypes.length > 0) {
      learnedPreferences.propertyTypes = [
        ...topPropertyTypes,
        ...currentPreferences.propertyTypes?.filter(type => !topPropertyTypes.includes(type)) || []
      ];
    }
    
    // Extract room preferences
    const roomCounts: number[] = [];
    prioritizedMatches.forEach(({ match, weight }) => {
      const rooms = typeof match.listing.rooms === 'string'
        ? parseInt(match.listing.rooms, 10)
        : match.listing.rooms;
      
      if (!isNaN(rooms)) {
        for (let i = 0; i < weight; i++) {
          roomCounts.push(rooms);
        }
      }
    });
    
    if (roomCounts.length > 0) {
      // Sort room counts
      roomCounts.sort((a, b) => a - b);
      
      // Get min and max rooms (10th and 90th percentiles to avoid outliers)
      const minIndex = Math.floor(roomCounts.length * 0.1);
      const maxIndex = Math.floor(roomCounts.length * 0.9);
      
      learnedPreferences.minRooms = roomCounts[minIndex];
      learnedPreferences.maxRooms = roomCounts[maxIndex];
    }
    
    // Extract amenity preferences
    const amenityCounts: Record<string, number> = {
      furnished: 0,
      pets: 0,
      smoking: 0,
      garden: 0,
      balcony: 0,
      parking: 0,
    };
    
    prioritizedMatches.forEach(({ match, weight }) => {
      if (match.listing.furnished) amenityCounts.furnished += weight;
      if (match.listing.pets) amenityCounts.pets += weight;
      if (match.listing.smoking) amenityCounts.smoking += weight;
      if (match.listing.garden) amenityCounts.garden += weight;
      if (match.listing.balcony) amenityCounts.balcony += weight;
      if (match.listing.parking) amenityCounts.parking += weight;
    });
    
    // Get top amenities
    const topAmenities = Object.entries(amenityCounts)
      .filter(([_, count]) => count > prioritizedMatches.length * 0.3) // At least 30% of matches have this amenity
      .map(([amenity]) => amenity);
    
    if (topAmenities.length > 0) {
      learnedPreferences.amenities = [
        ...topAmenities,
        ...currentPreferences.amenities?.filter(amenity => !topAmenities.includes(amenity)) || []
      ];
    }
    
    return learnedPreferences;
  }
  
  // Check if there are significant changes between current and learned preferences
  private hasSignificantChanges(
    currentPreferences: UserPreferences,
    learnedPreferences: Partial<UserPreferences>
  ): boolean {
    // Check price changes
    if (
      learnedPreferences.minPrice &&
      Math.abs(learnedPreferences.minPrice - currentPreferences.minPrice) > currentPreferences.minPrice * 0.1
    ) {
      return true;
    }
    
    if (
      learnedPreferences.maxPrice &&
      Math.abs(learnedPreferences.maxPrice - currentPreferences.maxPrice) > currentPreferences.maxPrice * 0.1
    ) {
      return true;
    }
    
    // Check location changes
    if (
      learnedPreferences.preferredLocations &&
      !this.areArraysEqual(learnedPreferences.preferredLocations, currentPreferences.preferredLocations || [])
    ) {
      return true;
    }
    
    // Check property type changes
    if (
      learnedPreferences.propertyTypes &&
      !this.areArraysEqual(learnedPreferences.propertyTypes, currentPreferences.propertyTypes || [])
    ) {
      return true;
    }
    
    // Check room changes
    if (
      learnedPreferences.minRooms &&
      learnedPreferences.minRooms !== currentPreferences.minRooms
    ) {
      return true;
    }
    
    if (
      learnedPreferences.maxRooms &&
      learnedPreferences.maxRooms !== currentPreferences.maxRooms
    ) {
      return true;
    }
    
    // Check amenity changes
    if (
      learnedPreferences.amenities &&
      !this.areArraysEqual(learnedPreferences.amenities, currentPreferences.amenities || [])
    ) {
      return true;
    }
    
    return false;
  }
  
  // Helper to check if arrays are equal
  private areArraysEqual(arr1: any[], arr2: any[]): boolean {
    if (arr1.length !== arr2.length) return false;
    
    const sorted1 = [...arr1].sort();
    const sorted2 = [...arr2].sort();
    
    return sorted1.every((val, i) => val === sorted2[i]);
  }
  
  // Format preference suggestions for notification
  private formatPreferenceSuggestions(
    currentPreferences: UserPreferences,
    learnedPreferences: Partial<UserPreferences>
  ): { field: string; current: any; suggested: any }[] {
    const suggestions: { field: string; current: any; suggested: any }[] = [];
    
    // Price suggestions
    if (
      learnedPreferences.minPrice &&
      Math.abs(learnedPreferences.minPrice - currentPreferences.minPrice) > currentPreferences.minPrice * 0.1
    ) {
      suggestions.push({
        field: 'minPrice',
        current: currentPreferences.minPrice,
        suggested: learnedPreferences.minPrice,
      });
    }
    
    if (
      learnedPreferences.maxPrice &&
      Math.abs(learnedPreferences.maxPrice - currentPreferences.maxPrice) > currentPreferences.maxPrice * 0.1
    ) {
      suggestions.push({
        field: 'maxPrice',
        current: currentPreferences.maxPrice,
        suggested: learnedPreferences.maxPrice,
      });
    }
    
    // Location suggestions
    if (
      learnedPreferences.preferredLocations &&
      !this.areArraysEqual(learnedPreferences.preferredLocations, currentPreferences.preferredLocations || [])
    ) {
      suggestions.push({
        field: 'preferredLocations',
        current: currentPreferences.preferredLocations || [],
        suggested: learnedPreferences.preferredLocations,
      });
    }
    
    // Property type suggestions
    if (
      learnedPreferences.propertyTypes &&
      !this.areArraysEqual(learnedPreferences.propertyTypes, currentPreferences.propertyTypes || [])
    ) {
      suggestions.push({
        field: 'propertyTypes',
        current: currentPreferences.propertyTypes || [],
        suggested: learnedPreferences.propertyTypes,
      });
    }
    
    // Room suggestions
    if (
      learnedPreferences.minRooms &&
      learnedPreferences.minRooms !== currentPreferences.minRooms
    ) {
      suggestions.push({
        field: 'minRooms',
        current: currentPreferences.minRooms,
        suggested: learnedPreferences.minRooms,
      });
    }
    
    if (
      learnedPreferences.maxRooms &&
      learnedPreferences.maxRooms !== currentPreferences.maxRooms
    ) {
      suggestions.push({
        field: 'maxRooms',
        current: currentPreferences.maxRooms,
        suggested: learnedPreferences.maxRooms,
      });
    }
    
    // Amenity suggestions
    if (
      learnedPreferences.amenities &&
      !this.areArraysEqual(learnedPreferences.amenities, currentPreferences.amenities || [])
    ) {
      suggestions.push({
        field: 'amenities',
        current: currentPreferences.amenities || [],
        suggested: learnedPreferences.amenities,
      });
    }
    
    return suggestions;
  }
}

// Export singleton instance
export const realTimeUpdateService = RealTimeUpdateService.getInstance();
export default realTimeUpdateService;