import { Ionicons } from "@expo/vector-icons";
import * as Haptics from "expo-haptics";
import React, { useEffect, useState } from "react";
import {
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import Animated, {
  Easing,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from "react-native-reanimated";
import { UserPreferences } from "../../services/authService";

import { validatePreferences } from "../../services/preferencesValidationService";
import { useAIStore } from "../../store/aiStore";
import { PreferencesSaveButton } from "./PreferencesSaveButton";

// Define theme colors
const THEME = {
  primary: "#4361ee",
  secondary: "#7209b7",
  accent: "#f72585",
  dark: "#0a0a18",
  light: "#ffffff",
  gray: "#6b7280",
};

interface SummaryStepProps {
  preferences: UserPreferences;
  onEditSection: (sectionIndex: number) => void;
  marketData?: any;
  onSavePreferences?: (preferences: UserPreferences) => Promise<boolean>;
  onComplete?: () => Promise<void>;
}

export const SummaryStep: React.FC<SummaryStepProps> = ({
  preferences,
  onEditSection,
  marketData,
  onSavePreferences,
  onComplete,
}) => {
  const { requestPropertyMatching } = useAIStore();

  // State
  const [isValid, setIsValid] = useState(false);
  const [validationScore, setValidationScore] = useState(0);
  const [isMatchingStarted, setIsMatchingStarted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Animation values
  const summaryOpacity = useSharedValue(0);

  // Show animations when component mounts
  useEffect(() => {
    summaryOpacity.value = withTiming(1, {
      duration: 500,
      easing: Easing.out(Easing.ease),
    });
  }, []);

  // Handle validation complete
  const handleValidationComplete = (result: any) => {
    setIsValid(result.isValid);
    setValidationScore(result.score);
  };

  // Handle edit section
  const handleEditSection = (sectionIndex: number) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    onEditSection(sectionIndex);
  };

  // Handle save preferences
  const handleSavePreferences = async (prefs: UserPreferences) => {
    if (onSavePreferences) {
      return await onSavePreferences(prefs);
    }
    return true;
  };

  // Handle completion button press - SIMPLIFIED
  const handleComplete = async () => {
    try {
      setIsLoading(true);

      // Save preferences first
      const saveSuccess = await handleSavePreferences(preferences);

      if (saveSuccess) {
        // Start AI matching in background (do not block navigation)
        handleMatchingStart().catch(() => {});

        // Call the onComplete callback and wait so the loader stays until navigation kicks in
        if (onComplete) {
          await onComplete();
        }
      }
    } catch (error) {
      console.error("Error completing preferences:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle matching start
  const handleMatchingStart = async () => {
    setIsMatchingStarted(true);

    try {
      // Request property matching from AI
      await requestPropertyMatching(preferences);
    } catch (error) {
      console.error("Error starting property matching:", error);
    }
  };

  // Format property types for display
  const formatPropertyTypes = (types: string[]) => {
    return types
      .map((type) => type.charAt(0).toUpperCase() + type.slice(1))
      .join(", ");
  };

  // Format amenities for display
  const formatAmenities = (amenities: string[]) => {
    if (!amenities || amenities.length === 0) return "None selected";

    return amenities
      .map((amenity) => amenity.charAt(0).toUpperCase() + amenity.slice(1))
      .join(", ");
  };

  // Animated styles
  const summaryAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: summaryOpacity.value,
    };
  });

  // Perform initial validation
  useEffect(() => {
    const result = validatePreferences(preferences, marketData);
    setIsValid(result.isValid);
    setValidationScore(result.score);
  }, [preferences, marketData]);

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <Animated.View style={[styles.content, summaryAnimatedStyle]}>
        <Text style={styles.title}>Review Your Preferences</Text>
        <Text style={styles.subtitle}>
          Make sure everything looks good before saving
        </Text>

        {/* Preferences Summary */}
        <View style={styles.summaryContainer}>
          {/* Location Section */}
          <View style={styles.sectionContainer}>
            <View style={styles.sectionHeader}>
              <View style={styles.sectionTitleContainer}>
                <Ionicons name="location" size={20} color={THEME.accent} />
                <Text style={styles.sectionTitle}>Locations</Text>
              </View>
              <TouchableOpacity
                style={styles.editButton}
                onPress={() => handleEditSection(1)}
              >
                <Ionicons name="pencil" size={16} color={THEME.gray} />
                <Text style={styles.editButtonText}>Edit</Text>
              </TouchableOpacity>
            </View>
            <View style={styles.sectionContent}>
              {preferences.preferredLocations &&
              preferences.preferredLocations.length > 0 ? (
                <View style={styles.locationChipsContainer}>
                  {preferences.preferredLocations.map((location) => (
                    <View key={location} style={styles.locationChip}>
                      <Text style={styles.locationChipText}>{location}</Text>
                    </View>
                  ))}
                </View>
              ) : (
                <Text style={styles.emptyText}>No locations selected</Text>
              )}
            </View>
          </View>

          {/* Budget Section */}
          <View style={styles.sectionContainer}>
            <View style={styles.sectionHeader}>
              <View style={styles.sectionTitleContainer}>
                <Ionicons name="cash" size={20} color={THEME.accent} />
                <Text style={styles.sectionTitle}>Budget</Text>
              </View>
              <TouchableOpacity
                style={styles.editButton}
                onPress={() => handleEditSection(2)}
              >
                <Ionicons name="pencil" size={16} color={THEME.gray} />
                <Text style={styles.editButtonText}>Edit</Text>
              </TouchableOpacity>
            </View>
            <View style={styles.sectionContent}>
              <Text style={styles.budgetText}>
                €{preferences.minPrice} - €{preferences.maxPrice} per month
              </Text>
            </View>
          </View>

          {/* Property Type Section */}
          <View style={styles.sectionContainer}>
            <View style={styles.sectionHeader}>
              <View style={styles.sectionTitleContainer}>
                <Ionicons name="home" size={20} color={THEME.accent} />
                <Text style={styles.sectionTitle}>Property Type</Text>
              </View>
              <TouchableOpacity
                style={styles.editButton}
                onPress={() => handleEditSection(3)}
              >
                <Ionicons name="pencil" size={16} color={THEME.gray} />
                <Text style={styles.editButtonText}>Edit</Text>
              </TouchableOpacity>
            </View>
            <View style={styles.sectionContent}>
              <Text style={styles.propertyTypeText}>
                {preferences.propertyTypes
                  ? formatPropertyTypes(preferences.propertyTypes)
                  : "No property types selected"}
              </Text>
              <Text style={styles.roomsText}>
                {preferences.minRooms} - {preferences.maxRooms} rooms
              </Text>
            </View>
          </View>

          {/* Amenities Section */}
          <View style={styles.sectionContainer}>
            <View style={styles.sectionHeader}>
              <View style={styles.sectionTitleContainer}>
                <Ionicons name="list" size={20} color={THEME.accent} />
                <Text style={styles.sectionTitle}>Amenities</Text>
              </View>
              <TouchableOpacity
                style={styles.editButton}
                onPress={() => handleEditSection(4)}
              >
                <Ionicons name="pencil" size={16} color={THEME.gray} />
                <Text style={styles.editButtonText}>Edit</Text>
              </TouchableOpacity>
            </View>
            <View style={styles.sectionContent}>
              <Text style={styles.amenitiesText}>
                {formatAmenities(preferences.amenities || [])}
              </Text>
            </View>
          </View>

          {/* Notifications Section */}
          <View style={styles.sectionContainer}>
            <View style={styles.sectionHeader}>
              <View style={styles.sectionTitleContainer}>
                <Ionicons name="notifications" size={20} color={THEME.accent} />
                <Text style={styles.sectionTitle}>Notifications</Text>
              </View>
              <TouchableOpacity
                style={styles.editButton}
                onPress={() => handleEditSection(5)}
              >
                <Ionicons name="pencil" size={16} color={THEME.gray} />
                <Text style={styles.editButtonText}>Edit</Text>
              </TouchableOpacity>
            </View>
            <View style={styles.sectionContent}>
              <View style={styles.notificationSettings}>
                <View style={styles.notificationSetting}>
                  <Text style={styles.notificationLabel}>Email</Text>
                  <Ionicons
                    name={
                      preferences.notifications?.email
                        ? "checkmark-circle"
                        : "close-circle"
                    }
                    size={20}
                    color={
                      preferences.notifications?.email ? "#10b981" : "#ef4444"
                    }
                  />
                </View>
                <View style={styles.notificationSetting}>
                  <Text style={styles.notificationLabel}>Push</Text>
                  <Ionicons
                    name={
                      preferences.notifications?.push
                        ? "checkmark-circle"
                        : "close-circle"
                    }
                    size={20}
                    color={
                      preferences.notifications?.push ? "#10b981" : "#ef4444"
                    }
                  />
                </View>
                <View style={styles.notificationSetting}>
                  <Text style={styles.notificationLabel}>SMS</Text>
                  <Ionicons
                    name={
                      preferences.notifications?.sms
                        ? "checkmark-circle"
                        : "close-circle"
                    }
                    size={20}
                    color={
                      preferences.notifications?.sms ? "#10b981" : "#ef4444"
                    }
                  />
                </View>
              </View>
            </View>
          </View>
        </View>

        {/* Save Button */}
        <PreferencesSaveButton
          onPress={handleComplete}
          isLoading={isLoading}
          isLastStep={true}
          label="Complete & Save Preferences"
        />

        {/* AI Matching Info */}
        <View style={styles.matchingInfoContainer}>
          <Ionicons
            name="information-circle-outline"
            size={20}
            color={THEME.gray}
          />
          <Text style={styles.matchingInfoText}>
            When you save your preferences, our AI will immediately start
            finding properties that match your criteria
          </Text>
        </View>
      </Animated.View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    paddingBottom: 24,
  },
  title: {
    fontSize: 20,
    fontWeight: "600",
    color: "#1f2937",
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 14,
    color: THEME.gray,
    marginBottom: 24,
  },
  summaryContainer: {
    marginBottom: 24,
  },
  sectionContainer: {
    marginBottom: 16,
    backgroundColor: "#f9fafb",
    borderRadius: 12,
    borderWidth: 1,
    borderColor: "#e5e7eb",
    overflow: "hidden",
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#e5e7eb",
    backgroundColor: "#f3f4f6",
  },
  sectionTitleContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1f2937",
    marginLeft: 8,
  },
  editButton: {
    flexDirection: "row",
    alignItems: "center",
    padding: 6,
  },
  editButtonText: {
    fontSize: 14,
    color: THEME.gray,
    marginLeft: 4,
  },
  sectionContent: {
    padding: 16,
  },
  locationChipsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
  },
  locationChip: {
    backgroundColor: "rgba(247, 37, 133, 0.1)",
    borderRadius: 20,
    paddingVertical: 6,
    paddingHorizontal: 12,
    marginRight: 8,
    marginBottom: 8,
  },
  locationChipText: {
    fontSize: 14,
    color: THEME.accent,
    fontWeight: "500",
  },
  emptyText: {
    fontSize: 14,
    color: THEME.gray,
    fontStyle: "italic",
  },
  budgetText: {
    fontSize: 16,
    color: "#1f2937",
    fontWeight: "500",
  },
  propertyTypeText: {
    fontSize: 16,
    color: "#1f2937",
    marginBottom: 4,
  },
  roomsText: {
    fontSize: 14,
    color: THEME.gray,
  },
  amenitiesText: {
    fontSize: 16,
    color: "#1f2937",
  },
  notificationSettings: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  notificationSetting: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#ffffff",
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#e5e7eb",
  },
  notificationLabel: {
    fontSize: 14,
    color: "#1f2937",
    marginRight: 8,
  },
  matchingInfoContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#f9fafb",
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: "#e5e7eb",
    marginTop: 16,
  },
  matchingInfoText: {
    flex: 1,
    fontSize: 14,
    color: THEME.gray,
    marginLeft: 8,
    lineHeight: 20,
  },
});
