# ZakMakelaar – Full Project Summary

This document provides a consolidated overview of the ZakMakelaar codebase across backend and frontend, covering architecture, tech stack, data flow, key modules, tooling, operations, and documentation trail. It synthesizes the current repository state to help onboard developers, guide maintenance, and surface risks and next steps.

---

## High-Level Architecture

- Domain: AI-powered Dutch rental property aggregation, matching, and automation.
- Architecture:
  - Backend: Node.js/Express API with MongoDB (Mongoose), Redis caching/queues, scraping via Puppeteer/Cheerio, JWT Auth, scheduling via node-schedule, logging with <PERSON>, Swagger docs, Socket.IO for real-time events. Extensive operational scripts and dashboards for monitoring automation.
  - Frontend: Expo (React Native) app with Expo Router, React Navigation, React Query, Zustand stores. Multi-screen app targeting iOS/Android/Web with background tasks and notifications.
- Data Flow:
  1. Scrapers fetch listings from multiple sources on schedule (5 min default).
  2. Listings normalized and stored in MongoDB with deduplication and enrichment.
  3. Matching/AI services compute recommendations and automated actions.
  4. Users interact via mobile app; real-time notifications via push/SMS/email; Socket.IO streams live updates.
  5. Admin/monitoring dashboards (HTML) for ops visibility.
- DevOps: Dockerized backend; environment managed via .env and docs; local scripts to create test data and clean caches; Jest test setup for backend and RN tests for frontend.

---

## Backend Overview (zakmakelaar-backend)

### Runtime & Dependencies

- Node.js (Express), MongoDB (Mongoose), Redis (ioredis/redis), JWT (jsonwebtoken), validation (Joi, express-validator), security (helmet, rate limit, cors), file uploads (multer), schedulers (node-schedule/cron), scraping (puppeteer, cheerio), logging (winston + daily rotate), docs (swagger-jsdoc/ui), notifications (SendGrid, Twilio), websockets (socket.io), OpenAI SDK for AI features.
- Dev: Jest 30, Supertest, mongodb-memory-server, Nodemon, Chromium (runtime for headless contexts).

### Entry Points & Scripts (package.json)

- Run: npm start (node --no-deprecation src/index.js), npm run dev (nodemon)
- Test suite: jest; coverage configs; memory server for DB
- Scraper/AI test runners: test:scraper, test:huurwoningen, test:all-scrapers, test:ai
- Operational scripts:
  - Data: add-mock-property-data.js, clean-bad-data.js
  - Users: create-test-user.js
  - Cache: clear-cache.js
  - Monitoring: start-monitoring.js, monitor-auto-application.js, benchmark-scraper.js
  - Debug: multiple debug-workflow\*.js, debug-price-extraction.js
  - Health checks: test:auto-app[:health], test:notifications
  - Unified/performance: run-unified-schema-tests.js, run-performance-tests.js

### Source Structure (src/)

- index.js: Main server bootstrap. Schedules jobs (every 5 minutes by default as per README), initializes Express, connects to MongoDB/Redis, mounts routes and Swagger.
- config/: Env management and app-level config. See ENV_EXAMPLE.md and .env.example for variables.
- controllers/: REST handlers (auth, listings, scraper, user/application, etc.).
- middleware/: Auth (JWT), request validation, rate limiting, security headers.
- models/: Mongoose schemas (User, Listing/Property, Application, etc.). Migrations present under migrations/001–004 to evolve user/property and migrate listings to properties.
- routes/: Auth, listing, scraper, notifications, health, etc.
- services/: Business logic consolidation:
  - Scraper services for Funda/Pararius/Huurwoningen (+ multi-city coverage and anti-bot behavior).
  - AI services (OpenAI/OpenRouter aligned with AI_INTEGRATION.md).
  - Notification service (email/SMS/push), alertService.
  - Real-time via Socket.IO.
- schemas/: Validation schemas (Joi) for DTOs.
- utils/: Helpers (parsing, deduplication, logging, error handling, etc.).
- monitoring/: Performance/transformation metrics, job monitoring, dashboards publisher.
- tests/: Jest tests for controllers/services, integration with Supertest, memory-server.

### Public Dashboards (public/)

- monitoring-dashboard.html, performance-dashboard.html, transformation-dashboard.html, unified-dashboard.html
  - Used for operational visibility of scraping throughput, transformation correctness, and auto-application flows.

### Operational Documents

- README.md: Detailed overview, endpoints, scrapers per site, scheduling, DB schema, how to run.
- PROJECT_SUMMARY.md: Product- and business-level summary with AI features and scalability vision.
- AI_INTEGRATION.md: Outlines the AI layer and model procurement (OpenRouter/LLMs).
- AUTO_APPLICATION_API_DOCUMENTATION.md and SCRAPER_AUTO_APPLICATION_INTEGRATION.md: Define APIs and flows for automated application submission.
- ERROR_HANDLING_DOCUMENTATION.md: Centralized error standards and strategies.
- SECURITY_IMPLEMENTATION_SUMMARY.md: Security posture (JWT, rate limiting, helmet, validation, logging).
- SWAGGER_UPDATES.md: API docs evolution.
- NOTIFICATION_SYSTEM.md: Channel orchestration (email/SMS/push), providers, and retries.
- DOCKER_README.md, docker-compose.yml, Dockerfile: Containerization guide and services.
- ENV_EXAMPLE.md, .env.example, .env.docker: Environment variable contracts.
- HUURWONINGEN_INTEGRATION.md: Specific scraper implementation notes.

### Data & Filesystem

- uploads/: documents, profile-pictures, thumbnails
- logs/: Winston logs (rotating)
- migrations/: 001–004 scripts for evolving schemas

### Notable Behaviors

- Scheduler: Runs scrapers every 5 minutes; parallel execution with Promise.allSettled to avoid cross-failure contagion.
- Anti-scraping mitigations: Puppeteer driven, human-like events, JSON-LD parsing fallback to DOM scraping; cookie persistence, randomized delays, UA management.
- Matching/AI: Real-time and batch matching; supports enrichment, translation, summarization, contract review, application generation.
- Notifications: Real-time alerts via Socket.IO and external channels; health tests (test:notifications).
- Security: Helmet, rate limit, input validation, JWT auth, structured error handling, audit logs.

---

## Frontend Overview (zakmakelaar-frontend)

### Runtime & Dependencies

- Expo 53, React Native 0.79.5, React 19, React Navigation 7, Expo Router 5, React Query 5, Zustand 5.
- Expo modules: notifications, background-fetch/task-manager, device, document-picker, image/image-picker, web-browser, etc.
- UI: Expo icons, blur, linear-gradient, thematic components.
- Platform targets: Android, iOS, Web (react-native-web).

### Scripts & Tooling

- Start: expo start; platform runners: android/ios/web
- Linting: eslint + eslint-config-expo
- Tests: Jest + @testing-library/react-native/jest-native
- Types: TypeScript 5.8.3
- Project management: reset-project script

### App Structure (observed files)

- app/: Screens using file-based routing via Expo Router
  - index.tsx, login.tsx, dashboard.tsx, preferences.tsx, profile.tsx, application.tsx
  - auto-application-dashboard.tsx, auto-application-settings.tsx
  - autonomous-settings.tsx, autonomous-status.tsx
  - contract-review.tsx
  - listing-details.tsx
  - notification-history.tsx, notification-settings.tsx
  - safety-controls.tsx
  - (tabs)/ and property-owner/ route groups
  - +not-found.tsx, \_layout.tsx
- components/:
  - Core UI: ThemedText, ThemedView, PrimaryButton, Collapsible, ParallaxScrollView, ExternalLink, HapticTab, FuturisticGrid, SocialButton.
  - App bootstrap: AppInitializer, ErrorBoundary, QueryProvider, OnboardingNavigator/Wrapper/Progress, PreferencesOnboarding, ContextualHelp.
  - Domain: SmartPreferencesWizard, AIFeatureHighlight, HomeScreenTest, ListingsDebugTest.
  - Connectivity: ConnectionTest, SimpleConnectionTest, AutoRedirect.
  - Autonomous: components/autonomous/, components/dashboard/, components/preferences/, components/property-owner/, components/ui/.
- services/:
  - api.ts, authService.ts, listingsService.ts, autoApplicationService.ts, backgroundTaskService.ts, realTimeUpdateService.ts, realTimeMatchingService.ts, enhancedAIMatchingService.ts, aiService.ts, navigationService.ts, notificationService.ts, preferencesValidationService.ts, welcomeService.ts, queryClient.ts, logService.ts, index.ts.
- store/: Zustand slices for authStore, listingsStore, notificationStore, aiStore.
- hooks/: useAuthQueries, useListingsQueries, useAIQueries, useRealTimeUpdates, useThemeColor, useColorScheme(.web).
- config/: api.ts with base URLs and env helpers.
- constants/: Colors.ts; assets/ fonts/images for UI polish.
- tests: **tests**/app and jest.setup.js.

### Key Functional Areas

- Authentication flow and session persistence via AsyncStorage.
- Real-time updates: Subscription via Socket.IO/HTTP long-polling integrated through services + React Query.
- Matching UI: Preferences wizard, AI highlight components, listing detail views.
- Auto-application: Settings and dashboard screens reflect backend automation features; safety controls for guardrails.
- Notifications: History and settings management; device registration and push handling through Expo Notifications.
- Background processing: background-task service setup for periodic sync on device.
- Web compatibility: react-native-web and expo-router support.

---

## Cross-Cutting Concerns

- API Documentation: Swagger mounted at /api-docs on backend.
- Error Handling: Centralized logging (Winston), error docs, Express error middleware; retry logic in scrapers/services.
- Security: JWT auth, Helmet, rate limiting, input validation (Joi/express-validator). File upload handling via multer.
- Observability: HTML dashboards in public/, logs/, debug-workflow scripts, performance benchmarks.
- Testing:
  - Backend: Jest with mongodb-memory-server, Supertest; coverage config excludes debug/test runner scripts.
  - Frontend: Jest with Testing Library for RN; jest.setup configured.
- Data migrations: Progressive migrations 001–004 for user/property schema evolution and listings-to-properties migration.
- Data storage: MongoDB main store; Redis used for caching/rate limiting/queues; Node-Cache for process-level caching.

---

## How to Run Locally

Backend

1. cd zakmakelaar-backend
2. Copy .env.example to .env and adjust per ENV_EXAMPLE.md
3. npm install
4. npm run dev
5. Visit http://localhost:3000/api-docs for Swagger

Frontend

1. cd zakmakelaar-frontend
2. npm install
3. npx expo start
4. Open on Android/iOS simulators or web; configure API base URL in config/api.ts

---

## Risks, Gaps, and Recommendations

1. Scraping fragility

   - Risk: Source HTML/anti-bot changes break parsers. Puppeteer headless changes and site defenses can throttle/ban.
   - Actions: Add source adapters with contract tests; rotate proxies; persistent cookies/profiles per domain; central anti-bot config; structured feature flags for per-site fallbacks.

2. Rate limiting and IP hygiene

   - Risk: Ban due to high concurrency or bursty schedules.
   - Actions: Distributed rate limiter via Redis; staggered schedules; per-source concurrency caps; backoff with jitter.

3. Data quality and normalization

   - Risk: Inconsistent schema between sources (price, m², rooms).
   - Actions: Central normalization pipeline with unit tests; strict schema validators; anomaly detection dashboard; automated cleaners (expand clean-bad-data.js).

4. Security hardening

   - Risk: JWT handling, uploads, and admin endpoints exposure.
   - Actions: Enforce cookie flags (if web), refresh tokens/rotation, CSRF if applicable, file type/size validation, audit logging, endpoint ACLs, Secret scanning; align with SECURITY_IMPLEMENTATION_SUMMARY.md.

5. Observability

   - Risk: Hard-to-debug automation failures.
   - Actions: Structured logs with request IDs; metrics (Prometheus/OpenTelemetry); alerting for failures; expand public dashboards; SLOs for new-listing latency.

6. Automated application safety

   - Risk: Sending applications without user consent context.
   - Actions: Safety controls already appear present; add rule-based guardrails, audit trails, dry-run mode, and approval queues; robust error handling in AUTO_APPLICATION docs.

7. AI integration

   - Risk: Model drift/costs/errors.
   - Actions: Implement provider abstraction with retries, timeouts, cost tracking; prompt versioning; test harnesses in test:ai.

8. Testing coverage

   - Risk: Regressions in scrapers and matching.
   - Actions: Snapshot tests on HTML fixtures; city coverage permutations; contract tests for services and controllers; expand Supertest integration and smoke tests via docker-compose.

9. Config and secrets

   - Risk: Misconfigured env leads to runtime errors.
   - Actions: Centralized config loader with schema validation (Joi) at boot; fail-fast on missing keys; environment documentation kept in sync.

10. Mobile networking and background tasks
    - Risk: Expo background/API timeouts; web vs native differences.
    - Actions: Defensive retries and offline support in services; background-task permission guards; feature flags for platform-specific functionality.

---

## Notable Files for Quick Orientation

- Backend

  - src/index.js – Server boot, scheduling
  - src/services/scraper.js and specific scraper services
  - src/routes/\*.js – API surface
  - public/\*.html – Ops dashboards
  - migrations/00\*.js – Schema evolution
  - ERROR_HANDLING_DOCUMENTATION.md, SECURITY_IMPLEMENTATION_SUMMARY.md, AUTO_APPLICATION_API_DOCUMENTATION.md

- Frontend
  - app/\_layout.tsx – Navigation shell
  - app/\*.tsx – Primary screens (dashboard, preferences, matching, auto-application, contract-review)
  - components/QueryProvider.tsx – React Query setup
  - services/\* – API, notifications, background tasks, matching, sockets
  - store/\* – Zustand slices

---

## Quick Start Commands

- Backend dev

  - cd zakmakelaar-backend && npm i && npm run dev
  - npm run test, npm run benchmark, npm run monitor

- Frontend dev
  - cd zakmakelaar-frontend && npm i && npx expo start
  - npm run test

---

## Documentation Index (selected, repo root and backend)

- Root: IMPLEMENTATION_SUMMARY.md, backend-summary.md, frontend-summary.md, MOCK_DATA_MIGRATION_SUMMARY.md, HOW_TO_TEST_AUTO_APPLICATION.md, NAVIGATION_FIX_SUMMARY.md, BROWSER_AUTOMATION_IMPLEMENTATION_SUMMARY.md, PHOTO_GRID_FIX.md, PROPERTY_CAROUSEL_FEATURE.md, EDIT_PROPERTY_FEATURE.md, ADD_PROPERTY_FEATURE_SUMMARY.md, test-\*.md.
- Backend: PROJECT_SUMMARY.md, README.md, AI_INTEGRATION.md, AUTO_APPLICATION_API_DOCUMENTATION.md, SCRAPER_AUTO_APPLICATION_INTEGRATION.md, SECURITY_IMPLEMENTATION_SUMMARY.md, SWAGGER_UPDATES.md, ERROR_HANDLING_DOCUMENTATION.md, DOCKER_README.md, ENV_EXAMPLE.md, HUURWONINGEN_INTEGRATION.md.
- Frontend: README.md, EXPO_GO_LIMITATIONS.md, FRONTEND_CONNECTION_GUIDE.md, FRONTEND_AUTO_APPLICATION_UPDATE.md, AUTH_ANALYSIS.md, setup-dev-build.md.

---

In short: The repository is a well-structured full-stack system for scraping and normalizing rental listings, enriching them with AI-driven matching and automated application workflows, exposed via a secure API and consumed by an Expo-based mobile app with real-time updates and background operations. The project is production-oriented with Docker, monitoring dashboards, and a comprehensive set of operational scripts. The next phase should focus on resilience (scraping), observability, security hardening, and end-to-end test coverage to support scale.
