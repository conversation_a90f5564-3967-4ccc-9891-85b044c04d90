require("dotenv").config();

// AI Configuration (Google AI, OpenRouter, and OpenAI support)
const googleAIConfig = {
  apiKey: process.env.GOOGLE_AI_API_KEY,
  model: process.env.GOOGLE_AI_MODEL || "gemini-1.5-flash",
  maxTokens: parseInt(process.env.GOOGLE_AI_MAX_TOKENS) || 4000,
  temperature: parseFloat(process.env.GOOGLE_AI_TEMPERATURE) || 0.7,
};

const openRouterConfig = {
  apiKey: process.env.OPENROUTER_API_KEY || process.env.OPENAI_API_KEY,
  baseURL: process.env.OPENROUTER_API_KEY ? "https://openrouter.ai/api/v1" : "https://api.openai.com/v1",
  defaultModel: process.env.OPENROUTER_DEFAULT_MODEL || (process.env.OPENROUTER_API_KEY ? "deepseek/deepseek-r1-0528:free" : "gpt-4o-mini"),
  maxTokens: parseInt(process.env.OPENROUTER_MAX_TOKENS) || 4000,
  temperature: parseFloat(process.env.OPENROUTER_TEMPERATURE) || 0.7,
  // Available models for different tasks
  models: {
    analysis: process.env.OPENROUTER_ANALYSIS_MODEL || (process.env.OPENROUTER_API_KEY ? "deepseek/deepseek-r1-0528:free" : "gpt-4o-mini"),
    matching: process.env.OPENROUTER_MATCHING_MODEL || (process.env.OPENROUTER_API_KEY ? "deepseek/deepseek-r1-0528:free" : "gpt-4o-mini"),
    summarization: process.env.OPENROUTER_SUMMARIZATION_MODEL || (process.env.OPENROUTER_API_KEY ? "deepseek/deepseek-r1-0528:free" : "gpt-4o-mini"),
    translation: process.env.OPENROUTER_TRANSLATION_MODEL || (process.env.OPENROUTER_API_KEY ? "deepseek/deepseek-r1-0528:free" : "gpt-4o-mini"),
  },
};

module.exports = {
  // Server Configuration
  port: process.env.PORT || 3000,
  nodeEnv: process.env.NODE_ENV || "development",

  // Database Configuration
  mongoURI: process.env.MONGO_URI || "mongodb://localhost:27017/zakmakelaar",

  // JWT Configuration
  jwtSecret: process.env.JWT_SECRET || "fallback-secret-change-in-production",
  jwtExpiresIn: process.env.JWT_EXPIRES_IN || "7d",

  // SendGrid Configuration
  sendgridApiKey: process.env.SENDGRID_API_KEY,
  sendgridFromEmail:
    process.env.SENDGRID_FROM_EMAIL || "<EMAIL>",

  // Twilio Configuration
  twilioAccountSid: process.env.TWILIO_ACCOUNT_SID,
  twilioAuthToken: process.env.TWILIO_AUTH_TOKEN,
  twilioWhatsAppFrom:
    process.env.TWILIO_WHATSAPP_FROM || "whatsapp:+***********",

  // Rate Limiting Configuration
  rateLimitWindowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 900000, // 15 minutes
  rateLimitMaxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,

  // Scraping Configuration
  scrapingIntervalMinutes: parseInt(process.env.SCRAPING_INTERVAL_MINUTES) || 5,
  scrapingTimeoutMs: parseInt(process.env.SCRAPING_TIMEOUT_MS) || 60000,

  // CORS Configuration
  corsOrigin: process.env.CORS_ORIGIN
    ? process.env.CORS_ORIGIN.split(",")
    : ["http://localhost:3000"],

  // Redis Configuration
  redisHost: process.env.REDIS_HOST || "localhost",
  redisPort: parseInt(process.env.REDIS_PORT) || 6379,
  redisPassword: process.env.REDIS_PASSWORD || null,

  // Cache Configuration
  cacheDefaultTTL: parseInt(process.env.CACHE_DEFAULT_TTL) || 3600, // 1 hour
  cacheListingsTTL: parseInt(process.env.CACHE_LISTINGS_TTL) || 300, // 5 minutes
  cacheUserTTL: parseInt(process.env.CACHE_USER_TTL) || 1800, // 30 minutes

  openRouter: openRouterConfig,
  googleAI: googleAIConfig,
};
