import React from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withTiming,
  Easing,
  withSequence,
  withDelay,
  cancelAnimation,
} from 'react-native-reanimated';
import { useRealTimeUpdates } from '../../hooks/useRealTimeUpdates';

// Define theme colors
const THEME = {
  primary: '#4361ee',
  secondary: '#7209b7',
  accent: '#f72585',
  dark: '#0a0a18',
  light: '#ffffff',
  gray: '#6b7280',
};

interface RealTimeUpdateStatusProps {
  compact?: boolean;
  showControls?: boolean;
}

export const RealTimeUpdateStatus: React.FC<RealTimeUpdateStatusProps> = ({
  compact = false,
  showControls = true,
}) => {
  // Get real-time update state and actions
  const {
    isActive,
    refreshStrategy,
    lastRefreshTime,
    refreshNow,
    startUpdates,
    stopUpdates,
  } = useRealTimeUpdates();
  
  // Animation values
  const pulseAnimation = useSharedValue(1);
  const rotateAnimation = useSharedValue(0);
  
  // Start animations when component mounts
  React.useEffect(() => {
    if (isActive) {
      // Pulse animation for active status
      pulseAnimation.value = withRepeat(
        withSequence(
          withTiming(1.2, { duration: 1000, easing: Easing.ease }),
          withTiming(1, { duration: 1000, easing: Easing.ease })
        ),
        -1, // Infinite repeat
        true // Reverse
      );
    } else {
      // Cancel animation when inactive
      cancelAnimation(pulseAnimation);
      pulseAnimation.value = withTiming(1);
    }
    
    return () => {
      cancelAnimation(pulseAnimation);
    };
  }, [isActive]);
  
  // Start rotation animation when refreshing
  const handleRefresh = async () => {
    // Start rotation animation
    rotateAnimation.value = withSequence(
      withTiming(360, { duration: 1000, easing: Easing.linear }),
      withDelay(
        100,
        withTiming(0, { duration: 0 })
      )
    );
    
    // Refresh matches
    await refreshNow();
  };
  
  // Format time remaining until next refresh
  const formatTimeRemaining = () => {
    if (!refreshStrategy.nextScheduledRefresh) return 'Not scheduled';
    
    const now = new Date();
    const next = new Date(refreshStrategy.nextScheduledRefresh);
    const diffMs = next.getTime() - now.getTime();
    
    if (diffMs <= 0) return 'Due now';
    
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const remainingMins = diffMins % 60;
    
    if (diffHours > 0) {
      return `${diffHours}h ${remainingMins}m`;
    } else {
      return `${remainingMins}m`;
    }
  };
  
  // Format last refresh time
  const formatLastRefresh = () => {
    if (!lastRefreshTime) return 'Never';
    
    const now = new Date();
    const diff = now.getTime() - lastRefreshTime.getTime();
    
    if (diff < 60000) return 'Just now';
    
    const diffMins = Math.floor(diff / 60000);
    const diffHours = Math.floor(diffMins / 60);
    
    if (diffHours > 0) {
      return `${diffHours}h ago`;
    } else {
      return `${diffMins}m ago`;
    }
  };
  
  // Animated styles
  const pulseAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { scale: pulseAnimation.value },
      ],
    };
  });
  
  const rotateAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { rotate: `${rotateAnimation.value}deg` },
      ],
    };
  });
  
  // Render compact version
  if (compact) {
    return (
      <View style={styles.compactContainer}>
        <Animated.View style={[styles.statusIndicator, pulseAnimatedStyle]}>
          <View 
            style={[
              styles.statusDot,
              isActive ? styles.activeStatusDot : styles.inactiveStatusDot
            ]}
          />
        </Animated.View>
        
        <Text style={styles.compactText}>
          {isActive ? 'Auto-refresh on' : 'Auto-refresh off'}
        </Text>
        
        <TouchableOpacity 
          style={styles.compactRefreshButton}
          onPress={handleRefresh}
        >
          <Animated.View style={rotateAnimatedStyle}>
            <Ionicons name="refresh" size={16} color={THEME.primary} />
          </Animated.View>
        </TouchableOpacity>
      </View>
    );
  }
  
  // Render full version
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.statusContainer}>
          <Animated.View style={[styles.statusIndicator, pulseAnimatedStyle]}>
            <View 
              style={[
                styles.statusDot,
                isActive ? styles.activeStatusDot : styles.inactiveStatusDot
              ]}
            />
          </Animated.View>
          
          <Text style={styles.statusText}>
            {isActive ? 'Real-time updates active' : 'Real-time updates paused'}
          </Text>
        </View>
        
        {showControls && (
          <TouchableOpacity 
            style={styles.refreshButton}
            onPress={handleRefresh}
          >
            <Animated.View style={rotateAnimatedStyle}>
              <Ionicons name="refresh" size={20} color={THEME.primary} />
            </Animated.View>
          </TouchableOpacity>
        )}
      </View>
      
      <View style={styles.infoContainer}>
        <View style={styles.infoItem}>
          <Text style={styles.infoLabel}>Last update:</Text>
          <Text style={styles.infoValue}>{formatLastRefresh()}</Text>
        </View>
        
        {isActive && (
          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>Next update:</Text>
            <Text style={styles.infoValue}>{formatTimeRemaining()}</Text>
          </View>
        )}
      </View>
      
      {showControls && (
        <View style={styles.controlsContainer}>
          {isActive ? (
            <TouchableOpacity 
              style={[styles.controlButton, styles.pauseButton]}
              onPress={stopUpdates}
            >
              <Ionicons name="pause" size={16} color={THEME.light} />
              <Text style={styles.controlButtonText}>Pause Updates</Text>
            </TouchableOpacity>
          ) : (
            <TouchableOpacity 
              style={[styles.controlButton, styles.startButton]}
              onPress={() => startUpdates()}
            >
              <Ionicons name="play" size={16} color={THEME.light} />
              <Text style={styles.controlButtonText}>Resume Updates</Text>
            </TouchableOpacity>
          )}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusIndicator: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(67, 97, 238, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  statusDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
  },
  activeStatusDot: {
    backgroundColor: '#10b981',
  },
  inactiveStatusDot: {
    backgroundColor: '#ef4444',
  },
  statusText: {
    fontSize: 16,
    fontWeight: '500',
    color: THEME.dark,
  },
  refreshButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(67, 97, 238, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  infoContainer: {
    marginBottom: 16,
  },
  infoItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  infoLabel: {
    fontSize: 14,
    color: THEME.gray,
  },
  infoValue: {
    fontSize: 14,
    fontWeight: '500',
    color: THEME.dark,
  },
  controlsContainer: {
    flexDirection: 'row',
  },
  controlButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    borderRadius: 8,
  },
  startButton: {
    backgroundColor: '#10b981',
  },
  pauseButton: {
    backgroundColor: '#ef4444',
  },
  controlButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: THEME.light,
    marginLeft: 8,
  },
  compactContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(67, 97, 238, 0.1)',
    borderRadius: 16,
    paddingVertical: 4,
    paddingHorizontal: 8,
  },
  compactText: {
    fontSize: 12,
    color: THEME.primary,
    fontWeight: '500',
    marginLeft: 4,
  },
  compactRefreshButton: {
    marginLeft: 8,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(67, 97, 238, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});