const axios = require('axios');

// Test configuration
const BASE_URL = 'http://localhost:3000';
const TEST_USER = {
    email: '<EMAIL>',
    password: 'Password123'
};

const TEST_PROPERTY = {
    "_id": "68aa22092a8c01773a0e9eaf",
    "title": "Acaciastraat 1",
    "price": "€ 1.000 per maand",
    "location": "Utrecht",
    "url": "https://www.funda.nl/detail/huur/utrecht/huis-acaciastraat-1/89474643/",
    "size": null,
    "bedrooms": null,
    "rooms": "1",
    "propertyType": "woning",
    "description": "Per direct beschikbaar\nKamer voor 1 persoon, de woning wordt gedeeld met 4 andere huurders\nHuurprijs: € 1000,- (inclusief gas, water en licht)\nBorg  € 1000,-\nVoor bezichtiging neem telefonisch contact met ons op.",
    "year": "1900",
    "interior": null,
    "source": "funda.nl",
    "images": [
        "https://cloud.funda.nl/valentina_media/214/850/069_720x480.jpg",
        "https://cloud.funda.nl/valentina_media/214/850/070_720x480.jpg",
        "https://cloud.funda.nl/valentina_media/214/850/071_720x480.jpg",
        "https://cloud.funda.nl/valentina_media/214/791/857_720x480.jpg",
        "https://cloud.funda.nl/valentina_media/214/791/856_720x480.jpg",
        "https://marketinsightsassets.funda.nl/maps/<EMAIL>"
    ],
    "dateAdded": "2025-08-23T20:18:17.714Z",
    "timestamp": "2025-08-23T20:18:17.732Z"
};

let authToken = null;

async function login() {
    try {
        console.log('🔐 Logging in user...');
        const response = await axios.post(`${BASE_URL}/api/auth/login`, {
            email: TEST_USER.email,
            password: TEST_USER.password
        });

        authToken = response.data.token;
        console.log('✅ Login successful');
        return response.data;
    } catch (error) {
        console.error('❌ Login failed:', error.response?.data || error.message);
        throw error;
    }
}

async function getUserProfile() {
    try {
        console.log('👤 Getting user profile...');
        const response = await axios.get(`${BASE_URL}/api/auth/me`, {
            headers: { Authorization: `Bearer ${authToken}` }
        });

        console.log('✅ User profile retrieved');
        console.log('User ID:', response.data._id);
        console.log('User preferences:', response.data.preferences ? 'Available' : 'Not set');
        return response.data;
    } catch (error) {
        console.error('❌ Failed to get user profile:', error.response?.data || error.message);
        throw error;
    }
}

async function checkAutoApplicationSettings() {
    try {
        console.log('⚙️ Checking auto application settings...');
        const response = await axios.get(`${BASE_URL}/api/auto-application/settings`, {
            headers: { Authorization: `Bearer ${authToken}` }
        });

        console.log('✅ Auto application settings retrieved');
        console.log('Settings:', JSON.stringify(response.data, null, 2));
        return response.data;
    } catch (error) {
        console.error('❌ Failed to get auto application settings:', error.response?.data || error.message);
        return null;
    }
}

async function enableAutoApplication() {
    try {
        console.log('🔄 Enabling auto application...');
        const response = await axios.post(`${BASE_URL}/api/auto-application/enable`, {}, {
            headers: { Authorization: `Bearer ${authToken}` }
        });

        console.log('✅ Auto application enabled');
        console.log('Response:', JSON.stringify(response.data, null, 2));
        return response.data;
    } catch (error) {
        console.error('❌ Failed to enable auto application:', error.response?.data || error.message);
        return null;
    }
}

async function updatePersonalInfo(userId) {
    try {
        console.log('📝 Updating personal information...');

        const personalInfo = {
            personalInfo: {
                fullName: "Wellishant Test User",
                email: "<EMAIL>",
                phone: "+31612345678",
                dateOfBirth: "1990-01-01T00:00:00.000Z",
                nationality: "Dutch",
                occupation: "Software Developer",
                employer: "Tech Company",
                monthlyIncome: 4000,
                moveInDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
                leaseDuration: 12,
                numberOfOccupants: 1,
                hasGuarantor: false
            }
        };

        const response = await axios.put(`${BASE_URL}/api/auto-application/settings/${userId}`, personalInfo, {
            headers: { Authorization: `Bearer ${authToken}` }
        });

        console.log('✅ Personal information updated');
        return response.data;
    } catch (error) {
        console.error('❌ Failed to update personal info:', error.response?.data || error.message);
        return null;
    }
}

async function addToQueue() {
    try {
        console.log('📋 Adding property to auto application queue...');
        console.log('Property:', TEST_PROPERTY.title, '-', TEST_PROPERTY.location);

        const queueItem = {
            listingId: TEST_PROPERTY._id,
            listingTitle: TEST_PROPERTY.title,
            listingUrl: TEST_PROPERTY.url,
            price: parseInt(TEST_PROPERTY.price.replace(/[^\d]/g, '')) || 1000,
            location: TEST_PROPERTY.location,
            propertyType: TEST_PROPERTY.propertyType || 'apartment',
            rooms: parseInt(TEST_PROPERTY.rooms) || 1,
            description: TEST_PROPERTY.description,
            priority: 5,
            scheduledFor: new Date(Date.now() + 60000) // 1 minute from now
        };

        const response = await axios.post(`${BASE_URL}/api/auto-application/queue`, queueItem, {
            headers: { Authorization: `Bearer ${authToken}` }
        });

        console.log('✅ Property added to queue successfully');
        console.log('Queue item:', JSON.stringify(response.data, null, 2));
        return response.data;
    } catch (error) {
        console.error('❌ Failed to add to queue:', error.response?.data || error.message);
        throw error;
    }
}

async function processQueue() {
    try {
        console.log('⚙️ Processing auto application queue...');

        const response = await axios.post(`${BASE_URL}/api/auto-application/process-queue`, {}, {
            headers: { Authorization: `Bearer ${authToken}` }
        });

        console.log('✅ Queue processing initiated');
        console.log('Process result:', JSON.stringify(response.data, null, 2));
        return response.data;
    } catch (error) {
        console.error('❌ Queue processing failed:', error.response?.data || error.message);
        // Don't throw error here as this might be expected if queue is empty
        return null;
    }
}

async function checkQueue() {
    try {
        console.log('📋 Checking queue status...');
        const response = await axios.get(`${BASE_URL}/api/auto-application/queue`, {
            headers: { Authorization: `Bearer ${authToken}` }
        });

        console.log('✅ Queue retrieved');
        console.log('Queue items:', response.data.data?.length || 0);

        if (response.data.data && response.data.data.length > 0) {
            response.data.data.forEach((item, index) => {
                console.log(`Queue item ${index + 1}:`, {
                    id: item._id,
                    listingId: item.listingId,
                    status: item.status,
                    priority: item.priority,
                    createdAt: item.createdAt
                });
            });
        }

        return response.data;
    } catch (error) {
        console.error('❌ Failed to check queue:', error.response?.data || error.message);
        return { data: [] };
    }
}

async function checkResults() {
    try {
        console.log('📊 Checking application results...');
        const response = await axios.get(`${BASE_URL}/api/auto-application/results`, {
            headers: { Authorization: `Bearer ${authToken}` }
        });

        console.log('✅ Results retrieved');
        console.log('Total results:', response.data.data?.length || 0);

        if (response.data.data && response.data.data.length > 0) {
            response.data.data.forEach((result, index) => {
                console.log(`Result ${index + 1}:`, {
                    id: result._id,
                    listingId: result.listingId,
                    status: result.status,
                    submittedAt: result.submittedAt,
                    errorMessage: result.errorMessage || 'None'
                });
            });
        }

        return response.data;
    } catch (error) {
        console.error('❌ Failed to check results:', error.response?.data || error.message);
        return { data: [] };
    }
}

async function runTest() {
    console.log('🚀 Starting Auto Application Test for Funda Listing');
    console.log('='.repeat(60));

    try {
        // Step 1: Login
        await login();

        // Step 2: Get user profile
        const user = await getUserProfile();

        // Step 3: Check auto application settings
        const settings = await checkAutoApplicationSettings();

        // Step 3.5: Enable auto application if not enabled
        if (settings && !settings.data.canAutoApply) {
            console.log('⚠️ Auto application is not enabled, attempting to enable...');
            await enableAutoApplication();

            // Update personal info if profile is not complete
            if (!settings.data.isProfileComplete) {
                console.log('⚠️ Profile is not complete, updating personal information...');
                await updatePersonalInfo(settings.data.userId);
            }
        }

        // Step 4: Add property to queue
        const queueResult = await addToQueue();

        // Step 5: Check queue status
        await checkQueue();

        // Step 6: Process the queue (this will attempt the actual application)
        const processResult = await processQueue();

        // Step 7: Check results
        await checkResults();

        console.log('\n✅ Test completed successfully!');

    } catch (error) {
        console.error('\n❌ Test failed:', error.message);
        process.exit(1);
    }
}

// Run the test
runTest();