/**
 * Detailed analysis of Funda contact options
 */

const axios = require('axios');
const cheerio = require('cheerio');
require('dotenv').config();

const FUNDA_TEST_URL = 'https://www.funda.nl/detail/huur/utrecht/huis-acaciastraat-1/89474643/';

class FundaContactAnalyzer {
  async fetchAndAnalyze() {
    try {
      console.log('🌐 Fetching Funda page for detailed analysis...');
      const response = await axios.get(FUNDA_TEST_URL, {
        timeout: 10000,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
      });
      
      const $ = cheerio.load(response.data);
      
      console.log('\n📞 Analyzing all contact-related elements...');
      
      // Look for all elements containing "contact" (case insensitive)
      const contactElements = $('*').filter(function() {
        const text = $(this).text().toLowerCase();
        const classes = $(this).attr('class') || '';
        const id = $(this).attr('id') || '';
        const dataAttrs = Object.keys(this.attribs || {}).filter(attr => attr.startsWith('data-'));
        
        return text.includes('contact') || 
               classes.toLowerCase().includes('contact') || 
               id.toLowerCase().includes('contact') ||
               dataAttrs.some(attr => this.attribs[attr].toLowerCase().includes('contact'));
      });
      
      console.log(`Found ${contactElements.length} contact-related elements:`);
      
      contactElements.each((i, el) => {
        const $el = $(el);
        const tagName = $el.prop('tagName')?.toLowerCase();
        const text = $el.text()?.trim().substring(0, 100);
        const classes = $el.attr('class');
        const id = $el.attr('id');
        const href = $el.attr('href');
        
        console.log(`\n${i + 1}. <${tagName}>${id ? ` id="${id}"` : ''}${classes ? ` class="${classes}"` : ''}`);
        console.log(`   Text: "${text}"`);
        if (href) console.log(`   Href: ${href}`);
        
        // Check for data attributes
        const dataAttrs = Object.keys(el.attribs || {}).filter(attr => attr.startsWith('data-'));
        if (dataAttrs.length > 0) {
          dataAttrs.forEach(attr => {
            console.log(`   ${attr}="${el.attribs[attr]}"`);
          });
        }
      });
      
      console.log('\n📧 Looking for email-related elements...');
      const emailElements = $('*').filter(function() {
        const text = $(this).text().toLowerCase();
        const classes = $(this).attr('class') || '';
        const id = $(this).attr('id') || '';
        const dataAttrs = Object.keys(this.attribs || {}).filter(attr => attr.startsWith('data-'));
        
        return text.includes('email') || text.includes('e-mail') ||
               classes.toLowerCase().includes('email') || 
               id.toLowerCase().includes('email') ||
               dataAttrs.some(attr => this.attribs[attr].toLowerCase().includes('email'));
      });
      
      console.log(`Found ${emailElements.length} email-related elements:`);
      emailElements.each((i, el) => {
        const $el = $(el);
        const tagName = $el.prop('tagName')?.toLowerCase();
        const text = $el.text()?.trim().substring(0, 50);
        console.log(`${i + 1}. <${tagName}> - "${text}"`);
      });
      
      console.log('\n📱 Looking for phone-related elements...');
      const phoneElements = $('*').filter(function() {
        const text = $(this).text().toLowerCase();
        const classes = $(this).attr('class') || '';
        const href = $(this).attr('href') || '';
        
        return text.includes('phone') || text.includes('telefoon') || text.includes('bel') ||
               classes.toLowerCase().includes('phone') || 
               href.startsWith('tel:');
      });
      
      console.log(`Found ${phoneElements.length} phone-related elements:`);
      phoneElements.each((i, el) => {
        const $el = $(el);
        const tagName = $el.prop('tagName')?.toLowerCase();
        const text = $el.text()?.trim().substring(0, 50);
        const href = $el.attr('href');
        console.log(`${i + 1}. <${tagName}> - "${text}"${href ? ` (${href})` : ''}`);
      });
      
      console.log('\n🏢 Looking for broker/makelaar information...');
      const brokerElements = $('*').filter(function() {
        const text = $(this).text().toLowerCase();
        return text.includes('makelaar') || text.includes('broker') || text.includes('agent');
      });
      
      console.log(`Found ${brokerElements.length} broker-related elements:`);
      brokerElements.slice(0, 5).each((i, el) => {
        const $el = $(el);
        const tagName = $el.prop('tagName')?.toLowerCase();
        const text = $el.text()?.trim().substring(0, 100);
        console.log(`${i + 1}. <${tagName}> - "${text}"`);
      });
      
      console.log('\n🔍 Looking for all clickable elements that might trigger contact forms...');
      const clickableElements = $('button, a, [onclick], [data-*]').filter(function() {
        const text = $(this).text().toLowerCase();
        const classes = $(this).attr('class') || '';
        const id = $(this).attr('id') || '';
        const dataAttrs = Object.keys(this.attribs || {});
        
        return text.includes('contact') || text.includes('reageren') || text.includes('vraag') ||
               text.includes('bericht') || text.includes('mail') || text.includes('bel') ||
               classes.includes('contact') || id.includes('contact') ||
               dataAttrs.some(attr => attr.includes('contact') || attr.includes('optimizely'));
      });
      
      console.log(`Found ${clickableElements.length} potentially relevant clickable elements:`);
      clickableElements.each((i, el) => {
        const $el = $(el);
        const tagName = $el.prop('tagName')?.toLowerCase();
        const text = $el.text()?.trim().substring(0, 80);
        const classes = $el.attr('class');
        const id = $el.attr('id');
        const href = $el.attr('href');
        
        console.log(`\n${i + 1}. <${tagName}>${id ? ` id="${id}"` : ''}`);
        console.log(`   Text: "${text}"`);
        if (classes) console.log(`   Classes: ${classes}`);
        if (href) console.log(`   Href: ${href}`);
        
        // Check for data attributes
        const dataAttrs = Object.keys(el.attribs || {}).filter(attr => attr.startsWith('data-'));
        if (dataAttrs.length > 0) {
          dataAttrs.forEach(attr => {
            console.log(`   ${attr}="${el.attribs[attr]}"`);
          });
        }
      });
      
    } catch (error) {
      console.error('❌ Analysis failed:', error.message);
    }
  }
}

// Run the analysis
if (require.main === module) {
  const analyzer = new FundaContactAnalyzer();
  analyzer.fetchAndAnalyze().catch(console.error);
}

module.exports = FundaContactAnalyzer;
