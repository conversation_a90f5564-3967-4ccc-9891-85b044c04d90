// Debug script for application submission issues
const axios = require('axios');

const API_BASE_URL = 'http://localhost:3001/api';

// Test data
const testApplication = {
  applicationId: 'debug_test_' + Date.now(),
  method: 'manual',
  applicationData: {
    listingId: '1', // Simple ID to test
    message: 'Test application message',
    subject: 'Test Application Subject'
  }
};

async function debugApplicationSubmission() {
  console.log('🔍 Debugging Application Submission Issues\n');
  console.log('=' .repeat(60));

  try {
    // Step 1: Test the simple test endpoint first
    console.log('\n📋 Step 1: Testing simple endpoint...');
    
    const testResponse = await axios.post(
      `${API_BASE_URL}/ai/application/test`,
      testApplication,
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test-token' // This will likely fail auth, but we can see the error
        },
        timeout: 10000
      }
    );

    console.log('✅ Test endpoint response:', testResponse.status);
    console.log('📋 Test data:', JSON.stringify(testResponse.data, null, 2));

  } catch (testError) {
    console.log('❌ Test endpoint failed:');
    
    if (testError.response) {
      console.log('Status:', testError.response.status);
      console.log('Error data:', JSON.stringify(testError.response.data, null, 2));
      
      if (testError.response.status === 401) {
        console.log('\n💡 Authentication error - this is expected with test token');
        console.log('The endpoint is reachable, auth is working');
      } else if (testError.response.status === 500) {
        console.log('\n🚨 Server error - there\'s an issue with the backend code');
        console.log('Error details:', testError.response.data.details);
        console.log('Error type:', testError.response.data.errorType);
      }
    } else if (testError.code === 'ECONNREFUSED') {
      console.log('❌ Cannot connect to backend server.');
      console.log('Make sure the backend is running on http://localhost:3001');
      return;
    } else {
      console.log('Unexpected error:', testError.message);
    }
  }

  try {
    // Step 2: Test the actual submission endpoint
    console.log('\n📋 Step 2: Testing actual submission endpoint...');
    
    const submitResponse = await axios.post(
      `${API_BASE_URL}/ai/application/submit`,
      testApplication,
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test-token'
        },
        timeout: 10000
      }
    );

    console.log('✅ Submission endpoint response:', submitResponse.status);
    console.log('📋 Submission data:', JSON.stringify(submitResponse.data, null, 2));

  } catch (submitError) {
    console.log('❌ Submission endpoint failed:');
    
    if (submitError.response) {
      console.log('Status:', submitError.response.status);
      console.log('Error data:', JSON.stringify(submitError.response.data, null, 2));
      
      if (submitError.response.status === 401) {
        console.log('\n💡 Authentication error - you need a valid token');
        console.log('Get a token from your browser dev tools when logged in');
      } else if (submitError.response.status === 404) {
        console.log('\n💡 Listing not found - this is expected with test data');
        console.log('The endpoint is working, just needs a real listing ID');
      } else if (submitError.response.status === 500) {
        console.log('\n🚨 Server error - backend issue detected');
        console.log('Error details:', submitError.response.data.details);
        console.log('Error type:', submitError.response.data.errorType);
        console.log('Error code:', submitError.response.data.errorCode);
      }
    } else {
      console.log('Network error:', submitError.message);
    }
  }

  console.log('\n' + '='.repeat(60));
  console.log('🔧 Debugging Summary:');
  console.log('1. Check if backend server is running');
  console.log('2. Check authentication (need valid token)');
  console.log('3. Check if listing ID exists in database');
  console.log('4. Check server logs for detailed error information');
  console.log('5. Verify Application model is properly imported');
  
  console.log('\n💡 Next steps:');
  console.log('- Replace test-token with real auth token');
  console.log('- Replace listingId with real listing from database');
  console.log('- Check backend console for detailed error logs');
}

// Run the debug
debugApplicationSubmission().catch(console.error);