/**
 * LogService - A centralized logging service for the ZakMakelaar app
 * 
 * This service provides consistent logging across the application with
 * different log levels, component tagging, and optional persistence.
 */

// Log levels
export enum LogLevel {
  DEBUG = 'DEBUG',
  INFO = 'INFO',
  WARN = 'WARN',
  ERROR = 'ERROR'
}

// Interface for log entries
export interface LogEntry {
  timestamp: Date;
  level: LogLevel;
  component: string;
  message: string;
  data?: any;
}

class LogServiceClass {
  private logs: LogEntry[] = [];
  private readonly MAX_LOGS = 1000; // Maximum number of logs to keep in memory
  private readonly PERSIST_LOGS = false; // Whether to persist logs (can be enabled later)
  private readonly DEV_MODE = __DEV__; // Development mode flag

  /**
   * Log a message at the DEBUG level
   */
  debug(component: string, message: string, data?: any): void {
    this.log(LogLevel.DEBUG, component, message, data);
  }

  /**
   * Log a message at the INFO level
   */
  info(component: string, message: string, data?: any): void {
    this.log(LogLevel.INFO, component, message, data);
  }

  /**
   * Log a message at the WARN level
   */
  warn(component: string, message: string, data?: any): void {
    this.log(LogLevel.WARN, component, message, data);
  }

  /**
   * Log a message at the ERROR level
   */
  error(component: string, message: string, data?: any): void {
    this.log(LogLevel.ERROR, component, message, data);
  }

  /**
   * Log preferences validation results
   */
  preferenceValidation(isValid: boolean, validationData: any): void {
    const level = isValid ? LogLevel.INFO : LogLevel.WARN;
    this.log(level, 'PreferencesValidation', 
      `Preferences validation ${isValid ? 'passed' : 'failed'}`, 
      validationData);
  }

  /**
   * Log navigation events
   */
  navigation(from: string, to: string, params?: any): void {
    this.log(LogLevel.INFO, 'Navigation', `Navigating from ${from} to ${to}`, params);
  }

  /**
   * Log API requests
   */
  apiRequest(endpoint: string, method: string, params?: any): void {
    this.log(LogLevel.DEBUG, 'API', `${method} request to ${endpoint}`, params);
  }

  /**
   * Log API responses
   */
  apiResponse(endpoint: string, status: number, data?: any): void {
    const level = status >= 400 ? LogLevel.ERROR : LogLevel.DEBUG;
    this.log(level, 'API', `Response from ${endpoint} with status ${status}`, data);
  }

  /**
   * Log state changes
   */
  stateChange(component: string, stateName: string, oldValue: any, newValue: any): void {
    this.log(LogLevel.DEBUG, component, `State change: ${stateName}`, {
      old: oldValue,
      new: newValue
    });
  }

  /**
   * Log user actions
   */
  userAction(component: string, action: string, data?: any): void {
    this.log(LogLevel.INFO, component, `User action: ${action}`, data);
  }

  /**
   * Log lifecycle events
   */
  lifecycle(component: string, event: string, data?: any): void {
    this.log(LogLevel.DEBUG, component, `Lifecycle: ${event}`, data);
  }

  /**
   * Get all logs
   */
  getLogs(): LogEntry[] {
    return [...this.logs];
  }

  /**
   * Clear all logs
   */
  clearLogs(): void {
    this.logs = [];
  }

  /**
   * Export logs as JSON string
   */
  exportLogs(): string {
    return JSON.stringify(this.logs);
  }

  /**
   * Internal log method
   */
  private log(level: LogLevel, component: string, message: string, data?: any): void {
    const entry: LogEntry = {
      timestamp: new Date(),
      level,
      component,
      message,
      data
    };

    // Add to in-memory logs
    this.logs.push(entry);

    // Trim logs if exceeding maximum
    if (this.logs.length > this.MAX_LOGS) {
      this.logs = this.logs.slice(-this.MAX_LOGS);
    }

    // Log to console in development mode
    if (this.DEV_MODE) {
      const logMethod = this.getConsoleMethod(level);
      const formattedMessage = `[${level}] [${component}] ${message}`;
      
      if (data !== undefined) {
        logMethod(formattedMessage, data);
      } else {
        logMethod(formattedMessage);
      }
    }

    // Persist logs if enabled
    if (this.PERSIST_LOGS) {
      this.persistLog(level, component, message, data);
    }
  }

  /**
   * Get the appropriate console method for the log level
   */
  /**
   * Persist log to storage (AsyncStorage for React Native)
   */
  private async persistLog(level: LogLevel, component: string, message: string, data?: any): Promise<void> {
    try {
      const logEntry = {
        timestamp: new Date().toISOString(),
        level,
        component,
        message,
        data: data ? JSON.stringify(data) : undefined
      };
      
      // In a real implementation, you would save to AsyncStorage
      // For now, we'll just store in memory with a size limit
      const logKey = `log_${Date.now()}_${Math.random()}`;
      // AsyncStorage.setItem(logKey, JSON.stringify(logEntry));
    } catch (error) {
      console.error('Failed to persist log:', error);
    }
  }

  private getConsoleMethod(level: LogLevel): (...args: any[]) => void {
    switch (level) {
      case LogLevel.DEBUG:
        return console.debug;
      case LogLevel.INFO:
        return console.info;
      case LogLevel.WARN:
        return console.warn;
      case LogLevel.ERROR:
        return console.error;
      default:
        return console.log;
    }
  }
}

// Export a singleton instance
export const LogService = new LogServiceClass();