const { loggers } = require('./logger');
const AutoApplicationSettings = require('../models/AutoApplicationSettings');
const ApplicationQueue = require('../models/ApplicationQueue');
const ApplicationResult = require('../models/ApplicationResult');
const websocketService = require('./websocketService');

/**
 * ErrorHandlingService - Comprehensive error handling and recovery system
 * 
 * This service provides:
 * - Error categorization and appropriate response strategies
 * - Automatic retry logic with intelligent backoff algorithms
 * - Manual intervention triggers for complex error scenarios
 * - Graceful degradation for partial system failures
 * - Comprehensive error logging and alerting system
 */
class ErrorHandlingService {
  constructor() {
    this.logger = loggers.errorHandling || loggers.app;
    
    // Error categories with specific handling strategies
    this.errorCategories = {
      NETWORK: {
        name: 'Network Error',
        retryable: true,
        maxRetries: 3,
        backoffMultiplier: 2,
        baseDelay: 5000, // 5 seconds
        maxDelay: 300000, // 5 minutes
        requiresManualIntervention: false
      },
      FORM: {
        name: 'Form Error',
        retryable: true,
        maxRetries: 2,
        backoffMultiplier: 1.5,
        baseDelay: 10000, // 10 seconds
        maxDelay: 60000, // 1 minute
        requiresManualIntervention: false
      },
      DETECTION: {
        name: 'Detection Error',
        retryable: false,
        maxRetries: 0,
        backoffMultiplier: 0,
        baseDelay: 0,
        maxDelay: 0,
        requiresManualIntervention: true
      },
      DATA: {
        name: 'Data Error',
        retryable: false,
        maxRetries: 0,
        backoffMultiplier: 0,
        baseDelay: 0,
        maxDelay: 0,
        requiresManualIntervention: true
      },
      SYSTEM: {
        name: 'System Error',
        retryable: true,
        maxRetries: 2,
        backoffMultiplier: 3,
        baseDelay: 30000, // 30 seconds
        maxDelay: 600000, // 10 minutes
        requiresManualIntervention: false
      },
      RATE_LIMIT: {
        name: 'Rate Limit Error',
        retryable: true,
        maxRetries: 5,
        backoffMultiplier: 2,
        baseDelay: 60000, // 1 minute
        maxDelay: 3600000, // 1 hour
        requiresManualIntervention: false
      },
      CAPTCHA: {
        name: 'CAPTCHA Error',
        retryable: false,
        maxRetries: 0,
        backoffMultiplier: 0,
        baseDelay: 0,
        maxDelay: 0,
        requiresManualIntervention: true
      },
      AUTHENTICATION: {
        name: 'Authentication Error',
        retryable: false,
        maxRetries: 0,
        backoffMultiplier: 0,
        baseDelay: 0,
        maxDelay: 0,
        requiresManualIntervention: true
      }
    };

    // Error patterns for automatic categorization
    this.errorPatterns = {
      NETWORK: [
        /network|connection|timeout|ECONNRESET|ENOTFOUND|ETIMEDOUT/i,
        /fetch.*failed|request.*failed|socket.*hang/i,
        /ERR_NETWORK|ERR_INTERNET_DISCONNECTED/i
      ],
      FORM: [
        /form.*not.*found|field.*not.*found|selector.*not.*found/i,
        /validation.*failed|required.*field|invalid.*input/i,
        /form.*submission.*failed|submit.*button.*not.*found/i
      ],
      DETECTION: [
        /bot.*detected|blocked.*request/i,
        /access.*denied|forbidden|unauthorized.*bot/i,
        /suspicious.*activity/i
      ],
      DATA: [
        /missing.*required.*field|user.*not.*found|settings.*not.*found|document.*not.*found/i,
        /invalid.*data|data.*not.*found/i,
        /validation.*error|schema.*validation/i
      ],
      SYSTEM: [
        /database.*connection.*failed|mongodb.*error|connection.*pool/i,
        /service.*unavailable|internal.*server.*error/i,
        /memory.*error|out.*of.*memory|heap.*out.*of.*memory/i
      ],
      RATE_LIMIT: [
        /rate.*limit.*exceeded|too.*many.*requests|429/i,
        /quota.*exceeded|limit.*exceeded/i,
        /throttled|throttling/i
      ],
      CAPTCHA: [
        /captcha.*required|captcha.*verification.*required|solve.*captcha|captcha.*challenge/i,
        /human.*verification|prove.*you.*are.*human/i
      ],
      AUTHENTICATION: [
        /authentication.*failed|login.*required|session.*expired/i,
        /unauthorized|access.*token.*invalid/i,
        /credentials.*invalid|password.*incorrect/i
      ]
    };

    // Recovery strategies for different error types
    this.recoveryStrategies = {
      NETWORK: this.handleNetworkError.bind(this),
      FORM: this.handleFormError.bind(this),
      DETECTION: this.handleDetectionError.bind(this),
      DATA: this.handleDataError.bind(this),
      SYSTEM: this.handleSystemError.bind(this),
      RATE_LIMIT: this.handleRateLimitError.bind(this),
      CAPTCHA: this.handleCaptchaError.bind(this),
      AUTHENTICATION: this.handleAuthenticationError.bind(this)
    };

    // System health metrics
    this.healthMetrics = {
      totalErrors: 0,
      errorsByCategory: {},
      errorsByUser: {},
      systemFailures: 0,
      lastSystemFailure: null,
      recoverySuccessRate: 0,
      averageRecoveryTime: 0
    };

    // Initialize error tracking
    this.initializeErrorTracking();
  }

  /**
   * Initialize error tracking and metrics
   */
  initializeErrorTracking() {
    // Initialize error counters for each category
    Object.keys(this.errorCategories).forEach(category => {
      this.healthMetrics.errorsByCategory[category] = 0;
    });

    // Schedule periodic health checks
    setInterval(() => {
      this.performHealthCheck().catch(error => {
        this.logger.error('Error during health check:', error);
      });
    }, 300000); // Every 5 minutes

    this.logger.info('Error handling service initialized');
  }

  /**
   * Handle an error with automatic categorization and recovery
   * @param {Error} error - The error to handle
   * @param {Object} context - Context information about where the error occurred
   * @returns {Promise<Object>} Recovery result
   */
  async handleError(error, context = {}) {
    try {
      const errorInfo = this.categorizeError(error, context);
      
      this.logger.error(`Handling ${errorInfo.category} error:`, {
        error: error.message,
        stack: error.stack,
        context,
        category: errorInfo.category,
        retryable: errorInfo.retryable
      });

      // Update metrics
      this.updateErrorMetrics(errorInfo, context);

      // Execute recovery strategy
      const recoveryResult = await this.executeRecoveryStrategy(errorInfo, context);

      // Log recovery result
      this.logger.info(`Recovery strategy executed for ${errorInfo.category}:`, {
        success: recoveryResult.success,
        action: recoveryResult.action,
        retryScheduled: recoveryResult.retryScheduled,
        manualInterventionRequired: recoveryResult.manualInterventionRequired
      });

      // Send notifications if needed
      if (recoveryResult.manualInterventionRequired || !recoveryResult.success) {
        await this.sendErrorNotification(errorInfo, context, recoveryResult);
      }

      return recoveryResult;

    } catch (handlingError) {
      this.logger.error('Error in error handling system:', handlingError);
      
      // Fallback to basic error handling
      return {
        success: false,
        action: 'FALLBACK_HANDLING',
        error: handlingError.message,
        manualInterventionRequired: true,
        retryScheduled: false
      };
    }
  }

  /**
   * Categorize an error based on its message and context
   * @param {Error} error - The error to categorize
   * @param {Object} context - Context information
   * @returns {Object} Error information with category and handling strategy
   */
  categorizeError(error, context) {
    const errorMessage = error.message || error.toString();
    let category = 'SYSTEM'; // Default category
    let confidence = 0;

    // Try to match error patterns
    for (const [categoryName, patterns] of Object.entries(this.errorPatterns)) {
      for (const pattern of patterns) {
        if (pattern.test(errorMessage)) {
          if (confidence < 0.8) {
            category = categoryName;
            confidence = 0.8;
          }
          break;
        }
      }
    }

    // Context-based categorization
    if (context.service === 'FormAutomationEngine') {
      if (errorMessage.includes('selector') || errorMessage.includes('element')) {
        category = 'FORM';
        confidence = 0.9;
      }
    } else if (context.service === 'BrowserAutomation') {
      if (errorMessage.includes('navigation') || errorMessage.includes('timeout')) {
        category = 'NETWORK';
        confidence = 0.9;
      }
    }

    // HTTP status code based categorization
    if (context.statusCode) {
      if (context.statusCode === 429) {
        category = 'RATE_LIMIT';
        confidence = 1.0;
      } else if (context.statusCode === 403) {
        category = 'DETECTION';
        confidence = 0.9;
      } else if (context.statusCode >= 500) {
        category = 'SYSTEM';
        confidence = 0.8;
      }
    }

    const categoryConfig = this.errorCategories[category];
    
    return {
      category,
      confidence,
      retryable: categoryConfig.retryable,
      maxRetries: categoryConfig.maxRetries,
      requiresManualIntervention: categoryConfig.requiresManualIntervention,
      originalError: error,
      context
    };
  }

  /**
   * Execute the appropriate recovery strategy for an error
   * @param {Object} errorInfo - Categorized error information
   * @param {Object} context - Error context
   * @returns {Promise<Object>} Recovery result
   */
  async executeRecoveryStrategy(errorInfo, context) {
    const strategy = this.recoveryStrategies[errorInfo.category];
    
    if (!strategy) {
      this.logger.warn(`No recovery strategy found for category: ${errorInfo.category}`);
      return {
        success: false,
        action: 'NO_STRATEGY',
        manualInterventionRequired: true,
        retryScheduled: false
      };
    }

    try {
      return await strategy(errorInfo, context);
    } catch (strategyError) {
      this.logger.error(`Recovery strategy failed for ${errorInfo.category}:`, strategyError);
      return {
        success: false,
        action: 'STRATEGY_FAILED',
        error: strategyError.message,
        manualInterventionRequired: true,
        retryScheduled: false
      };
    }
  }

  /**
   * Handle network errors with retry logic
   */
  async handleNetworkError(errorInfo, context) {
    const { queueItemId, userId, attemptNumber = 1 } = context;

    if (attemptNumber > errorInfo.maxRetries) {
      return {
        success: false,
        action: 'MAX_RETRIES_EXCEEDED',
        manualInterventionRequired: false,
        retryScheduled: false
      };
    }

    // Calculate backoff delay
    const delay = this.calculateBackoffDelay(
      errorInfo.category,
      attemptNumber,
      this.errorCategories.NETWORK
    );

    // Schedule retry if we have a queue item
    if (queueItemId) {
      await this.scheduleRetry(queueItemId, delay, attemptNumber + 1);
    }

    return {
      success: true,
      action: 'RETRY_SCHEDULED',
      retryDelay: delay,
      retryAttempt: attemptNumber + 1,
      manualInterventionRequired: false,
      retryScheduled: true
    };
  }

  /**
   * Handle form errors with adaptive strategies
   */
  async handleFormError(errorInfo, context) {
    const { queueItemId, userId, formType, attemptNumber = 1 } = context;

    if (attemptNumber > errorInfo.maxRetries) {
      // Try to update form configuration for future attempts
      await this.updateFormConfiguration(formType, errorInfo.originalError);
      
      return {
        success: false,
        action: 'FORM_CONFIG_UPDATED',
        manualInterventionRequired: true,
        retryScheduled: false
      };
    }

    // For form errors, we might need to adapt our approach
    const adaptationResult = await this.adaptFormStrategy(errorInfo, context);
    
    if (adaptationResult.adapted) {
      // Schedule retry with adapted strategy
      const delay = this.calculateBackoffDelay(
        errorInfo.category,
        attemptNumber,
        this.errorCategories.FORM
      );

      if (queueItemId) {
        await this.scheduleRetry(queueItemId, delay, attemptNumber + 1, adaptationResult.adaptedConfig);
      }

      return {
        success: true,
        action: 'RETRY_WITH_ADAPTATION',
        adaptation: adaptationResult.adaptedConfig,
        retryDelay: delay,
        retryAttempt: attemptNumber + 1,
        manualInterventionRequired: false,
        retryScheduled: true
      };
    }

    return {
      success: false,
      action: 'ADAPTATION_FAILED',
      manualInterventionRequired: true,
      retryScheduled: false
    };
  }

  /**
   * Handle detection errors (CAPTCHA, bot detection)
   */
  async handleDetectionError(errorInfo, context) {
    const { userId, queueItemId } = context;

    // Pause user's auto-application temporarily
    if (userId) {
      await this.pauseUserAutoApplication(userId, 'Detection system triggered', 3600000); // 1 hour
    }

    // Mark queue item for manual intervention
    if (queueItemId) {
      await this.markForManualIntervention(queueItemId, 'Detection error - manual verification required');
    }

    return {
      success: true,
      action: 'USER_PAUSED_MANUAL_INTERVENTION',
      pauseDuration: 3600000,
      manualInterventionRequired: true,
      retryScheduled: false
    };
  }

  /**
   * Handle data errors (missing user info, invalid data)
   */
  async handleDataError(errorInfo, context) {
    const { userId, queueItemId, missingData } = context;

    // Notify user about missing data
    if (userId) {
      await this.notifyUserDataIssue(userId, errorInfo.originalError, missingData);
    }

    // Pause auto-application until data is fixed
    if (userId) {
      await this.pauseUserAutoApplication(userId, 'Missing or invalid data', null); // Indefinite pause
    }

    return {
      success: true,
      action: 'USER_NOTIFIED_PAUSED',
      manualInterventionRequired: true,
      retryScheduled: false
    };
  }

  /**
   * Handle system errors with graceful degradation
   */
  async handleSystemError(errorInfo, context) {
    const { service, queueItemId, attemptNumber = 1 } = context;

    // Check if this is a widespread system failure
    const isSystemWideFailure = await this.detectSystemWideFailure(errorInfo.originalError);

    if (isSystemWideFailure) {
      // Implement graceful degradation
      await this.implementGracefulDegradation(service);
      
      return {
        success: true,
        action: 'GRACEFUL_DEGRADATION',
        degradationMode: true,
        manualInterventionRequired: true,
        retryScheduled: false
      };
    }

    // For isolated system errors, retry with exponential backoff
    if (attemptNumber <= errorInfo.maxRetries) {
      const delay = this.calculateBackoffDelay(
        errorInfo.category,
        attemptNumber,
        this.errorCategories.SYSTEM
      );

      if (queueItemId) {
        await this.scheduleRetry(queueItemId, delay, attemptNumber + 1);
      }

      return {
        success: true,
        action: 'SYSTEM_RETRY_SCHEDULED',
        retryDelay: delay,
        retryAttempt: attemptNumber + 1,
        manualInterventionRequired: false,
        retryScheduled: true
      };
    }

    return {
      success: false,
      action: 'SYSTEM_ERROR_MAX_RETRIES',
      manualInterventionRequired: true,
      retryScheduled: false
    };
  }

  /**
   * Handle rate limit errors with intelligent backoff
   */
  async handleRateLimitError(errorInfo, context) {
    const { userId, queueItemId, rateLimitInfo, attemptNumber = 1 } = context;

    // Extract rate limit information if available
    const retryAfter = rateLimitInfo?.retryAfter || this.calculateBackoffDelay(
      errorInfo.category,
      attemptNumber,
      this.errorCategories.RATE_LIMIT
    );

    // Pause user temporarily
    if (userId) {
      await this.pauseUserAutoApplication(userId, 'Rate limit exceeded', retryAfter);
    }

    // Schedule retry after rate limit period
    if (queueItemId && attemptNumber <= errorInfo.maxRetries) {
      await this.scheduleRetry(queueItemId, retryAfter, attemptNumber + 1);
    }

    return {
      success: true,
      action: 'RATE_LIMIT_BACKOFF',
      retryDelay: retryAfter,
      retryAttempt: attemptNumber + 1,
      manualInterventionRequired: false,
      retryScheduled: attemptNumber <= errorInfo.maxRetries
    };
  }

  /**
   * Handle CAPTCHA errors
   */
  async handleCaptchaError(errorInfo, context) {
    const { userId, queueItemId } = context;

    // Notify user about CAPTCHA requirement
    if (userId) {
      await this.notifyUserCaptchaRequired(userId, context.url);
    }

    // Mark for manual intervention
    if (queueItemId) {
      await this.markForManualIntervention(queueItemId, 'CAPTCHA verification required');
    }

    return {
      success: true,
      action: 'CAPTCHA_MANUAL_INTERVENTION',
      manualInterventionRequired: true,
      retryScheduled: false
    };
  }

  /**
   * Handle authentication errors
   */
  async handleAuthenticationError(errorInfo, context) {
    const { userId, service } = context;

    // Check if this is a session expiry or credential issue
    if (errorInfo.originalError.message.includes('session')) {
      // Try to refresh session
      const refreshResult = await this.refreshUserSession(userId, service);
      
      if (refreshResult.success) {
        return {
          success: true,
          action: 'SESSION_REFRESHED',
          manualInterventionRequired: false,
          retryScheduled: true
        };
      }
    }

    // Notify user about authentication issue
    if (userId) {
      await this.notifyUserAuthenticationIssue(userId, service);
    }

    return {
      success: false,
      action: 'AUTHENTICATION_FAILED',
      manualInterventionRequired: true,
      retryScheduled: false
    };
  }

  /**
   * Calculate intelligent backoff delay
   */
  calculateBackoffDelay(category, attemptNumber, categoryConfig) {
    const baseDelay = categoryConfig.baseDelay;
    const multiplier = categoryConfig.backoffMultiplier;
    const maxDelay = categoryConfig.maxDelay;

    // Exponential backoff with jitter
    const exponentialDelay = baseDelay * Math.pow(multiplier, attemptNumber - 1);
    const jitter = Math.random() * 0.1 * exponentialDelay; // ±10% jitter
    const finalDelay = Math.min(exponentialDelay + jitter, maxDelay);

    return Math.floor(finalDelay);
  }

  /**
   * Schedule a retry for a queue item
   */
  async scheduleRetry(queueItemId, delay, attemptNumber, adaptedConfig = null) {
    try {
      const queueItem = await ApplicationQueue.findById(queueItemId);
      if (!queueItem) {
        this.logger.warn(`Queue item not found for retry: ${queueItemId}`);
        return;
      }

      // Update queue item for retry
      queueItem.status = 'retrying';
      queueItem.attempts = attemptNumber;
      queueItem.scheduledAt = new Date(Date.now() + delay);
      queueItem.delayUntil = new Date(Date.now() + delay);

      // Add adapted configuration if provided
      if (adaptedConfig) {
        queueItem.metadata.adaptedConfig = adaptedConfig;
      }

      // Add retry information
      queueItem.metadata.retryInfo = {
        reason: 'Error recovery',
        delay,
        attemptNumber,
        scheduledAt: queueItem.scheduledAt
      };

      await queueItem.save();

      this.logger.info(`Scheduled retry for queue item ${queueItemId}: attempt ${attemptNumber} in ${delay}ms`);

    } catch (error) {
      this.logger.error(`Error scheduling retry for ${queueItemId}:`, error);
    }
  }

  /**
   * Pause user's auto-application
   */
  async pauseUserAutoApplication(userId, reason, duration = null) {
    try {
      const settings = await AutoApplicationSettings.findByUserId(userId);
      if (!settings) {
        this.logger.warn(`Auto-application settings not found for user ${userId}`);
        return;
      }

      settings.status.isActive = false;
      settings.status.pausedReason = reason;
      settings.status.pausedUntil = duration ? new Date(Date.now() + duration) : null;
      settings.status.pausedAt = new Date();

      await settings.save();

      // Send WebSocket notification
      websocketService.sendAutoApplicationUpdate(userId, {
        action: 'paused',
        reason,
        pausedUntil: settings.status.pausedUntil,
        message: `Auto-application paused: ${reason}`
      });

      this.logger.info(`Paused auto-application for user ${userId}: ${reason}`);

    } catch (error) {
      this.logger.error(`Error pausing auto-application for user ${userId}:`, error);
    }
  }

  /**
   * Mark queue item for manual intervention
   */
  async markForManualIntervention(queueItemId, reason) {
    try {
      const queueItem = await ApplicationQueue.findById(queueItemId);
      if (!queueItem) {
        return;
      }

      queueItem.status = 'manual_intervention_required';
      queueItem.metadata.manualInterventionReason = reason;
      queueItem.metadata.manualInterventionRequiredAt = new Date();

      await queueItem.save();

      this.logger.info(`Marked queue item ${queueItemId} for manual intervention: ${reason}`);

    } catch (error) {
      this.logger.error(`Error marking queue item ${queueItemId} for manual intervention:`, error);
    }
  }

  /**
   * Update error metrics
   */
  updateErrorMetrics(errorInfo, context) {
    this.healthMetrics.totalErrors++;
    this.healthMetrics.errorsByCategory[errorInfo.category]++;

    if (context.userId) {
      if (!this.healthMetrics.errorsByUser[context.userId]) {
        this.healthMetrics.errorsByUser[context.userId] = 0;
      }
      this.healthMetrics.errorsByUser[context.userId]++;
    }

    if (errorInfo.category === 'SYSTEM') {
      this.healthMetrics.systemFailures++;
      this.healthMetrics.lastSystemFailure = new Date();
    }
  }

  /**
   * Detect system-wide failures
   */
  async detectSystemWideFailure(error) {
    // Check if we've had multiple system errors in a short time
    const recentSystemErrors = this.healthMetrics.systemFailures;
    const timeSinceLastFailure = this.healthMetrics.lastSystemFailure ? 
      Date.now() - this.healthMetrics.lastSystemFailure.getTime() : Infinity;

    // If we've had 3+ system errors in the last 5 minutes, consider it system-wide
    return recentSystemErrors >= 3 && timeSinceLastFailure < 300000;
  }

  /**
   * Implement graceful degradation
   */
  async implementGracefulDegradation(service) {
    this.logger.warn(`Implementing graceful degradation for service: ${service}`);

    // Pause all queue processing temporarily
    await ApplicationQueue.updateMany(
      { status: 'pending' },
      { 
        status: 'paused',
        'metadata.pauseReason': 'System degradation mode'
      }
    );

    // Send system-wide notification
    websocketService.broadcastSystemUpdate({
      action: 'degradation_mode',
      message: 'System is operating in degraded mode due to technical issues',
      affectedService: service
    });
  }

  /**
   * Perform periodic health check
   */
  async performHealthCheck() {
    try {
      const healthStatus = {
        timestamp: new Date(),
        totalErrors: this.healthMetrics.totalErrors,
        errorsByCategory: { ...this.healthMetrics.errorsByCategory },
        systemHealth: 'healthy'
      };

      // Check error rates
      const errorRate = this.healthMetrics.totalErrors / Math.max(1, Date.now() / 3600000); // errors per hour
      if (errorRate > 100) {
        healthStatus.systemHealth = 'degraded';
      } else if (errorRate > 50) {
        healthStatus.systemHealth = 'warning';
      }

      // Check for stuck queue items
      const stuckItems = await ApplicationQueue.countDocuments({
        status: 'processing',
        updatedAt: { $lt: new Date(Date.now() - 1800000) } // 30 minutes ago
      });

      if (stuckItems > 0) {
        this.logger.warn(`Found ${stuckItems} stuck queue items`);
        healthStatus.stuckItems = stuckItems;
      }

      this.logger.info('Health check completed:', healthStatus);

    } catch (error) {
      this.logger.error('Error during health check:', error);
    }
  }

  /**
   * Send error notification to relevant parties
   */
  async sendErrorNotification(errorInfo, context, recoveryResult) {
    try {
      const notification = {
        timestamp: new Date(),
        category: errorInfo.category,
        error: errorInfo.originalError.message,
        context,
        recoveryResult,
        severity: this.calculateErrorSeverity(errorInfo, context)
      };

      // Send to user if applicable
      if (context.userId && recoveryResult.manualInterventionRequired) {
        websocketService.sendAutoApplicationUpdate(context.userId, {
          action: 'error_notification',
          error: notification,
          message: this.generateUserFriendlyErrorMessage(errorInfo, recoveryResult)
        });
      }

      // Send to system administrators for critical errors
      if (notification.severity === 'critical') {
        // This would integrate with your alerting system
        this.logger.error('CRITICAL ERROR ALERT:', notification);
      }

    } catch (error) {
      this.logger.error('Error sending error notification:', error);
    }
  }

  /**
   * Calculate error severity
   */
  calculateErrorSeverity(errorInfo, context) {
    if (errorInfo.category === 'SYSTEM' && context.service === 'core') {
      return 'critical';
    }
    if (errorInfo.requiresManualIntervention) {
      return 'high';
    }
    if (errorInfo.retryable) {
      return 'medium';
    }
    return 'low';
  }

  /**
   * Generate user-friendly error message
   */
  generateUserFriendlyErrorMessage(errorInfo, recoveryResult) {
    const categoryMessages = {
      NETWORK: 'We\'re experiencing connection issues. Your application will be retried automatically.',
      FORM: 'There was an issue with the application form. We\'re adapting our approach and will retry.',
      DETECTION: 'The website has requested additional verification. Please check your email for instructions.',
      DATA: 'Some of your profile information needs to be updated. Please review your settings.',
      SYSTEM: 'We\'re experiencing technical difficulties. Your applications will resume shortly.',
      RATE_LIMIT: 'We\'ve reached the application limit. Your applications will resume automatically.',
      CAPTCHA: 'Manual verification is required. Please check the property listing directly.',
      AUTHENTICATION: 'There\'s an issue with your account authentication. Please log in again.'
    };

    return categoryMessages[errorInfo.category] || 'An unexpected error occurred. Our team has been notified.';
  }

  /**
   * Additional helper methods for specific error handling scenarios
   */

  async adaptFormStrategy(errorInfo, context) {
    // This would implement form adaptation logic
    // For now, return a basic adaptation
    return {
      adapted: true,
      adaptedConfig: {
        useAlternativeSelectors: true,
        increaseTimeouts: true,
        additionalValidation: true
      }
    };
  }

  async updateFormConfiguration(formType, error) {
    // This would update form configuration based on errors
    this.logger.info(`Updating form configuration for ${formType} due to error: ${error.message}`);
  }

  async notifyUserDataIssue(userId, error, missingData) {
    websocketService.sendAutoApplicationUpdate(userId, {
      action: 'data_issue',
      error: error.message,
      missingData,
      message: 'Please update your profile information to continue auto-applications'
    });
  }

  async notifyUserCaptchaRequired(userId, url) {
    websocketService.sendAutoApplicationUpdate(userId, {
      action: 'captcha_required',
      url,
      message: 'Manual verification required for this property application'
    });
  }

  async notifyUserAuthenticationIssue(userId, service) {
    websocketService.sendAutoApplicationUpdate(userId, {
      action: 'auth_issue',
      service,
      message: 'Please log in again to continue auto-applications'
    });
  }

  async refreshUserSession(userId, service) {
    // This would implement session refresh logic
    return { success: false, reason: 'Session refresh not implemented' };
  }

  /**
   * Get error handling statistics
   */
  getErrorStatistics() {
    return {
      ...this.healthMetrics,
      errorCategories: Object.keys(this.errorCategories),
      recoveryStrategies: Object.keys(this.recoveryStrategies)
    };
  }

  /**
   * Reset error statistics
   */
  resetErrorStatistics() {
    this.healthMetrics = {
      totalErrors: 0,
      errorsByCategory: {},
      errorsByUser: {},
      systemFailures: 0,
      lastSystemFailure: null,
      recoverySuccessRate: 0,
      averageRecoveryTime: 0
    };

    Object.keys(this.errorCategories).forEach(category => {
      this.healthMetrics.errorsByCategory[category] = 0;
    });

    this.logger.info('Error statistics reset');
  }

  /**
   * Shutdown the error handling service
   */
  shutdown() {
    this.logger.info('Error handling service shutting down');
    // Clean up any intervals or resources
  }
}

module.exports = ErrorHandlingService;