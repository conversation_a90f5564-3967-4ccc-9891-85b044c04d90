const mongoose = require('mongoose');

const propertySchema = new mongoose.Schema({
  // Basic property information
  title: { type: String, required: true },
  description: { type: String },
  
  // Location details
  address: {
    street: { type: String, required: true },
    houseNumber: { type: String, required: true },
    postalCode: { type: String, required: true },
    city: { type: String, required: true },
    province: { type: String },
    country: { type: String, default: 'Netherlands' },
    coordinates: {
      lat: { type: Number },
      lng: { type: Number }
    }
  },
  
  // Property specifications
  propertyType: {
    type: String,
    enum: ['apartment', 'house', 'studio', 'room', 'commercial'],
    required: true
  },
  
  // Size and layout
  size: { type: Number }, // in square meters
  rooms: { type: Number },
  bedrooms: { type: Number },
  bathrooms: { type: Number },
  floors: { type: Number },
  
  // Rental information
  rent: {
    amount: { type: Number, required: true },
    currency: { type: String, default: 'EUR' },
    period: { type: String, enum: ['monthly', 'weekly'], default: 'monthly' },
    includesUtilities: { type: Boolean, default: false },
    deposit: { type: Number },
    additionalCosts: {
      utilities: { type: Number },
      serviceCharges: { type: Number },
      parking: { type: Number },
      other: { type: Number }
    }
  },
  
  // Property features
  features: {
    furnished: { type: Boolean, default: false },
    interior: {
      type: String,
      enum: ['kaal', 'gestoffeerd', 'gemeubileerd'],
      default: 'kaal'
    },
    parking: { type: Boolean, default: false },
    parkingType: {
      type: String,
      enum: ['garage', 'carport', 'street', 'private'],
    },
    balcony: { type: Boolean, default: false },
    garden: { type: Boolean, default: false },
    terrace: { type: Boolean, default: false },
    elevator: { type: Boolean, default: false },
    airConditioning: { type: Boolean, default: false },
    heating: {
      type: String,
      enum: ['central', 'individual', 'none'],
      default: 'central'
    },
    energyLabel: {
      type: String,
      enum: ['A+++', 'A++', 'A+', 'A', 'B', 'C', 'D', 'E', 'F', 'G'],
    }
  },
  
  // Rental policies
  policies: {
    petsAllowed: { type: Boolean, default: false },
    smokingAllowed: { type: Boolean, default: false },
    studentsAllowed: { type: Boolean, default: true },
    expatFriendly: { type: Boolean, default: true },
    registrationAllowed: { type: Boolean, default: true }, // Gemeente registration
    minimumIncome: { type: Number }, // Minimum monthly income required
    maximumOccupants: { type: Number },
    minimumRentalPeriod: { type: Number }, // in months
    maximumRentalPeriod: { type: Number }, // in months
  },
  
  // Property owner information
  owner: {
    userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
    contactPreference: {
      type: String,
      enum: ['email', 'phone', 'both'],
      default: 'email'
    },
    responseTime: { type: String }, // e.g., "within 24 hours"
  },
  
  // Property status and availability
  status: {
    type: String,
    enum: ['draft', 'active', 'rented', 'maintenance', 'inactive'],
    default: 'draft'
  },
  
  availability: {
    availableFrom: { type: Date },
    availableUntil: { type: Date },
    viewingSchedule: {
      type: String,
      enum: ['by_appointment', 'open_house', 'flexible'],
      default: 'by_appointment'
    }
  },
  
  // Media and documentation
  images: [{
    url: { type: String },
    caption: { type: String },
    isPrimary: { type: Boolean, default: false },
    uploadedAt: { type: Date, default: Date.now }
  }],
  
  documents: [{
    type: {
      type: String,
      enum: ['floor_plan', 'energy_certificate', 'rental_agreement_template', 'house_rules', 'other']
    },
    filename: { type: String },
    url: { type: String },
    uploadedAt: { type: Date, default: Date.now }
  }],
  
  // Application and screening settings
  applicationSettings: {
    requiresApplication: { type: Boolean, default: true },
    autoScreening: { type: Boolean, default: false },
    minimumTenantScore: { type: Number, default: 60 },
    requiredDocuments: [{
      type: String,
      enum: ['income_proof', 'employment_contract', 'bank_statement', 'id_document', 'rental_reference']
    }],
    applicationDeadline: { type: Date },
    maxApplications: { type: Number }
  },
  
  // Performance tracking
  metrics: {
    views: { type: Number, default: 0 },
    applications: { type: Number, default: 0 },
    viewings: { type: Number, default: 0 },
    averageViewingRating: { type: Number },
    daysOnMarket: { type: Number },
    renewalRate: { type: Number }
  },
  
  // Timestamps
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
  publishedAt: { type: Date },
  rentedAt: { type: Date },
  
  // SEO and search optimization
  seo: {
    slug: { type: String, unique: true },
    metaTitle: { type: String },
    metaDescription: { type: String },
    keywords: [{ type: String }]
  }
});

// Indexes for performance
propertySchema.index({ 'owner.userId': 1 });
propertySchema.index({ status: 1 });
propertySchema.index({ 'rent.amount': 1 });
propertySchema.index({ 'address.city': 1 });
propertySchema.index({ 'address.postalCode': 1 });
propertySchema.index({ propertyType: 1 });
propertySchema.index({ 'availability.availableFrom': 1 });
propertySchema.index({ createdAt: -1 });
propertySchema.index({ 'metrics.views': -1 });

// Compound indexes for common queries
propertySchema.index({ status: 1, 'address.city': 1, 'rent.amount': 1 });
propertySchema.index({ 'owner.userId': 1, status: 1 });
propertySchema.index({ propertyType: 1, 'address.city': 1, status: 1 });

// Virtual fields
propertySchema.virtual('fullAddress').get(function() {
  return `${this.address.street} ${this.address.houseNumber}, ${this.address.postalCode} ${this.address.city}`;
});

propertySchema.virtual('monthlyTotalCost').get(function() {
  const rent = this.rent.amount || 0;
  const utilities = this.rent.additionalCosts?.utilities || 0;
  const service = this.rent.additionalCosts?.serviceCharges || 0;
  const parking = this.rent.additionalCosts?.parking || 0;
  const other = this.rent.additionalCosts?.other || 0;
  
  return rent + utilities + service + parking + other;
});

propertySchema.virtual('isAvailable').get(function() {
  const now = new Date();
  const availableFrom = this.availability.availableFrom;
  const availableUntil = this.availability.availableUntil;
  
  return this.status === 'active' && 
         (!availableFrom || availableFrom <= now) &&
         (!availableUntil || availableUntil >= now);
});

propertySchema.virtual('primaryImage').get(function() {
  const primary = this.images.find(img => img.isPrimary);
  return primary || this.images[0] || null;
});

// Pre-save middleware
propertySchema.pre('save', function(next) {
  this.updatedAt = new Date();
  
  // Generate slug if not provided
  if (!this.seo.slug && this.title) {
    this.seo.slug = this.title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '') + '-' + this._id.toString().slice(-6);
  }
  
  // Set published date when status changes to active
  if (this.isModified('status') && this.status === 'active' && !this.publishedAt) {
    this.publishedAt = new Date();
  }
  
  // Set rented date when status changes to rented
  if (this.isModified('status') && this.status === 'rented' && !this.rentedAt) {
    this.rentedAt = new Date();
  }
  
  // Calculate days on market
  if (this.publishedAt) {
    const now = this.rentedAt || new Date();
    this.metrics.daysOnMarket = Math.floor((now - this.publishedAt) / (1000 * 60 * 60 * 24));
  }
  
  next();
});

// Static methods
propertySchema.statics.findByOwner = function(userId, status = null) {
  const query = { 'owner.userId': userId };
  if (status) query.status = status;
  return this.find(query).sort({ createdAt: -1 });
};

propertySchema.statics.findAvailable = function(filters = {}) {
  const query = { 
    status: 'active',
    'availability.availableFrom': { $lte: new Date() }
  };
  
  if (filters.city) {
    query['address.city'] = new RegExp(filters.city, 'i');
  }
  
  if (filters.minRent || filters.maxRent) {
    query['rent.amount'] = {};
    if (filters.minRent) query['rent.amount'].$gte = filters.minRent;
    if (filters.maxRent) query['rent.amount'].$lte = filters.maxRent;
  }
  
  if (filters.propertyType) {
    query.propertyType = filters.propertyType;
  }
  
  if (filters.minRooms) {
    query.rooms = { $gte: filters.minRooms };
  }
  
  return this.find(query).sort({ createdAt: -1 });
};

propertySchema.statics.getMarketStats = function(city, propertyType) {
  return this.aggregate([
    {
      $match: {
        'address.city': city,
        propertyType: propertyType,
        status: { $in: ['active', 'rented'] }
      }
    },
    {
      $group: {
        _id: null,
        averageRent: { $avg: '$rent.amount' },
        medianRent: { $median: '$rent.amount' },
        minRent: { $min: '$rent.amount' },
        maxRent: { $max: '$rent.amount' },
        totalProperties: { $sum: 1 },
        averageSize: { $avg: '$size' },
        averageDaysOnMarket: { $avg: '$metrics.daysOnMarket' }
      }
    }
  ]);
};

// Instance methods
propertySchema.methods.incrementViews = function() {
  this.metrics.views += 1;
  return this.save();
};

propertySchema.methods.incrementApplications = function() {
  this.metrics.applications += 1;
  return this.save();
};

propertySchema.methods.addViewing = function(rating = null) {
  this.metrics.viewings += 1;
  if (rating && this.metrics.averageViewingRating) {
    this.metrics.averageViewingRating = 
      (this.metrics.averageViewingRating * (this.metrics.viewings - 1) + rating) / this.metrics.viewings;
  } else if (rating) {
    this.metrics.averageViewingRating = rating;
  }
  return this.save();
};

propertySchema.methods.isOwnedBy = function(userId) {
  return this.owner.userId.toString() === userId.toString();
};

propertySchema.methods.canApply = function() {
  const now = new Date();
  return this.status === 'active' &&
         this.applicationSettings.requiresApplication &&
         (!this.applicationSettings.applicationDeadline || this.applicationSettings.applicationDeadline > now) &&
         (!this.applicationSettings.maxApplications || this.metrics.applications < this.applicationSettings.maxApplications);
};

// Ensure virtual fields are serialized
propertySchema.set('toJSON', { virtuals: true });
propertySchema.set('toObject', { virtuals: true });

module.exports = mongoose.model('Property', propertySchema);