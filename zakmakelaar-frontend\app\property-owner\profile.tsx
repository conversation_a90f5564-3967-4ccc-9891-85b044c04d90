import React, { useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { useNavigation } from 'expo-router';
import { PropertyOwnerProfileScreen } from '../../components/property-owner/PropertyOwnerProfileScreen';

export default function PropertyOwnerProfile() {
  const navigation = useNavigation();

  // Disable the default navigation header
  useEffect(() => {
    navigation.setOptions({
      headerShown: false,
      gestureEnabled: true,
    });
  }, [navigation]);

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="auto" />
      <PropertyOwnerProfileScreen />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F8F8',
  },
});
