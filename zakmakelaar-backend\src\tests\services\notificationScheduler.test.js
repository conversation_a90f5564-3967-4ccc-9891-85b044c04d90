const notificationScheduler = require('../../services/notificationScheduler');
const AutoApplicationSettings = require('../../models/AutoApplicationSettings');
const ApplicationResult = require('../../models/ApplicationResult');
const ApplicationQueue = require('../../models/ApplicationQueue');
const User = require('../../models/User');
const autoApplicationNotificationService = require('../../services/autoApplicationNotificationService');

// Mock dependencies
jest.mock('../../models/AutoApplicationSettings');
jest.mock('../../models/ApplicationResult');
jest.mock('../../models/ApplicationQueue');
jest.mock('../../models/User');
jest.mock('../../services/autoApplicationNotificationService');
jest.mock('../../services/logger', () => ({
  loggers: {
    app: {
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn()
    }
  }
}));

// Mock node-cron
jest.mock('node-cron', () => ({
  schedule: jest.fn((pattern, callback, options) => ({
    start: jest.fn(),
    stop: jest.fn(),
    running: false
  }))
}));

describe('NotificationScheduler', () => {
  let mockUsers;
  let mockSettings;
  let mockApplicationResults;

  beforeEach(() => {
    jest.clearAllMocks();

    mockUsers = [
      {
        _id: 'user1',
        email: '<EMAIL>',
        profile: { phone: '+31612345678' }
      },
      {
        _id: 'user2',
        email: '<EMAIL>',
        profile: { phone: '+31687654321' }
      }
    ];

    mockSettings = [
      {
        userId: 'user1',
        enabled: true,
        settings: {
          notificationPreferences: {
            daily: true,
            weekly: true
          },
          maxApplicationsPerDay: 5
        }
      },
      {
        userId: 'user2',
        enabled: true,
        settings: {
          notificationPreferences: {
            daily: true,
            weekly: false
          },
          maxApplicationsPerDay: 10
        }
      }
    ];

    mockApplicationResults = [
      {
        _id: 'result1',
        userId: 'user1',
        status: 'submitted',
        submittedAt: new Date(),
        response: { success: true },
        listingSnapshot: {
          title: 'Property 1',
          location: 'Amsterdam',
          price: 2000
        },
        landlordResponse: {
          responseReceived: true,
          responseDate: new Date(Date.now() + 2 * 60 * 60 * 1000), // 2 hours later
          finalDecision: 'pending'
        }
      },
      {
        _id: 'result2',
        userId: 'user1',
        status: 'submitted',
        submittedAt: new Date(),
        response: { success: true },
        listingSnapshot: {
          title: 'Property 2',
          location: 'Utrecht',
          price: 1800
        },
        landlordResponse: {
          responseReceived: false
        }
      }
    ];

    // Mock database queries
    AutoApplicationSettings.find.mockResolvedValue(mockSettings);
    User.find.mockResolvedValue(mockUsers);
    ApplicationResult.find.mockResolvedValue(mockApplicationResults);
    ApplicationResult.aggregate.mockResolvedValue([]);
    ApplicationResult.countDocuments.mockResolvedValue(0);
    AutoApplicationSettings.findByUserId.mockImplementation(userId => 
      Promise.resolve(mockSettings.find(s => s.userId === userId))
    );
  });

  describe('initialization', () => {
    it('should initialize scheduler with cron jobs', () => {
      const cron = require('node-cron');
      
      notificationScheduler.initialize();
      
      expect(cron.schedule).toHaveBeenCalledTimes(3); // daily, weekly, cleanup
      expect(notificationScheduler.isInitialized).toBe(true);
    });

    it('should not reinitialize if already initialized', () => {
      const cron = require('node-cron');
      
      notificationScheduler.initialize();
      notificationScheduler.initialize(); // Second call
      
      expect(cron.schedule).toHaveBeenCalledTimes(3); // Should still be 3, not 6
    });
  });

  describe('start and stop', () => {
    it('should start all scheduled jobs', () => {
      const mockJob = {
        start: jest.fn(),
        stop: jest.fn()
      };
      
      const cron = require('node-cron');
      cron.schedule.mockReturnValue(mockJob);
      
      notificationScheduler.start();
      
      expect(mockJob.start).toHaveBeenCalledTimes(3);
    });

    it('should stop all scheduled jobs', () => {
      const mockJob = {
        start: jest.fn(),
        stop: jest.fn()
      };
      
      const cron = require('node-cron');
      cron.schedule.mockReturnValue(mockJob);
      
      notificationScheduler.initialize();
      notificationScheduler.stop();
      
      expect(mockJob.stop).toHaveBeenCalledTimes(3);
    });
  });

  describe('sendDailySummaries', () => {
    it('should send daily summaries to eligible users', async () => {
      const mockEligibleUsers = mockUsers.filter(u => u._id === 'user1' || u._id === 'user2');
      
      // Mock the populate method
      const mockSettingsWithUsers = mockSettings.map(setting => ({
        ...setting,
        userId: mockUsers.find(u => u._id === setting.userId)
      }));
      
      AutoApplicationSettings.find.mockReturnValue({
        populate: jest.fn().mockResolvedValue(mockSettingsWithUsers)
      });

      await notificationScheduler.sendDailySummaries();

      expect(autoApplicationNotificationService.sendDailySummary).toHaveBeenCalledTimes(2);
      expect(autoApplicationNotificationService.sendDailySummary).toHaveBeenCalledWith(
        'user1',
        expect.any(Object)
      );
      expect(autoApplicationNotificationService.sendDailySummary).toHaveBeenCalledWith(
        'user2',
        expect.any(Object)
      );
    });

    it('should skip users without daily notifications enabled', async () => {
      const settingsWithoutDaily = [{
        ...mockSettings[0],
        settings: {
          ...mockSettings[0].settings,
          notificationPreferences: { daily: false }
        },
        userId: mockUsers[0]
      }];

      AutoApplicationSettings.find.mockReturnValue({
        populate: jest.fn().mockResolvedValue(settingsWithoutDaily)
      });

      await notificationScheduler.sendDailySummaries();

      expect(autoApplicationNotificationService.sendDailySummary).not.toHaveBeenCalled();
    });

    it('should handle errors for individual users gracefully', async () => {
      const mockSettingsWithUsers = mockSettings.map(setting => ({
        ...setting,
        userId: mockUsers.find(u => u._id === setting.userId)
      }));
      
      AutoApplicationSettings.find.mockReturnValue({
        populate: jest.fn().mockResolvedValue(mockSettingsWithUsers)
      });

      // Make the first user fail
      autoApplicationNotificationService.sendDailySummary
        .mockRejectedValueOnce(new Error('Email service error'))
        .mockResolvedValueOnce();

      await notificationScheduler.sendDailySummaries();

      // Should still try to send to the second user
      expect(autoApplicationNotificationService.sendDailySummary).toHaveBeenCalledTimes(2);
    });
  });

  describe('sendWeeklySummaries', () => {
    it('should send weekly summaries to eligible users', async () => {
      const mockSettingsWithUsers = [{
        ...mockSettings[0],
        userId: mockUsers[0]
      }]; // Only user1 has weekly enabled

      AutoApplicationSettings.find.mockReturnValue({
        populate: jest.fn().mockResolvedValue(mockSettingsWithUsers)
      });

      await notificationScheduler.sendWeeklySummaries();

      expect(autoApplicationNotificationService.sendWeeklySummary).toHaveBeenCalledTimes(1);
      expect(autoApplicationNotificationService.sendWeeklySummary).toHaveBeenCalledWith(
        'user1',
        expect.any(Object)
      );
    });

    it('should skip users without weekly notifications enabled', async () => {
      const settingsWithoutWeekly = mockSettings.map(setting => ({
        ...setting,
        settings: {
          ...setting.settings,
          notificationPreferences: { weekly: false }
        },
        userId: mockUsers.find(u => u._id === setting.userId)
      }));

      AutoApplicationSettings.find.mockReturnValue({
        populate: jest.fn().mockResolvedValue(settingsWithoutWeekly)
      });

      await notificationScheduler.sendWeeklySummaries();

      expect(autoApplicationNotificationService.sendWeeklySummary).not.toHaveBeenCalled();
    });
  });

  describe('generateDailySummaryData', () => {
    it('should generate correct daily summary data', async () => {
      const today = new Date();
      const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
      const endOfDay = new Date(startOfDay.getTime() + 24 * 60 * 60 * 1000);

      const mockQueueItems = [
        { userId: 'user1', status: 'pending', createdAt: new Date() }
      ];

      ApplicationQueue.find.mockResolvedValue(mockQueueItems);
      ApplicationResult.find.mockResolvedValue(mockApplicationResults);

      const summaryData = await notificationScheduler.generateDailySummaryData('user1');

      expect(summaryData).toEqual(expect.objectContaining({
        applicationsSubmitted: expect.any(Number),
        applicationsSuccessful: expect.any(Number),
        applicationsRejected: expect.any(Number),
        applicationsPending: expect.any(Number),
        successRate: expect.any(Number),
        remainingApplications: expect.any(Number),
        recommendations: expect.any(Array),
        upcomingViewings: expect.any(Array)
      }));

      expect(ApplicationQueue.find).toHaveBeenCalledWith({
        userId: 'user1',
        createdAt: { $gte: startOfDay, $lt: endOfDay }
      });

      expect(ApplicationResult.find).toHaveBeenCalledWith({
        userId: 'user1',
        submittedAt: { $gte: startOfDay, $lt: endOfDay }
      });
    });

    it('should handle errors gracefully and return default data', async () => {
      ApplicationQueue.find.mockRejectedValue(new Error('Database error'));

      const summaryData = await notificationScheduler.generateDailySummaryData('user1');

      expect(summaryData).toEqual({
        applicationsSubmitted: 0,
        applicationsSuccessful: 0,
        applicationsRejected: 0,
        applicationsPending: 0,
        successRate: 0,
        recommendations: [],
        upcomingViewings: []
      });
    });
  });

  describe('generateWeeklySummaryData', () => {
    it('should generate correct weekly summary data', async () => {
      const mockPopulatedResults = mockApplicationResults.map(result => ({
        ...result,
        listingId: {
          title: result.listingSnapshot.title,
          location: result.listingSnapshot.location,
          price: result.listingSnapshot.price
        }
      }));

      ApplicationResult.find.mockReturnValue({
        populate: jest.fn().mockResolvedValue(mockPopulatedResults)
      });

      const summaryData = await notificationScheduler.generateWeeklySummaryData('user1');

      expect(summaryData).toEqual(expect.objectContaining({
        weekStart: expect.any(String),
        weekEnd: expect.any(String),
        totalApplications: expect.any(Number),
        successfulApplications: expect.any(Number),
        landlordResponses: expect.any(Number),
        viewingInvites: expect.any(Number),
        acceptances: expect.any(Number),
        successRate: expect.any(Number),
        responseRate: expect.any(Number),
        acceptanceRate: expect.any(Number),
        topPerformingLocations: expect.any(Array),
        bestApplicationTimes: expect.any(Array),
        marketInsights: expect.any(Array),
        performanceComparison: expect.any(Object),
        actionItems: expect.any(Array)
      }));
    });

    it('should handle errors gracefully and return default data', async () => {
      ApplicationResult.find.mockImplementation(() => {
        throw new Error('Database error');
      });

      const summaryData = await notificationScheduler.generateWeeklySummaryData('user1');

      expect(summaryData.totalApplications).toBe(0);
      expect(summaryData.successfulApplications).toBe(0);
      expect(summaryData.successRate).toBe(0);
    });
  });

  describe('getTopPerformingLocation', () => {
    it('should return top performing location', async () => {
      const mockAggregateResult = [
        { _id: 'Amsterdam', count: 5, successRate: 80 }
      ];

      ApplicationResult.aggregate.mockResolvedValue(mockAggregateResult);

      const topLocation = await notificationScheduler.getTopPerformingLocation(
        'user1',
        new Date('2024-01-01'),
        new Date('2024-01-02')
      );

      expect(topLocation).toBe('Amsterdam');
    });

    it('should return null if no results', async () => {
      ApplicationResult.aggregate.mockResolvedValue([]);

      const topLocation = await notificationScheduler.getTopPerformingLocation(
        'user1',
        new Date('2024-01-01'),
        new Date('2024-01-02')
      );

      expect(topLocation).toBeNull();
    });

    it('should handle errors gracefully', async () => {
      ApplicationResult.aggregate.mockRejectedValue(new Error('Database error'));

      const topLocation = await notificationScheduler.getTopPerformingLocation(
        'user1',
        new Date('2024-01-01'),
        new Date('2024-01-02')
      );

      expect(topLocation).toBeNull();
    });
  });

  describe('getAverageResponseTime', () => {
    it('should calculate average response time correctly', async () => {
      const mockAggregateResult = [
        { avgResponseTime: 2 * 60 * 60 * 1000 } // 2 hours in milliseconds
      ];

      ApplicationResult.aggregate.mockResolvedValue(mockAggregateResult);

      const avgResponseTime = await notificationScheduler.getAverageResponseTime(
        'user1',
        new Date('2024-01-01'),
        new Date('2024-01-02')
      );

      expect(avgResponseTime).toBe('2 hours');
    });

    it('should return days for longer response times', async () => {
      const mockAggregateResult = [
        { avgResponseTime: 3 * 24 * 60 * 60 * 1000 } // 3 days in milliseconds
      ];

      ApplicationResult.aggregate.mockResolvedValue(mockAggregateResult);

      const avgResponseTime = await notificationScheduler.getAverageResponseTime(
        'user1',
        new Date('2024-01-01'),
        new Date('2024-01-02')
      );

      expect(avgResponseTime).toBe('3 days');
    });

    it('should return null if no data', async () => {
      ApplicationResult.aggregate.mockResolvedValue([]);

      const avgResponseTime = await notificationScheduler.getAverageResponseTime(
        'user1',
        new Date('2024-01-01'),
        new Date('2024-01-02')
      );

      expect(avgResponseTime).toBeNull();
    });
  });

  describe('generateDailyRecommendations', () => {
    it('should generate appropriate recommendations based on metrics', async () => {
      const lowSuccessMetrics = {
        successRate: 30,
        applicationsSubmitted: 5,
        remainingApplications: 3
      };

      const recommendations = await notificationScheduler.generateDailyRecommendations(
        'user1',
        lowSuccessMetrics
      );

      expect(recommendations).toContain(
        'Consider reviewing your application template - success rate is below average'
      );
    });

    it('should recommend more applications if user has remaining capacity', async () => {
      const lowVolumeMetrics = {
        successRate: 70,
        applicationsSubmitted: 2,
        remainingApplications: 8
      };

      const recommendations = await notificationScheduler.generateDailyRecommendations(
        'user1',
        lowVolumeMetrics
      );

      expect(recommendations.some(rec => rec.includes('remaining applications'))).toBe(true);
    });

    it('should congratulate high success rate', async () => {
      const highSuccessMetrics = {
        successRate: 85,
        applicationsSubmitted: 4,
        remainingApplications: 1
      };

      const recommendations = await notificationScheduler.generateDailyRecommendations(
        'user1',
        highSuccessMetrics
      );

      expect(recommendations).toContain(
        'Excellent success rate today! Your application strategy is working well'
      );
    });
  });

  describe('generateWeeklyActionItems', () => {
    it('should generate action items for low success rate', async () => {
      const lowSuccessMetrics = {
        successRate: 45,
        responseRate: 60,
        acceptanceRate: 10,
        totalApplications: 20
      };

      const actionItems = await notificationScheduler.generateWeeklyActionItems(
        'user1',
        lowSuccessMetrics
      );

      expect(actionItems).toContainEqual(
        expect.objectContaining({
          priority: 'high',
          action: 'Review and optimize your application templates'
        })
      );
    });

    it('should generate action items for low response rate', async () => {
      const lowResponseMetrics = {
        successRate: 70,
        responseRate: 30,
        acceptanceRate: 15,
        totalApplications: 25
      };

      const actionItems = await notificationScheduler.generateWeeklyActionItems(
        'user1',
        lowResponseMetrics
      );

      expect(actionItems).toContainEqual(
        expect.objectContaining({
          priority: 'medium',
          action: 'Consider expanding your search criteria or target locations'
        })
      );
    });

    it('should generate action items for low application volume', async () => {
      const lowVolumeMetrics = {
        successRate: 80,
        responseRate: 70,
        acceptanceRate: 20,
        totalApplications: 5
      };

      const actionItems = await notificationScheduler.generateWeeklyActionItems(
        'user1',
        lowVolumeMetrics
      );

      expect(actionItems).toContainEqual(
        expect.objectContaining({
          priority: 'medium',
          action: 'Increase your daily application limit or expand search criteria'
        })
      );
    });
  });

  describe('cleanupOldData', () => {
    it('should clean up old application results and queue items', async () => {
      ApplicationResult.deleteMany.mockResolvedValue({ deletedCount: 5 });
      ApplicationQueue.deleteMany.mockResolvedValue({ deletedCount: 3 });

      await notificationScheduler.cleanupOldData();

      expect(ApplicationResult.deleteMany).toHaveBeenCalledWith(
        expect.objectContaining({
          submittedAt: expect.any(Object),
          'landlordResponse.finalDecision': { $in: ['rejected', 'expired'] }
        })
      );

      expect(ApplicationQueue.deleteMany).toHaveBeenCalledWith(
        expect.objectContaining({
          processedAt: expect.any(Object),
          status: { $in: ['completed', 'failed', 'cancelled'] }
        })
      );
    });

    it('should handle cleanup errors gracefully', async () => {
      ApplicationResult.deleteMany.mockRejectedValue(new Error('Database error'));

      await expect(notificationScheduler.cleanupOldData()).resolves.not.toThrow();
    });
  });

  describe('manual triggers', () => {
    it('should manually trigger daily summary', async () => {
      await notificationScheduler.triggerDailySummary('user1');

      expect(autoApplicationNotificationService.sendDailySummary).toHaveBeenCalledWith(
        'user1',
        expect.any(Object)
      );
    });

    it('should manually trigger weekly summary', async () => {
      await notificationScheduler.triggerWeeklySummary('user1');

      expect(autoApplicationNotificationService.sendWeeklySummary).toHaveBeenCalledWith(
        'user1',
        expect.any(Object)
      );
    });

    it('should handle manual trigger errors', async () => {
      autoApplicationNotificationService.sendDailySummary.mockRejectedValue(
        new Error('Service error')
      );

      await expect(
        notificationScheduler.triggerDailySummary('user1')
      ).rejects.toThrow('Service error');
    });
  });

  describe('getStatus', () => {
    it('should return scheduler status', () => {
      const status = notificationScheduler.getStatus();

      expect(status).toEqual(expect.objectContaining({
        initialized: expect.any(Boolean),
        activeJobs: expect.any(Array),
        runningJobs: expect.any(Array)
      }));
    });
  });

  describe('shutdown', () => {
    it('should shutdown gracefully', () => {
      notificationScheduler.shutdown();

      expect(notificationScheduler.isInitialized).toBe(false);
    });
  });

  describe('market insights generation', () => {
    it('should generate price-based insights', async () => {
      const mockPriceAnalysis = [{
        avgPrice: 2000,
        successfulAvgPrice: 2200,
        minPrice: 1500,
        maxPrice: 3000
      }];

      ApplicationResult.aggregate.mockResolvedValue(mockPriceAnalysis);

      const insights = await notificationScheduler.generateMarketInsights(
        'user1',
        new Date('2024-01-01'),
        new Date('2024-01-07')
      );

      expect(insights).toContain(
        expect.stringContaining('successful applications tend to be for higher-priced properties')
      );
    });

    it('should include general market insights', async () => {
      ApplicationResult.aggregate.mockResolvedValue([]);

      const insights = await notificationScheduler.generateMarketInsights(
        'user1',
        new Date('2024-01-01'),
        new Date('2024-01-07')
      );

      expect(insights).toContain('The rental market remains competitive - quick applications continue to be crucial');
    });

    it('should handle errors and return fallback insights', async () => {
      ApplicationResult.aggregate.mockRejectedValue(new Error('Database error'));

      const insights = await notificationScheduler.generateMarketInsights(
        'user1',
        new Date('2024-01-01'),
        new Date('2024-01-07')
      );

      expect(insights).toContain('Market analysis temporarily unavailable');
    });
  });

  describe('performance comparison', () => {
    it('should compare current week with previous week', async () => {
      const currentWeekStart = new Date('2024-01-08');
      
      // Mock current week metrics
      ApplicationResult.find
        .mockResolvedValueOnce([
          { status: 'submitted', response: { success: true }, landlordResponse: { responseReceived: true } },
          { status: 'submitted', response: { success: true }, landlordResponse: { responseReceived: false } }
        ])
        // Mock previous week metrics
        .mockResolvedValueOnce([
          { status: 'submitted', response: { success: true }, landlordResponse: { responseReceived: true } }
        ]);

      const comparison = await notificationScheduler.getPerformanceComparison('user1', currentWeekStart);

      expect(comparison).toEqual(expect.objectContaining({
        applications: expect.objectContaining({
          current: expect.any(Number),
          previous: expect.any(Number),
          change: expect.any(Number)
        }),
        successRate: expect.objectContaining({
          current: expect.any(Number),
          previous: expect.any(Number),
          change: expect.any(Number)
        }),
        responseRate: expect.objectContaining({
          current: expect.any(Number),
          previous: expect.any(Number),
          change: expect.any(Number)
        })
      }));
    });
  });
});

describe('Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should handle complete daily summary flow', async () => {
    const mockUser = {
      _id: 'user1',
      email: '<EMAIL>'
    };

    const mockSettings = {
      userId: 'user1',
      enabled: true,
      settings: {
        notificationPreferences: { daily: true },
        maxApplicationsPerDay: 5
      }
    };

    AutoApplicationSettings.find.mockReturnValue({
      populate: jest.fn().mockResolvedValue([{ ...mockSettings, userId: mockUser }])
    });

    ApplicationQueue.find.mockResolvedValue([]);
    ApplicationResult.find.mockResolvedValue([]);
    AutoApplicationSettings.findByUserId.mockResolvedValue(mockSettings);

    await notificationScheduler.sendDailySummaries();

    expect(autoApplicationNotificationService.sendDailySummary).toHaveBeenCalledWith(
      'user1',
      expect.objectContaining({
        applicationsSubmitted: 0,
        applicationsSuccessful: 0,
        successRate: 0
      })
    );
  });

  it('should handle complete weekly summary flow', async () => {
    const mockUser = {
      _id: 'user1',
      email: '<EMAIL>'
    };

    const mockSettings = {
      userId: 'user1',
      enabled: true,
      settings: {
        notificationPreferences: { weekly: true }
      }
    };

    AutoApplicationSettings.find.mockReturnValue({
      populate: jest.fn().mockResolvedValue([{ ...mockSettings, userId: mockUser }])
    });

    ApplicationResult.find.mockReturnValue({
      populate: jest.fn().mockResolvedValue([])
    });

    ApplicationResult.aggregate.mockResolvedValue([]);

    await notificationScheduler.sendWeeklySummaries();

    expect(autoApplicationNotificationService.sendWeeklySummary).toHaveBeenCalledWith(
      'user1',
      expect.objectContaining({
        totalApplications: 0,
        successfulApplications: 0,
        successRate: 0
      })
    );
  });
});